package com.jdh.o2oservice.vertical.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName:BizExtStatus
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/17 00:08
 * @Vserion: 1.0
 **/
@AllArgsConstructor
@Getter
public enum AngelCareBizExtStatus {

    /**
     * 初始状态
     */
    INIT_STATUS(0, "待服务"),

    /**
     * 点击出门后
     */
    ANGEL_GOUT_OUT(1, "护士上门中"),

    /**
     * 验证码验证通过后
     */
    CARE_CODE_WRITE_OFF(200, "服务中"),

    /**
     * 上门已确认身份
     */
    CARE_CONFIRM_IDENTITY(201, "上门已确认身份"),

    /**
     * 服务完成
     */
    CARE_SERVICE_FINISH(202, "服务完成"),

    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;
}
