package com.jdh.o2oservice.vertical.ext;

import com.google.common.collect.Lists;
import com.jd.matrix.sdk.annotation.Extension;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.work.WorkStatusHandleExt;
import com.jdh.o2oservice.ext.work.param.AngelTaskStatusParam;
import com.jdh.o2oservice.ext.work.param.CheckTaskStatusAlreadyParam;
import com.jdh.o2oservice.ext.work.response.AngelTaskStatusResponse;
import com.jdh.o2oservice.vertical.DeliveryApp;
import com.jdh.o2oservice.vertical.enums.AngelTaskStatusEnum;
import com.jdh.o2oservice.vertical.enums.SelfTestBizExtStatus;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName:TransferWorkStatusHandleExtImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/17 00:08
 * @Vserion: 1.0
 **/
@Extension(code = DeliveryApp.CODE)
@Slf4j
public class DadaWorkStatusHandleExtImpl implements WorkStatusHandleExt {

    private List<Integer> statusList = Lists.newArrayList(1, 2, 3, 4, 5, 6, 7);

    /**
     * 检查任务单的状态是否都到达了当前发布的新状态
     *
     * @param alreadyParam
     * @return
     */
    @Override
    public ExtResponse<Boolean> checkTaskStatusAlready(CheckTaskStatusAlreadyParam alreadyParam) {
        log.info("[TransferWorkStatusHandleExtImpl.checkTaskStatusAlready],执行骑手自检测任务工单状态处理!");

        Integer newTaskStatus = alreadyParam.getNewTaskStatus();
        int targetIndex = statusList.lastIndexOf(newTaskStatus);

        Set<Integer> compareIndexSet = alreadyParam.getCompareTaskParamList().stream()
                .map(item -> statusList.lastIndexOf(item.getTaskStatus()))
                .collect(Collectors.toSet());

        return ExtResponse.buildSuccess(compareIndexSet.stream().allMatch(item -> item >= targetIndex));
    }

    /**
     * 查询工单任务状态
     *
     * @param angelTaskStatusParam
     * @return
     */
    @Override
    public ExtResponse<AngelTaskStatusResponse> getTaskStatus(AngelTaskStatusParam angelTaskStatusParam) {
        if(angelTaskStatusParam.getWorkStopStatus() != 0) {
            return ExtResponse.buildSuccess(null);
        }
        AngelTaskStatusResponse taskStatusResponse = new AngelTaskStatusResponse();
        if(angelTaskStatusParam.getWorkStatus() == 1) {
            taskStatusResponse.setTaskStatus(AngelTaskStatusEnum.INIT.getType());
            taskStatusResponse.setTaskExtStatus(SelfTestBizExtStatus.INIT_STATUS.getType());
        }else if(angelTaskStatusParam.getWorkStatus() == 2) {
            taskStatusResponse.setTaskStatus(AngelTaskStatusEnum.INIT.getType());
            taskStatusResponse.setTaskExtStatus(SelfTestBizExtStatus.INIT_STATUS.getType());
        }else if(angelTaskStatusParam.getWorkStatus() == 3) {
            taskStatusResponse.setTaskStatus(AngelTaskStatusEnum.WAIT_SERVICE.getType());
            taskStatusResponse.setTaskExtStatus(SelfTestBizExtStatus.ANGEL_GOUT_OUT.getType());
        }else if (angelTaskStatusParam.getWorkStatus() == 4) {
            taskStatusResponse.setTaskStatus(AngelTaskStatusEnum.SERVICING.getType());
            taskStatusResponse.setTaskExtStatus(SelfTestBizExtStatus.CODE_WRITE_OFF.getType());
        }else if(angelTaskStatusParam.getWorkStatus() == 5) {
            taskStatusResponse.setTaskStatus(AngelTaskStatusEnum.SERVICED.getType());
            taskStatusResponse.setTaskExtStatus(SelfTestBizExtStatus.CHOOSE_DELIVERY_WAY.getType());
        }else if(angelTaskStatusParam.getWorkStatus() == 6) {
            taskStatusResponse.setTaskStatus(AngelTaskStatusEnum.CONFIRMED.getType());
            taskStatusResponse.setTaskExtStatus(SelfTestBizExtStatus.SAMPLE_DELIVERING.getType());
        }else if(angelTaskStatusParam.getWorkStatus() == 7) {
            taskStatusResponse.setTaskStatus(AngelTaskStatusEnum.COMPLETED.getType());
            taskStatusResponse.setTaskExtStatus(SelfTestBizExtStatus.SERVICE_FINISH.getType());
        }
        return ExtResponse.buildSuccess(taskStatusResponse);
    }
}
