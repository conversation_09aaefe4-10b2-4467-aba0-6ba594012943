package com.jdh.o2oservice.vertical.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author:lichen55
 * @Description:
 * @date 2024-05-27 20:47
 */
@Getter
@AllArgsConstructor
public enum AngelTaskStatusEnum {

    /**
     * 待服务
     */
    INIT(0, "初始状态"),

    /**
     * 待服务
     */
    WAIT_SERVICE(1, "已出门"),

    /**
     * 服务中
     */
    SERVICING(2, "服务中"),

    /**
     * 服务结束
     */
    SERVICED(3, "服务结束"),

    /**
     * 送检中
     */
    CONFIRMED(4, "送检中"),

    /**
     * 服务完成
     */
    COMPLETED(5, "服务完成"),

    /**
     * 已取消
     */
    CANCEL(6, "已取消"),

    /**
     * 已退款
     */
    REFUND(7, "已退款"),

    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;

}
