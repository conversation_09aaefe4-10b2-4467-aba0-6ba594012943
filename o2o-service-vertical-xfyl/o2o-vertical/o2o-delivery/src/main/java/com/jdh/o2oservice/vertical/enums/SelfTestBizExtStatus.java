package com.jdh.o2oservice.vertical.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName:BizExtStatus
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/17 00:08
 * @Vserion: 1.0
 **/
@AllArgsConstructor
@Getter
public enum SelfTestBizExtStatus {

    /**
     * 初始状态
     */
    INIT_STATUS(0, "待服务"),

    /**
     * 点击出门后
     */
    ANGEL_GOUT_OUT(1, "护士上门中"),

    /**
     * 验证码验证通过后
     */
    CODE_WRITE_OFF(100, "核验采样"),

    /**
     * 患者身份确认后
     */
    CONFIRM_IDENTITY(101, "核验采样"),

    /**
     * 保存条码并选择送检方式
     */
    CHOOSE_DELIVERY_WAY(102, "样本送检"),

    /**
     * 全部实验室选择完配送方式
     */
    SAMPLE_DELIVERING(103, "样本送检"),

    /**
     * 全部样本送到实验室
     */
    SERVICE_FINISH(104, "服务完成"),
    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;
}
