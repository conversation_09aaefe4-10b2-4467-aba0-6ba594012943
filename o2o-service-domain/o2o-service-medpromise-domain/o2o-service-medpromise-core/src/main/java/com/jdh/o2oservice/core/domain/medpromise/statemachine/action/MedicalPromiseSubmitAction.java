package com.jdh.o2oservice.core.domain.medpromise.statemachine.action;

import cn.hutool.core.convert.Convert;
import com.jd.fastjson.JSON;
import com.jd.health.xfyl.merchant.export.param.AppointmentParam;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.medpromise.context.MedicalPromiseSubmitContext;
import com.jdh.o2oservice.core.domain.medpromise.converter.MedicalPromiseDomainConvert;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @ClassName MedicalPromiseSubmitAction
 * @Description
 * <AUTHOR>
 * @Date 2024/4/24 21:29
 **/
@Service
@Slf4j
@Lazy // 保持懒加载注解
public class MedicalPromiseSubmitAction extends AbstractMedicalPromiseAction{

    /**
     *
     * @param from
     * @param to
     * @param event
     * @param stateContext
     */
    @Override
    protected void extendExecute(MedicalPromiseStatusEnum from, MedicalPromiseStatusEnum to, MedPromiseEventTypeEnum event, StateContext stateContext) {
        MedicalPromiseSubmitContext context = Convert.convert(MedicalPromiseSubmitContext.class, stateContext);
        log.info("MedicalPromiseSubmitAction -> execute, context={}", JSON.toJSONString(context));
        AppointmentParam appointmentParam = MedicalPromiseDomainConvert.INSTANCE.convert(context);
        appointmentParam.setAppointmentDate(new Date());
        context.setAppointmentParam(appointmentParam);
        MedicalPromise medicalPromise = context.getMedicalPromise();
        //绑码之后是正常流转，否则保持原状态
        if (StringUtils.isBlank(medicalPromise.getSpecimenCode())){
            return;
        }
        if(MedicalPromiseStatusEnum.needChangeBindStatus(from.getStatus())) {
            medicalPromise.setStatus(to.getStatus());
        }
        log.info("MedicalPromiseSubmitAction -> execute end, context={}", JSON.toJSONString(context));
    }
}