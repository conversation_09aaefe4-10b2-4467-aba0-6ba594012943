package com.jdh.o2oservice.core.domain.medpromise.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/9/26
 * @description 检测单统计数据
 */
@Data
public class MedicalPromiseSummary {

    private Long medicalPromiseId;//检测单id

    private String stationName;//实验室名称

    private Long stationId;//实验室id

    private String serviceItemName;//检测项目名称

    private Long serviceItemId;//检测项目id

    private Date checkTime;//收样时间

    private Date deliveryFinishTime;//骑手妥投时间

    private Date testTime;//上机时间

    private Date testFinishTime;//报告审核时间

    private Date reportTime;//出报告时间

    private Long testDuration;//检测项目配置检测时长,单位分

    private Integer testTimeOut;//履约超时

    private Integer testNoTimeOut;//履约不超时

    private Integer upTimeOut;//上机超时

    private Integer upNoTimeOut;//上机不超时

    private Integer exportTimeOut;//出报告超时

    private Integer exportNoTimeOut;//出报告不超时




}
