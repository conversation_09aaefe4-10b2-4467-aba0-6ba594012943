package com.jdh.o2oservice.core.domain.angel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Splitter;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.JdhAngelNegativeReasonConfig;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.core.domain.angel.context.AngelEvaluationContext;
import com.jdh.o2oservice.core.domain.angel.context.AngelEvaluationQueryContext;
import com.jdh.o2oservice.core.domain.angel.converter.JdhAngelEcologyValuationConverter;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelEcologyValuation;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelEcologyValuationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelEcologyValuationQuery;
import com.jdh.o2oservice.core.domain.angel.service.AngelEcologyDomainService;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhAngelEvaluationTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SensitiveSenceEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.KeeperDetectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.SensitiveWordParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName AngelEcologyDomainServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/4/10 18:55
 */
@Service
@Slf4j
public class AngelEcologyDomainServiceImpl implements AngelEcologyDomainService {

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private KeeperDetectionServiceRpc keeperDetectionServiceRpc;

    @Resource
    private AngelEcologyValuationRepository angelEcologyValuationRepository;

    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * 处理召回评价数据
     *
     * @param angelEvaluationContext
     * @return
     */
    @Override
    public int handleRecallEvaluationData(AngelEvaluationContext angelEvaluationContext) {
        if(Objects.isNull(angelEvaluationContext)) {
            return 0;
        }
        JdhAngelEcologyValuation jdhAngelEcologyValuation = JdhAngelEcologyValuationConverter.INS.convertToJdhAngelEcologyValuation(angelEvaluationContext);

        //处理差评标签
        String tags = angelEvaluationContext.getTags();
        if(StringUtils.isNotBlank(tags)) {
            List<JdhAngelNegativeReasonConfig> angelNegativeReasonList = duccConfig.getAngelNegativeReasonList();
            List<String> tagList = Splitter.on(",").splitToList(tags);
            log.info("AngelEcologyDomainServiceImpl -> handleRecallEvaluationData, tagList={}", JSON.toJSONString(tagList));
            List<JdhAngelNegativeReasonConfig> negativeReasonConfigList = angelNegativeReasonList.stream().filter(item -> tagList.contains(item.getNegativeReasonTypeDesc())).collect(Collectors.toList());
            log.info("AngelEcologyDomainServiceImpl -> handleRecallEvaluationData, negativeReasonConfigList={}", JSON.toJSONString(negativeReasonConfigList));
            if(CollectionUtils.isNotEmpty(negativeReasonConfigList)) {
                Map<String, List<JdhAngelNegativeReasonConfig>> evaluationTypeGroup = negativeReasonConfigList.stream().collect(Collectors.groupingBy(JdhAngelNegativeReasonConfig::getEvaluationType));
                evaluationTypeGroup.forEach((key, value) -> {
                    List<String> reasonTypeList = value.stream().map(JdhAngelNegativeReasonConfig::getNegativeReasonType).collect(Collectors.toList());
                    log.info("AngelEcologyDomainServiceImpl -> handleRecallEvaluationData, reasonTypeList={}, negativeReasonKey={}", JSON.toJSONString(reasonTypeList), JdhAngelEvaluationTypeEnum.getByType(key).getNegativeReasonKey());
                    BeanUtil.setProperty(jdhAngelEcologyValuation, JdhAngelEvaluationTypeEnum.getByType(key).getNegativeReasonKey(), JSONObject.toJSONString(reasonTypeList));
                });
            }
        }

        if(StringUtils.isNotBlank(angelEvaluationContext.getAroundValuation())) {
            boolean isPass = keeperDetectionServiceRpc.checkSensitiveWord(SensitiveWordParam.builder().sence(SensitiveSenceEnum.EVALUEATE_SENCE).text(angelEvaluationContext.getAroundValuation()).build());
            jdhAngelEcologyValuation.setValuationSensitiveStatus(isPass ? 1 : 2);
            jdhAngelEcologyValuation.setShowStatus(isPass ? 1 : 0);
        }else {
            jdhAngelEcologyValuation.setValuationSensitiveStatus(1);
            jdhAngelEcologyValuation.setShowStatus(1);
        }
        BigDecimal totalScore = angelEvaluationContext.getRespondTimeScore()
                .add(angelEvaluationContext.getHomeTimeScore())
                .add(angelEvaluationContext.getSkillScore())
                .add(angelEvaluationContext.getMannerScore());
        jdhAngelEcologyValuation.setAroundScore(totalScore.divide(new BigDecimal(4), 2, BigDecimal.ROUND_HALF_UP));
        jdhAngelEcologyValuation.setValuationId(generateIdFactory.getId());
        return angelEcologyValuationRepository.save(jdhAngelEcologyValuation);
    }

    /**
     * 查询服务者评价数据
     *
     * @param evaluationQueryContext
     * @return
     */
    @Override
    @LogAndAlarm
    public List<JdhAngelEcologyValuation> queryUserValuation(AngelEvaluationQueryContext evaluationQueryContext) {
        AngelEcologyValuationQuery query = new AngelEcologyValuationQuery();
        query.setUserPin(evaluationQueryContext.getUserPin());
        query.setSkuIdList(evaluationQueryContext.getSkuId());
        query.setOrderId(evaluationQueryContext.getOrderId());
        query.setPromiseId(evaluationQueryContext.getPromiseId());
        query.setAngelId(evaluationQueryContext.getAngelId());
        query.setValuationSensitiveStatus(evaluationQueryContext.getValuationSensitiveStatus());
        query.setShowStatus(evaluationQueryContext.getShowStatus());
        query.setPageSize(CommonConstant.NUMBER_FIFTY);
        query.setPageNum(CommonConstant.ONE);
        Page<JdhAngelEcologyValuation> pageList = angelEcologyValuationRepository.findPageList(query);
        log.info("AngelEcologyDomainServiceImpl -> queryUserValuation, pageList={}", JSON.toJSONString(pageList));
        if(Objects.isNull(pageList) || CollectionUtils.isEmpty(pageList.getRecords())) {
            log.error("AngelEcologyDomainServiceImpl -> queryUserValuation,没有查询到评价数据!");
            return Collections.emptyList();
        }
        return pageList.getRecords();
    }
}
