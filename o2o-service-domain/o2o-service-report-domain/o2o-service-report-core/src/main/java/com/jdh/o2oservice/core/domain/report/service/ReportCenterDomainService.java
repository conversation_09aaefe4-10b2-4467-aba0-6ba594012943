package com.jdh.o2oservice.core.domain.report.service;

import com.jdh.o2oservice.core.domain.report.bo.SyncReportToCenterResBo;
import com.jdh.o2oservice.core.domain.report.model.DrugStrategyConfig;
import com.jdh.o2oservice.core.domain.report.model.ReportCenterReport;
import com.jdh.o2oservice.base.model.User;

import java.util.Set;

/**
 * 报告中心Domain
 */
public interface ReportCenterDomainService {

    /**
     * 同步血检报告给报告中心
     * @return
     */
    SyncReportToCenterResBo syncMedicalReportToReportCenter(ReportCenterReport reportCenterReport);



    DrugStrategyConfig matchDrugStrategyConfig(String env, User patient, Set<String> indicators);

}
