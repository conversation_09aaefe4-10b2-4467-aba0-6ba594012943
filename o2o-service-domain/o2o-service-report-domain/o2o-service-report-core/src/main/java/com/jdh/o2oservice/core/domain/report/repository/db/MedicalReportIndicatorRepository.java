package com.jdh.o2oservice.core.domain.report.repository.db;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.report.bo.MedReportIndicatorQueryBO;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicator;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicatorQueryPageBo;

import java.util.List;

/**
 * 报告指标仓储
 * <AUTHOR>
 * @date 2024-11-13 13:31
 */
public interface MedicalReportIndicatorRepository {

    /**
     * 保存医疗报告指标。
     * @param medicalReportIndicator 医疗报告指标对象。
     * @return 保存是否成功。
     */
    Boolean saveMedicalReportIndicators(List<MedicalReportIndicator> medicalReportIndicator);


    /**
     * 分页获取医疗报告指标。
     * @param pageNum 当前页码，从1开始。
     * @param pageSize 每页记录数。
     * @return 分页数据对象，包含医疗报告指标列表。
     */
    PageDto<MedicalReportIndicator> pageMedicalReportIndicators(Integer pageNum, Integer pageSize);


    /**
     * 更新医疗报告指标列表。
     * @param medicalReportIndicator 医疗报告指标列表。
     * @return 更新操作是否成功。
     */
    Boolean updateMedicalReportIndicators(List<MedicalReportIndicator> medicalReportIndicator);

    /**
     * 分页获取医疗报告指标。
     * @param pageNum 当前页码，从1开始。
     * @param pageSize 每页记录数。
     * @return 分页数据对象，包含医疗报告指标列表。
     */
    PageDto<MedicalReportIndicator> pageReportIndicatorsNoCheckTime(Integer pageNum, Integer pageSize);

    /**
     * 根据查询条件获取医疗报告指标列表
     * @param medReportIndicatorQueryBO 查询条件对象，包含需要查询的指标信息
     * @return 符合查询条件的医疗报告指标列表
     */
    List<MedicalReportIndicator> listMedicalReportIndicators(MedReportIndicatorQueryBO medReportIndicatorQueryBO);


    /**
     * 根据指定的id删除医疗报告指标
     * @param reportId 要删除的医疗报告指标的id
     * @return 删除操作是否成功
     */
    Boolean deleteMedicalReportIndicators(String reportId);


    /**
     * 根据查询条件获取医疗报告指标列表
     * @return 符合查询条件的医疗报告指标列表
     */
    List<MedicalReportIndicator> listNoIndicatorId(Long startId,Long endId);

    /**
     * 保存医疗报告指标。
     * @param medicalReportIndicator 医疗报告指标对象。
     * @return 保存是否成功。
     */
    Boolean saveIndicatorId(MedicalReportIndicator medicalReportIndicator);




    /**
     * 分页获取医疗报告指标。
     * @return 分页数据对象，包含医疗报告指标列表。
     */
    PageDto<MedicalReportIndicator> pageMedicalReportIndicators(MedicalReportIndicatorQueryPageBo medicalReportIndicatorQueryPageBo);

    /**
     * 查询
     * @param id
     * @return
     */
    MedicalReportIndicator getMedicalReportIndicatorById(Long id);



}
