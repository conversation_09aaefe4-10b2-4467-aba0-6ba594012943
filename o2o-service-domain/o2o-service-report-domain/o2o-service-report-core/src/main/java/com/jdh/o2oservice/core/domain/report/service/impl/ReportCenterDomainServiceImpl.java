package com.jdh.o2oservice.core.domain.report.service.impl;

import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdh.o2oservice.core.domain.report.bo.SyncReportToCenterResBo;
import com.jdh.o2oservice.core.domain.report.model.DrugStrategyConfig;
import com.jdh.o2oservice.core.domain.report.model.ReportCenterReport;
import com.jdh.o2oservice.core.domain.report.rpc.ReportCenterRpc;
import com.jdh.o2oservice.core.domain.report.service.ReportCenterDomainService;
import com.jdh.o2oservice.base.model.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Slf4j
@Service("reportCenterDomainService")
@Data
public class ReportCenterDomainServiceImpl implements ReportCenterDomainService {


    @Autowired
    ReportCenterRpc reportCenterRpc;


    /**
     * 报告详情页用药推荐配置，后续如果策略多应该迁移到数据库
     */
    @LafValue("drug_strategy_configs")
    @JsonConverter(isSupportGeneric =true)
    private List<DrugStrategyConfig> drugStrategyConfigs;

    @Override
    public SyncReportToCenterResBo syncMedicalReportToReportCenter(ReportCenterReport reportCenterReport) {

        if(StringUtils.isBlank(reportCenterReport.getReportCenterId())) {
            return reportCenterRpc.createMedicalReportToReportCenter(reportCenterReport);
        }

        return reportCenterRpc.updateMedicalReportToReportCenter(reportCenterReport);

    }


    @Override
    public DrugStrategyConfig matchDrugStrategyConfig(String env, User patient, Set<String> indicators) {

        if (CollectionUtils.isEmpty(drugStrategyConfigs)) {
            return null;
        }
        DrugStrategyConfig drugStrategyConfig = null;
        for (DrugStrategyConfig config : drugStrategyConfigs) {
            if (BooleanUtils.isTrue(config.getIsDefault())){
                drugStrategyConfig = config;
            }
            if(config.match(env, patient, indicators)){
                return config;
            }
        }

        return drugStrategyConfig;
    }


}
