package com.jdh.o2oservice.core.domain.promise.factory;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseUser;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.base.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Queue;

/**
 * JdhVoucherFactory
 *
 * <AUTHOR>
 * @date 2023/12/21
 */
@Slf4j
public class JdhPromisePatientFactory {


    /**
     * 创建
     *
     * @return {@link JdhVoucher}
     */
    public static List<JdhPromisePatient> create(List<User> users, JdhPromise snapshot) {
        List<JdhPromisePatient> res = Lists.newArrayList();
        for (User user : users) {
            JdhPromisePatient patient = new JdhPromisePatient();
            BeanUtils.copyProperties(user, patient);
            patient.setPromiseId(snapshot.getPromiseId());
            res.add(patient);
        }
        Queue<Long> uniqId = SpringUtil.getBean(GenerateIdFactory.class).getBatchId(res.size());
        for (JdhPromisePatient re : res) {
            re.setPromisePatientId(uniqId.poll());
        }
        return res;
    }

    /**
     * 创建患者MAP
     *
     * @return {@link JdhVoucher}
     */
    public static Map<JdhPromisePatient, List<String>> createPatientMap(JdhPromise jdhPromise, Map<PromiseUser, List<String>> userSpecimenCode) {
        Map<JdhPromisePatient, List<String>> res = Maps.newHashMap();
        long count = userSpecimenCode.values().stream().mapToLong(Collection::size).sum();
        Queue<Long> uniqId = SpringUtil.getBean(GenerateIdFactory.class).getBatchId((int) count);
        userSpecimenCode.forEach((user, codes) -> {

            JdhPromisePatient patient = new JdhPromisePatient();
            BeanUtils.copyProperties(user, patient);
            patient.setPromiseId(jdhPromise.getPromiseId());
            patient.setPromisePatientId(uniqId.poll());
            patient.setVerticalCode(jdhPromise.getVerticalCode());
            patient.setServiceType(jdhPromise.getServiceType());
            patient.setVoucherId(jdhPromise.getVoucherId());
            patient.setSourceVoucherId(jdhPromise.getSourceVoucherId());
            patient.setUserPin(jdhPromise.getUserPin());
            res.put(patient, codes);
        });
        return res;
    }
}
