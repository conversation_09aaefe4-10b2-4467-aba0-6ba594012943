package com.jdh.o2oservice.core.domain.promise.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.trade.TradeExtApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.model.AbilityCode;
import com.jdh.o2oservice.base.model.DomainAbility;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.statemachine.AbilityExecutor;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.statemachine.StateMachine;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.bo.PatientExtBo;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseSku;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseUser;
import com.jdh.o2oservice.core.domain.promise.context.*;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseTypeEnum;
import com.jdh.o2oservice.core.domain.promise.factory.JdhPromisePatientFactory;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.rpc.LocPromiseCodeRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.LocCodeBO;
import com.jdh.o2oservice.core.domain.promise.service.JdhPromiseDomainService;
import com.jdh.o2oservice.core.domain.promise.service.JdhVoucherDomainService;
import com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit.DecryptUserAbility;
import com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit.SyncPatientAbility;
import com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit.UserServiceCheckAbility;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.ServiceTagEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.ServiceDetailRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.ServiceDetailBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.ServiceDetailRpcParam;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedPromiseCmdAppointmentPatient;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedPromiseCmdServiceItem;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseCreateCmd;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * JdhPromiseDomainServiceImpl
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Service
@Slf4j
public class JdhPromiseDomainServiceImpl implements JdhPromiseDomainService {


    /**
     * locPromiseCodeRpc
     */
    @Resource
    private LocPromiseCodeRpc locPromiseCodeRpc;
    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    @Resource
    private SkuInfoRpc skuInfoRpc;

    @Resource
    private TradeExtApplication tradeExtApplication;

    /**
     * promiseStatemachine
     */
    @Resource
    private StateMachine<JdhPromiseStatusEnum, PromiseEventTypeEnum, StateContext> promiseStatemachine;

    /**
     * conditions
     */
    private final Map<String, DomainAbility> conditions = Maps.newConcurrentMap();

    /**
     * actions
     */
    private final Map<String, DomainAbility> actions = Maps.newConcurrentMap();

    /**
     * applicationContext
     */
    @Resource
    private ApplicationContext applicationContext;

    /**
     * promiseRepository
     */
    @Autowired
    private PromiseRepository promiseRepository;

    /**
     * serviceDetailRpc
     */
    @Autowired
    private ServiceDetailRpc serviceDetailRpc;
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;
    @Resource
    private DecryptUserAbility decryptUserAbility;
    @Resource
    private SyncPatientAbility syncPatientAbility;
    @Resource
    private UserServiceCheckAbility userServiceCheckAbility;
    @Resource
    private JdhVoucherDomainService jdhVoucherDomainService;

    /**
     * init
     */
    @PostConstruct
    public void init() {
        Map<String, DomainAbility> map = applicationContext.getBeansOfType(DomainAbility.class);
        for (DomainAbility ability : map.values()) {
            if (!Objects.equals(ability.getAbilityCode().domainType().getCode(), DomainEnum.PROMISE.getCode())) {
                log.info("JdhPromiseDomainServiceImpl init continue code={}", ability.getAbilityCode().getCode());
                continue;
            }
            if (Objects.equals(ability.getAbilityCode().getAbilityType(), AbilityCode.ACTION)) {
                actions.put(ability.getAbilityCode().getCode(), ability);
            } else if (Objects.equals(ability.getAbilityCode().getAbilityType(), AbilityCode.CONDITION)) {
                conditions.put(ability.getAbilityCode().getCode(), ability);
            }
        }
    }


    /**
     * 批量创建履约单
     *
     * @param context 上下文
     * @return {@link List}<{@link JdhPromise}>
     */
    @Override
    public List<JdhPromise> batchCreatePromise(BatchCreatePromiseContext context) {
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.POP_LOC.getCode())) {
            return popBatchCreate(context);
            // 自动发码
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.YK.getCode())) {
            return null;
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.POP_FREE.getCode())) {
            return popBatchCreate(context);
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode())) {
            return homeBatchCreate(context);
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())) {
            return homeBatchCreate(context);
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode())) {
            return homeBatchCreate(context);
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode())) {
            return homeBatchCreate(context);
        }
        return null;
    }

    /**
     * 到家 批创建履约单
     *
     * @param context 上下文
     * @return {@link List}<{@link JdhPromise}>
     */
    private List<JdhPromise> homeBatchCreate(BatchCreatePromiseContext context) {
        List<JdhVoucher> jdhVoucherList = context.getJdhVoucherList();
        if (CollUtil.isEmpty(jdhVoucherList)) {
            log.warn("JdhPromiseDomainServiceImpl->popBatchCreate 无服务单数据");
            return null;
        }
        List<JdhPromise> result = Lists.newArrayList();

        int serialNum = 0;
        for (JdhVoucher jdhVoucher : jdhVoucherList) {
            String userPin = jdhVoucher.getUserPin();
            String sourceVoucherId = jdhVoucher.getSourceVoucherId();

            Long promiseId = generateIdFactory.getId();
            List<JdhVoucherItem> jdhVoucherItemList = jdhVoucher.getVoucherItemList();

            //sku放serviceId
            List<PromiseService> serviceDetailList = new ArrayList<>(jdhVoucherItemList.size());
            for (JdhVoucherItem jdhVoucherItem : jdhVoucherItemList) {
                PromiseSku promiseSku = context.getPromiseSkuMap().get(jdhVoucherItem.getServiceId().toString());
                PromiseService serviceDetail = PromiseService.builder()
                        .promiseId(promiseId)
                        .serviceId(jdhVoucherItem.getServiceId())
                        .serviceName(promiseSku.getSkuName())
                        .tags(jdhVoucherItem.getTag())
                        .createUser(StrUtil.isEmpty(userPin) ? StrUtil.EMPTY : userPin)
                        .build();
                serviceDetailList.add(serviceDetail);
            }

            List<JdhPromiseExtend> promiseExtends = createPromiseExtendList(promiseId, jdhVoucher);

            JdhPromise jdhPromise = JdhPromise.builder()
                    .verticalCode(jdhVoucher.getVerticalCode())
                    .serviceType(jdhVoucher.getServiceType())
                    .promiseId(promiseId)
                    .userPin(jdhVoucher.getUserPin())
                    .patients(createPromisePatientList(promiseId, jdhVoucher))
                    .promiseExtends(promiseExtends)
                    .createUser(StrUtil.isEmpty(userPin) ? StrUtil.EMPTY : userPin)
                    .voucherId(jdhVoucher.getVoucherId())
                    .sourceVoucherId(sourceVoucherId)
                    .promiseType(PromiseTypeEnum.ONLINE_APPOINTMENT.getCode())
                    .serialNum(serialNum++)
                    .promiseStatus(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus())
                    .expireDate(TimeUtils.dateToLocalDateTime(jdhVoucher.getExpireDate()))
                    .codeId(generateIdFactory.getIdStr())
                    .code(RandomUtil.randomNumbers(4))
                    .codePwd(RandomUtil.randomNumbers(4))
                    .services(serviceDetailList)
                    .build();
            result.add(jdhPromise);
        }
        return result;
    }

    /**
     * createPromisePatientList
     *
     * @param promiseId  promiseId
     * @param jdhVoucher jdhVoucher
     * @return {@link List}<{@link JdhPromisePatient}>
     */
    private List<JdhPromisePatient> createPromisePatientList(Long promiseId, JdhVoucher jdhVoucher) {
        Integer promisePatientNum = jdhVoucher.getExtend().getPromisePatientNum();
        if (Objects.isNull(promisePatientNum) || promisePatientNum <= NumConstant.NUM_0) {
            return null;
        }
        List<JdhPromisePatient> result = new ArrayList<>();
        for (int i = 0; i < promisePatientNum; i++) {
            JdhPromisePatient promisePatient = new JdhPromisePatient();
            promisePatient.setPromiseId(promiseId);
            promisePatient.setPromisePatientId(generateIdFactory.getId());
            promisePatient.setVoucherId(jdhVoucher.getVoucherId());
            promisePatient.setSourceVoucherId(jdhVoucher.getSourceVoucherId());
            promisePatient.setVerticalCode(jdhVoucher.getVerticalCode());
            promisePatient.setServiceType(jdhVoucher.getServiceType());
            promisePatient.setUserPin(jdhVoucher.getUserPin());
            result.add(promisePatient);
        }
        return result;
    }


    /**
     * 批量创建履约单
     *
     * @param context 上下文
     * @return {@link List}<{@link JdhPromise}>
     */
    private List<JdhPromise> popBatchCreate(BatchCreatePromiseContext context) {
        List<JdhVoucher> jdhVoucherList = context.getJdhVoucherList();
        if (CollUtil.isEmpty(jdhVoucherList)) {
            log.warn("JdhPromiseDomainServiceImpl->popBatchCreate 无服务单数据");
            return null;
        }
        List<JdhPromise> result = Lists.newArrayList();

        //批量产的时候，voucher中的扩展信息不能发生不一样的情况。所以用某一个voucher上的信息即可
        JdhVoucher oneVoucher = jdhVoucherList.get(NumConstant.NUM_0);
        String userPin = oneVoucher.getUserPin();
        Long orderId = Long.parseLong(oneVoucher.getExtend().getOrderId());
        Long skuId = Long.parseLong(oneVoucher.getExtend().getSkuId());
        //查询服务信息，无论哪个预约方式，都生产履约单
        ServiceDetailBo serviceDetailBo = serviceDetailRpc.queryServiceDetail(ServiceDetailRpcParam.builder().skuNo(skuId.toString()).build());
        //这里匹配不到会兜底为免预约的方式
        PromiseTypeEnum promiseType = PromiseTypeEnum.getPromiseType(serviceDetailBo.getAppointmentType());
        List<LocCodeBO> list = locPromiseCodeRpc.listLocCode(userPin, orderId);
        if (CollectionUtil.isEmpty(list)) {
            log.info("JdhPromiseDomainServiceImpl->popBatchCreate loc code is empty");
            throw new BusinessException(PromiseErrorCode.CREATE_CODE_BUSY);
        }
        //本次对于多次卡的情况，产品规则目前只处理主码。 研发 shenjifeng1 提供规则 判断:parentPwdNumber 为空为父码
        Set<LocCodeBO> curCodes = list.stream().filter(e -> Objects.equals(e.getSkuId(), skuId) && StrUtil.isEmpty(e.getParentPwdNumber())).collect(Collectors.toSet());
        log.info("JdhPromiseDomainServiceImpl->popBatchCreate curCodes:{}", JSON.toJSONString(curCodes));
        if (CollectionUtil.isEmpty(curCodes)) {
            log.warn("JdhPromiseDomainServiceImpl->popBatchCreate cur locCode not exist");
            throw new BusinessException(PromiseErrorCode.CREATE_CODE_ERROR);
        }

        List<String> usedCode = new ArrayList<>();

        //循环为每一个voucher创建自己的履约单数据
        for (JdhVoucher jdhVoucher : jdhVoucherList) {
            List<JdhPromise> res = Lists.newArrayList();
            //一个订单 在之前的产码中已经用过了。不能重复用，异步产码中，同一个订单拆成了多个服务单。
            Integer promiseNum = jdhVoucher.getPromiseNum();
            String sourceVoucherId = jdhVoucher.getSourceVoucherId();
            //当前码的次序
            int serialNum = -1;
            //当前服务单已经产了几个码
            int createdNum = 0;

            List<JdhPromise> generatedJdhPromises = result.stream().filter(ele -> ele.getVoucherId().equals(jdhVoucher.getVoucherId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(generatedJdhPromises)) {
                createdNum = generatedJdhPromises.size();
                for (JdhPromise generatedJdhPromise : generatedJdhPromises) {
                    serialNum = Math.max(serialNum, generatedJdhPromise.getSerialNum());
                }
            }
            //如果没产够
            if (createdNum < promiseNum) {
                for (LocCodeBO locCodeBo : curCodes) {
                    // 如果一单多份，看之前的码哪个已经产了。
                    if (usedCode.contains(locCodeBo.getCodeRelationId().toString())) {
                        continue;
                    } else {
                        usedCode.add(locCodeBo.getCodeRelationId().toString());
                    }
                    Long promiseId = generateIdFactory.getId();
                    List<JdhVoucherItem> jdhVoucherItemList = jdhVoucher.getVoucherItemList();
                    List<PromiseService> serviceDetailList = new ArrayList<>(jdhVoucherItemList.size());
                    for (JdhVoucherItem jdhVoucherItem : jdhVoucherItemList) {
                        PromiseSku promiseSku = context.getPromiseSkuMap().get(jdhVoucherItem.getServiceId().toString());
                        PromiseService serviceDetail = PromiseService.builder()
                                .promiseId(promiseId)
                                .serviceId(jdhVoucherItem.getServiceId())
                                .serviceName(promiseSku.getSkuName())
                                .tags(jdhVoucherItem.getTag())
                                .createUser(StrUtil.isEmpty(userPin) ? StrUtil.EMPTY : userPin)
                                .build();
                        RpcSkuBO skuBO = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(jdhVoucherItem.getServiceId()));
                        if (Objects.nonNull(skuBO)) {
                            serviceDetail.setOutSkuId(skuBO.getOuterId());
                        }
                        serviceDetailList.add(serviceDetail);
                    }

                    List<JdhPromiseExtend> promiseExtends = createPromiseExtendList(promiseId, jdhVoucher);

                    JdhPromise jdhPromise = JdhPromise.builder()
                            .verticalCode(jdhVoucher.getVerticalCode())
                            .serviceType(jdhVoucher.getServiceType())
                            .promiseId(promiseId)
                            .userPin(jdhVoucher.getUserPin())
                            .patients(createPromisePatientList(promiseId, jdhVoucher))
                            .promiseExtends(promiseExtends)
                            .createUser(StrUtil.isEmpty(userPin) ? StrUtil.EMPTY : userPin)
                            .voucherId(jdhVoucher.getVoucherId())
                            .sourceVoucherId(sourceVoucherId)
                            .promiseType(promiseType.getCode())
                            .serialNum(++serialNum)
                            .channelNo(serviceDetailBo.getChannelNo())
                            .promiseStatus(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus())
                            .expireDate(TimeUtils.dateToLocalDateTime(jdhVoucher.getExpireDate()))
                            .codeId(locCodeBo.getCodeRelationId().toString())
                            .code(locCodeBo.getCodeNum())
                            .codePwd(locCodeBo.getPwdNumber())
                            .services(serviceDetailList)
                            .build();
                    res.add(jdhPromise);

                    createdNum++;
                    //针对服务单对应的 服务次数和本次产的数量，进行对比，如果产够了。就终止
                    if (createdNum >= promiseNum) {
                        break;
                    }
                }
            }
            log.info("JdhPromiseDomainServiceImpl->popBatchCreate res:{}", JSON.toJSONString(result));
            result.addAll(res);
        }
        log.info("JdhPromiseDomainServiceImpl->popBatchCreate result:{}", JSON.toJSONString(result));
        return result;
    }


    /**
     * 创建履约单 发码
     *
     * @param context 上下文
     * @return {@link List}<{@link JdhPromise}>
     */
    @Override
    public List<JdhPromise> createPromise(CreatePromiseContext context) {
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.POP_LOC.getCode())) {
            return popCreate(context);
            // 自动发码
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.YK.getCode())) {
            return null;
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.POP_FREE.getCode())) {
            return popCreate(context);
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode())) {
            return homeCreate(context);
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())) {
            return homeCreate(context);
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode())) {
            return homeCreate(context);
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST_TRANSPORT.getCode())) {
            return homeTestTransport(context);
        } else if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode())) {
            return homeCreate(context);
        }
        return null;
    }

    /**
     * 上门护理创建履约单
     *
     * @param context 上下文
     * @return {@link List}<{@link JdhPromise}>
     */
    private List<JdhPromise> homeCreate(CreatePromiseContext context) {
        List<JdhPromise> res = new ArrayList<>();

        JdhVoucher jdhVoucher = context.getJdhVoucher();
        String userPin = context.getJdhVoucher().getUserPin();
        String sourceVoucherId = jdhVoucher.getSourceVoucherId();

        Long promiseId = generateIdFactory.getId();
        List<JdhVoucherItem> jdhVoucherItemList = jdhVoucher.getVoucherItemList();

        //sku放serviceId
        List<PromiseService> serviceDetailList = new ArrayList<>(jdhVoucherItemList.size());
        for (JdhVoucherItem jdhVoucherItem : jdhVoucherItemList) {
            JdhSkuDto promiseSku = context.getPromiseSkuMap().get(jdhVoucherItem.getServiceId());
            PromiseService serviceDetail = PromiseService.builder()
                    .promiseId(promiseId)
                    .serviceId(jdhVoucherItem.getServiceId())
                    .serviceName(promiseSku.getSkuName())
                    .tags(jdhVoucherItem.getTag())
                    .createUser(StrUtil.isEmpty(userPin) ? StrUtil.EMPTY : userPin)
                    .build();
            serviceDetailList.add(serviceDetail);
        }

        List<JdhPromiseExtend> promiseExtends = createPromiseExtendList(promiseId, jdhVoucher);

        JdhPromise jdhPromise = JdhPromise.builder()
                .verticalCode(jdhVoucher.getVerticalCode())
                .serviceType(jdhVoucher.getServiceType())
                .promiseId(promiseId)
                .userPin(jdhVoucher.getUserPin())
                .patients(createPromisePatientList(promiseId, jdhVoucher))
                .promiseExtends(promiseExtends)
                .createUser(StrUtil.isEmpty(userPin) ? StrUtil.EMPTY : userPin)
                .voucherId(jdhVoucher.getVoucherId())
                .sourceVoucherId(sourceVoucherId)
                .promiseType(PromiseTypeEnum.ONLINE_APPOINTMENT.getCode())
                .serialNum(0)
                .promiseStatus(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus())
                .expireDate(TimeUtils.dateToLocalDateTime(jdhVoucher.getExpireDate()))
                .codeId(generateIdFactory.getIdStr())
                .code(RandomUtil.randomNumbers(4))
                .codePwd(RandomUtil.randomNumbers(4))
                .services(serviceDetailList)
                .build();
        res.add(jdhPromise);

        return res;
    }


    public static void main(String[] args) {
        List<JdhPromise> promiseList = new ArrayList<>();
        JdhPromise promise = JdhPromise.builder().promiseId(1L).build();
        promiseList.add(promise );
        System.out.println(promiseList.stream().map(JdhPromise::getPatients).filter(CollectionUtils::isNotEmpty).mapToLong(List::size).sum());
    }

    /**
     * 可用的项目数量 = 未冻结的数量 - 已经使用的数量
     * @return
     */
    public int availableNum(JdhVoucher jdhVoucher){

        int total = jdhVoucher.nonFreezePromiseNum();
        PromiseRepQuery query = PromiseRepQuery.builder()
                .voucherIds(Lists.newArrayList(jdhVoucher.getVoucherId()))
                .build();
        List<JdhPromise> promiseList = promiseRepository.findList(query);
        if (CollectionUtils.isEmpty(promiseList)) {
            return total;
        }
        int use = (int)promiseList.stream().map(JdhPromise::getPatients).filter(CollectionUtils::isNotEmpty).mapToLong(List::size).sum();
        return total - use;
    }
    /**
     * 上门护理创建履约单
     *
     * @param context 上下文
     * @return {@link List}<{@link JdhPromise}>
     */
    private List<JdhPromise> homeTestTransport(CreatePromiseContext context) {
        JdhVoucher jdhVoucher = context.getJdhVoucher();
        Set<PromiseUser> users = context.getUserSpecimenCodeMap().keySet();

        int num = jdhVoucherDomainService.availableNum(jdhVoucher);
        // 校验剩余可用检测项目数量
        if (num <= 0 || num <  users.size()) {
            throw new BusinessException(PromiseErrorCode.VOUCHER_NUM_LACK);
        }

        // 码重复校验
        List<String> specimenCodes = Lists.newArrayList();

        for (List<String> value : context.getUserSpecimenCodeMap().values()) {
            specimenCodes.addAll(value);
        }
        Integer exist = medicalPromiseApplication.listMedicalPromiseBySpecimenCode(specimenCodes);
        if (exist > 0){
            throw new BusinessException(PromiseErrorCode.SPECIMEN_CODE_REPEAT_FAIL);
        }

        // 用户校验，如果用户有pid需要调健康档案进行异步保存；如果用户没有pid需要调健康档案保存患者信息，并获取pid。
        for (PromiseUser user : users) {
            // 用户信息解密，如果存在掩码信息会反查健康档案补齐
            decryptUserAbility.decrypt(user);
            userServiceCheckAbility.homeCheck(context.getPromiseSkuMap().values(), Lists.newArrayList(user));
            // 用户如果是新增或者修改的健康档案，需要调档案接口更新
            syncPatientAbility.sync(user);
        }


        List<JdhPromise> res = new ArrayList<>();
        String userPin = context.getJdhVoucher().getUserPin();
        String sourceVoucherId = jdhVoucher.getSourceVoucherId();

        Long promiseId = generateIdFactory.getId();
        // 服务列表
        List<PromiseService> serviceDetailList = Lists.newArrayList();
        JdhVoucherItem voucherItem = jdhVoucher.getVoucherItemList().stream().findFirst()
                .orElseThrow(() -> new BusinessException(SystemErrorCode.UNKNOWN_ERROR));
        JdhSkuDto promiseSku = context.getPromiseSkuMap().get(voucherItem.getServiceId());
        PromiseService serviceDetail = PromiseService.builder()
                .promiseId(promiseId)
                .serviceId(voucherItem.getServiceId())
                .serviceName(promiseSku.getSkuName())
                .tags(ServiceTagEnum.BASIC.getType())
                .createUser(StrUtil.isEmpty(userPin) ? StrUtil.EMPTY : userPin)
                .build();
        serviceDetailList.add(serviceDetail);


        // 扩展信息
        List<JdhPromiseExtend> promiseExtends = createPromiseExtendList(promiseId, jdhVoucher);
        JdhPromise jdhPromise = JdhPromise.builder()
                .verticalCode(jdhVoucher.getVerticalCode())
                .serviceType(jdhVoucher.getServiceType())
                .promiseId(promiseId)
                .userPin(jdhVoucher.getUserPin())
                .promiseExtends(promiseExtends)
                .createUser(StrUtil.isEmpty(userPin) ? StrUtil.EMPTY : userPin)
                .voucherId(jdhVoucher.getVoucherId())
                .sourceVoucherId(sourceVoucherId)
                .promiseType(PromiseTypeEnum.ONLINE_APPOINTMENT.getCode())
                .serialNum(0)
                .promiseStatus(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus())
                .expireDate(TimeUtils.dateToLocalDateTime(jdhVoucher.getExpireDate()))
                .codeId(generateIdFactory.getIdStr())
                .code(RandomUtil.randomNumbers(4))
                .codePwd(RandomUtil.randomNumbers(4))
                .services(serviceDetailList)
                .promiseExtends(promiseExtends)
                .build();

        // 构建检测人信息
        Map<JdhPromisePatient, List<String>> specimenCodePatientMap = JdhPromisePatientFactory.createPatientMap(jdhPromise, context.getUserSpecimenCodeMap());
        jdhPromise.setPatients(Lists.newArrayList(specimenCodePatientMap.keySet()));
        res.add(jdhPromise);

        // 创建medicalPromise
        MedicalPromiseCreateCmd medicalPromiseCreateCmd = new MedicalPromiseCreateCmd();
        medicalPromiseCreateCmd.setVerticalCode(jdhVoucher.getVerticalCode());
        medicalPromiseCreateCmd.setServiceType(jdhVoucher.getServiceType());
        medicalPromiseCreateCmd.setUserPin(jdhVoucher.getUserPin());
        medicalPromiseCreateCmd.setPromiseId(jdhPromise.getPromiseId());
        medicalPromiseCreateCmd.setVoucherId(jdhPromise.getVoucherId());

        List<MedPromiseCmdAppointmentPatient> patientList = Lists.newArrayList();
        // 非快检模式，一个商品只有一个检测项目
        List<ServiceItemDto> serviceItemList = promiseSku.getServiceItemList();
        ServiceItemDto skuItem ;
        if (CollectionUtils.isEmpty(serviceItemList) || serviceItemList.size() > 1){
            throw new BusinessException(PromiseErrorCode.SKU_ITEM_ILLEGAL);
        }else{
            skuItem = serviceItemList.get(0);
        }
        specimenCodePatientMap.forEach((user, codes) -> {
            MedPromiseCmdAppointmentPatient p = new MedPromiseCmdAppointmentPatient();
            p.setPromisePatientId(user.getPromisePatientId());

            List<MedPromiseCmdServiceItem> serviceItems = Lists.newArrayList();
            for (String code : codes) {
                MedPromiseCmdServiceItem serviceItem = new MedPromiseCmdServiceItem();
                serviceItem.setServiceId(promiseSku.getSkuId());
                serviceItem.setItemName(skuItem.getItemName());
                serviceItem.setItemId(skuItem.getItemId());
                serviceItem.setSpecimenCode(StringUtils.trim(code));
                serviceItems.add(serviceItem);
            }
            p.setServiceItems(serviceItems);
            patientList.add(p);
        });




        medicalPromiseCreateCmd.setPatientList(patientList);
        medicalPromiseApplication.createMedicalPromise(medicalPromiseCreateCmd);

        return res;
    }


    /**
     * 组装 promiseExtend 信息集合
     *
     * @param jdhVoucher jdhVoucher
     * @param promiseId  promiseId
     * @return {@link List}<{@link JdhPromiseExtend}>
     */
    private List<JdhPromiseExtend> createPromiseExtendList(Long promiseId, JdhVoucher jdhVoucher) {
        log.info("JdhPromiseDomainServiceImpl.createPromiseExtendList.promiseId={},jdhVoucher={}", promiseId, JSON.toJSONString(jdhVoucher));
        String orderPhone = jdhVoucher.getExtend().getOrderPhone();
        String orderRemark = jdhVoucher.getExtend().getOrderRemark();
        Integer hasAdded = jdhVoucher.getExtend().getHasAdded();
        String orderId = jdhVoucher.getExtend().getOrderId();
        String openTestInfo = jdhVoucher.getExtend().getOpenTestInfo();
        PromiseIntendedNurse intendedNurse = jdhVoucher.getExtend().getIntendedNurse();
        //履约单扩展信息
        List<JdhPromiseExtend> promiseExtends = new ArrayList<>();
        if (StrUtil.isNotEmpty(orderPhone)) {
            JdhPromiseExtend orderPhoneExtend = new JdhPromiseExtend();
            orderPhoneExtend.setOrderPhone(orderPhone);
            orderPhoneExtend.setPromiseId(promiseId);
            promiseExtends.add(orderPhoneExtend);
        }
        if (StrUtil.isNotEmpty(orderRemark)) {
            JdhPromiseExtend orderRemarkExtend = new JdhPromiseExtend();
            orderRemarkExtend.setOrderRemark(orderRemark);
            orderRemarkExtend.setPromiseId(promiseId);
            promiseExtends.add(orderRemarkExtend);
        }


        if (StrUtil.isNotEmpty(orderId)) {
            JdhPromiseExtend orderRemarkExtend = new JdhPromiseExtend();
            orderRemarkExtend.setAttribute(PromiseExtendKeyEnum.ORDER_ID.getFiledKey());
            orderRemarkExtend.setValue(orderId);
            orderRemarkExtend.setPromiseId(promiseId);
            promiseExtends.add(orderRemarkExtend);
        }
        //是否含有加项
        if (Objects.nonNull(hasAdded)) {
            JdhPromiseExtend hasAddedExtend = new JdhPromiseExtend();
            hasAddedExtend.setAttribute(PromiseExtendKeyEnum.HAS_ADDED.getFiledKey());
            hasAddedExtend.setValue(String.valueOf(hasAdded));
            hasAddedExtend.setPromiseId(promiseId);
            promiseExtends.add(hasAddedExtend);
        }

        if (Objects.nonNull(intendedNurse)) {
            JdhPromiseExtend intendedNurseExtend = new JdhPromiseExtend();
            intendedNurseExtend.setAttribute(PromiseExtendKeyEnum.INTENDED_NURSE.getFiledKey());
            intendedNurseExtend.setValue(JSON.toJSONString(intendedNurse));
            intendedNurseExtend.setPromiseId(promiseId);
            promiseExtends.add(intendedNurseExtend);
        }

        if (StringUtils.isNotEmpty(openTestInfo)){
            JdhPromiseExtend orderPhoneExtend = new JdhPromiseExtend();
            orderPhoneExtend.setAttribute(PromiseExtendKeyEnum.OPEN_TEST_INFO.getFiledKey());
            orderPhoneExtend.setValue(openTestInfo);
            orderPhoneExtend.setPromiseId(promiseId);
            promiseExtends.add(orderPhoneExtend);
        }


        //病例资料
        if (Objects.nonNull(jdhVoucher.getExtend()) && CollectionUtils.isNotEmpty(jdhVoucher.getExtend().getMedicalCertificateFileIds())){
            JdhPromiseExtend jdhPromiseExtend = new JdhPromiseExtend();
            jdhPromiseExtend.setMedicalCertificateFileIds(JsonUtil.toJSONString(jdhVoucher.getExtend().getMedicalCertificateFileIds()));
            jdhPromiseExtend.setPromiseId(promiseId);
            promiseExtends.add(jdhPromiseExtend);
        }

        //项目风险等级
        if (Objects.nonNull(jdhVoucher.getExtend()) && CollectionUtils.isNotEmpty(jdhVoucher.getExtend().getDangerLevelSet())){
            JdhPromiseExtend jdhPromiseExtend = new JdhPromiseExtend();
            jdhPromiseExtend.setItemDangerLevel(JsonUtil.toJSONString(jdhVoucher.getExtend().getDangerLevelSet()));
            jdhPromiseExtend.setPromiseId(promiseId);
            promiseExtends.add(jdhPromiseExtend);
        }

        log.info("JdhPromiseDomainServiceImpl.createPromiseExtendList.hasAdded={},promiseExtends={}", promiseId, JSON.toJSONString(promiseExtends));
        return promiseExtends;
    }

    /**
     * 提交预约
     *
     * @param context 上下文
     * @return {@link Boolean}
     */
    @Override
    public Boolean submitPromise(PromiseSubmitAbilityContext context) {
        Integer promiseStatus = context.getSnapshot().getPromiseStatus();
        JdhPromiseStatusEnum curStatus = JdhPromiseStatusEnum.convert(promiseStatus);
        //noinspection JdJDClassCast
        promiseStatemachine.fireEvent(curStatus, (PromiseEventTypeEnum) context.getTriggerCommand(), context);
        return Boolean.TRUE;
    }

    /**
     * 提交预约草稿数据
     *
     * @param context
     * @return
     */
    @Override
    public void submitDraft(SubmitAppointmentDraftContext context) {
        context.getExecutor();
        AbilityExecutor executor = context.getExecutor();
        execute(executor, context);
    }


    @Override
    public void callback(PromiseCallbackAbilityContext context) {
        JdhPromiseStatusEnum curStatus = JdhPromiseStatusEnum.convert(context.getPromise().getPromiseStatus());
        //noinspection JdJDClassCast
        promiseStatemachine.fireEvent(curStatus, (PromiseEventTypeEnum) context.getTriggerCommand(), context);
    }

    @Override
    public void writeOff(PromiseWriteOffContext context) {
        JdhPromiseStatusEnum curStatus = JdhPromiseStatusEnum.convert(context.getPromise().getPromiseStatus());
        //noinspection JdJDClassCast
        promiseStatemachine.fireEvent(curStatus, (PromiseEventTypeEnum) context.getTriggerCommand(), context);
    }

    /**
     * 是否需要派单
     *
     * @param context 上下文
     */
    @Override
    public Boolean requiredDispatch(PromiseDispatchContext context) {
        JdhPromise promise = context.getPromise();
        //1、判断businessMode是上门检测，上门护理才有派单
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode()) && !promise.isReversal()) {
            return Boolean.TRUE;
        }
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode()) && !promise.isReversal()) {
            return Boolean.TRUE;
        }
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode()) && !promise.isReversal()) {
            return Boolean.TRUE;
        }
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST_TRANSPORT.getCode()) && !promise.isReversal()) {
            return Boolean.TRUE;
        }
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode()) && !promise.isReversal()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 是否需要创建实验室检测单
     *
     * @param context 上下文
     * @return Boolean
     */
    @Override
    public Boolean requiredCreateMedicalPromise(PromiseCreateMedicalPromiseContext context) {
        JdhPromise promise = context.getPromise();
        //1、判断businessMode是上门检测，上门护理才有派单
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode()) && !promise.isReversal()) {
            return Boolean.TRUE;
        }
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode()) && !promise.isReversal()) {
            return Boolean.TRUE;
        }
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode()) && !promise.isReversal()) {
            return Boolean.TRUE;
        }
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode()) && !promise.isReversal()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 冻结
     *
     * @param context 上下文
     */
    @Override
    public void freeze(PromiseFreezeContext context) {
        AbilityExecutor executor = context.getExecutor();
        execute(executor, context);
    }

    /**
     * 作废
     *
     * @param context 上下文
     */
    @Override
    public void invalid(PromiseInvalidContext context) {
        AbilityExecutor executor = context.getExecutor();
        execute(executor, context);
    }

    /**
     * 更新预约单预约人医嘱信息
     *
     * @param jdhPromise
     * @param promisePatientRecordContext
     * @return
     */
    @Override
    public boolean modifyPatientDoctor(JdhPromise jdhPromise, PromisePatientRecordContext promisePatientRecordContext) {
        if(Objects.nonNull(promisePatientRecordContext) && CollectionUtils.isNotEmpty(promisePatientRecordContext.getFileUrlContexts())) {
            if(StringUtils.isBlank(promisePatientRecordContext.getPatientId()) || Objects.isNull(promisePatientRecordContext.getTaskId())) {
                log.error("JdhPromiseDomainServiceImpl -> modifyPatientDoctor, 被服务人楼层上传参数不正确");
                return false;
            }
            List<JdhPromisePatient> patients = jdhPromise.getPatients();
            Optional.ofNullable(patients).orElse(Lists.newArrayList()).forEach(item -> {
                if(Objects.nonNull(item.getPromisePatientId())
                        && promisePatientRecordContext.getPatientId().equals(String.valueOf(item.getPromisePatientId()))) {
                    PatientExtBo patientExtBo = item.getPatientExtBo();
                    if(Objects.isNull(patientExtBo)) {
                        patientExtBo = new PatientExtBo();
                    }
                    List<String> fileIdList = promisePatientRecordContext.getFileUrlContexts().stream().map(file -> String.valueOf(file.getFileId())).collect(Collectors.toList());
                    patientExtBo.setPatientDoctorAdviceFileList(fileIdList);
                    item.setPatientExtBo(patientExtBo);
                    promiseRepository.updatePromisePatient(item);
                }
            });
        }
        return false;
    }

    /**
     * 非快检需求专用
     * @param delPromiseContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean delPromise(DelPromiseContext delPromiseContext) {
        JdhPromise jdhPromise = new JdhPromise();
        jdhPromise.setPromiseId(delPromiseContext.getPromiseId());
        return promiseRepository.remove(jdhPromise)>0;
    }


    /**
     * 非快检需求专用
     * @param updatePromiseStatusContext
     * @return
     */
    @LogAndAlarm
    @Override
    public Boolean updatePromiseStatus(UpdatePromiseStatusContext updatePromiseStatusContext) {
        JdhPromise jdhPromise = new JdhPromise();
        jdhPromise.setPromiseId(updatePromiseStatusContext.getPromiseId());
        jdhPromise.setPromiseStatus(updatePromiseStatusContext.getPromiseStatus());

        List<String> nullFields = new ArrayList<>();
        if(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus().equals(updatePromiseStatusContext.getPromiseStatus())){
            //待履约状态,清空预约时间
            nullFields.add("appointment_start_time");
            nullFields.add("appointment_end_time");
        }
        return promiseRepository.update(jdhPromise,nullFields)>0;
    }

    /**
     * POP业务发码
     *
     * @param context 上下文
     * @return {@link List}<{@link JdhPromise}>
     */
    private List<JdhPromise> popCreate(CreatePromiseContext context) {
        JdhVoucher jdhVoucher = context.getJdhVoucher();
        String userPin = context.getJdhVoucher().getUserPin();
        String sourceVoucherId = jdhVoucher.getSourceVoucherId();
        Long orderId = Long.parseLong(context.getJdhVoucher().getExtend().getOrderId());
        Long skuId = Long.parseLong(context.getJdhVoucher().getExtend().getSkuId());

        //查询服务信息，无论哪个预约方式，都生产履约单
        ServiceDetailBo serviceDetailBo = serviceDetailRpc.queryServiceDetail(ServiceDetailRpcParam.builder().skuNo(skuId.toString()).build());
        //这里匹配不到会兜底为免预约的方式
        PromiseTypeEnum promiseType = PromiseTypeEnum.getPromiseType(serviceDetailBo.getAppointmentType());

        List<LocCodeBO> list = locPromiseCodeRpc.listLocCode(userPin, orderId);
        if (CollectionUtil.isEmpty(list)) {
            log.info("JdhPromiseDomainServiceImpl->popCreate loc code is empty");
            throw new BusinessException(PromiseErrorCode.CREATE_CODE_BUSY);
        }
        //本次对于多次卡的情况，产品规则目前只处理主码。 研发 shenjifeng1 提供规则 判断:parentPwdNumber 为空为父码
        Set<LocCodeBO> curCodes = list.stream().filter(e -> Objects.equals(e.getSkuId(), skuId) && StrUtil.isEmpty(e.getParentPwdNumber())).collect(Collectors.toSet());
        log.info("JdhPromiseDomainServiceImpl->popCreate curCodes:{}", JSON.toJSONString(curCodes));
        if (CollectionUtil.isEmpty(curCodes)) {
            log.warn("JdhPromiseDomainServiceImpl->popCreate cur locCode not exist");
            throw new BusinessException(PromiseErrorCode.CREATE_CODE_ERROR);
        }
        List<JdhPromise> res = Lists.newArrayList();
        //一个订单 在之前的产码中已经用过了。不能重复用，异步产码中，同一个订单拆成了多个服务单。
        Integer promiseNum = jdhVoucher.getPromiseNum();

        //当前码的次序
        int serialNum = -1;
        //当前服务单已经产了几个码
        int createdNum = 0;
        List<JdhPromise> generatedJdhPromises = promiseRepository.findList(PromiseRepQuery.builder().voucherIds(Collections.singletonList(jdhVoucher.getVoucherId())).build());
        if (CollUtil.isNotEmpty(generatedJdhPromises)) {
            createdNum = generatedJdhPromises.size();
            for (JdhPromise generatedJdhPromise : generatedJdhPromises) {
                serialNum = Math.max(serialNum, generatedJdhPromise.getSerialNum());
            }
        }

        //如果没产够
        if (createdNum < promiseNum) {
            for (LocCodeBO locCodeBo : curCodes) {
                // 如果一单多份，看之前的码哪个已经产了。
                JdhPromise usedPromise = promiseRepository.findByCodeId(locCodeBo.getCodeRelationId().toString());
                if (Objects.nonNull(usedPromise)) {
                    continue;
                }

                Long promiseId = generateIdFactory.getId();
                List<JdhVoucherItem> jdhVoucherItemList = jdhVoucher.getVoucherItemList();
                List<PromiseService> serviceDetailList = new ArrayList<>(jdhVoucherItemList.size());
                for (JdhVoucherItem jdhVoucherItem : jdhVoucherItemList) {
                    //RpcSkuBO skuBO = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(jdhVoucherItem.getServiceId()));
                    JdhSkuDto promiseSku = context.getPromiseSkuMap().get(jdhVoucherItem.getServiceId());
                    PromiseService serviceDetail = PromiseService.builder()
                            .promiseId(promiseId)
                            .serviceId(jdhVoucherItem.getServiceId())
                            .serviceName(promiseSku.getSkuName())
                            .tags(jdhVoucherItem.getTag())
                            .createUser(StrUtil.isEmpty(userPin) ? StrUtil.EMPTY : userPin)
                            .build();
                    serviceDetailList.add(serviceDetail);
                }

                List<JdhPromiseExtend> promiseExtends = createPromiseExtendList(promiseId, jdhVoucher);

                JdhPromise jdhPromise = JdhPromise.builder()
                        .verticalCode(jdhVoucher.getVerticalCode())
                        .serviceType(jdhVoucher.getServiceType())
                        .userPin(jdhVoucher.getUserPin())
                        .promiseId(promiseId)
                        .patients(createPromisePatientList(promiseId, jdhVoucher))
                        .promiseExtends(promiseExtends)
                        .createUser(StrUtil.isEmpty(userPin) ? StrUtil.EMPTY : userPin)
                        .voucherId(jdhVoucher.getVoucherId())
                        .sourceVoucherId(sourceVoucherId)
                        .promiseType(promiseType.getCode())
                        .serialNum(++serialNum)
                        .channelNo(serviceDetailBo.getChannelNo())
                        .promiseStatus(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus())
                        .expireDate(TimeUtils.dateToLocalDateTime(jdhVoucher.getExpireDate()))
                        .codeId(locCodeBo.getCodeRelationId().toString())
                        .code(locCodeBo.getCodeNum())
                        .codePwd(locCodeBo.getPwdNumber())
                        .services(serviceDetailList)
                        .build();
                res.add(jdhPromise);

                createdNum++;
                //针对服务单对应的 服务次数和本次产的数量，进行对比，如果产够了。就终止
                if (createdNum >= promiseNum) {
                    break;
                }
            }
        }

        log.info("JdhPromiseDomainServiceImpl->popCreate res:{}", JSON.toJSONString(res));
        return res;
    }

    /**
     * 只想配置的condition和action
     * 这个execute和状态机调用的差异是，executor不由履约单状态控制
     *
     * @param executor executor
     * @param context  context
     */
    private void execute(AbilityExecutor executor, BusinessContext context) {
        // 未查询到当前状态+事件配置的执行器
        if (Objects.isNull(executor)) {
            log.error("JdhPromiseDomainServiceImpl submitDraft 未找到状态机执行器");
            throw new SystemException(SystemErrorCode.CONFIG_ERROR);
        }
        List<String> validConditions = executor.getConditionCodes();
        for (String code : validConditions) {
            DomainAbility condition = conditions.get(code);
            condition.execute(context);
            log.info("JdhPromiseDomainServiceImpl submitDraft conditionCode={}, context={}", code, JSON.toJSONString(context));
        }
        List<String> actionCodes = executor.getActionCodes();
        for (String code : actionCodes) {
            DomainAbility action = actions.get(code);
            action.execute(context);
            log.info("JdhPromiseDomainServiceImpl submitDraft cationCode={}, context={}", code, JSON.toJSONString(context));
        }
    }
}
