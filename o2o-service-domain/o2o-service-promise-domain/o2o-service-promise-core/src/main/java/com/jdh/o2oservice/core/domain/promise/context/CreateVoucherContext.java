package com.jdh.o2oservice.core.domain.promise.context;

import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherExtend;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherItem;
import com.jdh.o2oservice.core.domain.support.vertical.context.AbstractStateAbilityContext;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * CreateVoucherContext
 *
 * <AUTHOR>
 * @date 2024/01/18
 */
@Data
public class CreateVoucherContext extends AbstractStateAbilityContext {

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 服务兑换的来源id，兑换id/orderId/outerOrderId
     */
    private String sourceVoucherId;

    /**
     * 来源类型：京东订单、外部订单、兑换
     */
    private Integer sourceType;

    /**
     * 履约次数
     */
    private Integer promiseNum;


    /**
     * 服务过期时间
     */
    private Date expireDate;

    /**
     * 扩展字段
     */
    private JdhVoucherExtend extend;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * JDH 服务单明细
     */
    private List<JdhVoucherItem> voucherItemList;

    /**
     * 存在的服务单列表
     */
    private List<JdhVoucher> existVoucherList;

    /**
     * 实验室开放平台扩展字段
     */
    private String openTestInfo;

    /**
     * 就医记录文件ID
     */
    private List<Long> medicalCertificateFileIds;

}
