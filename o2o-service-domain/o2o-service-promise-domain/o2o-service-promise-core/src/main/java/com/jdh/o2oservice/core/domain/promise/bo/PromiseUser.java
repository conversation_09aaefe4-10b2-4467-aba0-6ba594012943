package com.jdh.o2oservice.core.domain.promise.bo;

import com.jdh.o2oservice.base.model.User;
import lombok.Data;

import java.util.Objects;

/**
 * 履约sku信息
 *
 * <AUTHOR>
 * @date 2024/04/21
 */
@Data
public class PromiseUser extends User {

    /**
     * 1:修改过健康档案的信息，需要调健康档案修改；
     * 2:没有使用健康档案，直接写入患者信息，需要调健康档案新增
     */
    private Integer operationStatus;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PromiseUser)) return false;
        if (!super.equals(o)) return false;
        PromiseUser user = (PromiseUser) o;
        return Objects.equals(operationStatus, user.operationStatus);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), operationStatus);
    }
}
