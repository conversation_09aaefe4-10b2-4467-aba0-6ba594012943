package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import com.google.common.base.Joiner;
import com.google.common.collect.Range;
import com.jdh.o2oservice.application.product.ProductExtApplication;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.base.model.User;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class UserServiceCheckAbility implements SubmitAbility{

    /**
     * productExtApplication
     */
    @Autowired
    private ProductExtApplication productExtApplication;

    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.USER_SERVICE_CHECK;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        JdhVerticalBusiness verticalBusiness = context.getVerticalBusiness();
        //到家 进行预约时间校验
        if(BusinessModeEnum.ANGEL_CARE.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.SELF_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())){

            Set<Long> skuIdList = context.getServices().stream().map(PromiseService::getServiceId).collect(Collectors.toSet());
            Map<Long, JdhSkuDto> skuDtoMap = productExtApplication.queryJdhSkuInfoByList(JdhSkuListRequest.builder().skuIdList(skuIdList).build());
            homeCheck(skuDtoMap.values(), context.getUsers());
        }
    }

    /**
     * 到家检测
     **/
    public void homeCheck(Collection<JdhSkuDto> skuDtos, List<User> users) {
        /**
         * 1、人 的校验
            根据pid 反查档案数据 & 预约的sku配置信息 执行如下规则
            - 与 sku 配置的 是否实名 匹配  认证状态：1=未认证; 2=算法验证通过; 3=实名认证通过
            - 与 sku 配置的 年龄范围 是否匹配
            - 与 sku 配置的 性别范围 是否匹配
         */
        for (JdhSkuDto jdhSkuDto : skuDtos) {
            // 是否实名 0-否 1-是
            Integer requiredRealName = jdhSkuDto.getRequiredRealName();
            if(NumConstant.NUM_1.equals(requiredRealName)){
                for (User user : users) {
                    if(!NumConstant.NUM_3.equals(user.getVerifyStatus())){
                        throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_USER_VERIFY_ERROR);
                    }
                }
            }

            //年龄
            Integer maxAge = jdhSkuDto.getMaxAge();
            Integer minAge = jdhSkuDto.getMinAge();
            if(Objects.nonNull(maxAge) && Objects.nonNull(minAge)){
                Range<Integer> ageRange = Range.closed(minAge,maxAge);
                for (User user : users) {
                    Integer age = user.getBirthday().getAge();
                    if(!ageRange.contains(age)){
                        throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_AGE_ERROR.formatDescription(minAge, maxAge));
                    }
                }
            }

            //性别
            List<Integer> genderLimit = jdhSkuDto.getGenderLimit();
            for (User user : users) {
                Integer gender = user.getGender();
                if(!genderLimit.contains(gender)){
                    throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_GENDER_ERROR.formatDescription(Joiner.on("/").join(genderLimit.stream().map(GenderEnum::getDescOfType).collect(Collectors.toList()))));
                }
            }
        }
    }
}
