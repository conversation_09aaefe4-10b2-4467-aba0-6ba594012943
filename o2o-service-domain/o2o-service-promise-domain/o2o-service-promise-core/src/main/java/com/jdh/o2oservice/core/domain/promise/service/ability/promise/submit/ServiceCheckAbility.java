package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import cn.hutool.core.collection.CollUtil;
import com.jdh.o2oservice.application.product.ProductExtApplication;
import com.jdh.o2oservice.application.provider.ProviderExtApplication;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.DockingTypeEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.core.domain.promise.model.PromiseStation;
import com.jdh.o2oservice.core.domain.promise.rpc.PopLocProviderRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.ServiceRpcBO;
import com.jdh.o2oservice.base.model.User;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.provider.dto.ProviderDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务校验
 * @author: yangxiyu
 * @date: 2022/9/9 9:43 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class ServiceCheckAbility implements SubmitAbility {

    /** 健康档案服务 */
    @Resource
    private PopLocProviderRpc popLocProviderRpc;
    /**
     * providerExtApplication
     */
    @Resource
    private ProviderExtApplication providerExtApplication;

    /**
     * productExtApplication
     */
    @Autowired
    private ProductExtApplication productExtApplication;

    /**
     *
     * @return
     */
    @Override
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.SERVICE_CHECK;
    }

    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        JdhVerticalBusiness verticalBusiness = context.getVerticalBusiness();
        if (Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.POP_LOC.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.POP_FREE.getCode()) ){
            List<PromiseService> exist = context.getPromise().getServices();
            Map<Long, PromiseService> serviceMap = context.getServices().stream().collect(Collectors.toMap(PromiseService::getServiceId, Function.identity(), (o, n)->n));
            exist.forEach(e-> {
                PromiseService s = serviceMap.get(e.getServiceId());
                if (Objects.isNull(s)){
                    log.error("ServiceCheckAbility->execute serviceMap get is null serviceId={}", e.getServiceId());
                    throw new SystemException(SystemErrorCode.PARAM_NULL_ERROR);
                }else{
                    e.setOutServiceId(s.getOutServiceId());
                    e.setOutServiceName(s.getOutServiceName());
                }
            });
            popLoc(exist, context.getUsers().get(0), context.getSnapshot(), context.getPromise().getStore());
        }else if (Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode())
                | Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())){
            //到家的服务校验
            home(context);
        }else{
            throw new UnsupportedOperationException();
        }
    }

    /**
     * 到家的service校验
     *
     * @param context 上下文
     */
    private void home(PromiseSubmitAbilityContext context) {
        //根据serviceId查询 商品信息，看是否开启爆单
        Set<Long> serviceIdSet = context.getServices().stream().map(PromiseService::getServiceId).collect(Collectors.toSet());
        Map<Long, JdhSkuDto> skuDtoMap = productExtApplication.queryJdhSkuInfoByList(JdhSkuListRequest.builder().skuIdList(serviceIdSet).build());
        if(CollUtil.isEmpty(skuDtoMap)){
            throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_SERVICE_NOT_FOUND_ERROR);
        }
        for (Map.Entry<Long, JdhSkuDto> skuDtoEntry : skuDtoMap.entrySet()) {
            JdhSkuDto jdhSkuDto = skuDtoEntry.getValue();
            Integer saleStatus = jdhSkuDto.getSaleStatus();
            // 售卖状态 0-不可售卖 1-可售卖,仅控制是否可下单,非上下架
            if(NumConstant.NUM_0.equals(saleStatus)){
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_SERVICE_SALE_STATUS_ERROR);
            }
        }
    }

    /**
     * POP LOC当前仅支持一个服务
     * @param
     */
    public void popLoc(List<PromiseService> services, User user, JdhPromise promise, PromiseStation station){

        PromiseService service = services.get(0);
        ProviderDto providerDto = providerExtApplication.find(station.getChannelNo());
        if (Objects.equals(providerDto.getDockingType(), DockingTypeEnum.JDM.getType())){
            log.info("ServiceCheckAbility->poploc 京麦对接直接跳过");
            return;
        }

        List<ServiceRpcBO>  bos = popLocProviderRpc.listGoods(String.valueOf(service.getServiceId()), promise, station.getStoreId());
        Optional<ServiceRpcBO> goods = bos.stream().filter(e -> StringUtils.equals(service.getOutServiceId(), e.getOutServiceId())).findFirst();

        AssertUtils.isFalse(goods.isPresent(), new DynamicErrorCode(SystemErrorCode.ILLEGAL_OPERATION.getCode(),"无效套餐，请重新选择套餐"));
        goods.get().suitableUserCheck(user);
    }

}
