package com.jdh.o2oservice.core.domain.promise.rpc.bo;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.enums.MarryEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.base.model.User;
import lombok.Data;

import java.util.Objects;

/**
 * 套餐RPCBO
 * @author: yangxiyu
 * @date: 2023/12/25 11:57 上午
 * @version: 1.0
 */
@Data
public class ServiceRpcBO {


    /**
     * 根据婚姻状态和性别构建动态的套餐名称
     */
    private static final Table<Integer, Integer, String> DYNAMIC_GOODS_NAME_TABLE = HashBasedTable.create();
    static {

        DYNAMIC_GOODS_NAME_TABLE.put(GenderEnum.MAN.getType(), MarryEnum.NO.getType(), "男未婚套餐");
        DYNAMIC_GOODS_NAME_TABLE.put(GenderEnum.MAN.getType(), MarryEnum.YES.getType(), "男已婚套餐");
        DYNAMIC_GOODS_NAME_TABLE.put(GenderEnum.MAN.getType(), MarryEnum.COMMON.getType(), "男性套餐");


        DYNAMIC_GOODS_NAME_TABLE.put(GenderEnum.WOMEN.getType(), MarryEnum.NO.getType(), "女未婚套餐");
        DYNAMIC_GOODS_NAME_TABLE.put(GenderEnum.WOMEN.getType(), MarryEnum.YES.getType(), "女已婚套餐");
        DYNAMIC_GOODS_NAME_TABLE.put(GenderEnum.WOMEN.getType(), MarryEnum.COMMON.getType(), "女性套餐");

        DYNAMIC_GOODS_NAME_TABLE.put(GenderEnum.COMMON.getType(), MarryEnum.NO.getType(), "女未婚、男性套餐");
        DYNAMIC_GOODS_NAME_TABLE.put(GenderEnum.COMMON.getType(), MarryEnum.YES.getType(), "女已婚、男性套餐");
        DYNAMIC_GOODS_NAME_TABLE.put(GenderEnum.COMMON.getType(), MarryEnum.COMMON.getType(), "男女性通用套餐");
    }




    /**
     * 服务编号
     */
    private Long serviceId;

    /**
     * 商品id
     */
    private String outServiceId;
    /**
     * 商品名称
     */
    private String outServiceName;

    /**
     * 年龄上限
     */
    private Integer ageUpper;

    /**
     * 年龄下限
     */
    private Integer ageFloor;

    /**
     * 支持的性别
     */
    private Integer supportGender;
    /**
     * 支持的婚姻状态
     */
    private Integer supportMarry;


    /**
     * 服务适用人员验证
     * @param user
     */
    public void suitableUserCheck(User user){

        /**
         * 支持的性别不为空，支持的性别不是通用，需要校验
         */
        if (Objects.nonNull(supportGender) && !Objects.equals(supportGender, GenderEnum.COMMON.getType())){
            if (!Objects.equals(user.getGender(), supportGender)){
                throw new BusinessException(PromiseErrorCode.SERVICE_GENDER_NOT_SUPPORT);
            }
        }
        /**
         * 支持的婚姻状态不为空，支持的婚姻状态不是通用，需要校验
         */
        if (Objects.nonNull(supportMarry) && !Objects.equals(supportMarry, MarryEnum.COMMON.getType())){
            if (!Objects.equals(user.getMarriage(), supportMarry)){
                throw new BusinessException(PromiseErrorCode.SERVICE_MARRY_NOT_SUPPORT);
            }
        }

        if (Objects.nonNull(user.getBirthday()) && Objects.nonNull(ageUpper)){
            if (user.getBirthday().getAge() > ageUpper){
                throw new BusinessException(PromiseErrorCode.USER_AGE_NOT_SUITABLE_SERVICE_ERROR.formatDescription(ageFloor,ageUpper));
            }
        }
        if (Objects.nonNull(user.getBirthday()) && Objects.nonNull(ageFloor)){
            if (user.getBirthday().getAge() < ageFloor){
                throw new BusinessException(PromiseErrorCode.USER_AGE_NOT_SUITABLE_SERVICE_ERROR.formatDescription(ageFloor,ageUpper));
            }
        }
    }

    /**
     *
     * @return
     */
    public String dynamicGoodsName(){

        return DYNAMIC_GOODS_NAME_TABLE.get(supportGender, supportMarry);

    }

}
