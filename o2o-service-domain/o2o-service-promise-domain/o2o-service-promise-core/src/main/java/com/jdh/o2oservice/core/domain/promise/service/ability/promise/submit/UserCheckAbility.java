package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.base.model.User;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 履约人信息校验
 * 这三种来源的区别是 供应商对预约人的限制，业务模式本身对规则的限制，业务身份对规则的限制
 *
 * @author: yangxiyu
 * @date: 2022/9/9 9:43 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class UserCheckAbility implements SubmitAbility {

    /**
     *
     * @return
     */
    @Override
    @SuppressWarnings("JdAvoidMethodReturnEnum")
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.USER_CHECK;
    }

    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        // 提交的参数中的user信息
        List<User> users = context.getUsers();
        if (CollectionUtils.isNotEmpty(users)){
            for (User user : users) {
                check(context.getVerticalBusiness(), user);
            }
        }

        // promise实体从库里那处理的用户信息
        // 如果校验通过，更新到实体中，后续进行仓储的save操作
        List<JdhPromisePatient> patients = context.getPromise().getPatients();
        for (int i = 0; i < patients.size(); i++) {
            User user = users.get(i);
            JdhPromisePatient patient = patients.get(i);
            patient.fill(user);
        }
    }

    /**
     *
     * @param user
     * @return
     */
    public void check(JdhVerticalBusiness business, User user){
        // 解析预约人信息
        user.parse();
        // 校验预约人信息，可扩展，根据配置的模版进行校验
        user.verify();

        specialCheck(business, user);

    }

    /**
     * 临时校验，医美项目预约需要限制年龄18-70，临时添加校验。
     * 具体的年龄限制应该是配置在SKU纬度上的
     * @param business
     */
    private void specialCheck(JdhVerticalBusiness business, User user){
        if (Objects.isNull(business) || Objects.isNull(user)){
            return;
        }
        String serviceType = business.getServiceType();
        if (StringUtils.equals(serviceType, ServiceTypeEnum.COSMETIC.getServiceType())){
            if (Objects.nonNull(user.getBirthday())){
                Integer age = user.getBirthday().getAge();
                if (age < 18 || age > 70){
                    throw new BusinessException(PromiseErrorCode.USER_AGE_NOT_SUITABLE_SERVICE_ERROR.formatDescription(18,70));
                }
            }
        }

    }
}
