package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.base.model.User;
import com.jdh.o2oservice.core.domain.support.reach.service.ReachDomainService;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 短信验证码condition
 * @author: yangxiyu
 * @date: 2022/9/9 9:43 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class SmsCodeAbility implements SubmitAbility {


    /**
     * reachDomainService
     */
    @Resource
    @Lazy
    private ReachDomainService reachDomainService;
    /**
     *
     * @return
     */
    @Override
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.SMS_CODE_CHECK;
    }

    /**
     * 执行
     *
     * @param context 上下文
     */
    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        AssertUtils.nonNull(context.getSmsCode(), PromiseErrorCode.SMS_CODE_ILLEGAL);
        JdhVerticalBusiness verticalBusiness = context.getVerticalBusiness();

        if (Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.POP_LOC.getCode())
                || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.POP_FREE.getCode()) ){
            // 兼容POP逻辑，这里应该是拿预约人信息
            User user = context.getUsers().get(0);
            log.info("SmsCodeAbility->execute user={}", JSON.toJSONString(user));
            if(!reachDomainService.checkSmsCode(user.getUserPin(),user.getPhoneNumber().getPhone(),
                    verticalBusiness.getVerticalCode(), verticalBusiness.getServiceType(), context.getSmsCode())){
                log.info("SmsCodeAbility 短信验证码错误");
                throw new BusinessException(PromiseErrorCode.SMS_CODE_ILLEGAL);
            }
        }

    }


}
