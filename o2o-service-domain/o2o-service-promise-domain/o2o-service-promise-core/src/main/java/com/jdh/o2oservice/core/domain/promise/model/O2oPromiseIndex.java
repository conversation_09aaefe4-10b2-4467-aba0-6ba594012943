package com.jdh.o2oservice.core.domain.promise.model;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.jdh.o2oservice.base.enums.CredentialEnum;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.basic.enums.DateTypeEnum;
import com.jdh.o2oservice.base.model.CredentialNumber;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.UserName;
import lombok.Data;

import java.text.MessageFormat;
import java.util.Date;
import java.util.Objects;

/**
 * O2oPromiseIndexCmd
 *
 * <AUTHOR>
 * @date 2024/08/07
 */
@Data
public class O2oPromiseIndex {

    /**
     * verticalCode
     */
    private String verticalCode;

    /**
     * serviceType
     */
    private String serviceType;

    /**
     * orderId
     */
    private Long orderId;

    /**
     * promiseId
     */
    private String promiseId;

    /**
     * channelNo
     */
    private String channelNo;

    /**
     * skuId
     */
    private String skuId;

    /**
     * skuName
     */
    private String skuName;

    /**
     * userName
     */
    private String userName;

    /**
     * userPin
     */
    private String userPin;

    /**
     * userPhone
     */
    private String userPhone;

    /**
     * userCredentialType
     */
    private Integer userCredentialType;

    /**
     * userCredentialNo
     */
    private String userCredentialNo;

    /**
     * marriage
     */
    private Integer marriage;

    /**
     * appointmentStartTime
     */
    private Date appointmentStartTime;

    /**
     * appointmentEndTime
     */
    private Date appointmentEndTime;

    /**
     * storeId
     */
    private String storeId;

    /**
     * storeName
     */
    private String storeName;

    /**
     * promiseStatus
     */
    private Integer promiseStatus;

    /**
     * userGender
     */
    private Integer userGender;

    /**
     * birthday
     */
    private String birthday;

    /**
     * userAge
     */
    private Integer userAge;

    /**
     * expireDate
     */
    private Date expireDate;

    /**
     * createTime
     */
    private Date createTime;

    /**
     * updateTime
     */
    private Date updateTime;

    /**
     * processStatus
     */
    private Integer processStatus;

    /**
     * dateType
     */
    private Integer dateType;

    /**
     * 手机号打码
     * @return
     */
    public String maskUserPhone() {
        return new PhoneNumber(this.getUserPhone()).mask();
    }

    /**
     * 姓名打码
     * @return
     */
    public String maskUserName() {
        return new UserName(this.getUserName()).mask();
    }

    /**
     * 身份证号打码
     * @return
     */
    public String maskUserCredentialNo() {
        return new CredentialNumber(this.getUserCredentialType(), this.getUserCredentialNo()).mask();
    }

    /**
     * 时间拼接成前段展示样式
     *
     * @return
     */
    public String swapAppointmentTime() {
        return MessageFormat.format("{0} {1}", formatAppointmentDate(), formatAppointmentTime());
    }

    /**
     * 预约日期
     *
     * @return
     */
    public String formatAppointmentTime() {
        //按照时间段
        if(DateTypeEnum.SCHEDULE_BY_TIME.getType().equals(this.getDateType())){
            return TimeUtils.dateTimeToStr(appointmentStartTime, TimeFormat.DATE_PATTERN_HM_SIMPLE) + "-"
                    + TimeUtils.dateTimeToStr(appointmentEndTime, TimeFormat.DATE_PATTERN_HM_SIMPLE);
        }
        //上下午
        if(DateTypeEnum.AP_PM.getType().equals(this.getDateType())){
            //上午，返回am
            if (DateUtil.isAM(appointmentStartTime)) {
                return "上午";
            }else{
                return "下午";
            }
        }
        return StrUtil.EMPTY;
    }

    /**
     * 预约日期
     *
     * @return
     */
    public String formatAppointmentDate() {
        if (Objects.isNull(appointmentStartTime)){
            return "";
        }
        return TimeUtils.dateTimeToStr(appointmentStartTime, TimeFormat.SHORT_PATTERN_LINE);
    }

    public String parseCredentialType(){
        return CredentialEnum.getDescOfType(userCredentialType);
    }
}
