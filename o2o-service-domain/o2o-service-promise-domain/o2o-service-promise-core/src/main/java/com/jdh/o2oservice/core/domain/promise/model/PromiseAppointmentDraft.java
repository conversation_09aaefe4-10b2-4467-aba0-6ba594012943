package com.jdh.o2oservice.core.domain.promise.model;

import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseTime;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.base.model.User;
import lombok.Data;

import java.util.List;

/**
 * 履约单预约草稿数据
 * @author: yang<PERSON>yu
 * @date: 2024/1/3 7:43 下午
 * @version: 1.0
 */
@Data
public class PromiseAppointmentDraft implements Aggregate<DraftIdentifier> {

    /**
     * 草稿类型
     */
    private Integer draftType;
    /**
     * 草稿id
     */
    private String medicalLocId;
    /**
     * 预约人
     */
    private User user;
    /**
     * 预约门店
     */
    private String storeId;
    /**
     * 预约服务
     */
    private List<PromiseService> services;

    private PromiseTime appointmentTime;

    private Integer version;

    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.PROMISE;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return PromiseAggregateEnum.PROMISE_DRAFT;
    }

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {

    }

    @Override
    public DraftIdentifier getIdentifier() {
        return new DraftIdentifier(medicalLocId);
    }
}
