package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.base.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 基于健康档案信息的初始化能力点，不做任何校验，用户信息来自健康档案，健康档案支持什么我们就支持什么。
 *
 * @author: yangxiyu
 * @date: 2022/9/9 9:43 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class PatientInitAbility implements SubmitAbility {

    /**
     *
     * @return
     */
    @Override
    @SuppressWarnings("JdAvoidMethodReturnEnum")
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.PATIENT_INIT;
    }

    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        // 提交的参数中的user信息
        List<User> users = context.getUsers();
        if (CollectionUtils.isNotEmpty(users)){
            for (User user : users) {
                user.parse();
            }
        }

        // promise实体从库里那处理的用户信息
        // 如果校验通过，更新到实体中，后续进行仓储的save操作
        List<JdhPromisePatient> patients = context.getPromise().getPatients();
        for (int i = 0; i < patients.size(); i++) {
            User user = users.get(i);
            JdhPromisePatient patient = patients.get(i);
            patient.fill(user);
        }
    }

}
