package com.jdh.o2oservice.core.domain.promise.service.ability.promise.submit;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.trade.TradeExtApplication;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.context.PromiseTime;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.export.trade.dto.AvaiableAppointmentTimeDTO;
import com.jdh.o2oservice.export.trade.dto.XfylAppointDateDTO;
import com.jdh.o2oservice.export.trade.dto.XfylAppointDateTimeDTO;
import com.jdh.o2oservice.export.trade.dto.XfylAppointDateTimeGroupDTO;
import com.jdh.o2oservice.export.trade.query.AppointmentTimeParam;
import com.jdh.o2oservice.export.trade.query.AvaiableAppointmentTimeParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预约时间校验 能力
 *
 * <AUTHOR>
 * @date 2024/08/30
 */
@Slf4j
@Component
public class AppointmentTimeCheckAbility implements SubmitAbility{

    /**
     * tradeExtApplication
     */
    @Autowired
    private TradeExtApplication tradeExtApplication;

    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SubmitAbilityCode getAbilityCode() {
        return SubmitAbilityCode.APPOINTMENT_TIME_CHECK;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(PromiseSubmitAbilityContext context) {
        JdhVerticalBusiness verticalBusiness = context.getVerticalBusiness();
        //到家 进行预约时间校验
        if(BusinessModeEnum.ANGEL_CARE.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(verticalBusiness.getBusinessModeCode())
                || BusinessModeEnum.SELF_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())){
            homeCheck(context);
            // 快递模式
        }else if(BusinessModeEnum.SELF_TEST_TRANSPORT.getCode().equals(verticalBusiness.getBusinessModeCode())){
            List<PromiseService> services = context.getSnapshot().getServices();
            context.setServices(services);
            //判断时间是否已过去
            PromiseTime userAppointmentTime = context.getAppointmentTime();
            String appointmentStartTime = userAppointmentTime.getAppointmentStartTime();
            String appointmentEndTime = userAppointmentTime.getAppointmentEndTime();
            Date endAppointDate = TimeUtils.timeStrToDate(appointmentEndTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
            Date startAppointDate = TimeUtils.timeStrToDate(appointmentStartTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
            if(startAppointDate.after(endAppointDate)){
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_TIME_ERROR);
            }
            if(endAppointDate.getTime()<System.currentTimeMillis()){
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_TIME_ERROR);
            }
        }
    }

    /**
     * homeCheck
     *
     * @param context 上下文
     */
    private void homeCheck(PromiseSubmitAbilityContext context){
        //查排期数据
        AvaiableAppointmentTimeParam param = new AvaiableAppointmentTimeParam();
        param.setUserPin(context.getUserPin());
        param.setSkuIds(context.getServices().stream().map(PromiseService::getServiceId).collect(Collectors.toList()));
        //世博说: 目前只有4号单会进入到这个方法;4号单可保证地址id和全地址有值
        param.setAddressId(context.getStation().getStoreId());
        param.setFullAddress(context.getStation().getStoreAddr());
        AvaiableAppointmentTimeDTO appointmentTimeDTO = tradeExtApplication.queryAvailableAppointmentTime(param);
        log.info("AppointmentTimeCheckAbility -> execute appointmentTimeDTO:{}", JSON.toJSONString(appointmentTimeDTO));
        //可用时间列表
        List<XfylAppointDateDTO> compatibleGroupDTO = appointmentTimeDTO.getCompatibleGroupDTO();
        if(CollUtil.isEmpty(compatibleGroupDTO)){
            throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_TIME_ERROR);
        }
        List<XfylAppointDateTimeDTO> dateTimeList = new ArrayList<>();
        for (XfylAppointDateDTO xfylAppointDateDTO : compatibleGroupDTO) {
            List<XfylAppointDateTimeGroupDTO> groupDTOList = xfylAppointDateDTO.getAppointDateTimeGroupDTOList();
            if(CollUtil.isNotEmpty(groupDTOList)){
                for (XfylAppointDateTimeGroupDTO xfylAppointDateTimeGroupDTO : groupDTOList) {
                    List<XfylAppointDateTimeDTO> dateTimeDTOList = xfylAppointDateTimeGroupDTO.getDateTimeDTOList();
                    if(CollUtil.isNotEmpty(dateTimeDTOList)){
                        dateTimeList.addAll(dateTimeDTOList);
                    }
                }
            }
        }
        if(CollUtil.isEmpty(dateTimeList)){
            throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_TIME_ERROR);
        }
        PromiseTime userAppointmentTime = context.getAppointmentTime();

        // 基于当前时间的预约时间矫正
        userAppointmentTime.correctionTime(new Date());


        String appointmentStartTime = userAppointmentTime.getAppointmentStartTime();

        String appointmentEndTime = userAppointmentTime.getAppointmentEndTime();
        Date endAppointDate = TimeUtils.timeStrToDate(appointmentEndTime, TimeFormat.LONG_PATTERN_LINE_NO_S);

        Boolean isImmediately = userAppointmentTime.getIsImmediately();
        log.info("AppointmentTimeCheckAbility -> execute isImmediately={}, dateTimeList={}", isImmediately, JSON.toJSONString(dateTimeList));
        // 立即预约校验
        if(Boolean.TRUE.equals(isImmediately)){
            Optional<XfylAppointDateTimeDTO> first = dateTimeList.stream().filter(ele -> Boolean.TRUE.equals(ele.getIsImmediately()) && NumConstant.NUM_1.equals(ele.getStatus())).findFirst();
            if(first.isPresent()){
                XfylAppointDateTimeDTO appointDateTimeDTO = first.get();
                String canUseStartTime = appointDateTimeDTO.getAppointmentStartTime();
                // 查询日历第一个可约时段，如果第一个可约的时段和选择的时段无交集（预约结束时间小于第一个可约时段的开始时间）则无法预约
                Date canUseStartDate = TimeUtils.timeStrToDate(canUseStartTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
                if(endAppointDate.getTime() < canUseStartDate.getTime()){
                    throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_TIME_ERROR);
                }
            }else{
                // 没有可约的时段
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_TIME_ERROR);
            }
        }else{
            boolean checkPass = false;
            for (XfylAppointDateTimeDTO dateTimeDTO : dateTimeList) {
                //非立即预约,并且可约
                if(Boolean.FALSE.equals(dateTimeDTO.getIsImmediately()) && NumConstant.NUM_1.equals(dateTimeDTO.getStatus())){
                    String canUseStartTime = dateTimeDTO.getAppointmentStartTime();
                    String canUseEndTime = dateTimeDTO.getAppointmentEndTime();
                    if(appointmentStartTime.equals(canUseStartTime) && appointmentEndTime.equals(canUseEndTime)){
                        checkPass = true;
                        break;
                    }
                }
            }

            if(!checkPass){
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_TIME_ERROR);
            }
        }
    }

    /**
     * 预约时间和当前时间比较
     * 即时单：预约结束时间不能早于当前时间，
     * 预约单：预约开始时间不能晚于当前时间。
     * @param param
     * @return
     */
    public static boolean checkTime(PromiseTime param){
        if (param == null){
            return Boolean.TRUE;
        }
        // 按时段预约
        if (Objects.equals(param.getDateType(), 2)){
            // 立即预约：校验当前时间不晚于预约结束时间
            if (Objects.equals(param.getIsImmediately(), Boolean.TRUE) && StringUtils.isNotBlank(param.getAppointmentEndTime())){
                String appointmentEndTime = param.getAppointmentEndTime();
                LocalDateTime endTime = TimeUtils.timeStrToLocalDate(appointmentEndTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
                if (endTime.isBefore(LocalDateTime.now())){
                    return Boolean.FALSE;
                }
                // 时段预约：当前时间不晚于预约开始时间
            }else if(Objects.equals(param.getIsImmediately(), Boolean.FALSE)){
                String appointmentStartTime = param.getAppointmentStartTime();
                LocalDateTime  startTime = TimeUtils.timeStrToLocalDate(appointmentStartTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
                if (startTime.isBefore(LocalDateTime.now())){
                    return Boolean.FALSE;
                }
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 预约时间校验，给提单接口使用
     * @param param
     * @return
     */
    public static boolean checkTime(AppointmentTimeParam param){
        if (param == null){
            return Boolean.TRUE;
        }
        // 按时段预约
        if (Objects.equals(param.getDateType(), 2)){

            // 立即预约：校验当前时间不晚于预约结束时间
            if (Objects.equals(param.getIsImmediately(), Boolean.TRUE) && StringUtils.isNotBlank(param.getAppointmentEndTime())){
                String appointmentEndTime = param.getAppointmentEndTime();
                LocalDateTime endTime = TimeUtils.timeStrToLocalDate(appointmentEndTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
                if (endTime.isBefore(LocalDateTime.now())){
                    return Boolean.FALSE;
                }
                // 时段预约：当前时间不晚于预约开始时间
            }else if(Objects.equals(param.getIsImmediately(), Boolean.FALSE)){
                String appointmentStartTime = param.getAppointmentStartTime();
                LocalDateTime  startTime = TimeUtils.timeStrToLocalDate(appointmentStartTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
                if (startTime.isBefore(LocalDateTime.now())){
                    return Boolean.FALSE;
                }
            }
        }
        return Boolean.TRUE;
    }
}
