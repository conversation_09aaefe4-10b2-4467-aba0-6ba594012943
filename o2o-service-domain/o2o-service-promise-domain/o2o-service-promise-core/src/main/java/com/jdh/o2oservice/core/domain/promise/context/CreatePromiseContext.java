package com.jdh.o2oservice.core.domain.promise.context;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.core.domain.promise.bo.PromiseUser;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.base.model.Birthday;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.UserName;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.promise.cmd.CreatePromiseCmd;
import com.jdh.o2oservice.export.promise.cmd.SubmitUser;
import com.jdh.o2oservice.export.promise.dto.PatientSpecimenCodeDto;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 创建履约单上下文
 * @author: yangxiyu
 * @date: 2023/12/18 4:39 下午
 * @version: 1.0
 */
@Data
public class CreatePromiseContext extends BusinessContext {

    /**
     * 服务单
     */
    private JdhVoucher jdhVoucher;

    /**
     * 商品信息
     */
    private Map<Long, JdhSkuDto> promiseSkuMap;

    /**
     * 条码和检测人信息
     */
    private Map<PromiseUser, List<String>> userSpecimenCodeMap;

    /**
     * 创建Promise上下文
     *
     * @param jdhVoucher jdhVoucher
     */
    public CreatePromiseContext(JdhVoucher jdhVoucher, CreatePromiseCmd cmd) {
        this.jdhVoucher = jdhVoucher;
        super.setServiceType(jdhVoucher.getServiceType());
        if(StrUtil.isEmpty(jdhVoucher.getServiceType())){
            super.setServiceId(jdhVoucher.getVoucherItemList().get(0).getServiceId().toString());
        }
        super.setVerticalCode(jdhVoucher.getVerticalCode());

        // 初始化人和码的信息
        if (CollectionUtils.isNotEmpty(cmd.getPatientSpecimenCodes())){
            userSpecimenCodeMap = Maps.newHashMap();
            for (PatientSpecimenCodeDto dto : cmd.getPatientSpecimenCodes()) {
                SubmitUser userDto = dto.getUser();
                PromiseUser user = new PromiseUser();
                user.setPatientId(userDto.getPatientId());
                user.setUserPin(cmd.getUserPin());
                user.setGender(userDto.getGender());
                user.setOperationStatus(userDto.getOperationStatus());
                if (StringUtils.isNotBlank(userDto.getPhone())){
                    user.setPhoneNumber(new PhoneNumber(userDto.getPhone()));
                }
                if (StringUtils.isNotBlank(userDto.getBirthday())){
                    user.setBirthday(new Birthday(userDto.getBirthday()));
                }
                if (StringUtils.isNotBlank(userDto.getName())){
                    user.setUserName(new UserName(userDto.getName()));
                }
                userSpecimenCodeMap.put(user, Lists.newArrayList(dto.getSpecimenCodes()));
            }
        }


    }
}
