package com.jdh.o2oservice.core.domain.promise.context;

import com.jdh.o2oservice.core.domain.promise.model.PromiseAppointmentDraft;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.base.model.User;
import com.jdh.o2oservice.core.domain.support.vertical.context.AbstractAbilityContext;
import lombok.Data;

import java.util.List;

/**
 * 提交预约草稿数据上下文
 * @author: yangxiyu
 * @date: 2024/1/3 5:46 下午
 * @version: 1.0
 */
@Data
public class SubmitAppointmentDraftContext extends AbstractAbilityContext {

    /**
     * 草稿类型
     */
    private Integer draftType;
    /**
     * 预约人
     */
    private User user;
    /**
     * 预约门店
     */
    private String storeId;
    /**
     * 预约服务
     */
    private List<PromiseService> services;

    private PromiseTime appointmentTime;

    private String smsCode;

    private PromiseAppointmentDraft draft;
}
