package com.jdh.o2oservice.core.domain.promise.event;

import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.PromiseStation;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.User;
import lombok.Data;

/**
 * @author: yang<PERSON><PERSON>
 * @date: 2023/12/27 3:27 下午
 * @version: 1.0
 */
@Data
public class PromiseEventBody extends PromiseEventBaseBody {

    private String phone;
    private String name;

    /**
     * 商家门店名称
     */
    private String storeName;

    /**
     * 商家门店地址
     */
    private String storeAddr;

    /** 商家门店电话 */
    private String storePhone;

    /**
     * 预约时间类型1-按日 2-按时间段
     * 类型为1时，必传appointmentDate
     * 类型为2时，必传appointmentBeginTime、appointmentEndTime
     * 类型为4时，必传appointmentDate、timeRange
     */
    private Integer dateType;

    /**
     * 预约开始时间
     * 格式 yyyy-MM-dd HH:mm
     */
    private String appointmentStartTime;

    /**
     * 预约结束时间
     * 格式 yyyy-MM-dd HH:mm
     */
    private String appointmentEndTime;

    /**
     * 卡号
     */
    private String code;

    /**
     * 卡密
     */
    private String codePwd;

    /**
     * channelNo
     */
    private Long channelNo;

    public PromiseEventBody(JdhPromise snapshot, JdhPromise jdhPromise) {
        super(snapshot, jdhPromise);
        if (jdhPromise == null) {
            return;
        }
        this.beforeStatus = snapshot.getPromiseStatus();
        this.afterStatus = jdhPromise.getPromiseStatus();

        User user = jdhPromise.findOnlyPatient();
        if (user != null) {
            //手机号
            PhoneNumber phoneNumber = user.getPhoneNumber();
            if (phoneNumber != null && phoneNumber.verify()) {
                this.phone = user.getPhoneNumber().getPhone();
            }
        }

        //预约门店名和地址
        PromiseStation store = jdhPromise.getStore();
        if (store != null) {
            this.storeName = store.getStoreName();
            this.storeAddr = store.getStoreAddr();
        }

        //预约时间
        if (jdhPromise.getAppointmentTime() != null) {
            this.appointmentStartTime = jdhPromise.getAppointmentTime().formatAppointmentStartTime();
        }

        //卡号密码
        this.code = jdhPromise.getCode();
        this.codePwd = jdhPromise.getCodePwd();

        this.channelNo = jdhPromise.getStore().getChannelNo();
    }

    public PromiseEventBody() {

    }
}
