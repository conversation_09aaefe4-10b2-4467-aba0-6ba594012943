package com.jdh.o2oservice.core.domain.promise.context;

import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.PromiseIntendedNurse;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.core.domain.promise.model.PromiseStation;
import com.jdh.o2oservice.base.model.User;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 创建履约单上下文
 * @author: yangxiyu
 * @date: 2023/12/18 4:39 下午
 * @version: 1.0
 */
@Getter
@Setter
public class PromiseSubmitAbilityContext extends AbstractPromiseStateAbilityContext {

    /**
     * 预约人
     */
    private List<User> users;
    /**
     * 预约服务
     */
    private List<PromiseService> services;

    /**
     * 预约时间
     */
    private PromiseTime appointmentTime;
    /**
     * 履约站点信息
     */
    private PromiseStation station;

    /**
     * 短信验证码
     */
    private String smsCode;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 预约人手机号
     */
    private String appointmentPhone;

    /**
     * 联系人姓名
     */
    private String appointmentUserName;

    /**
     * jdhVoucher
     */
    private JdhVoucher jdhVoucher;

    /**
     * 意向护士
     */
    private PromiseIntendedNurse intendedNurse;

    /**
     * 预约地址
     */
    private PromiseAddress appointmentAddress;

    /**
     *
     */
    public PromiseSubmitAbilityContext() {
    }
}
