package com.jdh.o2oservice.core.domain.promise.service.ability.promise.draft;


import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.core.domain.promise.context.SubmitAppointmentDraftContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.base.model.User;
import com.jdh.o2oservice.core.domain.support.reach.service.ReachDomainService;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 体检预约能力 组件
 * @author: yangxiyu
 * @date: 2022/9/9 9:54 上午
 * @version: 1.0
 */
@Slf4j
@Component
public class DraftSmsCheckAbility implements DraftAbility {

    /**
     * getAbilityCode
     *
     * @return {@link DraftAbilityCode}
     */
    @Override
    public DraftAbilityCode getAbilityCode() {
        return DraftAbilityCode.SMS_CODE_CHECK;
    }

    /**
     * 触达domainService
     */
    @Resource
    @Lazy
    private ReachDomainService reachDomainService;

    /**
     * 执行
     *
     * @param context 上下文
     */
    @Override
    public void execute(SubmitAppointmentDraftContext context) {
        AssertUtils.nonNull(context.getSmsCode(), PromiseErrorCode.SMS_CODE_ILLEGAL);

        User user = context.getUser();
        JdhVerticalBusiness verticalBusiness = context.getVerticalBusiness();
        if(!reachDomainService.checkSmsCode(user.getUserPin(),user.getPhoneNumber().getPhone(),
                verticalBusiness.getVerticalCode(), verticalBusiness.getServiceType(), context.getSmsCode())){
            log.info("DraftSmsCheckAbility 短信验证码错误");
            throw new BusinessException(PromiseErrorCode.SMS_CODE_ILLEGAL);
        }
    }

}
