package com.jdh.o2oservice.core.domain.promise.context;

import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import lombok.Data;

import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * @author: yang<PERSON>yu
 * @date: 2024/1/16 2:19 下午
 * @version: 1.0
 */
@Data
public class PromiseTime implements Serializable {

    /**
     * 预约时间类型1-按日 2-按时间段
     * 类型为1时，必传appointmentDate
     * 类型为2时，必传appointmentBeginTime、appointmentEndTime
     * 类型为4时，必传appointmentDate、timeRange
     */
    private Integer dateType;
    /**
     * 预约开始时间
     * 格式 yyyy-MM-dd HH:mm
     */
    private String appointmentStartTime;
    /**
     * 预约结束时间
     * 格式 yyyy-MM-dd HH:mm
     */
    private String appointmentEndTime;


    /**
     * 是否是立即预约
     */
    private Boolean isImmediately;



    /**
     * 预约时间矫正
     * @param payTime
     */
    public void correctionTime(Date payTime){

        // 预约单不需要矫正
        if (Objects.isNull(isImmediately) || !isImmediately){
            return;
        }
        payTime = Objects.nonNull(payTime) ? payTime : new Date();

        /**
         * 即时单的预约开始时间和结束时间取值规则：时段以半小时为单位，往后追加N小时，再取整作为结束时间。其中N根据服务类型的差异不同而不同
         * 矫正时间逻辑：计算选择的预约开始时间到预约结束时间的长度L1，按照30分钟的刻度进行划分L1/30作为支付时间需要追加的时间，payTime + N * 30;
         * 最后再往后取整到半点
         */
        LocalDateTime startTime = TimeUtils.timeStrToLocalDate(appointmentStartTime, TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS);
        LocalDateTime endTime = TimeUtils.timeStrToLocalDate(appointmentEndTime, TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS);
        long minutes = Duration.between(startTime, endTime).toMinutes();
        int step = (int)minutes / 30;

        LocalDateTime newEndTime = TimeUtils.dateToLocalDateTime(payTime).plusMinutes(step * 30);
        // 获取结束时间距离下一个半点的差值
        int add = 30 - (newEndTime.getMinute() % 30);
        this.appointmentStartTime = TimeUtils.dateTimeToStr(payTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
        this.appointmentEndTime = TimeUtils.localDateTimeToStr(newEndTime.plusMinutes(add), TimeFormat.LONG_PATTERN_LINE_NO_S);
    }

}
