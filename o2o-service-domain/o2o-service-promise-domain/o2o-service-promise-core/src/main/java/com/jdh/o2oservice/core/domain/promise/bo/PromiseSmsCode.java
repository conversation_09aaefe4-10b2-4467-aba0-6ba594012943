package com.jdh.o2oservice.core.domain.promise.bo;

import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import lombok.Data;
import org.slf4j.helpers.MessageFormatter;

/**
 * 履约域短信验证码校验对象
 * @author: yang<PERSON>yu
 * @date: 2023/12/21 6:29 下午
 * @version: 1.0
 */
@Data
public class PromiseSmsCode {

    /** 短信验证码key=smsCode:{pin}:{phone}:{业务身份}:{业务类型}:{事件} */
    private static final String SMS_CODE_KEY = "smsCode:{}:{}:{}:{}:{}";

    private JdhVerticalBusiness verticalBusiness;

    private PromiseEventTypeEnum eventType;

    private PhoneNumber phoneNumber;

    private String userPin;

    private String smsCode;

    /**
     * 短信验证码前缀
     * @return
     */
    public String getSmsCodeKey(){
        Object[] array = new Object[]{userPin, phoneNumber.getPhone(), verticalBusiness.getVerticalCode(), verticalBusiness.getServiceType(), eventType.getCode()};
        return MessageFormatter.arrayFormat(SMS_CODE_KEY, array).getMessage();

    }



}
