package com.jdh.o2oservice.core.domain.settlement.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;

/**
 * @ClassName SettleItemTypeGroupEnum
 * @Description 结算金额类型分组枚举
 * <AUTHOR>
 * @Date 2025/9/3 10:43
 **/
@Getter
@AllArgsConstructor
public enum SettleItemGroupTypeEnum {

    // 1-订单收入 2-调账收入 3-考核激励 4-申诉补款 5-任务激励
    INCOME_ORDER(1, "订单收入", SettleTypeEnum.INCOME.getType(), Lists.newArrayList(SettleItemTypeEnum.TESTING, SettleItemTypeEnum.NURSING, SettleItemTypeEnum.FEE, SettleItemTypeEnum.OTHER),1),
    INCOME_ADJUST(2, "调账收入",SettleTypeEnum.INCOME.getType(), Lists.newArrayList(SettleItemTypeEnum.ADJUST, SettleItemTypeEnum.ADJUST_TESTING, SettleItemTypeEnum.ADJUST_NURSING, SettleItemTypeEnum.ADJUST_ADJUST, SettleItemTypeEnum.ADJUST_FEE, SettleItemTypeEnum.ADJUST_INCENTIVE, SettleItemTypeEnum.ADJUST_OTHER),2),
    INCENTIVE_ASSESSMENT(3, "考核激励",SettleTypeEnum.INCOME.getType(), Lists.newArrayList(SettleItemTypeEnum.INCENTIVE),3),
    INCOME_APPEAL(4, "申诉补款",SettleTypeEnum.INCOME.getType(), Lists.newArrayList(SettleItemTypeEnum.APPEAL_PAYMENT),4),
    INCENTIVE_TASK(5, "任务激励",SettleTypeEnum.INCOME.getType(), Lists.newArrayList(SettleItemTypeEnum.ACTIVITY),5),
    EXPEND_ADJUST(6, "调账扣除",SettleTypeEnum.EXPEND.getType(), Lists.newArrayList(SettleItemTypeEnum.DEDUCT_ADJUST, SettleItemTypeEnum.ADJUST_TESTING, SettleItemTypeEnum.ADJUST_NURSING, SettleItemTypeEnum.ADJUST_ADJUST, SettleItemTypeEnum.ADJUST_FEE, SettleItemTypeEnum.ADJUST_INCENTIVE, SettleItemTypeEnum.ADJUST_OTHER),6),
    EXPEND_ASSESSMENT(7, "考核扣除",SettleTypeEnum.EXPEND.getType(), Lists.newArrayList(SettleItemTypeEnum.DEDUCT_ASSESSMENT),7),
    ;

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String desc;

    /**
     * 结算类型 1 收入2 支出
     */
    private Integer settlementType;

    /**
     * 包含的结算金额类型
     */
    private List<SettleItemTypeEnum> containItemTypeList;

    /**
     *
     */
    private Integer sort;

    private static final Map<Integer, SettleItemGroupTypeEnum> TYPE_MAP = Maps.newHashMap();
    private static final List<SettleItemGroupTypeEnum> INCOME_LIST = new ArrayList<>();
    private static final List<SettleItemGroupTypeEnum> EXPEND_LIST = new ArrayList<>();

    static {
        for (SettleItemGroupTypeEnum value : values()) {
            TYPE_MAP.put(value.type, value);
            if (Objects.equals(value.settlementType, 1)) {
                INCOME_LIST.add(value);
            } else if (Objects.equals(value.settlementType, 2)) {
                EXPEND_LIST.add(value);
            }
        }
    }

    /**
     * @param type
     * @return
     */
    public static SettleItemGroupTypeEnum getSettleItemGroupTypeEnum(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        return TYPE_MAP.get(type);
    }

    /**
     * 获取收入枚举列表
     * @return
     */
    public static List<SettleItemGroupTypeEnum> getIncomeGroupEnumList() {
        return INCOME_LIST;
    }

    /**
     * 获取支出枚举列表
     * @return
     */
    public static List<SettleItemGroupTypeEnum> getExpendGroupEnumList() {
        return EXPEND_LIST;
    }

    /**
     * 是否包含结算金额类型
     * @param settleItemTypeEnum
     * @return
     */
    public boolean hasContainItemType(SettleItemTypeEnum settleItemTypeEnum) {
        if (Objects.isNull(settleItemTypeEnum)) {
            return false;
        }
        Optional<SettleItemTypeEnum> first = this.getContainItemTypeList().stream().filter(itemTypeEnum -> Objects.equals(itemTypeEnum.getType(), settleItemTypeEnum.getType())).findFirst();
        return first.isPresent();
    }
}