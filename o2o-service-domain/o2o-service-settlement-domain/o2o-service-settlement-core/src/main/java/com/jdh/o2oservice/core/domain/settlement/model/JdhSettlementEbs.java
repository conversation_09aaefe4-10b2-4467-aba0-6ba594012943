package com.jdh.o2oservice.core.domain.settlement.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <pre>
 *  ebs结算流水明细表
 * </pre>
 *
 * <AUTHOR>
 * @date 2024-05-30 16:58
 */
@Data
public class JdhSettlementEbs implements Serializable {

    /**
     * <pre>
     * 京东健康，业务身份
     * </pre>
     */
    private String jdhVerticalCode;

    /**
     * <pre>
     * 京东健康，业务模式
     * </pre>
     */
    private String jdhBusinessModeCode;

    /**
     * <pre>
     * 京东健康，服务类型
     * </pre>
     */
    private String jdhServiceType;

    /**
     * <pre>
     * 业务系统唯一ID,计费单的主键
     * </pre>
     */
    private String preId;

    /**
     * <pre>
     * 主分类：消费医疗（XFYL），传XFYL即可
     * </pre>
     */
    private String sourceType;

    /**
     * <pre>
     * 子分类(计费单类型) 收入: XNKTJTC-SRQR 非企销-收入 XNKTJTC-SRQRDKH 企销-收入 XNKTJTC-SRQRCYS 冲预收
     * </pre>
     */
    private String detailSourceType;

    /**
     * <pre>
     * 来源系统：XFYL
     * </pre>
     */
    private String srcSystem;

    /**
     * <pre>
     * 业务单号（采购订单号）(实验室结算时,存的检测单号)
     * </pre>
     */
    private String preDocNum;

    /**
     * <pre>
     * 客商编码
     * </pre>
     */
    private String unionNum;

    /**
     * <pre>
     * 客商名称
     * </pre>
     */
    private String unionName;

    /**
     * <pre>
     * 支付时间，格式为：2020-03-01 20:01:01
     * </pre>
     */
    private Date docCreateTime;

    /**
     * <pre>
     * 入账时间（完成时间），格式为：2020-03-01 20:01:01
     * </pre>
     */
    private Date appliedDate;

    /**
     * <pre>
     * 总金额，单位:元
     * </pre>
     */
    private BigDecimal totalAmount;

    /**
     * <pre>
     * 税率，格式如：6
     * </pre>
     */
    private String taxRate;

    /**
     * <pre>
     * 入账主体机构
     * </pre>
     */
    private String orgId;

    /**
     * <pre>
     * 币种 例：CNY
     * </pre>
     */
    private String currencyCode;

    /**
     * <pre>
     * 业务线编号:如: 100001 这个就是默认值B2C，专门指这个业务
     * </pre>
     */
    private String businessLine;

    /**
     * <pre>
     * sku编码
     * </pre>
     */
    private Long skuId;

    /**
     * <pre>
     * 父订单号
     * </pre>
     */
    private Long parentOrderId;

    /**
     * <pre>
     * 订单号
     * </pre>
     */
    private Long orderId;

    /**
     * <pre>
     * 品类(一级品类ID)
     * </pre>
     */
    private String firstCategoryCode;

    /**
     * <pre>
     * 项目（门店id）
     * </pre>
     */
    private String projectCode;

    /**
     * <pre>
     * 表组：JD_HY_PROVISION
     * </pre>
     */
    private String midTabGroupName;

    /**
     * <pre>
     * 费用类型
     * </pre>
     */
    private String lineDocTypeName;

    /**
     * <pre>
     * 大客户的机构ID(EBS推送) 非企销 不传 企销 传企业客户的下单主体
     * </pre>
     */
    private String newOrgId;

    /**
     * <pre>
     * 部门编码
     * </pre>
     */
    private String deptCode;

    /**
     * <pre>
     * 部门名称
     * </pre>
     */
    private String deptName;

    /**
     * <pre>
     * MQ发送状态：0-未发送 1-已发送 -1-发送失败
     * </pre>
     */
    private Integer sendStatus;

    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;

    /**
     * <pre>
     * 数据来源分支
     * </pre>
     */
    private String branch;

    /**
     * <pre>
     * 版本号
     * </pre>
     */
    private Integer version;

    /**
     * <pre>
     * 是否有效 0-无效 1-有效
     * </pre>
     */
    private Integer yn;

    /**
     * <pre>
     * 创建人
     * </pre>
     */
    private String createUser;

    /**
     * <pre>
     * 更新人
     * </pre>
     */
    private String updateUser;

    /**
     * <pre>
     * 创建时间
     * </pre>
     */
    private Date createTime;

    /**
     * <pre>
     * 更新时间
     * </pre>
     */
    private Date updateTime;

    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageNum = 1;

    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageSize = 10;
    /**
     * 测试检测单 1是 0 null 不是
     */
    private Integer isTest;

    /**
     * <pre>
     * 合同号
     * </pre>
     */
    private String contractNumber;

    /**
     * <pre>
     * 客户名称
     * </pre>
     */
    private String customerName;

    /**
     * <pre>
     * 客户id
     * </pre>
     */
    private String customerId;
    /**
     * <pre>
     * 账期天数
     * </pre>
     */
    private Integer settleDuration;

    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * 创建时间-开始
     */
    private Date createTimeStart;
    /**
     * 创建时间-结束
     */
    private Date createTimeEnd;
}