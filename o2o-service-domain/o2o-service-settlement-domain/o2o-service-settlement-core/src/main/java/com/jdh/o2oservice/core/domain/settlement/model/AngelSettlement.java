package com.jdh.o2oservice.core.domain.settlement.model;

import com.jdh.o2oservice.base.model.*;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleAggregateEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleItemTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleItemGroupTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 护士结算
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 05:53:18
 */
@Data
public class AngelSettlement implements Aggregate<AngelSettlementIdentifier>{


    /**
     * ID主键
     */
    private Long id;

    /**
     * 结算id
     */
    private Long settleId;

    /**
     * 服务者id
     */
    private Long angelId;
    /**
     * 服务者
     */
    private String angelName;
    /**
     * 人员标签 0兼职 1全职
     */
    private Integer jobNature;

    /**
     * 结算单号 or 提现单号
     */
    private Long settlementNo;
    /**
     * 结算业务id
     */
    private String settlementBusinessId;
    /**
     * 履约单id
     */
    private Long promiseId;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 结算类型 1 收入2 支出
     */
    private Integer settlementType;

    /**
     * 收入类型 1上门检测服务 2上门护理服务 3调整项 4 激励 
     */
    private Integer itemType;

    /**
     * 结算状态0 初始化 1 冻结中 2 已结算
     */
    private Integer settleStatus;

    /**
     * 提现状态 1提现中 2提现成功 3提现失败
     */
    private Integer cashStatus;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 预计结算时间
     */
    private Date expectSettleTime;

    /**
     * 结算时间
     */
    private Date settleTime;

    /**
     * 提现时间
     */
    private Date cashTime;

    /**
     * 到账时间
     */
    private Date receivedTime;

    /**
     * 金额
     */
    private BigDecimal settleAmount;

    /**
     * 失败原因code
     */
    private String failReasonCode;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 提现失败按钮
     */
    private String cashFailButton;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer yn;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 收入来源id
     */
    private Long itemSourceId;

    /**
     * 同步互医状态 0-未同步 1-已同步
     */
    private Integer syncStatus;

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        this.version++;
    }

    /**
     * 获取标识符
     *
     */
    @Override
    public AngelSettlementIdentifier getIdentifier() {
        return new AngelSettlementIdentifier(this.settleId);
    }

    /**
     * 结算时间格式化（触达使用）
     * @return
     */
    public String formatSettleTime(){
        if (Objects.nonNull(this.settleTime)){
            return TimeUtils.dateTimeToStr(settleTime);
        }else {
            return "";
        }
    }

    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.SETTLE_MENT;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return SettleAggregateEnum.SETTLE_ANGEL;
    }

    /**
     * 获取金额类型分组
     * 1.如果类型是支出，则放入支出类型分组中
     * 2.如果类型是收入，判断金额是否大于0，大于0放入收入类型分组，小于0放入支出类型分组
     * @return
     */
    public SettleItemGroupTypeEnum getSettleItemTypeGroupEnum() {
        //支出
        if (Objects.equals(this.getSettlementType(), SettleTypeEnum.EXPEND.getType())) {
            Optional<SettleItemGroupTypeEnum> first = SettleItemGroupTypeEnum.getExpendGroupEnumList().stream().filter(groupEnum -> groupEnum.hasContainItemType(SettleItemTypeEnum.getSettleTypeEnumByType(this.getItemType()))).findFirst();
            return first.orElse(null);
        }
        //支出
        else if (BigDecimal.ZERO.compareTo(this.getSettleAmount()) > 0) {
            Optional<SettleItemGroupTypeEnum> first = SettleItemGroupTypeEnum.getExpendGroupEnumList().stream().filter(groupEnum -> groupEnum.hasContainItemType(SettleItemTypeEnum.getSettleTypeEnumByType(this.getItemType()))).findFirst();
            return first.orElse(null);
        }
        //收入
        else {
            Optional<SettleItemGroupTypeEnum> first = SettleItemGroupTypeEnum.getIncomeGroupEnumList().stream().filter(groupEnum -> groupEnum.hasContainItemType(SettleItemTypeEnum.getSettleTypeEnumByType(this.getItemType()))).findFirst();
            return first.orElse(null);
        }
    }
}
