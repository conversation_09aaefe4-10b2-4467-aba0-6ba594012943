package com.jdh.o2oservice.core.domain.support.via.model;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.core.domain.support.via.enums.ActionType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * ViaActionInfo
 */
@Data
@Slf4j
public class ViaActionInfo {

    /**
     * 类型
     */
    private String type;

    /**
     * 函数ID
     */
    private String functionId;

    /**
     * URL
     */
    private String url;

    /**
     * 固定参数
     */
    private Map<String,Object> params;

    /**
     * false时不会初始化参数
     */
    private Boolean initSwitch;
    /**
     * 初始化
     */
    private Map<String, ViaItemParse> parseMap;

    /**
     * 扩展参数
     */
    private List<Map<String,Object>> extendParams;

    /**
     * 下一个动作
     */
    private ViaActionInfo nextAction;

    /**
     * 跳转链接规则
     */
    private String jumpUrlRule;
    /**
     * 当actionType是alert、toast是的文案信息
     */
    private String text;

    /**
     * 格式化params，params使用了占位符，如果从bean中可以获取到对应属性值，则以bean的属性值为准
     * 否则，使用params默认配置
     * @param bean
     * @return
     */
    public Map<String,Object> formatParams(Object bean){
        if (CollectionUtil.isEmpty(params)){
            return Collections.emptyMap();
        }
        Map<String, Object> res = Maps.newHashMap(params);
        res.forEach((key, filed)->{
            Object value = BeanUtil.getProperty(bean, Objects.toString(filed, ""));
            if (Objects.nonNull(value)){
                res.put(key, Objects.toString(value).trim());
            }
        });
        return res;
    }
    /**
     * 初始化action的params，当initSwitch不存在或者initSwitch为false时不执行初始化操作。
     * 配置initSwitch=true的场景：B端列表页表头，有的字段配置了action，但是这个action在初始化表头时不会同步初始化，这个action
     * 是在数据列表返回的时候需要添加到数据上返回的action。
     * @param map
     * @return
     */
    public void init(Map<String,Object> map) {
        if (Objects.isNull(initSwitch) || !initSwitch) {
            return;
        }

        if (Objects.equals(ActionType.JUMP.getCode(), type)){
            url = EntityUtil.fillUrlByBean(url, map);

        }else if(Objects.equals(ActionType.REQUEST.getCode(), type)){
            if (CollectionUtil.isEmpty(parseMap)) {
                return;
            }
            // 向后兼容，initParams操作可能是第一步初始化params的节点，之前的params可能为空
            if (CollectionUtil.isEmpty(params)) {
                params = new HashMap<>();
            }
            parseMap.forEach((key, init) -> {
                Object value = init.parse(map);
                params.put(init.getFiledKey(), value);
            });
        }
        if (Objects.nonNull(nextAction)){
            nextAction.init(map);
        }


    }

    /**
     * 解析action的parseMap，获取需要的params参数。
     * 和init方法的区别：init是初始化完参数后将参数值赋值给params，action本身被修改了。parseAction只是获取解析后的值
     * @param map
     * @return
     */
    public Map<String,Object> parseAction(Map<String,Object> map) {
        if (CollectionUtil.isEmpty(parseMap)) {
            return null;
        }
        Map<String,Object> res = new HashMap<>();
        parseMap.forEach((key, init) -> {
            Object value = init.parse(map);
            res.put(init.getFiledKey(), Objects.toString(value, ""));
        });
        return res;
    }


    /**
     * type为 JUMP时，需要根据URL的占位符填充参数
     * @param bean
     * @return
     */
    public String parseUrl(Object bean){
        if (Objects.equals(ActionType.JUMP.getCode(), type)){
            return EntityUtil.fillUrlByBean(url, bean);
        }
        return url;
    }
}
