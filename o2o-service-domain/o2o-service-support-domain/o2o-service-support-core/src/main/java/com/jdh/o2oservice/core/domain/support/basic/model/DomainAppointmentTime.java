package com.jdh.o2oservice.core.domain.support.basic.model;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.basic.enums.DateTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.text.MessageFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 通用的预约时间
 *
 * @author: yangxiyu
 * @date: 2024/1/19 3:55 下午
 * @version: 1.0
 */
@Data
public class DomainAppointmentTime implements Serializable {

    /**
     * 预约时间类型1-按日 2-按时间段
     * 类型为1时，必传appointmentDate
     * 类型为2时，必传appointmentBeginTime、appointmentEndTime
     * 类型为4时，必传appointmentDate、timeRange
     */
    protected Integer dateType;
    /**
     * 预约开始时间
     * 格式 yyyy-MM-dd HH:mm
     */
    protected LocalDateTime appointmentStartTime;
    /**
     * 预约结束时间
     * 格式 yyyy-MM-dd HH:mm
     */
    protected LocalDateTime appointmentEndTime;

    /**
     * 是否是立即预约
     */
    protected Boolean isImmediately;

    /**
     * 开始时间格式化
     *
     * @return
     */
    public String formatAppointmentStartTime() {
        if (Objects.isNull(appointmentStartTime)) {
            return null;
        }
        return TimeUtils.localDateTimeToStr(appointmentStartTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
    }

    /**
     * 结束时间格式化
     *
     * @return
     */
    public String formatAppointmentEndTime() {
        if (Objects.isNull(appointmentEndTime)) {
            return null;
        }
        return TimeUtils.localDateTimeToStr(appointmentEndTime, TimeFormat.LONG_PATTERN_LINE_NO_S);
    }

    /**
     * 开始时间格式化
     *
     * @return
     */
    public Date formatAppointmentTime(String date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return TimeUtils.timeStrToDate(date, TimeFormat.LONG_PATTERN_LINE_NO_S);
    }

    /**
     * 当前时间是的为预约时间
     *
     * @return
     */
    public Boolean arriveTime() {
        return LocalDate.now().equals(appointmentStartTime.toLocalDate());
    }


    /**
     * xx年xx月xx日 周x
     */
    private static final String DAY_FORMAT = "{0} {1}";
    /**
     * xx年xx月xx日 09:00-10:00 周x
     */
    private static final String TIME_FORMAT = "{0}月{1}日[星期{2}] {3}-{4}";

    /**
     * 格式化预约时间返回 xx年xx月xx日 周x
     *
     * @return
     */
    public String formatDesc() {
        if (Objects.isNull(appointmentStartTime)) {
            return "";
        }
        String day = formatAppointDate();
        String timeDesc = formatAppointTimeDesc();
        return MessageFormat.format(DAY_FORMAT, day, timeDesc);
    }

    /**
     * 4月1日[星期二]18:00-19:00
     * @return
     */
    public String formatDescDetail() {
        if (Objects.isNull(appointmentStartTime)) {
            return "";
        }
        if (DateTypeEnum.SCHEDULE_BY_TIME.getType().equals(this.getDateType())) {
            int month = appointmentStartTime.getMonthValue();
            int day = appointmentStartTime.getDayOfMonth();
            DayOfWeek dayOfWeek = appointmentStartTime.getDayOfWeek();
            return MessageFormat.format(TIME_FORMAT, month, day, WEEK_MAP.get(dayOfWeek),
                    TimeUtils.localDateTimeToStr(appointmentStartTime, TimeFormat.DATE_PATTERN_HM_SIMPLE)
                    , TimeUtils.localDateTimeToStr(appointmentEndTime, TimeFormat.DATE_PATTERN_HM_SIMPLE));
        }
        return "";
    }

    static final Map<DayOfWeek, String> WEEK_MAP = Maps.newHashMap();
    static {
        WEEK_MAP.put(DayOfWeek.MONDAY, "一");
        WEEK_MAP.put(DayOfWeek.TUESDAY, "二");
        WEEK_MAP.put(DayOfWeek.WEDNESDAY, "三");
        WEEK_MAP.put(DayOfWeek.THURSDAY, "四");
        WEEK_MAP.put(DayOfWeek.FRIDAY, "五");
        WEEK_MAP.put(DayOfWeek.SATURDAY, "六");
        WEEK_MAP.put(DayOfWeek.SUNDAY, "日");
    }
    /**
     * 格式化指定日期
     * 返回yyyy-MM-dd
     *
     * @return {@link String}
     */
    public String formatAppointDate() {
        if (Objects.isNull(appointmentStartTime)) {
            return "";
        }
        return TimeUtils.localDateTimeToStr(appointmentStartTime, TimeFormat.SHORT_PATTERN_LINE);
    }

    /**
     * 格式化时间描述
     *
     * @return
     */
    public String formatAppointTimeDesc() {
        //按照时间段
        if (DateTypeEnum.SCHEDULE_BY_TIME.getType().equals(this.getDateType())) {
            return TimeUtils.localDateTimeToStr(appointmentStartTime, TimeFormat.DATE_PATTERN_HM_SIMPLE) + "-"
                    + TimeUtils.localDateTimeToStr(appointmentEndTime, TimeFormat.DATE_PATTERN_HM_SIMPLE);
        }
        //上下午
        if (DateTypeEnum.AP_PM.getType().equals(this.getDateType())) {
            //上午，返回am
            if (DateUtil.isAM(Date.from(appointmentStartTime.atZone(ZoneId.systemDefault()).toInstant()))) {
                return "上午";
            } else {
                return "下午";
            }
        }
        return StrUtil.EMPTY;
    }

    /**
     * getAppointmentTimeFieldMap
     *
     * @return {@link Map}<{@link String},{@link Object}>
     */
    public Map<String, Object> buildAppointmentTimeFieldMap() {
        Map<String, Object> valueMap = new HashMap<>(NumConstant.NUM_8);
        // 1 2 4
        valueMap.put("dateType", this.getDateType());
        // yyyy-mm-dd
        valueMap.put("appointDate", this.formatAppointDate());
        // yyyy-mm-dd HH:mm
        valueMap.put("appointmentStartTime", this.formatAppointmentStartTime());
        // yyyy-mm-dd HH:mm
        valueMap.put("appointmentEndTime", this.formatAppointmentEndTime());
        // 1- yyyy-mm-dd  2- HH:mm -HH:mm  4- am、pm
        valueMap.put("appointTimeDesc", this.formatAppointTimeDesc());
        return valueMap;
    }

}
