package com.jdh.o2oservice.core.domain.support.patient.factory;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.enums.RelationTypeEnum;
import com.jdh.o2oservice.base.model.*;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.base.util.RSACode;
import com.jdh.o2oservice.core.domain.support.patient.context.PatientInfoContext;
import com.jdh.o2oservice.core.domain.support.patient.model.Patient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @created 2024-01-02-5:47 下午
 */
@Slf4j
public class PatientFactory {

    /**
     * 创建患者信息
     *
     * @param context 上下文
     * @return {@link Patient}
     */
    public static Patient createPatient(PatientInfoContext context){
        log.info("PatientFactory->createPatient context={}", JSON.toJSONString(context));
        Patient patient = new Patient();
        BeanUtils.copyProperties(context, patient);
        Integer relativesType = patient.getRelativesType();
        //父母，配偶，子女，关系属于其他
        if(RelationTypeEnum.PARENT.getCode().equals(relativesType)
                || RelationTypeEnum.SPOUSE.getCode().equals(relativesType)
                || RelationTypeEnum.CHILDREN.getCode().equals(relativesType)
                || RelationTypeEnum.OTHER.getCode().equals(relativesType)){

            patient.setRelativesType(RelationTypeEnum.PARENT.getParentCode());
            patient.setRelativesTypeDetail(relativesType);
        }

        if(StrUtil.isNotEmpty(context.getPhone())){
            PhoneNumber phoneNumber = new PhoneNumber(context.getPhone());
            phoneNumber.decrypt();
            patient.setPhone(phoneNumber.getPhone());
        }

        if(StrUtil.isNotEmpty(context.getName())){
            UserName userName = new UserName(context.getName());
            userName.decrypt();
            patient.setName(userName.getName());
        }

        if(StrUtil.isNotEmpty(context.getCredentialNo())){
            CredentialNumber credentialNumber = new CredentialNumber(context.getCredentialType(),context.getCredentialNo());
            credentialNumber.decrypt();
            credentialNumber.verifyException();
            patient.setCredentialNo(credentialNumber.getCredentialNo());

            if(StrUtil.isEmpty(context.getBirthday()) || StrUtil.isEmpty(context.getAge())){
                Birthday birthday = Birthday.parseByCredentialNumber(credentialNumber);
                log.info("PatientFactory.birthday={}", JSON.toJSONString(birthday));
                if(Objects.isNull(birthday)){
                    if(StrUtil.isEmpty(context.getBirthday())){
                        patient.setBirthday("1970-01-01");
                    }
                    if(StrUtil.isEmpty(context.getAge())){
                        patient.setAgeStr("18");
                    }
                }else{
                    if(StrUtil.isEmpty(context.getBirthday())){
                        patient.setBirthday(birthday.getBirth());
                    }
                    if(StrUtil.isEmpty(context.getAge())){
                        patient.setAgeStr(birthday.getAge().toString());
                    }
                }
            }
        }
        log.info("PatientFactory -> createPatient patient:{}", JSON.toJSONString(patient));
        if(StringUtils.isNotBlank(patient.getPhone())) {
            patient.setPhone(RSACode.encryptByPublicKey(RandomStringUtils.randomAlphanumeric(6) + patient.getPhone()));
        }
        if(StringUtils.isNotBlank(patient.getCredentialNo())) {
            patient.setCredentialNo(RSACode.encryptByPublicKey(RandomStringUtils.randomAlphanumeric(6) + patient.getCredentialNo()));
        }
        return patient;
    }

    public static Patient createPatient(User user){
        log.info("PatientFactory->createPatient user={}", JSON.toJSONString(user));
        Patient patient = new Patient();
        BeanUtils.copyProperties(user, patient);
        Integer relativesType = patient.getRelativesType();
        //父母，配偶，子女，关系属于其他
        if(RelationTypeEnum.PARENT.getCode().equals(relativesType)
                || RelationTypeEnum.SPOUSE.getCode().equals(relativesType)
                || RelationTypeEnum.CHILDREN.getCode().equals(relativesType)
                || RelationTypeEnum.OTHER.getCode().equals(relativesType)){

            patient.setRelativesType(RelationTypeEnum.PARENT.getParentCode());
            patient.setRelativesTypeDetail(relativesType);
        }

        String phone = EntityUtil.getFiledDefaultNull(user.getPhoneNumber(), PhoneNumber::getPhone);
        patient.setPhone(phone);

        String name = EntityUtil.getFiledDefaultNull(user.getUserName(), UserName::getName);
        patient.setName(name);

        String credentialNo = EntityUtil.getFiledDefaultNull(user.getCredentialNum(), CredentialNumber::getCredentialNo);
        patient.setCredentialNo(credentialNo);

        Integer credentialType = EntityUtil.getFiledDefaultNull(user.getCredentialNum(), CredentialNumber::getCredentialType);
        patient.setCredentialType(credentialType);


        String birth = EntityUtil.getFiledDefaultNull(user.getBirthday(), Birthday::getBirth);
        Integer age = EntityUtil.getFiledDefaultNull(user.getBirthday(), Birthday::getAge);
        patient.setBirthday(birth);
        patient.setAgeStr(Objects.toString(age, null));
        patient.setCredentialType(credentialType);

        log.info("PatientFactory -> createPatient patient:{}", JSON.toJSONString(patient));
        if(StringUtils.isNotBlank(patient.getPhone())) {
            patient.setPhone(RSACode.encryptByPublicKey(RandomStringUtils.randomAlphanumeric(6) + patient.getPhone()));
        }
        if(StringUtils.isNotBlank(patient.getCredentialNo())) {
            patient.setCredentialNo(RSACode.encryptByPublicKey(RandomStringUtils.randomAlphanumeric(6) + patient.getCredentialNo()));
        }
        return patient;
    }

    public static Patient createEmptyPatient(){
        return new Patient();
    }
}
