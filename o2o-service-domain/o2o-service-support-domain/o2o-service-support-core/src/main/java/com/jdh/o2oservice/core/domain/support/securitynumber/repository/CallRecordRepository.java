package com.jdh.o2oservice.core.domain.support.securitynumber.repository;
import com.jdh.o2oservice.core.domain.support.securitynumber.model.CallRecord;

import java.util.Date;
import java.util.List;

/**
 * @Description 外呼记录
 * @Date 2024/12/19 下午4:44
 * <AUTHOR>
 **/
public interface CallRecordRepository {

    int save(CallRecord entity);

    List<CallRecord> queryList(CallRecord query);

    int updateById(CallRecord entity);

    CallRecord queryCallRecordByCallId(String callId);

    List<CallRecord> queryWithoutAudioUrlCallPageList(Integer pageNum, Integer pageSize, Date queryStartTime);

}
