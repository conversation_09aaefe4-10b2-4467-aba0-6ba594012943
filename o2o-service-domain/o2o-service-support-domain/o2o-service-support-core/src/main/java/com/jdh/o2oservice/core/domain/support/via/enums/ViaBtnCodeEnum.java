package com.jdh.o2oservice.core.domain.support.via.enums;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 按钮code定义
 *
 * <AUTHOR>
 * @date 2024/01/03
 */
@Getter
public enum ViaBtnCodeEnum {

    /**
     * 按钮编码
     */
    SUBMIT_APPOINT_BTN("submitAppointBtn","预约单列表-立即预约"),

    BUY_APPOINT_SUBMIT_APPOINT_BTN("buyAppointSubmitAppointBtn","买约一体-立即预约"),

    BUY_FIRST_SUBMIT_APPOINT_BTN("buyFirstSubmitAppointBtn","先买后约-立即预约"),

    MODIFY_APPOINT_BTN("modifyAppointBtn","修改预约"),

    CANCEL_APPOINT_BTN("cancelAppointBtn","取消预约"),

    RE_APPOINT_BTN("reAppointBtn","重新预约"),

    /**
     * 商家端
     */
    /** 京麦履约单列表页，查询栏按钮*/
    JM_PROMISE_PAGE_SEARCH_BTN("jmPromisePageSearch","查询"),
    JM_PROMISE_PAGE_SEARCH_RESET_BTN("jmPromisePageReset","重置"),
    JM_PROMISE_PAGE_SEARCH_EXPORT_BTN("jmPromisePageExport","导出"),
    JM_PROMISE_PAGE_SEARCH_EXPORT_TASK_BTN("jmPromisePageExportTask","导出记录"),
    /** 京麦履约单列表页，每行数据支持的按钮*/
    JM_PROMISE_PAGE_MODIFY_APPOINT_BTN("jmPromisePageModify","修改预约"),
    JM_PROMISE_PAGE_MAKING_CALL_BTN("jmPromisePageCall","拨打电话"),
    /**
     * 用户发起预约、修改预约、取消操作，商家需要人工确认时展示确认和驳回按钮。
     * 提交预约：用户提交，履约单自动扣减库存成功后，商家操作确认或驳回（驳回需要释放库存）。
     * 修改预约：用户提交修改，履约单自动扣减新日期成功后，商家操作确认（需要释放修改前的预约日历库存）或驳回（释放修改后的日历库存）。
     * 取消预约：用户提交后，商家操作确认（需要释放库存）或驳回。
     */
    JM_PROMISE_PAGE_CONFIRM_BTN("jmPromisePageConfirm","确认"),
    JM_PROMISE_PAGE_REJECT_BTN("jmPromisePageReject","驳回"),

    /**
     * 到家订单详情
     */
    //申请退款 refundBtn
    REFUND_BTN("refundBtn","申请退款"),
    //再次购买 rePurchaseBtn
    RE_PURCHASE_BTN("rePurchaseBtn","再次购买"),
    //联系客服 contactCustomerBtn
    CONTACT_CUSTOMER_BTN("contactCustomerBtn","联系客服"),
    CONTACT_ANGEL("contactAngelBtn","联系Ta"),

    //取消订单 cancelOrderBtn
    CANCEL_ORDER_BTN("cancelOrderBtn","取消订单"),
    //立即支付 payNowBtn
    PAY_NOW_BTN("payNowBtn","立即支付"),
    //查看报告
    VIEW_REPORT_BTN("viewReportBtn","查看报告"),
    // 绑定条码按钮
    BIND_SPECIMEN_CODE("bindSpecimenCode","提交样本信息"),
    SCANNING_SPECIMEN_CODE("scanningSpecimenCodeBtn","已采样，去扫码"),
    REFRESH_VIA_PAGE("refreshViaPageBtn","刷新VIA页面"),
    ENTER_SPECIMEN_CODE("enterSpecimenCodeBtn","手动输入"),
    MULTI_ENTER_SPECIMEN_CODE("multiScanningSpecimenCodeBtn","已完成采样，去录入样本号"),
    CREATE_PROMISE_BTN("createPromiseBtn","提交信息创建履约单"),

    PROMISE_DETAIL_BTN("jumpPromiseDetail","预约详情页面按钮"),
    PROMISE_MODIFY_DETAIL_BTN("jumpPromiseModifyDetail","修改预约页面按钮"),

    //评价护士服务 evaluateAngelBtn
    EVALUATE_ANGEL_BTN("evaluateAngelBtn","评价护士服务"),

    //修改预约时间 modifyDatetimeBtn
    MODIFY_DATETIME_BTN("modifyDatetimeBtn","修改时间"),

    URGE_ORDER_BTN("urgeOrderBtn","点我催单"),
    /**
     * O2O运营端
     */
    RESET_MEDICAL_PROMISE_REPORT_STATUS_BTN("resetMedicalPromiseReportStatus", "重传报告"),
    RESET_MEDICAL_PROMISE_SPECIMEN_CODE_BTN("resetMedicalPromiseSpecimenCode", "换绑条码"),


    ;

    ViaBtnCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    private static final Map<String, ViaBtnCodeEnum> BTN_MAP = Maps.newHashMap();
    static {
        for (ViaBtnCodeEnum value : values()) {
            BTN_MAP.put(value.code, value);
        }
    }
    /**
     *
     * @param code
     * @return
     */
    public static Boolean containsCode(String code){
        return BTN_MAP.containsKey(code);
    }

    /**
     *
     * @param code
     * @return
     */
    public static Boolean containsCode(List<String> code){
        if (CollectionUtils.isEmpty(code)){
            return Boolean.FALSE;
        }
        for (String s : code) {
            if (BTN_MAP.containsKey(s)){
                return Boolean.TRUE;
            }
        }
        return  Boolean.FALSE;
    }
}
