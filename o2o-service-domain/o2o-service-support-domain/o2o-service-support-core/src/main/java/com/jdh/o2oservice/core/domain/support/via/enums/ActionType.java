package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * VIA-action类型
 * @author: yang<PERSON>yu
 * @date: 2024/1/9 11:46 上午
 * @version: 1.0
 */
@Getter
public enum ActionType {

    /**
     * action 类型
     */
    //location.href跳
    JUMP("jump"),
    //location.replace跳
    REPLACE_JUMP("replaceJump"),
    //从action的返回结果中取跳转字段进行跳转
    ACTION_JUMP("actionJump"),
    //请求接口
    REQUEST("request"),
    //刷新页面
    REFRESH("refresh"),
    // action会替换原表单的值（敏感信息解密场景）
    REPLACE("replace"),
    ALERT("alert"),
    //自定义弹层
    CUSTOM_POPUP("customPopup"),
    TOAST("toast"),
    ;

    /**
     * 类型操作
     *
     * @param code 代码
     */
    ActionType(String code) {
        this.code = code;
    }

    /**
     * code
     */
    private String code;

}
