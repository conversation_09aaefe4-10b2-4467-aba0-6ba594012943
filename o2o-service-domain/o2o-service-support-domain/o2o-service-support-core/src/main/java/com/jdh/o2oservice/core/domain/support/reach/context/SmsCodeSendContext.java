package com.jdh.o2oservice.core.domain.support.reach.context;

import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import lombok.Data;

/**
 * 短信代码发送上下文
 *
 * <AUTHOR>
 * @date 2024/01/23
 */
@Data
public class SmsCodeSendContext extends BusinessContext {

    /**
     * patientId
     */
    private String patientId;

    /**
     * 手机号
     */
    private PhoneNumber phoneNumber;

    /**
     * 短信验证码缓存唯一ID
     */
    private String uniqueRedisStr;



}
