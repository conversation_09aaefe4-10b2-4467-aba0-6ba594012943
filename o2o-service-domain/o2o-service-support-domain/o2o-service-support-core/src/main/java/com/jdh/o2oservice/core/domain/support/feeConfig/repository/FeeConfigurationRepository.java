package com.jdh.o2oservice.core.domain.support.feeConfig.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.support.feeConfig.model.FeeConfig;
import com.jdh.o2oservice.core.domain.support.feeConfig.model.FeeConfigIdentifier;

import java.util.List;

/**
 * 费项配置仓储层
 * @author: wangyu1387
 * @date: 2024/4/26 8:43 下午
 * @version: 1.0
 */
public interface FeeConfigurationRepository extends Repository<FeeConfig, FeeConfigIdentifier> {

    Page<FeeConfig> queryPage(FeeConfig feeConfig);

    int update(FeeConfig entity);

    Long insertReturnId(FeeConfig feeConfig);

    List<FeeConfig> queryList(FeeConfig feeConfig);
}
