package com.jdh.o2oservice.core.domain.support.reach.model;

import com.jdh.o2oservice.base.model.User;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName ReachPromise
 * @Description
 * <AUTHOR>
 * @Date 2024/2/5 21:12
 **/
@Data
public class ReachPromise {

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务单ID
     */
    private Long voucherId;


    /**
     * 冻结状态 0 正常 1 冻结
     */
    private Integer freeze;
    /**
     * 履约单序号，对于一个服务单需要多次履约的场景
     */
    private Integer serialNum;

    /**
     * 履约单状态：待履约/已履约/预约中/预约失败/预约成功/修改预约中/修改预约失败/取消中/取消失败/取消成功/
     */
    private Integer promiseStatus;

    /**
     * 免预约、在线预约、商家预约
     */
    private Integer promiseType;

    /**
     * 过期时间
     */
    private LocalDateTime expireDate;

    /**
     * 卡号ID
     */
    private String codeId;

    /**
     * 卡号
     */
    private String code;

    /**
     * 卡密
     */
    private String codePwd;

    /**
     * appointmentInfo
     */
    /**
     * 履约渠道编号
     */
    private Long channelNo;
    /**
     * 预约单号
     */
    private Long appointmentId;

    /**
     * 渠道方的履约单号
     */
    private String channelAppointmentId;

    /**
     * 服务兑换来源id
     */
    private String sourceVoucherId;

    /**
     * 预约时间类型1-按日 2-按时间段
     * 类型为1时，必传appointmentDate
     * 类型为2时，必传appointmentBeginTime、appointmentEndTime
     * 类型为4时，必传appointmentDate、timeRange
     */
    protected Integer dateType;
    /**
     * 预约开始时间
     * 格式 yyyy-MM-dd HH:mm
     */
    protected LocalDateTime appointmentStartTime;
    /**
     * 预约结束时间
     * 格式 yyyy-MM-dd HH:mm
     */
    protected LocalDateTime appointmentEndTime;


    /**
     * 受检人（使用服务的患者信息）
     */
    private List<User> patients;
    /**
     * 京东门店的ID
     */
    private String storeId;
    /**
     * 商家门店名称
     */
    private String storeName;
    /**
     * 商家门店地址
     */
    private String storeAddr;
    /** 商家门店电话 */
    private String storePhone;
    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新用户
     */
    private String updateUser;
}