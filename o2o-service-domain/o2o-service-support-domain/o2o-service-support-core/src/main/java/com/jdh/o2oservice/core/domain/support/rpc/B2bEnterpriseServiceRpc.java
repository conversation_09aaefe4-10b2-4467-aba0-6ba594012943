package com.jdh.o2oservice.core.domain.support.rpc;

import com.jdh.o2oservice.export.support.command.B2bOperationLogCmd;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseAccountContractDto;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseBillDetailDto;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseInfoDto;
import com.jdh.o2oservice.export.support.query.JdhEnterpriseBillRequest;
import com.jdh.o2oservice.export.support.query.JdhEnterpriseRequest;

import java.util.List;

/**
 * @ClassName EnterpriseBillServiceRpc
 * @Description 企业账单
 * <AUTHOR>
 * @Date 2025/03/07 11:49
 */
public interface B2bEnterpriseServiceRpc {

    /**
     * 查询企业账户+合同
     * @param jdhEnterpriseRequest
     * @return
     */
    JdhEnterpriseAccountContractDto queryEnterpriseAccountAndContract(JdhEnterpriseRequest jdhEnterpriseRequest);

    /**
     * 查询企业信息
     * @param jdhEnterpriseRequest
     * @return
     */
    JdhEnterpriseInfoDto queryEnterpriseInfo(JdhEnterpriseRequest jdhEnterpriseRequest);

    /**
     * 保存操作日志
     * @param b2bOperationLogCmd
     * @return
     */
    Boolean saveOperationLog(B2bOperationLogCmd b2bOperationLogCmd);

    /**
     * 查询企业id
     * @param enterpriseVoucherId
     * @return
     */
    Long queryEnterpriseId(Long enterpriseVoucherId);
}
