package com.jdh.o2oservice.core.domain.support.basic.enums;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.*;

/**
 * JdhPromiseStatusEnum
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Getter
public enum JdhPromiseStatusEnum {

    /**
     * 待履约
     */
    WAIT_PROMISE(1, "待履约"),

    /**
     * 上门服务预约中 == 派单中，预约成功 == 派单成功
     */
    APPOINTMENT_ING(2, "预约中"),
    APPOINTMENT_SUCCESS(3,"预约成功"),
    APPOINTMENT_FAIL(4,"预约失败"),
    MODIFY_ING(5,"修改预约中"),
    MODIFY_SUCCESS(6,"修改预约成功"),
    MODIFY_FAIL(7,"修改预约失败"),
    CANCEL_ING(8,"取消预约中"),
    CANCEL_SUCCESS(9,"取消预约成功"),
    CANCEL_FAIL(10,"取消预约失败"),

    /**
     * 服务
     */
    SERVICE_READY(15,"待服务"),
    SERVICING(16,"服务中"),
    // 到店场景：商家推到检表示服务完成
    SERVICE_COMPLETE(17,"服务完成"),

    /**
     * 完成
     */
    //到家检测：检测单的出报告 -> 履约完成
    //到家护理：履约单服务完成 -> 履约已完成
    //到店：已核销 -> 履约已完成
    COMPLETE(11,"履约已完成"),
    //WRITE_OFF(12,"已核销"),
    INVALID(13,"作废"),
    EXPIRATION(14,"已过期"),


    ;

    /**
     * @param status
     * @param desc
     */
    JdhPromiseStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     * status
     */
    private final Integer status;

    /**
     * desc
     */
    private final String desc;

    /**
     * STATUS_MAP
     */
    private static final Map<Integer, JdhPromiseStatusEnum> STATUS_MAP = Maps.newHashMap();


    static {
        for (JdhPromiseStatusEnum value : values()) {
            STATUS_MAP.put(value.getStatus(), value);
        }
    }

    /**
     * 转换
     *
     * @param status status
     * @return {@link JdhPromiseStatusEnum}
     */
    @SuppressWarnings("JdAvoidMethodReturnEnum")
    public static JdhPromiseStatusEnum convert(Integer status){
        if (Objects.isNull(status)){
            return null;
        }
        return STATUS_MAP.get(status);
    }

    public static final List<Integer> UN_FINISH_STATUS = Arrays.asList(APPOINTMENT_SUCCESS.status,
            MODIFY_SUCCESS.status, SERVICE_READY.status, SERVICING.status, SERVICE_COMPLETE.status);

    /**
     * 终态
     */
    private static final Set<Integer> FINISH_STATUS = Sets.newHashSet(COMPLETE.status, INVALID.status, EXPIRATION.status);

    /**
     * 不可重新绑码的状态
     */
    public static final Set<Integer> UN_BIND_STATUS = Sets.newHashSet(WAIT_PROMISE.status, APPOINTMENT_ING.status, CANCEL_SUCCESS.status, INVALID.status, EXPIRATION.status);

    /**
     * 是否完成
     * @return {@link Boolean}
     */
    public static Boolean isFinish(Integer status){
        return FINISH_STATUS.contains(status);
    }

    public static JdhPromiseStatusEnum getEnumByCode(Integer status){
        for (JdhPromiseStatusEnum value : JdhPromiseStatusEnum.values()) {
            if (value.status.equals(status)) {
                return value;
            }
        }
        return null;
    }
}
