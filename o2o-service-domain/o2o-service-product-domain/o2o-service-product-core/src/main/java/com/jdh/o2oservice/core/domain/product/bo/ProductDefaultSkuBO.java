package com.jdh.o2oservice.core.domain.product.bo;

import com.jd.health.medical.examination.export.base.dto.ProductSkuLabelDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品基本信息和价格
 */
@Data
public class ProductDefaultSkuBO {

    /**
     * 规格
     */
    private String specification;

    /**
     * sku名
     */
    private String skuName;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 是否是自营
     */
    private Boolean selfFlag;

    /**
     * 商品主图
     */
    private String imgDfsUrl;

    /**
     * 商品名称介绍
     */
    private String title;

    /**
     * <pre>
     * 子商品短名称
     * </pre>
     */
    private String skuShortName;

    /**
     * 划线价格字符串
     */
    private String jdPriceDesc;

    /**
     * 划线价格 京东价格
     */
    private BigDecimal jdPrice;

    /**
     * 预估到手价格
     */
    private BigDecimal finalPrice;

    /**
     * 预估到手价格字符串
     */
    private String finalPriceDesc;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 标签
     */
    private List<ProductSkuLabelBO> label;

    /**
     * 促销标签
     */
    private Map<Integer, String> promotionTags;

    /**
     * 双价格标签
     */
    private Integer doublePriceTag;

    /**
     * 到家业务编码
     */
    private String homeBizCode;
    /**
     * 价格描述
     */
    private String priceDesc;
    /**
     * sku跳转地址
     */
    private String skuJumpUrl;

    /**
     * 服务类型
     */
    private Integer serviceType;

    /**
     * plus到手价标，计算了plus优惠（plus95折、plus专享立减、plus券）, 叠加上plus专属优惠（包括券和促销）
     */
    private Boolean matchPlusFlag;

    /**
     * 匹配服务站
     */
    private Boolean matchStationFlag;

    /**
     * sku简称
     */
    private String skuAbbreviation;

    /**
     * sku组套内排序
     */
    private Integer skuGroupInnerOrder;

    /**
     * 价格描述提示
     */
    private String priceDescTip;

    /**
     * spuId
     */
    private String spuId;

}