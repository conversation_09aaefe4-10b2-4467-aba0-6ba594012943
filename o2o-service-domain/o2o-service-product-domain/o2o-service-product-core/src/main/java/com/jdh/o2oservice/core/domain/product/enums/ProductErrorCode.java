package com.jdh.o2oservice.core.domain.product.enums;

import com.jdh.o2oservice.base.enums.AlarmLevelEnum;
import com.jdh.o2oservice.base.exception.errorcode.AbstractErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.ToString;

import java.util.Objects;

/**
 * 商品域错误码
 *
 * @author: yangxiyu
 * @date: 2023/12/18 8:11 下午
 * @version: 1.0
 */
@ToString
public enum ProductErrorCode implements AbstractErrorCode {

    /**
     * 健康商品数据通用错误码
     */
    PARAM_NULL_ERROR(DomainEnum.PRODUCT, SystemErrorCode.PARAM_NULL_ERROR.getCode(), "参数为空"),
    PARAM_NULL_ERROR_FORMAT(DomainEnum.PRODUCT, SystemErrorCode.PARAM_NULL_ERROR.getCode(), "参数{}为空"),
    NULL_ERROR_FORMAT(DomainEnum.PRODUCT, SystemErrorCode.PARAM_NULL_ERROR.getCode(), "{}"),

    /**
     * 商品域错误码
     */
    SERVICE_NOT_EXIST(DomainEnum.PRODUCT, "60001", "商品不存在或已下架"),
    SERVICE_IN_POOL_EXIST(DomainEnum.PRODUCT, "60002", "套餐已加入套餐对比中"),
    SERVICE_POOL_RANG_OUT(DomainEnum.PRODUCT, "60003", "套餐池中套餐已超过最高限制"),
    SERVICE_CONTRAST_RANG_OUT(DomainEnum.PRODUCT, "60004", "最多选择3份套餐"),
    SERVICE_CONTRAST_POOL_NOT_SAME(DomainEnum.PRODUCT, "60004", "套餐池某商品已删除"),
    SERVICE_NOT_LOC(DomainEnum.PRODUCT, "60005", "非套餐对比商品"),
    SERVICE_CONTRAST_DIFF_TIME_OUT(DomainEnum.PRODUCT, "60006", "套餐对比差异，请刷新页面重新进入"),
    PRODUCT_SKU_REPEAT(DomainEnum.PRODUCT, "60007", "sku重复"),
    PRODUCT_SKU_NOT_EXIST_CHECK(DomainEnum.PRODUCT, "60008", "sku不存在，请输入正确的sku"),
    PRODUCT_SKU_NOT_EXIST_CHECK_FORMAT(DomainEnum.PRODUCT, "60008", "sku:{}，请输入正确的sku"),
    PRODUCT_SKU_EXIST_CHECK(DomainEnum.PRODUCT, "60009", "sku已存在"),
    PRODUCT_SKU_SUB_NUM_LIMIT(DomainEnum.PRODUCT, "60010", "最多10个升级sku"),
    PRODUCT_SKU_SUB_SERVICE_TYPE_NOT_MATCH_FORMAT(DomainEnum.PRODUCT, "60011", "sku:{}，与主品sku业务身份不一致，无法添加"),
    PRODUCT_SKU_SAVE_FAIL(DomainEnum.PRODUCT, "60012", "商品保存失败"),
    PRODUCT_SKU_UPDATE_FAIL(DomainEnum.PRODUCT, "60013", "商品更新失败"),
    PRODUCT_SKU_IMPORT_EXIST(DomainEnum.PRODUCT, "60014", "商品导入任务已存在"),
    PRODUCT_SKU_IMPORT_FAIL_FORMAT(DomainEnum.PRODUCT, "60015", "{}"),
    PRODUCT_SKU_CONFIG_EXIST_CHECK(DomainEnum.PRODUCT, "60016", "sku:{}已存在"),
    PRODUCT_SKU_TAG_CHECK(DomainEnum.PRODUCT, "60017", "请检查商品是否已打标jdhdjfw=1"),
    PRODUCT_SKU_ITEM_CHECK(DomainEnum.PRODUCT, "60018", "快递寄送检测只能添加一个项目"),
    PRODUCT_SKU_THIRD_CATEGORY_CHECK(DomainEnum.PRODUCT, "60019", "快递寄送检测商品需要配置三级类目白名单,请联系研发侧配置"),


    PRODUCT_SKU_TYPE_NOT_MAIN(DomainEnum.PRODUCT, "60018", "主sku的商品类型不是主品"),
    PRODUCT_SKU_TYPE_NOT_ADDED(DomainEnum.PRODUCT, "60019", "子sku的商品类型不是加项品,sku:{}"),
    MAIN_PRODUCT_SKU_CAN_NOT_CHANGE(DomainEnum.PRODUCT, "60020", "主sku已绑定加项品，无法修改为当前主品为加项商品"),
    ADD_PRODUCT_SKU_CAN_NOT_CHANGE(DomainEnum.PRODUCT, "60021", "附加品sku已绑定主品，无法修改为主品"),

    PRODUCT_PRODUCT_EXIST_CHECK(DomainEnum.PRODUCT, "60016", "product已存在"),
    SERVICE_CONTRAST_SUITABLE_ERROR(DomainEnum.PRODUCT, "60017", "套餐适用人群未配置，请删除重新选择"),
    JD_PRODUCT_ID_INSERT_EXIST(DomainEnum.PRODUCT, "60022", "已存在商品插入任务"),
    PRODUCT_SKU_BUSINESS_HOUR_ERROR(DomainEnum.PRODUCT, "60023", "sku每日可约时间范围设置不正确!"),
    /**
     * 健康商品项目数据错误码
     */
    PRODUCT_SERVICE_ITEM_ALL_IN_STATION(DomainEnum.PRODUCT, "61001", "一个sku中的所有检测项目必须有共同的实验室"),
    PRODUCT_SERVICE_ITEM_NOT_EXIST_FORMAT(DomainEnum.PRODUCT, "61002", "sku:{}，未配置项目信息,请配置后重新添加"),
    PRODUCT_SERVICE_ITEM_IMPORT_EXIST(DomainEnum.PRODUCT, "61003", "商品项目导入任务已存在"),
    PRODUCT_SERVICE_ITEM_IMPORT_FAIL_FORMAT(DomainEnum.PRODUCT, "61004", "{}"),


    /**
     * 健康商品耗材数据错误码
     */
    PRODUCT_MATERIAL_PACKAGE_SAVE_FAIL(DomainEnum.PRODUCT, "62001", "耗材保存失败"),
    PRODUCT_MATERIAL_PACKAGE_UPDATE_FAIL(DomainEnum.PRODUCT, "62002", "耗材更新失败"),
    PRODUCT_MATERIAL_PACKAGE_EXIST(DomainEnum.PRODUCT, "62003", "耗材包已存在"),
    PRODUCT_MATERIAL_PACKAGE_SKU_EXIST(DomainEnum.PRODUCT, "62004", "耗材包SKU已存在"),

    /**
     * 健康商品条码数据错误码
     */
    PRODUCT_SPECIMEN_CODE_SAVE_FAIL(DomainEnum.PRODUCT, "63001", "条码保存失败"),
    PRODUCT_SPECIMEN_CODE_UPDATE_FAIL(DomainEnum.PRODUCT, "63002", "条码更新失败"),
    PRODUCT_SPECIMEN_CODE_TASK_EXIST(DomainEnum.PRODUCT, "63003", "已存在条码创建任务，请稍后重试"),
    PRODUCT_SPECIMEN_CODE_CREATE_FAIL(DomainEnum.PRODUCT, "63004", "条码创建失败，请稍后重试"),


    /**
     * 健康商品指标错误码
     */
    PRODUCT_INDICATOR_CODE_SAVE_FAIL(DomainEnum.PRODUCT, "44001", "指标保存失败"),
    PRODUCT_INDICATOR_CODE_UPDATE_FAIL(DomainEnum.PRODUCT, "44002", "指标更新失败"),
    PRODUCT_INDICATOR_CODE_DATA_NO_EXIST(DomainEnum.PRODUCT, "44003", "没有数据"),
    PRODUCT_INDICATOR_CODE_CREATE_FAIL(DomainEnum.PRODUCT, "44004", "条码创建失败，请稍后重试"),
    PRODUCT_INDICATOR_IMPORT_FAIL(DomainEnum.PRODUCT, "44005", "指标导入失败"),
    PRODUCT_INDICATOR_WIKI_FAIL(DomainEnum.PRODUCT, "44006", "指标百科不存在"),
    PRODUCT_INDICATOR_CATEGORY_IMPORT_FAIL(DomainEnum.PRODUCT, "44007", "指标标准分类导入失败"),
    PRODUCT_INDICATOR_BIZ_CATEGORY_IMPORT_EXIST(DomainEnum.PRODUCT, "44007", "指标业务分类导入任务已存在"),
    PRODUCT_INDICATOR_IMPORT_FAIL_FORMAT(DomainEnum.PRODUCT, "44008", "{}"),
    PRODUCT_INDICATOR_DELETE_USE_FAIL_FORMAT(DomainEnum.PRODUCT, "44009", "{}个项目引用了该指标,无法删除"),
    PRODUCT_INDICATOR_EXIST(DomainEnum.PRODUCT, "440010", "指标名称已存在"),
    PRODUCT_INDICATOR_NOT_EXIST(DomainEnum.PRODUCT, "440011", "指标数据不存在"),


    /**
     * 健康商品指标错误码
     */
    PRODUCT_ITEM_CODE_SAVE_FAIL(DomainEnum.PRODUCT, "45001", "项目保存失败"),
    PRODUCT_ITEM_CODE_DATA_NO_EXIST(DomainEnum.PRODUCT, "45002", "没有数据!"),
    PRODUCT_ITEM_CODE_CREATE_FAIL(DomainEnum.PRODUCT, "45002", "项目创建失败，请稍后重试"),


    /**
     * 百科内容不存在
     */
    PRODUCT_CONTENT_NO_EXIST(DomainEnum.PRODUCT, "M00001", "百科不存在，请先创建百科内容"),

    PRODUCT_SKILL_CODE_SAVE_FAIL(DomainEnum.PRODUCT, "45001", "技能保存失败"),
    PRODUCT_MATERIAL_CODE_SAVE_FAIL(DomainEnum.PRODUCT, "46001", "耗材保存失败"),

    /**
     * 标准项目相关错误码 47xxxx
     */
    PRODUCT_STANDARD_ITEM_CREATE_PARAM_ERROR(DomainEnum.PRODUCT, "47001", "创建标准项目参数错误: {0}"),
    PRODUCT_STANDARD_ITEM_CREATE_ITEM_NAME_REPEAT_ERROR(DomainEnum.PRODUCT, "47002", "创建标准项目名称已存在"),
    PRODUCT_STANDARD_ITEM_QUERY_ERROR(DomainEnum.PRODUCT, "47003", "查询标准项目信息失败：{0}"),
    PRODUCT_STANDARD_ITEM_IMPORT_TEMPLATE_QUERY_ERROR(DomainEnum.PRODUCT, "47004", "查询标准项目导入模板失败"),
    PRODUCT_STANDARD_ITEM_IMPORT_ERROR(DomainEnum.PRODUCT, "47004", "标准项目导入失败:{0}"),
    PRODUCT_STANDARD_ITEM_UPDATE_ITEM_NAME_REPEAT_ERROR(DomainEnum.PRODUCT, "47005", "更新标准项目名称与其他已存在项目名称重复"),
    PRODUCT_STANDARD_ITEM_UPDATE_PARAM_ERROR(DomainEnum.PRODUCT, "47006", "更新标准项目参数错误: {0}"),
    PRODUCT_STANDARD_ITEM_DEL_PARAM_ERROR(DomainEnum.PRODUCT, "47007", "删除标准项目参数错误: {0}"),

    PRODUCT_SELF_BIZ_ITEM_NAME_REPEAT(DomainEnum.PRODUCT, "48001", "新增自营业务项目自定义和名称重复"),
    PRODUCT_STANDARD_ITEM_NULL(DomainEnum.PRODUCT, "48002", "标准项目不存在"),
    PRODUCT_INDICATOR_NUM_ERROR(DomainEnum.PRODUCT, "48003", "指标数量不一致，请选择{0}项指标"),

    PRODUCT_SELF_BIZ_ITEM_BIND_PROGRAM(DomainEnum.PRODUCT, "48004", "存在套餐已绑定项目，无法删除"),

    BATCH_QUERY_SERVICE_ITEM_MAX_LIMITED(DomainEnum.PRODUCT, "48005", "批量查询SKU最多支持50个项目"),
    BATCH_QUERY_SERVICE_ITEM_ADDREDD_NULL(DomainEnum.PRODUCT, "48005", "批量查询SKU时地址信息不能为空"),

    /**
     * 制定护士错误码
     *
     */
    APPOINT_ANGLE_SKU_IMPORT_EXIST(DomainEnum.PRODUCT, "64001", "指定护士 sku导入任务已存在"),
    ;

    /**
     * ProviderErrorCode
     *
     * @param domainEnum  域枚举
     * @param code        代码
     * @param description 描述
     */
    ProductErrorCode(DomainEnum domainEnum, String code, String description) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
    }

    ProductErrorCode(DomainEnum domainEnum, String code, String description, AlarmLevelEnum alarmLevelEnum) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
        this.alarmLevelEnum = alarmLevelEnum;
    }

    /**
     *
     */
    private DomainEnum domainEnum;
    /**
     *
     */
    private String code;
    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * 报警级别
     */
    private AlarmLevelEnum alarmLevelEnum;

    /**
     * 获取代码
     *
     * @return {@link String}
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return {@link String}
     */
    @Override
    public String getDescription() {
        return description;
    }

    /**
     * 获取报警级别
     *
     * @return
     */
    public AlarmLevelEnum getAlarmLevel() {
        if(Objects.isNull(alarmLevelEnum)) {
            return AlarmLevelEnum.WARING;
        }
        return alarmLevelEnum;
    }
}
