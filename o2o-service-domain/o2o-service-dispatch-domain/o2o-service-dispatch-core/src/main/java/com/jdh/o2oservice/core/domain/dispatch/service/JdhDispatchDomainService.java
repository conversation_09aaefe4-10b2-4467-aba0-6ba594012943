package com.jdh.o2oservice.core.domain.dispatch.service;

import com.jdh.o2oservice.base.ducc.model.DispatchFlowPipelineConfig;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.dispatch.context.*;
import com.jdh.o2oservice.core.domain.dispatch.context.bo.DispatchPlanTime;
import com.jdh.o2oservice.core.domain.dispatch.event.DispatchFlowResultEventBody;
import com.jdh.o2oservice.core.domain.dispatch.model.AngelWorkData;
import com.jdh.o2oservice.core.domain.dispatch.model.DispatchFilterConfig;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchServiceInfo;
import com.jdh.o2oservice.core.domain.dispatch.model.ServiceLocation;
import com.jdh.o2oservice.export.dispatch.query.DispatchFlowPipelineConfigRequest;

import java.util.List;

/**
 * @ClassName JdhDispatchDomainService
 * @Description
 * <AUTHOR>
 * @Date 2024/4/21 10:42
 **/
public interface JdhDispatchDomainService {

    /**
     * 提交派单任务
     * @param context
     * @return
     */
    Boolean submitDispatch(SubmitDispatchContext context);

    /**
     * 发起服务者派单
     * @param context
     * @return
     */
    Boolean angelDispatch(AngelDispatchContext context);

    /**
     *
     * @param dispatchContext
     * @return
     */
    DispatchFlowResultEventBody executeDispatch(AngelDispatchContext dispatchContext);

    /**
     * 冻结派单
     * @param context
     * @return
     */
    Boolean freezeDispatch(FreezeDispatchContext context);

    /**
     * 派单信息回调
     * @param context
     * @return
     */
    Boolean callBack(DispatchCallbackContext context);

    /**
     * 重派
     * @param context
     * @return
     */
    Boolean reDispatch(ReDispatchContext context);

    /**
     * 指定派单（定向）
     * @param context
     * @return
     */
    Boolean targetDispatch(TargetDispatchContext context);

    /**
     * 接单护士转单
     * @param context
     * @return
     */
    Boolean receiveTransferDispatch(TargetDispatchContext context);

    /**
     * 派单回收
     * @param context
     * @return
     */
    Boolean recoverDispatch(RecoverDispatchContext context);

    /**
     * 作废派单
     * @param context
     * @return
     */
    Boolean invalidDispatch(InvalidDispatchContext context);

    /**
     * 服务者拒绝接单
     * @param context
     * @return
     */
    Boolean angelRefuse(AngelRefuseDispatchContext context);

    /**
     * 派单完成
     *
     * @param context
     * @return
     */
    Boolean dispatchComplete(DispatchCompleteContext context);


    /**
     * 解析过滤策略
     * @return
     */
    DispatchFilterConfig parseFilterStrategy(ServiceLocation serviceLocation);

    /**
     * 查询工单列表
     * @param queryAngelWorkContext
     * @return
     */
    AngelWorkData queryValidAngelWork(QueryAngelWorkContext queryAngelWorkContext);

    /**
     * 派单任务解析出来的计划出门、计划完成时间。
     * 用途：其余订单派单全选护士时，会根据护士已接订单的计划时间进行冲突校验
     * @param serviceInfo
     * @return
     */
    DispatchPlanTime parsePlanTime(JdhDispatchServiceInfo serviceInfo);

    /**
     * 查询派单流程配置详情
     * @param request
     * @return
     */
    DispatchFlowPipelineConfig queryDispatchFlowPipelineConfig(DispatchFlowPipelineConfigRequest request);

    /**
     * 查询派单流程配置列表
     * @param request
     * @return
     */
    List<DispatchFlowPipelineConfig> queryDispatchFlowPipelineConfigList(DispatchFlowPipelineConfigRequest request);

    /**
     * 查询派单流程配置列表-分页
     * @param request
     * @return
     */
    PageDto<DispatchFlowPipelineConfig> queryDispatchFlowPipelineConfigPage(DispatchFlowPipelineConfigRequest request);

    /**
     * 保存派单流程配置
     * @param context
     * @return
     */
    Boolean saveDispatchFlowPipelineConfig(DispatchFlowPipelineConfigContext context);

    /**
     * 删除派单流程配置
     * @param context
     * @return
     */
    Boolean removeDispatchFlowPipelineConfig(DispatchFlowPipelineConfigContext context);
}