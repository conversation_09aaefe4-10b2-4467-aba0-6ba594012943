package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchTagEnum;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30 14:56
 */
@Component("dispatchIntendedDecisionService")
@Slf4j
public class DispatchIntendedDecisionService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("DispatchIntendedDecisionService -> call, 派单决策 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("DispatchIntendedDecisionService -> call, 派单决策 START context={}", JSON.toJSONString(context));
        List<DispatchAngelBO> angelList = context.getSelectionAngelList();
        if (CollectionUtils.isEmpty(angelList)) {
            log.info("DispatchIntendedDecisionService -> call, 派单决策 无可派护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }
        context.setSelectionAngelList(angelList);

        //获取派单持续时间配置
        Map<String, Map<String, JSONObject>> dispatchDetailDurationMap = duccConfig.getDispatchDetailDurationMap();
        log.info("DispatchIntendedDecisionService -> call, 获取派单持续时间配置 dispatchDetailDurationMap={}", JSON.toJSONString(dispatchDetailDurationMap));
        Map<String, JSONObject> durationMap = dispatchDetailDurationMap.getOrDefault(context.getVerticalBusiness().getBusinessModeCode(), JSON.parseObject("{\"1\":{\"startTime\":\"07:00\",\"endTime\":\"22:00\",\"durationMinute\":10},\"2\":{\"startTime\":\"07:00\",\"endTime\":\"22:00\",\"durationMinute\":10}}", new TypeReference<Map<String, JSONObject>>() {}));
        //当前派单任务对应的持续时间配置
        JSONObject durationConfig = durationMap.get(DispatchTagEnum.INTENDED.getCode());
        log.info("DispatchIntendedDecisionService -> call, 当前派单任务对应的持续时间配置 durationConfig={}", JSON.toJSONString(durationConfig));
        // 后续单位调整未秒 second
        Integer durationMinute = durationConfig.getInteger("durationMinute");

        context.setDispatchDecisionExecuteTime(new Date());
        context.setRedispatchTime((long)durationMinute);

        //预约上门开始时间、预约上门结束时间
        LocalDateTime appointmentStartTime = context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentStartTime();
        LocalDateTime appointmentEndTime = context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentEndTime();

        //常规派单轮时间可用性配置
        Map<String, Integer> dispatchTimeBufferDuration = duccConfig.getDispatchTimeBufferDuration();
        Integer planStartBufferMinute = dispatchTimeBufferDuration.getOrDefault("planStartBufferMinute", 60);//a
        Integer planServiceTimeMinute = dispatchTimeBufferDuration.getOrDefault("planServiceTimeMinute", 60);//b
        Integer dailyTimeBufferMinute = dispatchTimeBufferDuration.getOrDefault("dailyTimeBufferMinute", 20);//d
        //当前时间
        LocalDateTime now = LocalDateTime.now();
        //预计上门时间
        Date planDoorTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(-planStartBufferMinute));
        //预计服务完成时间
        Date planServiceDoneTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(planServiceTimeMinute));

        //如果预计上门时间比当前时间还早，则预计上门时间重新计算（用户预约时间往后+）
        if (now.isAfter(appointmentStartTime.plusMinutes(-planStartBufferMinute))) {
            planDoorTime = TimeUtils.localDateTimeToDate(now);
            planServiceDoneTime = TimeUtils.localDateTimeToDate(now.plusMinutes(planStartBufferMinute).plusMinutes(planServiceTimeMinute));
        }
        context.setPlanOutTime(planDoorTime);
        context.setPlanFinishTime(planServiceDoneTime);
        log.info("DispatchIntendedDecisionService -> call, 派单决策 END context={}", JSON.toJSONString(context));
        return new OutputMessage();
    }

    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    @Override
    public String getCode() {
        return DispatchFlowEnum.INTENDED_DISPATCH_DECISION.getFlowCode();
    }

    @Override
    public String getName() {
        return DispatchFlowEnum.INTENDED_DISPATCH_DECISION.getFlowName();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======DispatchIntendedDecisionService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}
