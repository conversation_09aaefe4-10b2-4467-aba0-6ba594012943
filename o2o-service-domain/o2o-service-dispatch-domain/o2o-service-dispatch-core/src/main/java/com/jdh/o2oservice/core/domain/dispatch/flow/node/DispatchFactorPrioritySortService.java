package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.DispatchTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelStationBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchQueryAngelStationParam;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.direction.GeoCalculateService;
import com.jdh.o2oservice.core.domain.support.rpc.AngelRealTrackRpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.AngelRealTrackBo;
import com.jdh.o2oservice.core.domain.support.rpc.param.AngelRealTrackParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchFactorPrioritySortService
 * @Description
 * <AUTHOR>
 * @Date 2025/3/9 13:41
 **/
@Service("dispatchFactorPrioritySortService")
@Slf4j
public class DispatchFactorPrioritySortService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     * 地址服务
     */
    @Resource
    AddressRpc addressRpc;

    /**
     * geoCalculateService
     */
    @Resource
    private GeoCalculateService geoCalculateService;

    /**
     * dispatchFlowDependRpc
     */
    @Resource
    private DispatchFlowDependRpc dispatchFlowDependRpc;

    /**
     *
     */
    @Resource
    private AngelRealTrackRpc angelRealTrackRpc;

    /**
     * 因子：1.护士身份  2.上门时间
     * 护士身份：全职 = 1，兼职 = 0
     * 距离：计算每名护士电动车（不支持的话用骑行）到目的地的时间，最短的分值为 1，最长的分值为 0，其余的按比例计算
     * 预约单：全职以服务站位置为起点，兼职以接单偏好中心点为起点
     * 即时单：以护士实时位置为起点，如果没有取到实时位置，全职按服务站，兼职按接单偏好中心点兜底
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("dispatchFactorPrioritySortService -> call, 排序逻辑 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("dispatchFactorPrioritySortService -> call, 排序逻辑 context={}", JSON.toJSONString(context));
        //对圈选的护士进行因子排序
        List<DispatchAngelBO> angelList = context.getSelectionAngelList();
        if (CollectionUtils.isEmpty(angelList)) {
            log.info("dispatchFactorPrioritySortService -> call, 排序逻辑 无可派护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }
        //遍历查询护士到用户家的距离
        Map<Long, GisPointBo> fromPoints = new HashMap<Long, GisPointBo>();

        //即时单组装护士经纬度
        if (Objects.equals(context.getJdhDispatch().getDispatchType(), DispatchTypeEnum.IMMEDIATELY.getType())){
            for (DispatchAngelBO angel : angelList) {
                AngelRealTrackParam angelRealTrackParam = new AngelRealTrackParam();
                angelRealTrackParam.setAngelId(angel.getAngelId());
                AngelRealTrackBo angelRealTrackBo = angelRealTrackRpc.queryAngelRealTrack(angelRealTrackParam);
                if (Objects.nonNull(angelRealTrackBo) && Objects.nonNull(angelRealTrackBo.getLat()) && Objects.nonNull(angelRealTrackBo.getLng())) {
                    GisPointBo fromPoint = new GisPointBo();
                    fromPoint.setLatitude(BigDecimal.valueOf(angelRealTrackBo.getLat()));
                    fromPoint.setLongitude(BigDecimal.valueOf(angelRealTrackBo.getLng()));
                    fromPoints.put(angel.getAngelId(), fromPoint);
                    continue;
                }
                if (Objects.isNull(angel.getStationId())) {
                    continue;
                }
                List<DispatchAngelStationBO> stationBOList = dispatchFlowDependRpc.queryAngelStationList(DispatchQueryAngelStationParam.builder().stationId(String.valueOf(angel.getStationId())).pageNum(1).pageSize(1).build());
                if (CollectionUtils.isEmpty(stationBOList)) {
                    continue;
                }
                DispatchAngelStationBO dispatchAngelStationBO = stationBOList.get(0);
                if (StringUtils.isBlank(dispatchAngelStationBO.getFenceRangeCenterLat()) || StringUtils.isBlank(dispatchAngelStationBO.getFenceRangeCenterLng())) {
                    continue;
                }
                GisPointBo fromPoint = new GisPointBo();
                fromPoint.setLatitude(new BigDecimal(dispatchAngelStationBO.getFenceRangeCenterLat()));
                fromPoint.setLongitude(new BigDecimal(dispatchAngelStationBO.getFenceRangeCenterLng()));
                fromPoints.put(angel.getAngelId(), fromPoint);
            }
        }
        //预约单组装护士经纬度(全职以服务站位置为起点，兼职以接单偏好中心点为起点)
        else if (Objects.equals(context.getJdhDispatch().getDispatchType(), DispatchTypeEnum.APPOINTMENT.getType())){
            Map<Long, Long> angel2StationId = angelList.stream().collect(Collectors.toMap(DispatchAngelBO::getAngelId, DispatchAngelBO::getStationId, (o1, o2) -> o2));
            //服务站、接单偏好IDList
            List<Long> angelStationIdList = angel2StationId.values().stream().filter(Objects::nonNull).collect(Collectors.toList());
            //查询全职服务站及兼职接单偏好
            List<DispatchAngelStationBO> dispatchAngelStationBOList = Optional.ofNullable(dispatchFlowDependRpc.queryAngelStationList(DispatchQueryAngelStationParam.builder().angelStationIdList(angelStationIdList).pageNum(1).pageSize(angelStationIdList.size()).build())).orElse(new ArrayList<>());
            Map<Long, DispatchAngelStationBO> id2StationMap = dispatchAngelStationBOList.stream().collect(Collectors.toMap(DispatchAngelStationBO::getAngelStationId, dispatchAngelStationBO -> dispatchAngelStationBO, (t, t2) -> t2));

            for (Map.Entry<Long, Long> entry : angel2StationId.entrySet()) {
                if (!id2StationMap.containsKey(entry.getValue())) {
                    continue;
                }
                DispatchAngelStationBO dispatchAngelStationBO = id2StationMap.get(entry.getValue());
                if (StringUtils.isBlank(dispatchAngelStationBO.getFenceRangeCenterLat()) || StringUtils.isBlank(dispatchAngelStationBO.getFenceRangeCenterLng())) {
                    continue;
                }
                GisPointBo fromPoint = new GisPointBo();
                fromPoint.setLatitude(new BigDecimal(dispatchAngelStationBO.getFenceRangeCenterLat()));
                fromPoint.setLongitude(new BigDecimal(dispatchAngelStationBO.getFenceRangeCenterLng()));
                fromPoints.put(entry.getKey(), fromPoint);
            }
        }

        //用户上门地址和经纬度
        GisPointBo toPoint = addressRpc.getLngLatByAddress(context.getJdhDispatch().getServiceLocation().getServiceLocationDetail());

        //查询护士到用户家的距离
        Map<Long, DirectionResultBO> directionResultMap = geoCalculateService.batchRoughCalculateDistanceAndBicycleDuration(fromPoints, toPoint);

        //权重分设置
        List<DispatchAngelBO> removeList = new ArrayList<>();
        for (DispatchAngelBO angel : angelList) {
            if (Objects.isNull(angel.getJobNature())) {
                removeList.add(angel);
                continue;
            }
            DirectionResultBO directionResultBO = directionResultMap.get(angel.getAngelId());
            if (Objects.isNull(directionResultBO)) {
                removeList.add(angel);
                continue;
            }
            angel.setSortScore((Objects.equals(angel.getJobNature(), 1) ? 1000 : 0) + (100d - directionResultBO.getDuration()));
            angel.setDistance(directionResultBO.getDistance());
        }
        //权重排序，按照权重分从高到低排序
        angelList.removeAll(removeList);
        angelList.sort((o1, o2) -> {
            int compare = o2.getSortScore().compareTo(o1.getSortScore());
            if (compare == 0 && Objects.nonNull(o2.getDistance()) && Objects.nonNull(o1.getDistance())) {
                return o1.getDistance().compareTo(o2.getDistance());
            }
            return compare;
        });
        context.setSelectionAngelList(angelList);
        log.info("dispatchFactorPrioritySortService -> call, 排序逻辑 END");
        return new OutputMessage();
    }

    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    @Override
    public String getCode() {
        return DispatchFlowEnum.FACTOR_PRIORITY_SORT.getFlowCode();
    }

    @Override
    public String getName() {
        return DispatchFlowEnum.FACTOR_PRIORITY_SORT.getFlowName();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======dispatchFactorPrioritySortService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}