package com.jdh.o2oservice.core.domain.dispatch.processor.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.DispatchTypeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.context.DispatchCallbackContext;
import com.jdh.o2oservice.core.domain.dispatch.context.DispatchCompleteContext;
import com.jdh.o2oservice.core.domain.dispatch.context.TargetDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.factory.DispatchProcessorFactory;
import com.jdh.o2oservice.core.domain.dispatch.flow.DispatchFlowDelegate;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchDetail;
import com.jdh.o2oservice.core.domain.dispatch.processor.DispatchProcessor;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.rpc.NewNethpDispatchRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.NewNethpDiagOrderBusinessExtendBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.NewNethpDiagOrderPatientBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.NewNethpDiagOrderSubmitBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.NewNethpDiagDoctorReplyParam;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.NewNethpDiagOrderParam;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.NewNethpOrderTransferByDoctorParam;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.NewNethpTradeEndDiagParam;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchDetailStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName HomeCareDispatchProcessor
 * @Description 上门服务者护理模式发起派单
 * <AUTHOR>
 * @Date 2024/4/22 20:57
 **/
@Service
@Slf4j
public class HomeCareDispatchProcessor implements DispatchProcessor, MapAutowiredKey {

    /**
     * newNethpDispatchRpc
     */
    @Resource
    private NewNethpDispatchRpc newNethpDispatchRpc;

    /**
     * dispatchFlowDelegate
     */
    @Resource
    private DispatchFlowDelegate dispatchFlowDelegate;

    /**
     * dispatchRepository
     */
    @Resource
    private DispatchRepository dispatchRepository;

    /**
     * ducc
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 发起派单
     * @param context
     * @return
     */
    @Override
    public Boolean angelDispatch(AngelDispatchContext context) {
        log.info("HomeCareDispatchProcessor -> angelDispatch, 护士上门context={}", JSON.toJSONString(context));
        NewNethpDiagOrderParam param = new NewNethpDiagOrderParam();
        //场景值，即时单619，预约单620
        Map<Integer, List<String>> angelNethpBizSceneMap = duccConfig.getAngelNethpBizSceneMap();
        List<String> bizSceneList = angelNethpBizSceneMap.get(context.getSkuServiceType());
        if(CollectionUtils.isNotEmpty(bizSceneList)) {
            param.setBusinessScene(Objects.equals(context.getJdhDispatch().getDispatchType(), DispatchTypeEnum.IMMEDIATELY.getType()) ? bizSceneList.get(0) : bizSceneList.get(1));
        }else {
            param.setBusinessScene(Objects.equals(context.getJdhDispatch().getDispatchType(), DispatchTypeEnum.IMMEDIATELY.getType()) ? "619" : "620");
        }
//        if (Objects.equals(ServiceTypeNewEnum.KFS_CARE.getType(), context.getSkuServiceType())) {
//            param.setBusinessScene(Objects.equals(context.getJdhDispatch().getDispatchType(), DispatchTypeEnum.IMMEDIATELY.getType()) ? "832" : "833");
//        } else {
//            param.setBusinessScene(Objects.equals(context.getJdhDispatch().getDispatchType(), DispatchTypeEnum.IMMEDIATELY.getType()) ? "619" : "620");
//        }
        //渠道，见：com.jd.newnethp.trade.center.export.core.enums.DiagChannelEnum
        param.setChannel(2);
        //下单时间，传当前时间即可
        param.setOrderTime(new Date());
        //传4即可，表示在线支付
        param.setPayType(4);
        //sku，9056619
        param.setProductId(duccConfig.getNewNethpProductId());
        //租户，传CON1000即可
        param.setTenantType(NewNethpDispatchRpc.TENANT_TYPE);
        //提单来源，1表示京东，本期传1即可
        param.setSource(1);
        param.setUserPin(context.getJdhDispatch().getUserPin());


        //服务信息
        NewNethpDiagOrderBusinessExtendBO businessExtendBO = NewNethpDiagOrderBusinessExtendBO.builder()
                .geolocation(String.format("%s,%s", context.getJdhDispatch().getServiceInfo().getGisPoint().getLatitude(), context.getJdhDispatch().getServiceInfo().getGisPoint().getLongitude()))
                .serviceDuration(context.getJdhDispatch().getServiceInfo().getServiceDuration())
                .detailAddress(context.getJdhDispatch().getServiceLocation().getServiceLocationDetail())
                .serviceTimeType(context.getJdhDispatch().getDispatchType())
                .appointTime(NewNethpDiagOrderBusinessExtendBO.NewNethpDiagOrderAppointmentTimeBO.builder().startTime(context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()).endTime(context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()).build())
                .serviceList(context.getServiceGroupIdList())
                .build();
        param.setBusinessExtendData(JSON.toJSONString(businessExtendBO));


        //患者信息
        /*List<NewNethpDiagOrderPatientBO> patientList = context.getJdhDispatch().getServiceInfo().getPatients().stream().map(appointmentPatient -> NewNethpDiagOrderPatientBO.builder()
                .patientName(appointmentPatient.getName())
                .businessId(appointmentPatient.getPromisePatientId().toString())
                //.patientSex(appointmentPatient.getPatientGender())
                .patientId(appointmentPatient.getPatientId())
                .userPhone(appointmentPatient.getUserPhone())
                .build()).collect(Collectors.toList());*/
        param.setParamExtendData(JSON.toJSONString(NewNethpDiagOrderPatientBO.builder().businessId(context.getJdhDispatch().getDispatchId().toString()).build()));
        param.setEventCode(context.getEventCode());
        NewNethpDiagOrderSubmitBO newNethpDiagOrderSubmitBO = dispatchFlowDelegate.submitOrder(param);
        //记录外部派单号
        if (Objects.nonNull(newNethpDiagOrderSubmitBO)) {
            JdhDispatch jdhDispatch = context.getJdhDispatch();
            jdhDispatch.setOutDispatchId(Objects.nonNull(newNethpDiagOrderSubmitBO.getDiagId()) ? String.valueOf(newNethpDiagOrderSubmitBO.getDiagId()) : "");
            jdhDispatch.getServiceInfo().setDispatchExecuteRoute(newNethpDiagOrderSubmitBO.getDispatchExecuteRoute());
            jdhDispatch.getServiceInfo().setFlowId(newNethpDiagOrderSubmitBO.getFlowId());
            if (CollUtil.isNotEmpty(jdhDispatch.getAngelDetailList())) {
                // 将旧的派单明细置为无效
                jdhDispatch.getAngelDetailList().forEach(JdhDispatchDetail::invalid);
            }
            dispatchRepository.save(jdhDispatch);
        }
        return true;
    }

    /**
     * 定向派单
     *
     * @param context
     * @return
     */
    @Override
    public Boolean targetDispatch(TargetDispatchContext context) {
        if(Objects.isNull(context.getAngelId())){
            log.error("[SelfHomeTestDispatchProcessor->targetDispatch],定向派单,资源类型或者资源类型明细为空!context={}", com.jd.fastjson.JSON.toJSONString(context));
            throw new BusinessException(DispatchErrorCode.DISPATCH_TARGET_ANGEL_TYPE_ERROR);
        }
        //系统重派 订单必须处于待接诊状态，roleType = 2，targetType = 3，operator=admin，reasonType = 6
        //运营人员操作的，不可以是系统触发的 roleType = 4，targetType = 4，operator=erp，target = 外部护士ID（互医护士主数据ID），reasonType = 9
        boolean isOperation = Objects.equals(context.getRoleType(), 4);
        JdhDispatchDetail dispatchDetail = context.getJdhDispatch().getAngelDetailList().stream().filter(detail -> Objects.equals(detail.getDispatchDetailStatus(), JdhDispatchDetailStatusEnum.DISPATCH_RECEIVED.getStatus())).findFirst().get();

        dispatchFlowDelegate.targetDispatch(NewNethpOrderTransferByDoctorParam.builder()
                .diagId(StringUtils.isNotBlank(context.getJdhDispatch().getOutDispatchId()) ? Long.valueOf(context.getJdhDispatch().getOutDispatchId()):null)
                .operator(isOperation ? context.getOperator() : "admin")
                .roleType(isOperation ? 4 : 2)
                .targetType(isOperation ? 4 : 3)
                .reasonType(isOperation ? 9 : 6)
                .target(isOperation ? dispatchDetail.getOutAngelId() : null)
                .reason(isOperation ? "运营指定护士派单" : "系统发起订单转诊")
                .dispatchExecuteRoute(context.getJdhDispatch().getServiceInfo().getDispatchExecuteRoute())
                .flowTaskId(context.getJdhDispatch().getServiceInfo().getFlowId())
                .tenantType(NewNethpDispatchRpc.TENANT_TYPE)
                .build());
        return true;
    }

    /**
     * 派单接单
     *
     * @param context
     * @return
     */
    @Override
    public Boolean doctorReply(DispatchCallbackContext context) {
        List<JdhDispatchDetail> receivedList = context.getJdhDispatch().getAngelDetailList().stream().filter(dispatchDetail -> Objects.equals(dispatchDetail.getDispatchDetailStatus(), JdhDispatchDetailStatusEnum.DISPATCH_RECEIVED.getStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(receivedList)){
            log.error("[HomeCareDispatchProcessor -> doctorReply], 派单明细不存在!");
            return false;
        }
        if (CollectionUtils.isNotEmpty(duccConfig.getDispatchIdWhitelist()) && duccConfig.getDispatchIdWhitelist().contains(context.getJdhDispatch().getDispatchId())) {
            log.info("JdhDispatchDomainServiceImpl -> callBack, 命中派单白名单, 不调用互医, dispatchIdWhitelist={}", duccConfig.getDispatchIdWhitelist());
        } else {
            dispatchFlowDelegate.angelReply(NewNethpDiagDoctorReplyParam.builder()
                    .diagId(StringUtils.isNotBlank(context.getJdhDispatch().getOutDispatchId()) ? Long.valueOf(context.getJdhDispatch().getOutDispatchId()):null)
                    .doctorId(Long.valueOf(receivedList.get(0).getOutAngelId()))
                    .dispatchExecuteRoute(context.getJdhDispatch().getServiceInfo().getDispatchExecuteRoute())
                    .flowTaskId(context.getJdhDispatch().getServiceInfo().getFlowId())
                    .tenantType(NewNethpDispatchRpc.TENANT_TYPE).build());
        }
        return true;
    }

    /**
     * 派单完成
     *
     * @param context
     * @return
     */
    @Override
    public Boolean dispatchComplete(DispatchCompleteContext context) {
        JdhDispatch dispatch = context.getDispatch();
        NewNethpTradeEndDiagParam endDiagParam = NewNethpTradeEndDiagParam.builder()
                .diagId(StringUtils.isNotBlank(dispatch.getOutDispatchId()) ? Long.valueOf(dispatch.getOutDispatchId()):null)
                .diagEventSourceCode("DOCTOR")
                .diagEndCode(21)
                .diagEndDesc("完成诊断")
                .dispatchExecuteRoute(dispatch.getServiceInfo().getDispatchExecuteRoute())
                .flowTaskId(dispatch.getServiceInfo().getFlowId())
                .build();
        return dispatchFlowDelegate.dispatchComplete(endDiagParam);
    }

    /**
     * 获取map key
     * @return
     */
    @Override
    public String getMapKey() {
        //业务身份 + 服务类型
        return DispatchProcessorFactory.createRouteKey(BusinessModeEnum.ANGEL_CARE.getCode(), ServiceTypeEnum.CARE.getServiceType());
    }
}