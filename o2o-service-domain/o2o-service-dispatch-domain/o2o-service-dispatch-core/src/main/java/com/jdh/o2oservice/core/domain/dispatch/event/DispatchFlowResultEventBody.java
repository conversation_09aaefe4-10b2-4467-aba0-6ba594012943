package com.jdh.o2oservice.core.domain.dispatch.event;

import com.jdh.o2oservice.base.ducc.model.DispatchRoundConfig;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @ClassName NethpTriageEventBody
 * @Description
 * <AUTHOR>
 * @Date 2024/4/25 22:55
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatchFlowResultEventBody extends DispatchEventBaseBody {

    /**
     *
     */
    private Long flowId;

    /**
     *
     */
    private Integer businessType;

    /**
     *
     */
    private Long diagId;

    /**
     *
     */
    private String eventContent;

    /**
     *
     */
    private Date eventTime;

    /**
     *
     */
    private String eventType;

    /**
     *
     */
    private String operateMan;

    /**
     *
     */
    private String operateSource;

    /**
     *
     */
    private Long orderId;

    /**
     *
     */
    private String serviceType;

    /**
     *
     */
    private String tenantType;

    /**
     *
     */
    private String userPin;

    /**
     *
     */
    private Long dispatchId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务单ID
     */
    private Long voucherId;

    /**
     *
     */
    private DispatchFlowResultEventContent content;

    /**
     *
     */
    private boolean dispatchResult;

    /**
     *
     */
    List<DispatchAngelBO> angelList;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 计划完成时间
     */
    private Date planFinishTime;

    /**
     * 当前执行的派单轮次
     */
    private DispatchRoundConfig currentDispatchRoundConfig;
}