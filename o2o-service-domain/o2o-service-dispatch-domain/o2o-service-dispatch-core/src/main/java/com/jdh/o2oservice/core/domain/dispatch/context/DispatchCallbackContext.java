package com.jdh.o2oservice.core.domain.dispatch.context;

import com.alibaba.fastjson.annotation.JSONField;
import com.jdh.o2oservice.base.ducc.model.DispatchRoundConfig;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.event.DispatchCallbackEventBody;
import com.jdh.o2oservice.core.domain.dispatch.model.DispatchAngelPlanCharge;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchDetail;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName DispatchCallbackContext
 * @Description
 * <AUTHOR>
 * @Date 2024/4/23 00:10
 **/
@Data
public class DispatchCallbackContext extends AbstractDispatchStateAbilityContext {

    /**
     * 履约单ID
     */
    private Long dispatchId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 外部派单ID
     */
    private String outDispatchId;

    /**
     * 派单任务类型 1-即时单 2-预约单
     */
    private Integer dispatchType;

    /**
     * 派单任务状态：待派单/已派单/已接单/已取消/派单失败
     * JdhDispatchStatusEnum
     */
    private Integer dispatchStatus;

    /**
     * 事件时间
     */
    private Date eventTime;

    /**
     * 过期时间
     */
    private Date expireDate;

    /**
     * 已派单服务者信息
     */
    private List<JdhDispatchDetail> angelDetailList;

    /**
     * 护士成本价格
     */
    private List<DispatchAngelPlanCharge> angelPlanCharges;

    /**
     * 是否创建服务者工单
     */
    private Boolean createAngelWork = false;

    /**
     * 服务者履约工单ID
     */
    private Long workId;

    /**
     * 派单执行路由：flow nethp
     */
    private String dispatchExecuteRoute;

    /**
     * 事件枚举
     */
    private DispatchEventTypeEnum eventTypeEnum;

    /**
     * 首次接单
     */
    private Boolean isFirstReceived;

    /**
     * 事件
     */
    private Event publishEvent;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 计划完成时间
     */
    private Date planFinishTime;

    /**
     * 当前执行的派单轮次
     */
    private DispatchRoundConfig currentDispatchRoundConfig;

    /**
     *
     * @param snapshot
     */
    public void init(JdhDispatch snapshot) {
        if (Objects.nonNull(snapshot)){
            this.verticalCode = snapshot.getVerticalCode();
            this.serviceType = snapshot.getServiceType();
            this.snapshot = snapshot;
        }
        super.initVertical();
    }

    /**
     *
     * @return
     */
    @JSONField(serialize = false)
    public Event getDispatchEvent() {
        //派单成功事件
        if (Objects.nonNull(this.publishEvent)) {
            return this.publishEvent;
        }
        if (Objects.equals(JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus(), super.getJdhDispatch().getDispatchStatus())) {
            return EventFactory.newDefaultEvent(super.getJdhDispatch(), DispatchEventTypeEnum.DISPATCH_SUCCESS, new DispatchCallbackEventBody(super.getSnapshot(), super.getJdhDispatch()));
        }
        //派单失败事件
        else if (Objects.equals(JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus(), super.getJdhDispatch().getDispatchStatus())){
            return EventFactory.newDefaultEvent(super.getJdhDispatch(), DispatchEventTypeEnum.DISPATCH_FAIL, new DispatchCallbackEventBody(super.getSnapshot(), super.getJdhDispatch()));
        }
        //派单已接单
        else if (Objects.equals(JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus(), super.getJdhDispatch().getDispatchStatus())){
            Long angelId = CollectionUtils.isNotEmpty(angelDetailList) ? angelDetailList.get(0).getAngelId() : null;
            return EventFactory.newDefaultEvent(super.getJdhDispatch(), DispatchEventTypeEnum.DISPATCH_RECEIVED, new DispatchCallbackEventBody(super.getSnapshot(), super.getJdhDispatch(), angelId, this.isFirstReceived));
        }
        return null;
    }
}