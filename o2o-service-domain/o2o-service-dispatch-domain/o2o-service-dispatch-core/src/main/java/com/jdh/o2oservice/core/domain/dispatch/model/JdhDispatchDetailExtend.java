package com.jdh.o2oservice.core.domain.dispatch.model;

import com.jdh.o2oservice.base.ducc.model.DispatchRoundConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName JdhDispatchDetailExtend
 * @Description
 * <AUTHOR>
 * @Date 2024/4/25 00:27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class JdhDispatchDetailExtend {

    /**
     * 成本价
     */
    private DispatchAngelPlanCharge planCharge;

    /**
     * 当前执行的派单轮次
     */
    private DispatchRoundConfig currentDispatchRoundConfig;
}