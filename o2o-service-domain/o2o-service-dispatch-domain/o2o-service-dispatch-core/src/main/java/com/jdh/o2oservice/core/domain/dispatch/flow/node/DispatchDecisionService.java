package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jd.store.enums.YNEnum;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchServiceInfo;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchDecisionService
 * @Description
 * <AUTHOR>
 * @Date 2024/10/21 10:32
 **/
@Service("dispatchDecisionService")
@Slf4j
public class DispatchDecisionService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("DispatchDecisionService -> call, 派单决策 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("DispatchDecisionService -> call, 派单决策 context={}", JSON.toJSONString(context));
        List<DispatchAngelBO> angelList = context.getSelectionAngelList();
        if (CollectionUtils.isEmpty(angelList)) {
            log.info("DispatchDecisionService -> call, 派单决策 无可派护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }
        //拍了下一次性最大圈选派单的护士数量，500
        Collections.shuffle(angelList);
        angelList = angelList.stream().limit(500).collect(Collectors.toList());
        context.setSelectionAngelList(angelList);

        //设置抢单持续时间
        //-------------------------------------------
        //一天中白天的开始时间 S，结束时间 E，抢持续时间 n
        //本期 S=7:00，E=22:00，n=10min
        //S-n 至 E 发起预约：持续 n 分钟
        //22 至次日 S-n 发起预约：持续至次日 S
        //示例
        //6 点 50 至 22 点：持续 10 分钟
        //22 点至次日 6 点 50：持续至 7 点
        //-------------------------------------------
        //获取派单持续时间配置
        Map<String, Map<String, JSONObject>> dispatchDetailDurationMap = duccConfig.getDispatchDetailDurationMap();
        log.info("DispatchDecisionService -> call, 获取派单持续时间配置 dispatchDetailDurationMap={}", JSON.toJSONString(dispatchDetailDurationMap));
        Map<String, JSONObject> durationMap = dispatchDetailDurationMap.getOrDefault(context.getVerticalBusiness().getBusinessModeCode(), JSON.parseObject("{\"1\":{\"startTime\":\"07:00\",\"endTime\":\"22:00\",\"durationMinute\":10},\"2\":{\"startTime\":\"07:00\",\"endTime\":\"22:00\",\"durationMinute\":10}}", new TypeReference<Map<String, JSONObject>>() {}));
        //当前派单任务对应的持续时间配置
        JSONObject durationConfig = durationMap.get(context.getJdhDispatch().getDispatchType().toString());
        log.info("DispatchDecisionService -> call, 当前派单任务对应的持续时间配置 durationConfig={}", JSON.toJSONString(durationConfig));

        Date now = DateUtil.parseDateTime(DateUtil.now());
        String date = DateUtil.formatDate(now);
        String dayStartTimeStr = String.format("%s %s", date, durationConfig.getString("startTime"));
        String dayEndTimeStr = String.format("%s %s", date, durationConfig.getString("endTime"));
        Integer durationMinute = durationConfig.getInteger("durationMinute");
        DateTime parse = DateUtil.parse(dayStartTimeStr, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
        DateTime startTime = DateUtil.offsetMinute(parse, -durationMinute);
        Date endTime = DateUtil.parse(dayEndTimeStr, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
        log.info("DispatchDecisionService -> call, 一天中白天的开始时间 startTime={}, endTime={}", startTime, endTime);
        //当前是否是白天
        boolean daytime = DateUtil.isIn(now, startTime, endTime);
        Long redispatchTime = 0L;
        //白天派单持续时间为durationMinute
        if (daytime) {
            redispatchTime = durationMinute.longValue() * 60;
        } else {
            //其余时间派单持续时间：持续至次日startTime

            //比开始时间早,说明已经是次日了
            if (now.before(startTime)) {
                redispatchTime = startTime.getTime() - now.getTime();
            }
            //比结束时间晚，需要获取次日startTime，取间隔时间
            else if (now.after(endTime)) {
                String tomorrowDate = DateUtil.formatDate(DateUtil.offsetDay(now, 1));
                String tomorrowStartTimeStr = String.format("%s %s", tomorrowDate, durationConfig.getString("startTime"));
                DateTime tomorrowStartTime = DateUtil.parse(tomorrowStartTimeStr, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
                redispatchTime = tomorrowStartTime.getTime() - now.getTime();
            }
            //毫秒转换为秒
            redispatchTime = redispatchTime / 1000;
            context.setIsNight(YNEnum.Y.getValue());
            context.setReceiveLimitTime(parse.toString());

        }
        log.info("DispatchDecisionService -> call, redispatchTime={}", redispatchTime);
        context.setDispatchDecisionExecuteTime(now);
        context.setRedispatchTime(redispatchTime);
        attachPlanTime(context);
        log.info("DispatchDecisionService -> call, 派单决策 context={}", JSON.toJSONString(context));
        log.info("DispatchDecisionService -> call, 派单决策 END");
        return new OutputMessage();
    }

    /**
     * 设置计划出门时间和计划服务完成时间
     * @param context
     */
    private void attachPlanTime(AngelDispatchContext context) {
        //预约上门开始时间、预约上门结束时间
        LocalDateTime appointmentStartTime = context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentStartTime();
        LocalDateTime appointmentEndTime = context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentEndTime();

        //常规派单轮时间可用性配置
        Map<String, Integer> dispatchTimeBufferDuration = duccConfig.getDispatchTimeBufferDuration();
        Integer planStartBufferMinute = dispatchTimeBufferDuration.getOrDefault("planStartBufferMinute", 60);//a
        Integer planServiceTimeMinute = dispatchTimeBufferDuration.getOrDefault("planServiceTimeMinute", 60);//b
        Integer dailyTimeBufferMinute = dispatchTimeBufferDuration.getOrDefault("dailyTimeBufferMinute", 20);//d
        //当前时间
        LocalDateTime now = LocalDateTime.now();
        //预计上门时间
        Date planDoorTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(-planStartBufferMinute));
        //预计服务完成时间
        Date planServiceDoneTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(planServiceTimeMinute));

        //如果预计上门时间比当前时间还早，则预计上门时间重新计算（用户预约时间往后+）
        if (now.isAfter(appointmentStartTime.plusMinutes(-planStartBufferMinute))) {
            planDoorTime = TimeUtils.localDateTimeToDate(now);
            planServiceDoneTime = TimeUtils.localDateTimeToDate(now.plusMinutes(planStartBufferMinute).plusMinutes(planServiceTimeMinute));
        }
        context.setPlanOutTime(planDoorTime);
        context.setPlanFinishTime(planServiceDoneTime);
    }

    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    @Override
    public String getCode() {
        return DispatchFlowEnum.DISPATCH_DECISION.getFlowCode();
    }

    @Override
    public String getName() {
        return DispatchFlowEnum.DISPATCH_DECISION.getFlowName();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======DispatchDecisionService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}