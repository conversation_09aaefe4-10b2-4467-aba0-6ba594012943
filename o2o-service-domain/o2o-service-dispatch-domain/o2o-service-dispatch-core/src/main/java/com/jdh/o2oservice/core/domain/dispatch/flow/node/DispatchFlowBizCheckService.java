package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName DispatchFlowBizCheckService
 * @Description
 * <AUTHOR>
 * @Date 2024/10/21 11:22
 **/
@Service("dispatchFlowBizCheckService")
@Slf4j
public class DispatchFlowBizCheckService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    @Override
    public OutputMessage call(InputMessage inputMessage) {
        OutputMessage outputMessage = new OutputMessage();
        log.info("DispatchFlowBizCheckService -> call, 业务参数检查 START");

        log.info("DispatchFlowBizCheckService -> call, 业务参数检查 END");
        return outputMessage;
    }

    @Override
    public String getCode() {
        return DispatchFlowEnum.BIZ_CHECK.getFlowCode();
    }

    @Override
    public String getName() {
        return DispatchFlowEnum.BIZ_CHECK.getFlowName();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======DispatchFlowBizCheckService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}