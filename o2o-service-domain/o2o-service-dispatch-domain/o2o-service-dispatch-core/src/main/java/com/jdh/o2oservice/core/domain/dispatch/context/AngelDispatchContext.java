package com.jdh.o2oservice.core.domain.dispatch.context;

import com.jdh.o2oservice.base.ducc.model.DispatchRoundConfig;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.core.domain.dispatch.model.DispatchFilterConfig;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchFlowTask;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchServiceInfo;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import lombok.Data;

import java.util.*;

/**
 * @ClassName AngelDispatchContext
 * @Description
 * <AUTHOR>
 * @Date 2024/4/22 18:15
 **/
@Data
public class AngelDispatchContext extends BusinessContext {

    /**
     * 履约单ID
     */
    private Long dispatchId;

    /**
     * 服务单ID
     */
    private Long voucherId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 派单任务实体
     */
    private JdhDispatch jdhDispatch;

    /**
     * 是否创建服务者工单
     */
    private Boolean createAngelWork = false;

    /**
     * 互医服务组id
     */
    private List<String> serviceGroupIdList;

    /**
     * 服务者资源类型 1：护士 2：骑手
     * AngelTypeEnum
     */
    private Integer angelType;

    /**
     * 服务者资源明细类型 2:达达 3:闪送 4:顺丰
     * AngelDetailTypeEnum
     */
    private Map<String, Integer> angelDeliveryType;


    /**
     * 消医技能ID
     */
    private List<String> angelSkillCodeList = new ArrayList<>();

    /**
     * 选中的护士列表
     */
    private List<DispatchAngelBO> selectionAngelList;

    /**
     * 派单决策执行时间
     */
    private Date dispatchDecisionExecuteTime;

    /**
     * 派单结果有效间隔时长：秒
     */
    private Long redispatchTime;

    /**
     * 护士过滤配置
     */
    private DispatchFilterConfig filterConfig;

    /**
     * 派单流程id
     */
    private Long flowId;

    /**
     *
     */
    private JdhDispatchFlowTask dispatchFlowTask;

    /**
     * 意向派单ID
     */
    private List<Long> intendedAngelIds;

    /**
     * 选中的服务者ID
     */
    private List<Long> selectAngelIds;

    /**
     *
     */
    private String flowExecuteType;

    /**
     * 多轮派单，圈选护士，只有第一轮会圈护士
     */
    private Boolean multiDispatchSelect = false;

    /**
     *
     */
    private Integer assignType;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 计划完成时间
     */
    private Date planFinishTime;

    /**
     * <pre>
     * 服务类型
     * </pre>
     */
    private Integer skuServiceType;

    /**
     * 是否为夜间派单
     */
    private Integer isNight;
    /**
     * 夜间单接单的限制时间
     */
    private String receiveLimitTime;

    /**
     * 当前执行的派单轮次
     */
    private DispatchRoundConfig currentDispatchRoundConfig;

    /**
     * 事件编码
     */
    private String eventCode;
    /**
     *
     * @param jdhDispatch
     */
    public void init(JdhDispatch jdhDispatch) {
        if (Objects.nonNull(jdhDispatch)){
            this.verticalCode = jdhDispatch.getVerticalCode();
            this.serviceType = jdhDispatch.getServiceType();
            this.jdhDispatch = jdhDispatch;
            this.intendedAngelIds = EntityUtil.getFiledDefaultNull(jdhDispatch.getServiceInfo(), JdhDispatchServiceInfo::getIntendedAngelIds);
        }

        super.initVertical();
    }
}