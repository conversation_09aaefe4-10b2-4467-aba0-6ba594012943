package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName PrioritySortService
 * @Description
 * <AUTHOR>
 * @Date 2024/10/21 10:30
 **/
@Service("dispatchPrioritySortService")
@Slf4j
public class DispatchPrioritySortService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("PrioritySortService -> call, 排序逻辑 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("PrioritySortService -> call, 排序逻辑 context={}", JSON.toJSONString(context));
        //对圈选的护士进行过滤
        List<DispatchAngelBO> angelList = context.getSelectionAngelList();
        if (CollectionUtils.isEmpty(angelList)) {
            log.info("PrioritySortService -> call, 排序逻辑 无可派护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }
        log.info("PrioritySortService -> call, 排序逻辑 END");
        return new OutputMessage();
    }

    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    @Override
    public String getCode() {
        return DispatchFlowEnum.PRIORITY_SORT.getFlowCode();
    }

    @Override
    public String getName() {
        return DispatchFlowEnum.PRIORITY_SORT.getFlowName();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======PrioritySortService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}