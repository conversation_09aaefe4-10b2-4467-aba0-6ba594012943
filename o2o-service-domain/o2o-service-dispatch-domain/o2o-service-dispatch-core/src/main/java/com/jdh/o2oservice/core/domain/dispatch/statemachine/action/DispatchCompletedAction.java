package com.jdh.o2oservice.core.domain.dispatch.statemachine.action;

import cn.hutool.core.convert.Convert;
import com.jd.fastjson.JSON;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.AngelDispatchPriceConfig;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.dispatch.context.AbstractDispatchStateAbilityContext;
import com.jdh.o2oservice.core.domain.dispatch.context.DispatchCallbackContext;
import com.jdh.o2oservice.core.domain.dispatch.context.ReDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.DispatchAngelPlanCharge;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchDetail;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchDetailExtend;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchDetailStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import io.vitess.shaded.com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchCompletedAction
 * @Description
 * <AUTHOR>
 * @Date 2024/4/24 21:23
 **/
@Service
@Slf4j
public class DispatchCompletedAction extends AbstractDispatchAction{

    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * ducc
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     *
     * @param from
     * @param to
     * @param event
     * @param stateContext
     */
    @Override
    @LogAndAlarm
    protected void extendExecute(JdhDispatchStatusEnum from, JdhDispatchStatusEnum to, DispatchEventTypeEnum event, StateContext stateContext) {
        DispatchCallbackContext context = Convert.convert(DispatchCallbackContext.class, stateContext);
        log.info("DispatchCompletedAction -> execute, context={}", JSON.toJSONString(context));

        //处理派单任务状态
        JdhDispatch jdhDispatch = context.getJdhDispatch();
        jdhDispatch.setDispatchStatus(to.getStatus());
        //轮次加一
        jdhDispatch.dispatchRoundIncrease();
        //获取护士成本价格
        Map<String, DispatchAngelPlanCharge> id2chargeMap = new HashMap<>();
        List<DispatchAngelPlanCharge> angelPlanCharges = context.getAngelPlanCharges();
        if (CollectionUtils.isNotEmpty(angelPlanCharges)) {
            id2chargeMap = angelPlanCharges.stream().collect(Collectors.toMap(DispatchAngelPlanCharge::getAngelId, angelPlanCharge -> angelPlanCharge, (t, t2) -> t2));
        }

        //创建派单任务明细数据
        List<JdhDispatchDetail> angelDetailList = context.getAngelDetailList();
        //记录派单成功两种派单类型的次数（派单类型、抢单类型）
        JdhDispatchDetail jdhDispatchDetail = angelDetailList.stream().findFirst().get();
        if (Objects.equals(jdhDispatchDetail.getDispatchDetailType(), 1)) {
            jdhDispatch.getServiceInfo().currentAppointNumIncrease();
        } else {
            jdhDispatch.getServiceInfo().currentGrabNumIncrease();
        }
        List<JdhDispatchDetail> detailList = Lists.newArrayListWithCapacity(angelDetailList.size());
        Map<String, AngelDispatchPriceConfig> angelDispatchPriceConfigMap = duccConfig.getAngelDispatchPriceConfigMap();
        AngelDispatchPriceConfig dispatchPriceConfig = JSON.parseObject(JSON.toJSONString(angelDispatchPriceConfigMap.get("default")), AngelDispatchPriceConfig.class);
        log.info("DispatchCompletedAction -> execute, dispatchPriceConfig={}", JSON.toJSONString(dispatchPriceConfig));
        Queue<Long> queue = SpringUtil.getBean(GenerateIdFactory.class).getBatchId(angelDetailList.size());
        for (JdhDispatchDetail dispatchDetail : angelDetailList) {
            JdhDispatchDetail detail = dispatchDetail.copyInstance();
            detail.setDispatchDetailId(queue.poll());
            detail.setVerticalCode(jdhDispatch.getVerticalCode());
            detail.setServiceType(jdhDispatch.getServiceType());
            detail.setDispatchId(jdhDispatch.getDispatchId());
            detail.setPromiseId(jdhDispatch.getPromiseId());
            detail.setVoucherId(jdhDispatch.getVoucherId());
            detail.setDispatchDetailStatus(JdhDispatchDetailStatusEnum.DISPATCH_COMPLETED.getStatus());
            //将护士成本价放入扩展字段
            DispatchAngelPlanCharge planCharge = id2chargeMap.get(String.valueOf(detail.getAngelId()));
            //未查到护士的结算价，此护士过滤掉不派单
            if (Objects.isNull(planCharge)) {
                log.info("DispatchCompletedAction -> execute, 未查到护士的结算价，此护士过滤掉不派单, angelId={}", detail.getAngelId());
                continue;
            }
            //判断服务者结算价是否需要加价
            //即时单：当派单结果类型为抢单时，进行加价；预约单：第二次抢单时，进行加价
            //2024-09-09 修改派单策略，去除派单加价
            planCharge.calculateDispatchMarkupPrice(jdhDispatch.getDispatchType(), jdhDispatch.getServiceInfo(), dispatchPriceConfig);
            detail.setDetailExtend(JdhDispatchDetailExtend.builder().planCharge(planCharge).currentDispatchRoundConfig(context.getCurrentDispatchRoundConfig()).build());
            detail.setDispatchRound(jdhDispatch.getDispatchRound());
            detailList.add(detail);
        }
        jdhDispatch.setAngelDetailList(detailList);
        jdhDispatch.getServiceInfo().setCurrentDispatchTime(context.getEventTime());
        jdhDispatch.getServiceInfo().setCurrentDispatchExpireTime(context.getExpireDate());
        jdhDispatch.getServiceInfo().setPlanOutTime(context.getPlanOutTime());
        jdhDispatch.getServiceInfo().setPlanFinishTime(context.getPlanFinishTime());
        log.info("DispatchCompletedAction -> execute end, context={}", JSON.toJSONString(context));
    }
}