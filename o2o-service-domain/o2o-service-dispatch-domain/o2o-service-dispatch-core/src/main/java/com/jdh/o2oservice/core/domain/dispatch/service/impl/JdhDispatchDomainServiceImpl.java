package com.jdh.o2oservice.core.domain.dispatch.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.DispatchFlowPipelineConfig;
import com.jdh.o2oservice.base.enums.CommonDictEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.statemachine.StateMachine;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.base.util.IpUtil;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.DispatchDetailTypeEnum;
import com.jdh.o2oservice.common.enums.DispatchTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.dispatch.context.*;
import com.jdh.o2oservice.core.domain.dispatch.context.bo.DispatchPlanTime;
import com.jdh.o2oservice.core.domain.dispatch.converter.DispatchDomainConverter;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.FlowExecuteTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.event.DispatchFlowResultEventBody;
import com.jdh.o2oservice.core.domain.dispatch.event.DispatchFlowResultEventContent;
import com.jdh.o2oservice.core.domain.dispatch.factory.DispatchProcessorFactory;
import com.jdh.o2oservice.core.domain.dispatch.flow.DispatchFlowDelegate;
import com.jdh.o2oservice.core.domain.dispatch.model.*;
import com.jdh.o2oservice.core.domain.dispatch.processor.DispatchProcessor;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchFlowTaskRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchRepQuery;
import com.jdh.o2oservice.core.domain.dispatch.rpc.JdhAngelWorkRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.AngelWorkBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.QueryAngelWorkParam;
import com.jdh.o2oservice.core.domain.dispatch.service.JdhDispatchDomainService;
import com.jdh.o2oservice.core.domain.support.basic.dict.model.CommonDictionary;
import com.jdh.o2oservice.core.domain.support.basic.dict.query.CommonDictionaryQuery;
import com.jdh.o2oservice.core.domain.support.basic.dict.repository.DictRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.DispatchFlowTaskStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchDetailStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.export.dispatch.query.DispatchFlowPipelineConfigRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName JdhDispatchDomainServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/4/21 10:42
 **/
@Service
@Slf4j
public class JdhDispatchDomainServiceImpl implements JdhDispatchDomainService {

    /**
     *
     */
    private static final MessageFormat TOWN_FORMAT = new MessageFormat("{0}_{1}_{2}_{3}");
    private static final MessageFormat DISTRICT_FORMAT = new MessageFormat("{0}_{1}_{2}");
    private static final MessageFormat CITY_FORMAT = new MessageFormat("{0}_{1}");
    private static final MessageFormat PROVINCE_FORMAT = new MessageFormat("{0}");


    static {
        NumberFormat numberFormat = NumberFormat.getInstance(Locale.US);
        if (numberFormat instanceof DecimalFormat) {
            ((DecimalFormat) numberFormat).setGroupingUsed(false);
        }
        CITY_FORMAT.setFormatByArgumentIndex(0, numberFormat);
        CITY_FORMAT.setFormatByArgumentIndex(1, numberFormat);


        DISTRICT_FORMAT.setFormatByArgumentIndex(0, numberFormat);
        DISTRICT_FORMAT.setFormatByArgumentIndex(1, numberFormat);


        TOWN_FORMAT.setFormatByArgumentIndex(0, numberFormat);
        TOWN_FORMAT.setFormatByArgumentIndex(1, numberFormat);


        PROVINCE_FORMAT.setFormatByArgumentIndex(0, numberFormat);
        PROVINCE_FORMAT.setFormatByArgumentIndex(1, numberFormat);
    }

    /**
     * dispatchRepository
     */
    @Resource
    private DispatchRepository dispatchRepository;

    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * dispatchProcessorFactory
     */
    @Resource
    private DispatchProcessorFactory dispatchProcessorFactory;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 地址服务
     */
    @Resource
    private AddressRpc addressRpc;

    /**
     * 词典仓库
     */
    @Resource
    DictRepository dictRepository;

    /**
     * 派单流程
     */
    @Resource
    private DispatchFlowDelegate dispatchFlowDelegate;

    /**
     * dispatchFlowTaskRepository
     */
    @Resource
    private DispatchFlowTaskRepository dispatchFlowTaskRepository;

    /**
     * dispatchStatemachine
     */
    @Resource
    private StateMachine<JdhDispatchStatusEnum, DispatchEventTypeEnum, StateContext> dispatchStatemachine;

    @Autowired
    private JdhAngelWorkRpc jdhAngelWorkRpc;


    /**
     * 提交派单任务
     *
     * @param context
     * @return
     */
    @Override
    public Boolean submitDispatch(SubmitDispatchContext context) {
        //任务幂等校验
        List<Integer> list = Lists.newArrayList(JdhDispatchStatusEnum.DISPATCH_WAIT.getStatus(), JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus(), JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus(), JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus());
        if (Objects.nonNull(context.getSnapshot()) && list.contains(context.getSnapshot().getDispatchStatus())) {
            log.error("JdhDispatchDomainServiceImpl -> submitDispatch idempotent check return false, context={}", JSON.toJSONString(context));
            return false;
        }
        Long dispatchId = generateIdFactory.getId();

        Map<Long, DispatchSku> jdhSkuDtoMap = context.getJdhSkuDtoMap();
        //获取服务时长信息(订单所有SKU配置的服务时长累加，如果买了多件，乘以件数)
        Integer serviceDuration = jdhSkuDtoMap.values().stream().map(DispatchSku::getServiceDuration).filter(Objects::nonNull).reduce(0, Integer::sum);
        //件数
        int patientNum = CollectionUtils.isNotEmpty(context.getServiceInfo().getPatients()) ? context.getServiceInfo().getPatients().size() : 1;
        context.getServiceInfo().setServiceDuration(serviceDuration * patientNum);
        //项目信息设置耗材数据&技能数据
        Map<Long, DispatchSkuServiceItem> itemMap = jdhSkuDtoMap.entrySet().stream().flatMap(entry -> entry.getValue().getServiceItemList().stream()).collect(Collectors.toMap(DispatchSkuServiceItem::getItemId, item -> item, (t, t2) -> t2));
        Map<Long, List<JdhDispatchServiceItemSkillRel>> itemSkillMap = context.getItemSkillRelList().stream().collect(Collectors.groupingBy(JdhDispatchServiceItemSkillRel::getItemId));
        context.getServiceInfo().getPatients().stream()
                .flatMap(patient -> patient.getServiceItems().stream())
                .forEach(serviceItem -> {
                    DispatchSkuServiceItem item = itemMap.get(serviceItem.getItemId());
                    if (item != null && CollectionUtils.isNotEmpty(item.getMaterialList())) {
                        serviceItem.setMaterialPackages(DispatchDomainConverter.INSTANCE.convertJdhDispatchMaterialPackage(item.getMaterialList()));
                    }
                    if (itemSkillMap.containsKey(serviceItem.getItemId())) {
                        serviceItem.setItemSkillList(itemSkillMap.get(serviceItem.getItemId()));
                    }
                });

        GisPointBo gisPointBo = addressRpc.getLngLatByAddress(context.getServiceLocation().getServiceLocationDetail());
        context.getServiceInfo().setGisPoint(Objects.nonNull(gisPointBo) ? gisPointBo : null);
        context.getServiceInfo().setBusinessModeCode(context.getVerticalBusiness().getBusinessModeCode());

        JdhDispatch jdhDispatch = JdhDispatch.builder()
                .verticalCode(context.getVerticalCode())
                .serviceType(context.getServiceType())
                .promiseId(context.getPromiseId())
                .voucherId(context.getVoucherId())
                .dispatchId(dispatchId)
                .outDispatchId("")
                .userPin(context.getUserPin())
                .dispatchType(context.getServiceInfo().getAppointmentTime().getIsImmediately() ? DispatchTypeEnum.IMMEDIATELY.getType() : DispatchTypeEnum.APPOINTMENT.getType())
                .dispatchStatus(JdhDispatchStatusEnum.DISPATCH_WAIT.getStatus())
                .serviceLocation(context.getServiceLocation())
                .serviceInfo(context.getServiceInfo())
                .createUser(StrUtil.isEmpty(context.getUserPin()) ? StrUtil.EMPTY : context.getUserPin())
                .build();

        context.setDispatch(jdhDispatch);
        return dispatchRepository.save(context.getDispatch()) > 0;
    }

    /**
     * 发起服务者派单
     *
     * @param context
     * @return
     */
    @Override
    public Boolean angelDispatch(AngelDispatchContext context) {
        DispatchProcessor dispatchProcessor = dispatchProcessorFactory.createDispatchProcessor(context);
        if (Objects.isNull(dispatchProcessor)) {
            log.error("JdhDispatchDomainServiceImpl -> angelDispatch dispatchProcessor isNull, context={}", JSON.toJSONString(dispatchProcessorFactory));
            return false;
        }
        return dispatchProcessor.angelDispatch(context);
    }

    /**
     * @param dispatchContext
     * @return
     */
    @Override
    public DispatchFlowResultEventBody executeDispatch(AngelDispatchContext dispatchContext) {
        if (Objects.isNull(dispatchContext.getFlowId())) {
            log.info("DispatchFlowDelegate -> startDispatch, 工作流执行失败!, 没有flowId, dispatchContext={}", com.jd.fastjson.JSON.toJSONString(dispatchContext));
            return DispatchFlowResultEventBody.builder().eventType("TRIAGE_ASSIGN_TERMINATION").operateMan(IpUtil.getInet4AddressNoException())
                    .operateSource("FLOW")
                    .dispatchId(dispatchContext.getJdhDispatch().getDispatchId())
                    .serviceType(dispatchContext.getServiceType())
                    .userPin(dispatchContext.getUserPin())
                    .dispatchResult(false)
                    .build();
        }


        //查询派单任务
        JdhDispatchFlowTask dispatchFlowTask = dispatchFlowTaskRepository.find(JdhDispatchFlowTaskIdentifier.builder().flowTaskId(dispatchContext.getFlowId()).build());
        dispatchContext.setDispatchFlowTask(dispatchFlowTask);

        log.info("DispatchFlowDelegate -> executeDispatch, dispatchFlowTask={}", com.jd.fastjson.JSON.toJSONString(dispatchFlowTask));
        if (Objects.isNull(dispatchFlowTask)) {
            log.info("DispatchFlowDelegate -> startDispatch, 工作流执行失败!, 没有dispatchFlowTask, dispatchContext={}", com.jd.fastjson.JSON.toJSONString(dispatchContext));
            return DispatchFlowResultEventBody.builder().eventType("TRIAGE_ASSIGN_TERMINATION").operateMan(IpUtil.getInet4AddressNoException())
                    .operateSource("FLOW")
                    .dispatchId(dispatchContext.getJdhDispatch().getDispatchId())
                    .serviceType(dispatchContext.getServiceType())
                    .userPin(dispatchContext.getUserPin())
                    .dispatchResult(false)
                    .build();
        }

        //可执行以下逻辑的任务状态
        ArrayList<Integer> canExecuteFlowStatus = Lists.newArrayList(DispatchFlowTaskStatusEnum.DISPATCH_FLOW_WAIT.getStatus(),
                DispatchFlowTaskStatusEnum.DISPATCH_FLOW_EXECUTING.getStatus());
        if (!canExecuteFlowStatus.contains(dispatchFlowTask.getFlowStatus())) {
            log.info("DispatchFlowDelegate -> startDispatch, 工作流执行失败!, 当前派单任务状态不可执行，默认不处理 dispatchFlowTask={}", com.jd.fastjson.JSON.toJSONString(dispatchFlowTask));
            return DispatchFlowResultEventBody.builder().eventType("TRIAGE_ASSIGN_TERMINATION").operateMan(IpUtil.getInet4AddressNoException())
                    .operateSource("FLOW")
                    .dispatchId(dispatchContext.getJdhDispatch().getDispatchId())
                    .serviceType(dispatchContext.getServiceType())
                    .userPin(dispatchContext.getUserPin())
                    .dispatchResult(false)
                    .build();
        }

        return execute(dispatchContext);
    }


    /**
     *
     * @param dispatchContext
     * @return
     */
    private DispatchFlowResultEventBody execute(AngelDispatchContext dispatchContext) {
        dispatchFlowDelegate.executeDispatch(dispatchContext);
        List<DispatchAngelBO> angelList = dispatchContext.getSelectionAngelList();
        JdhDispatchFlowTask dispatchFlowTask = dispatchContext.getDispatchFlowTask();
        // 流程执行次数
        dispatchFlowTask.numIncrease();
        // 派单流程，记录执行次数
        if (Objects.equals(dispatchContext.getFlowExecuteType(), FlowExecuteTypeEnum.STANDARD_ASSIGN_ROUNDS.getType())) {
            dispatchFlowTask.getExtendInfo().currentAssignNumIncrease();
        }

        // 保持夜间单数据
        if (Objects.nonNull(dispatchContext.getIsNight())){
            dispatchRepository.saveNightDispatchInfo(dispatchContext.getIsNight(), dispatchContext.getReceiveLimitTime(), dispatchContext.getDispatchId());
        }

        //无可派单的护士
        if (CollectionUtils.isEmpty(angelList)) {
            dispatchFlowTask.setFlowStatus(DispatchFlowTaskStatusEnum.DISPATCH_FAIL.getStatus());
            dispatchFlowTaskRepository.save(dispatchFlowTask);
            return DispatchFlowResultEventBody.builder()
                    //.content(content)
                    .eventTime(dispatchContext.getDispatchDecisionExecuteTime())
                    //.eventContent(com.alibaba.fastjson.JSON.toJSONString(content))
                    .eventType("TRIAGE_ASSIGN_TERMINATION")
                    .operateMan(IpUtil.getInet4AddressNoException())
                    .operateSource("FLOW")
                    //.orderId(context.getJdhDispatch().getServiceInfo().getSourceVoucherId())
                    .dispatchId(dispatchContext.getJdhDispatch().getDispatchId())
                    .serviceType(dispatchContext.getServiceType())
                    //.tenantType()
                    .userPin(dispatchContext.getUserPin())
                    .dispatchResult(false)
                    //.angelList(angelList)
                    .flowId(dispatchFlowTask.getFlowTaskId())
                    .currentDispatchRoundConfig(dispatchContext.getCurrentDispatchRoundConfig())
                    .build();
        } else {
            dispatchFlowTask.getExtendInfo().setAngelList(DispatchDomainConverter.INSTANCE.toMinimumPersistenceBO(angelList));
            dispatchFlowTask.getExtendInfo().setPlanOutTime(dispatchContext.getPlanOutTime());
            dispatchFlowTask.getExtendInfo().setPlanFinishTime(dispatchContext.getPlanFinishTime());
            dispatchFlowTask.setFlowStatus(DispatchFlowTaskStatusEnum.DISPATCH_SUCCESS.getStatus());
            dispatchFlowTaskRepository.save(dispatchFlowTask);
            DispatchFlowResultEventContent content = DispatchFlowResultEventContent.builder()
                    .assignResult("true")
                    .assignType(Objects.equals(dispatchContext.getAssignType(), DispatchDetailTypeEnum.ASSIGN.getType()) ? DispatchDetailTypeEnum.ASSIGN.getType() : DispatchDetailTypeEnum.GRAB.getType())
                    .created(dispatchContext.getDispatchDecisionExecuteTime())
                    .redispatchTime(dispatchContext.getRedispatchTime().intValue())
                    .status(1)
                    .build();

            return DispatchFlowResultEventBody.builder()
                    .content(content)
                    .eventTime(dispatchContext.getDispatchDecisionExecuteTime())
                    .eventContent(com.alibaba.fastjson.JSON.toJSONString(content))
                    .eventType("TRIAGE_ASSIGN_RECORD_CREATE")
                    .operateMan(IpUtil.getInet4AddressNoException())
                    .operateSource("FLOW")
                    //.orderId(context.getJdhDispatch().getServiceInfo().getSourceVoucherId())
                    .dispatchId(dispatchContext.getJdhDispatch().getDispatchId())
                    .serviceType(dispatchContext.getServiceType())
                    //.tenantType()
                    .userPin(dispatchContext.getUserPin())
                    .dispatchResult(true)
                    //.angelList(list)
                    .flowId(dispatchFlowTask.getFlowTaskId())
                    .planOutTime(dispatchContext.getPlanOutTime())
                    .planFinishTime(dispatchContext.getPlanFinishTime())
                    .currentDispatchRoundConfig(dispatchContext.getCurrentDispatchRoundConfig())
                    .build();
        }
    }

    /**
     * 冻结派单
     *
     * @param context
     * @return
     */
    @Override
    public Boolean freezeDispatch(FreezeDispatchContext context) {
        DispatchRepQuery dispatchRepQuery = new DispatchRepQuery();
        dispatchRepQuery.setPromiseId(context.getPromiseId());
        JdhDispatch dispatch = dispatchRepository.findValidDispatch(dispatchRepQuery);
        context.init(dispatch);

        //1：冻结、作废受检人信息
        if (Objects.equals(1, context.getFreezeType())) {
            //FIXME 判断是否冻结全部受检人：如果是，整个派单任务冻结；如果不是，只冻结目标受检人

            //受检人map，方便提取
            Map<Long, AppointmentPatient> id2Map = new HashMap<>();
            //过滤出未冻结的受检人ID列表
            List<Long> unFreezePatientIdList = new ArrayList<>();
            for (AppointmentPatient patient : dispatch.getServiceInfo().getPatients()) {
                id2Map.put(patient.getPromisePatientId(), patient);
                if (!Objects.equals(1, patient.getFreeze())) {
                    unFreezePatientIdList.add(patient.getPromisePatientId());
                }
            }
            //处理冻结
            List<AppointmentPatient> patients = context.getCancelPatients();
            for (AppointmentPatient patient : patients) {
                AppointmentPatient targetPatient = id2Map.get(patient.getPromisePatientId());
                if (Objects.equals(0, patient.getFreeze())) {
                    targetPatient.setFreeze(1);
                    unFreezePatientIdList.remove(targetPatient.getPromisePatientId());
                }
            }
            //如果全部受检人冻结，整个派单任务冻结
            if (CollectionUtils.isEmpty(unFreezePatientIdList)) {
                dispatch.freeze();
                context.setHasAvailableService(false);
            } else {
                context.setHasAvailableService(true);
            }
        }
        //2：冻结、作废服务信息
        else if (Objects.equals(2, context.getFreezeType())) {
            //FIXME 判断是否冻结全部服务信息：如果是，整个派单任务冻结；如果不是，只冻结目标服务

            //受检人 - 服务 - 项目table，方便提取
            HashBasedTable<Long, Long, List<ServiceItem>> idTable = HashBasedTable.create();
            //过滤出未冻结的服务列表（受检人ID + 服务ID）
            Set<String> unFreezeServiceIdSet = new HashSet<>();
            dispatch.getServiceInfo().getPatients().stream().forEach(patient -> {
                Map<Long, List<ServiceItem>> serviceId2Item = patient.getServiceItems().stream().collect(Collectors.groupingBy(ServiceItem::getServiceId));
                for (Map.Entry<Long, List<ServiceItem>> entry : serviceId2Item.entrySet()) {
                    List<ServiceItem> serviceItems = idTable.get(patient.getPromisePatientId(), entry.getKey());
                    if (CollectionUtils.isEmpty(serviceItems)) {
                        serviceItems = Lists.newArrayList();
                        idTable.put(patient.getPromisePatientId(), entry.getKey(), serviceItems);
                    }
                    serviceItems.addAll(entry.getValue());
                    if (CollectionUtils.isNotEmpty(entry.getValue()) && Objects.equals(0, entry.getValue().get(0).getFreeze())) {
                        unFreezeServiceIdSet.add(String.valueOf(patient.getPromisePatientId()) + entry.getKey());
                    }
                }
            });

            //处理冻结
            List<AppointmentPatient> patients = context.getCancelPatients();
            for (AppointmentPatient patient : patients) {
                for (ServiceItem serviceItem : patient.getServiceItems()) {
                    //将正常的服务冻结
                    if (unFreezeServiceIdSet.contains(String.valueOf(patient.getPromisePatientId()) + serviceItem.getServiceId())) {
                        List<ServiceItem> serviceItems = idTable.get(patient.getPromisePatientId(), serviceItem.getServiceId());
                        serviceItems.forEach(item -> item.setFreeze(1));
                        unFreezeServiceIdSet.remove(String.valueOf(patient.getPromisePatientId()) + serviceItem.getServiceId());
                    }
                }
            }
            //如果全部服务冻结，整个派单任务冻结
            if (CollectionUtils.isEmpty(unFreezeServiceIdSet)) {
                dispatch.freeze();
                context.setHasAvailableService(false);
            } else {
                context.setHasAvailableService(true);
            }
        }
        //3：冻结、作废派单任务
        else if (Objects.equals(3, context.getFreezeType())) {
            dispatch.freeze();
            context.setHasAvailableService(false);
        }
        //派单任务已有服务者接单
        if (Objects.equals(dispatch.getDispatchStatus(), JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus())) {
            context.setFreezeAngelWork(true);
        }
        dispatchRepository.save(dispatch);
        return true;
    }

    /**
     * 派单信息回调
     *
     * @param context
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean callBack(DispatchCallbackContext context) {
        log.info("JdhDispatchDomainServiceImpl -> callBack start context:{}", JSON.toJSONString(context));
        JdhDispatch snapshot = dispatchRepository.find(JdhDispatchIdentifier.builder().dispatchId(context.getDispatchId()).build());
        log.info("JdhDispatchDomainServiceImpl -> callBack snapshot:{}", JSON.toJSONString(snapshot));
        if (Objects.isNull(snapshot)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        if (Objects.nonNull(context.getEventTypeEnum())) {
            context.init(snapshot, context.getEventTypeEnum());
        } else if (Objects.equals(context.getDispatchStatus(), JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus())) {
            context.init(snapshot, DispatchEventTypeEnum.DISPATCH_SUCCESS);
        } else if (Objects.equals(context.getDispatchStatus(), JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus())) {
            context.init(snapshot, DispatchEventTypeEnum.DISPATCH_RECEIVED);
        } else if (Objects.equals(context.getDispatchStatus(), JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus())) {
            context.init(snapshot, DispatchEventTypeEnum.DISPATCH_FAIL);
        }
        log.info("JdhDispatchDomainServiceImpl -> callBack context:{}", JSON.toJSONString(context));
        JdhDispatchStatusEnum curStatus = JdhDispatchStatusEnum.getByType(snapshot.getDispatchStatus());
        dispatchStatemachine.fireEvent(curStatus, Convert.convert(DispatchEventTypeEnum.class, context.getTriggerCommand()), context);
        // 存储
        dispatchRepository.save(context.getJdhDispatch());

        //已接单通知
        if (Objects.equals(context.getDispatchStatus(), JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus())) {
            DispatchProcessor dispatchProcessor = dispatchProcessorFactory.createDispatchProcessor(context);
            if (Objects.isNull(dispatchProcessor)) {
                log.error("JdhDispatchDomainServiceImpl -> angelDispatch dispatchProcessor isNull, context={}", JSON.toJSONString(dispatchProcessorFactory));
                return false;
            }
            return dispatchProcessor.doctorReply(context);
        }
        return Boolean.TRUE;
    }

    /**
     * 重派
     *
     * @param context
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reDispatch(ReDispatchContext context) {
        JdhDispatch snapshot = dispatchRepository.findValidDispatchWithDetail(DispatchRepQuery.builder().dispatchId(context.getDispatchId()).promiseId(context.getPromiseId()).build());
        context.init(snapshot, DispatchEventTypeEnum.DISPATCH_REDISPATCH);
        JdhDispatchStatusEnum curStatus = JdhDispatchStatusEnum.getByType(snapshot.getDispatchStatus());
        dispatchStatemachine.fireEvent(curStatus, Convert.convert(DispatchEventTypeEnum.class, context.getTriggerCommand()), context);
        //将已接单单据作废
        dispatchRepository.save(context.getJdhDispatch());
        //如果不是从服务者履约域发起重派的，必须去取消服务者工单
        if (!Objects.equals(context.getRoleType(), 5)) {
            context.setCancelAngelWork(true);
        }
        return Boolean.TRUE;
    }

    /**
     * 指定派单（定向）
     *
     * @param context
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean targetDispatch(TargetDispatchContext context) {
        JdhDispatch snapshot = dispatchRepository.findValidDispatchWithDetail(DispatchRepQuery.builder().dispatchId(context.getDispatchId()).promiseId(context.getPromiseId()).build());
        context.init(snapshot, DispatchEventTypeEnum.DISPATCH_TARGET_DISPATCH);
        JdhDispatchStatusEnum curStatus = JdhDispatchStatusEnum.getByType(snapshot.getDispatchStatus());
        dispatchStatemachine.fireEvent(curStatus, Convert.convert(DispatchEventTypeEnum.class, context.getTriggerCommand()), context);
        //1.更新派单数据
        dispatchRepository.save(context.getJdhDispatch());
        //2.调用互医指定派单
        Optional<JdhDispatchDetail> optional = context.getJdhDispatch().getAngelDetailList().stream().filter(dispatchDetail -> Objects.equals(dispatchDetail.getDispatchDetailStatus(), JdhDispatchDetailStatusEnum.DISPATCH_RECEIVED.getStatus())).findFirst();
        if (optional.isPresent()) {
            //已接单通知互医
            if (CollectionUtils.isNotEmpty(duccConfig.getDispatchIdWhitelist()) && duccConfig.getDispatchIdWhitelist().contains(context.getJdhDispatch().getDispatchId())) {
                log.info("JdhDispatchDomainServiceImpl -> callBack, 命中派单白名单, 不调用互医, dispatchIdWhitelist={}", duccConfig.getDispatchIdWhitelist());
                return Boolean.TRUE;
            }
            DispatchProcessor dispatchProcessor = dispatchProcessorFactory.createDispatchProcessor(context);
            if (Objects.isNull(dispatchProcessor)) {
                log.error("JdhDispatchDomainServiceImpl -> angelDispatch dispatchProcessor isNull, context={}", JSON.toJSONString(dispatchProcessorFactory));
                return false;
            }
            return dispatchProcessor.targetDispatch(context);
        }
        return Boolean.TRUE;
    }

    /**
     * 接单护士转单
     *
     * @param context
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean receiveTransferDispatch(TargetDispatchContext context) {
        JdhDispatch snapshot = dispatchRepository.findValidDispatchWithDetail(DispatchRepQuery.builder().dispatchId(context.getDispatchId()).promiseId(context.getPromiseId()).build());
        context.init(snapshot, DispatchEventTypeEnum.RECEIVE_TRANSFER_DISPATCH);
        JdhDispatchStatusEnum curStatus = JdhDispatchStatusEnum.getByType(snapshot.getDispatchStatus());
        dispatchStatemachine.fireEvent(curStatus, Convert.convert(DispatchEventTypeEnum.class, context.getTriggerCommand()), context);
        //1.更新派单数据
        dispatchRepository.save(context.getJdhDispatch());
        return Boolean.TRUE;
    }

    /**
     * 派单回收
     *
     * @param context
     * @return
     */
    @Override
    public Boolean recoverDispatch(RecoverDispatchContext context) {
        return expireDispatchDetail(context.getJdhDispatch());
    }

    /**
     *
     * @param jdhDispatch
     * @return
     */
    private Boolean expireDispatchDetail(JdhDispatch jdhDispatch){
        if (Objects.isNull(jdhDispatch)){
            return Boolean.FALSE;
        }
        List<JdhDispatchDetail> angelDetailList = jdhDispatch.getAngelDetailList();
        if (CollectionUtils.isEmpty(angelDetailList)) {
            return Boolean.TRUE;
        }
        for (JdhDispatchDetail dispatchDetail : angelDetailList) {
            dispatchDetail.setDispatchDetailStatus(JdhDispatchDetailStatusEnum.DISPATCH_EXPIRED.getStatus());
        }
        //1.更新派单数据
        dispatchRepository.save(jdhDispatch);
        return Boolean.TRUE;
    }

    /**
     * 作废派单
     *
     * @param context
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean invalidDispatch(InvalidDispatchContext context) {
        JdhDispatch snapshot = dispatchRepository.findValidDispatchWithDetail(DispatchRepQuery.builder().promiseId(context.getPromiseId()).dispatchId(context.getDispatchId()).build());
        context.init(snapshot, DispatchEventTypeEnum.DISPATCH_INVALID);
        JdhDispatchStatusEnum curStatus = JdhDispatchStatusEnum.getByType(snapshot.getDispatchStatus());
        dispatchStatemachine.fireEvent(curStatus, Convert.convert(DispatchEventTypeEnum.class, context.getTriggerCommand()), context);
        //保存修改
        dispatchRepository.save(context.getJdhDispatch());
        return true;
    }

    /**
     * 服务者拒绝接单
     *
     * @param context
     * @return
     */
    @Override
    public Boolean angelRefuse(AngelRefuseDispatchContext context) {
        JdhDispatchDetail dispatchDetail = dispatchRepository.findDispatchDetail(DispatchDomainConverter.INSTANCE.convertDispatchDetailRepQuery(context));
        if (Objects.isNull(dispatchDetail)) {
            return false;
        }
        if (DateUtil.date().after(dispatchDetail.getExpireDate()) || Objects.equals(dispatchDetail.getDispatchDetailStatus(), JdhDispatchDetailStatusEnum.DISPATCH_EXPIRED.getStatus())) {
            log.warn("JdhDispatchDomainServiceImpl -> angelRefuse warn, 单据已过期, dispatchDetail={}", JSON.toJSONString(dispatchDetail));
            return false;
        }
        JdhDispatch dispatch = dispatchRepository.findDispatch(DispatchRepQuery.builder().dispatchId(dispatchDetail.getDispatchId()).build());
        if (Objects.isNull(dispatch)) {
            log.warn("JdhDispatchDomainServiceImpl -> angelRefuse warn, 派单任务不存在, dispatchDetail={}", JSON.toJSONString(dispatchDetail));
            return false;
        }
        dispatchDetail.setDispatchDetailStatus(JdhDispatchDetailStatusEnum.DISPATCH_REJECT.getStatus());
        dispatchDetail.setUpdateUser(context.getUserPin());
        dispatch.setAngelDetailList(Lists.newArrayList(dispatchDetail));
        dispatch.setUpdateUser(context.getUserPin());
        dispatchRepository.save(dispatch);
        return true;
    }

    /**
     * 派单完成
     *
     * @param context
     * @return
     */
    @Override
    public Boolean dispatchComplete(DispatchCompleteContext context) {
        DispatchProcessor dispatchProcessor = dispatchProcessorFactory.createDispatchProcessor(context);
        if (Objects.isNull(dispatchProcessor)) {
            log.error("JdhDispatchDomainServiceImpl -> angelDispatch dispatchProcessor isNull, context={}", JSON.toJSONString(dispatchProcessorFactory));
            return false;
        }
        return dispatchProcessor.dispatchComplete(context);
    }


    /**
     * 解析过滤策略
     *
     * @return
     */
    @Override
    public DispatchFilterConfig parseFilterStrategy(ServiceLocation serviceLocation) {
        BaseAddressBo baseAddressBo = null;
        if (Objects.nonNull(serviceLocation.getServiceLocationProvinceId()) && Objects.nonNull(serviceLocation.getServiceLocationCityId())) {
            baseAddressBo = BaseAddressBo.builder().provinceCode(serviceLocation.getServiceLocationProvinceId())
                    .cityCode(serviceLocation.getServiceLocationCityId()).districtCode(serviceLocation.getServiceLocationDistrictId())
                    .townCode(serviceLocation.getServiceLocationTownId()).build();
        } else {
            String address = serviceLocation.getServiceLocationDetail();
            baseAddressBo = addressRpc.getJDAddressFromAddress(address);
        }

        JSONObject jsonObject = findConfig(baseAddressBo);
        log.info("JdhDispatchDomainServiceImpl -> parseFilterStrategy, jsonObject={}", JsonUtil.toJSONString(jsonObject));
        if (Objects.isNull(jsonObject)) {
            return null;
        }

        DispatchFilterConfig config = new DispatchFilterConfig();
        Object dispatchFilter = jsonObject.get("skillFilterDispatch");
        if (Objects.nonNull(dispatchFilter)) {
            config.setSkillFilterDispatch((Boolean) dispatchFilter);
        }

        Object pushFilter = jsonObject.get("skillFilterPush");
        if (Objects.nonNull(pushFilter)) {
            config.setSkillFilterPush((Boolean) pushFilter);
        }

        Object provinceCode = jsonObject.get("provinceCode");
        if (Objects.nonNull(provinceCode)) {
            config.setProvinceCode(Convert.convert(Integer.class, provinceCode));
        }

        Object cityCode = jsonObject.get("cityCode");
        if (Objects.nonNull(cityCode)) {
            config.setCityCode(Convert.convert(Integer.class, cityCode));
        }

        Object districtCode = jsonObject.get("districtCode");
        if (Objects.nonNull(districtCode)) {
            config.setDistrictCode(Convert.convert(Integer.class, districtCode));
        }

        return config;
    }

    /**
     * 查询派单关联的特定工单列表
     *
     * @param queryAngelWorkContext
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelWorkData queryValidAngelWork(QueryAngelWorkContext queryAngelWorkContext) {
        //查询派单信息
        JdhDispatch jdhDispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(queryAngelWorkContext.getDispatchId()).build());
        //查询预约时间
        Date appointmentStartTime = Date.from(jdhDispatch.getServiceInfo().getAppointmentTime().getAppointmentStartTime().atZone(ZoneId.systemDefault()).toInstant());

        QueryAngelWorkParam queryAngelWorkParam = QueryAngelWorkParam.builder()
                .angelId(queryAngelWorkContext.getAngelId())
                //查询还未完成的有效状态的工单
                .statusList(Arrays.asList(0, 1, 2, 3, 4, 5, 6))
                .serviceStartTimeBegin(DateUtils.addMinutes(appointmentStartTime, -1 * duccConfig.getWorkStartTimeOffsetMinute()))
                .serviceStartTimeEnd(DateUtils.addMinutes(appointmentStartTime, duccConfig.getWorkStartTimeOffsetMinute()))
                .build();
        AngelWorkBO angelWorkBO = jdhAngelWorkRpc.queryAngelWork(queryAngelWorkParam);
        return DispatchDomainConverter.INSTANCE.toAngelWorkData(angelWorkBO);
    }

    @Override
    public DispatchPlanTime parsePlanTime(JdhDispatchServiceInfo serviceInfo) {
        //预约上门开始时间、预约上门结束时间
        LocalDateTime appointmentStartTime = serviceInfo.getAppointmentTime().getAppointmentStartTime();
        LocalDateTime appointmentEndTime = serviceInfo.getAppointmentTime().getAppointmentEndTime();

        //常规派单轮时间可用性配置
        Map<String, Integer> dispatchTimeBufferDuration = duccConfig.getDispatchTimeBufferDuration();
        Integer planStartBufferMinute = dispatchTimeBufferDuration.getOrDefault("planStartBufferMinute", 60);//a
        Integer planServiceTimeMinute = dispatchTimeBufferDuration.getOrDefault("planServiceTimeMinute", 60);//b
        //预计上门时间
        Date planDoorTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(-planStartBufferMinute));
        //预计服务完成时间
        Date planServiceDoneTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(planServiceTimeMinute));

        //如果预计上门时间比当前时间还早，则预计上门时间重新计算（用户预约时间往后+）
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(appointmentStartTime.plusMinutes(-planStartBufferMinute))) {
            planDoorTime = TimeUtils.localDateTimeToDate(now);
            planServiceDoneTime = TimeUtils.localDateTimeToDate(now.plusMinutes(planStartBufferMinute).plusMinutes(planServiceTimeMinute));
        }

        DispatchPlanTime planTime = new DispatchPlanTime();
        planTime.setPlanOutTime(planDoorTime);
        planTime.setPlanFinishTime(planServiceDoneTime);
        return planTime;
    }

    /**
     * 查询派单流程配置详情
     * @param request
     * @return
     */
    @Override
    public DispatchFlowPipelineConfig queryDispatchFlowPipelineConfig(DispatchFlowPipelineConfigRequest request) {
        if (Objects.isNull(request) || StringUtils.isBlank(request.getFlowPipelineCode())) {
            return null;
        }
        List<CommonDictionary> list = dictRepository.queryCommonDictListFromDB(CommonDictEnum.DISPATCH_FLOW_DICT.getSceneCode(), Lists.newArrayList(request.getFlowPipelineCode()));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        CommonDictionary dictionary = list.get(0);
        DispatchFlowPipelineConfig pipelineConfig = JSON.parseObject(dictionary.getDictValue(), DispatchFlowPipelineConfig.class);
        pipelineConfig.setId(String.valueOf(dictionary.getId()));
        pipelineConfig.setCreateUser(dictionary.getCreateUser());
        pipelineConfig.setCreateTime(dictionary.getCreateTime());
        pipelineConfig.setUpdateUser(dictionary.getUpdateUser());
        pipelineConfig.setUpdateTime(dictionary.getUpdateTime());
        return pipelineConfig;
    }

    /**
     * 查询派单流程配置列表
     * @param request
     * @return
     */
    @Override
    public List<DispatchFlowPipelineConfig> queryDispatchFlowPipelineConfigList(DispatchFlowPipelineConfigRequest request) {
        List<CommonDictionary> businessDictionaryInfoList = dictRepository.queryCommonDictListFromDB(CommonDictEnum.DISPATCH_FLOW_DICT.getSceneCode(),StringUtils.isNotBlank(request.getFlowPipelineCode()) ? Lists.newArrayList(request.getFlowPipelineCode()) : null);
        if (CollectionUtils.isEmpty(businessDictionaryInfoList)) {
            return Collections.emptyList();
        }
        List<DispatchFlowPipelineConfig> result = new ArrayList<>();
        for (CommonDictionary dictionaryInfo : businessDictionaryInfoList) {
            if (Objects.isNull(dictionaryInfo) || StringUtils.isBlank(dictionaryInfo.getDictValue())) {
                continue;
            }
            DispatchFlowPipelineConfig pipelineConfig = JSON.parseObject(dictionaryInfo.getDictValue(), DispatchFlowPipelineConfig.class);
            pipelineConfig.setId(String.valueOf(dictionaryInfo.getId()));
            pipelineConfig.setCreateUser(dictionaryInfo.getCreateUser());
            pipelineConfig.setCreateTime(dictionaryInfo.getCreateTime());
            pipelineConfig.setUpdateUser(dictionaryInfo.getUpdateUser());
            pipelineConfig.setUpdateTime(dictionaryInfo.getUpdateTime());
            result.add(pipelineConfig);
        }
        return result;
    }

    /**
     * 查询派单流程配置列表-分页
     * @param request
     * @return
     */
    @Override
    public PageDto<DispatchFlowPipelineConfig> queryDispatchFlowPipelineConfigPage(DispatchFlowPipelineConfigRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }
        CommonDictionaryQuery dictionaryQuery = CommonDictionaryQuery.builder().sceneCode(CommonDictEnum.DISPATCH_FLOW_DICT.getSceneCode()).dictKey(EntityUtil.getFiledDefaultNull(request, DispatchFlowPipelineConfigRequest::getFlowPipelineCode)).build();
        dictionaryQuery.setPageNum(request.getPageNum());
        dictionaryQuery.setPageSize(request.getPageSize());
        Page<CommonDictionary> page = dictRepository.queryCommonDictPageFromDB(dictionaryQuery);
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())) {
            return null;
        }
        List<DispatchFlowPipelineConfig> result = new ArrayList<>();
        for (CommonDictionary dictionaryInfo : page.getRecords()) {
            if (Objects.isNull(dictionaryInfo) || StringUtils.isBlank(dictionaryInfo.getDictValue())) {
                continue;
            }
            DispatchFlowPipelineConfig pipelineConfig = JSON.parseObject(dictionaryInfo.getDictValue(), DispatchFlowPipelineConfig.class);
            pipelineConfig.setId(String.valueOf(dictionaryInfo.getId()));
            pipelineConfig.setCreateUser(dictionaryInfo.getCreateUser());
            pipelineConfig.setCreateTime(dictionaryInfo.getCreateTime());
            pipelineConfig.setUpdateUser(dictionaryInfo.getUpdateUser());
            pipelineConfig.setUpdateTime(dictionaryInfo.getUpdateTime());
            result.add(pipelineConfig);
        }
        PageDto<DispatchFlowPipelineConfig> pageDto = new PageDto<>();
        pageDto.setList(result);
        pageDto.setPageSize(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setTotalPage(page.getPages());
        pageDto.setTotalCount(page.getTotal());
        return pageDto;
    }

    /**
     * 保存派单流程配置
     * @param context
     * @return
     */
    @Override
    public Boolean saveDispatchFlowPipelineConfig(DispatchFlowPipelineConfigContext context) {
        if (Objects.isNull(context) || Objects.isNull(context.getSnapshot())) {
            return false;
        }
        //如果有id则更新
        if (StringUtils.isNotBlank(context.getSnapshot().getId())) {
            return dictRepository.updateCommonDictFromDB(context.convertBusinessDictionaryInfo()) > 0;
        } else {
            //新增
            context.getSnapshot().setFlowPipelineCode(SpringUtil.getBean(GenerateIdFactory.class).getIdStr());
            CommonDictionary dictionary = context.convertBusinessDictionaryInfo();
            return dictRepository.batchSaveCommonDictToDB(Lists.newArrayList(dictionary)) > 0;
        }
    }

    /**
     * 删除派单流程配置
     * @param context
     * @return
     */
    @Override
    public Boolean removeDispatchFlowPipelineConfig(DispatchFlowPipelineConfigContext context) {
        if (Objects.isNull(context.getSnapshot()) || StringUtils.isBlank(context.getSnapshot().getFlowPipelineCode())) {
            return false;
        }
        return dictRepository.deleteCommonDictFromDB(CommonDictionary.builder().sceneCode(CommonDictEnum.DISPATCH_FLOW_DICT.getSceneCode()).dictKey(context.getSnapshot().getFlowPipelineCode()).build()) > 0;
    }


    /**
     * 默认从四级地址加载过滤配置
     *
     * @param baseAddressBo
     * @return
     */
    private JSONObject findConfig(BaseAddressBo baseAddressBo) {
        if (Objects.isNull(baseAddressBo)) {
            return null;
        }
        Map<String, JSONObject> config = duccConfig.getDispatchCityFilterMap();

        if (Objects.isNull(config)) {
            return null;
        }

        // 查找四级地址配置
        String townKey = TOWN_FORMAT.format(new Object[]{baseAddressBo.getProvinceCode(), baseAddressBo.getCityCode(), baseAddressBo.getDistrictCode(), baseAddressBo.getTownCode()});
        JSONObject townConfig = config.get(townKey);
        if (Objects.nonNull(townConfig)) {
            return townConfig;
        }

        // 查找三级地址配置
        String districtKey = DISTRICT_FORMAT.format(new Object[]{baseAddressBo.getProvinceCode(), baseAddressBo.getCityCode(), baseAddressBo.getDistrictCode()});
        JSONObject districtConfig = config.get(districtKey);
        if (Objects.nonNull(districtConfig)) {
            return districtConfig;
        }

        // 查找二级地址配置
        String cityKey = CITY_FORMAT.format(new Object[]{baseAddressBo.getProvinceCode(), baseAddressBo.getCityCode()});
        JSONObject cityConfig = config.get(cityKey);
        if (Objects.nonNull(cityConfig)) {
            return cityConfig;
        }

        // 查找一级级地址配置
        JSONObject provinceConfig = config.get(String.valueOf(baseAddressBo.getProvinceCode()));
        if (Objects.nonNull(provinceConfig)) {
            return provinceConfig;
        }
        return null;
    }

}