package com.jdh.o2oservice.core.domain.dispatch.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @ClassName DispatchFlowEnum
 * @Description
 * <AUTHOR>
 * @Date 2024/10/18 22:21
 **/
@AllArgsConstructor
@Getter
public enum DispatchFlowEnum {

    /**
     * 业务校验逻辑
     */
    BIZ_CHECK("bizCheck", "业务校验逻辑组件","filter", "业务数据校验使用，目前无实质逻辑", null),

    /**
     * 圈选逻辑
     */
    CIRCLE_SELECTION("circleSelection", "三级地址圈选护士逻辑组件","circle", "根据预约上门地址的京标地址进行圈选护士", null),

    /**
     * 意向护士圈选逻辑
     */
    INTENDED_CIRCLE_SELECTION("intendedCircleSelection", "意向护士圈选逻辑组件","circle", "根据用户下单选择进行圈选护士", null),

    /**
     * 小队服务者圈选逻辑
     */
    TEAM_ANGEL_CIRCLE_SELECTION("teamAngelCircleSelection", "小队服务者圈选逻辑组件","circle", "根据用户下单商品圈选命中的小队护士", null),

    /**
     * 通用过滤逻辑，过滤接单开关和技能
     */
    FILTER("filter", "通用过滤逻辑组件","filter", "过滤护士接单开关和技能匹配情况", null),
    /**
     * 优先排序逻辑
     */
    PRIORITY_SORT("prioritySort", "通用排序逻辑组件","sort", "常规排序逻辑，目前无实质逻辑", null),

    /**
     * 通用派单决策逻辑
     */
    DISPATCH_DECISION("dispatchDecision", "通用抢单调度逻辑组件","decision", "设置最多500护士抢单，设置接单持续时间", null),
    /**
     * 意向护士派单决策逻辑
     */
    INTENDED_DISPATCH_DECISION("intendedDispatchDecision", "意向护士抢单调度逻辑组件","decision", "设置意向轮抢单接单持续时间", Lists.newArrayList(INTENDED_CIRCLE_SELECTION)),
    MULTI_SELECTION("multiSelection", "多轮加价抢单圈选护士组件","circle", "根据预约上门地址的京标地址进行多轮圈选护士", null),
    MULTI_GROUP("multiGroup", "多轮派单分组逻辑组件","filter", "多轮圈选护士分批次处理", Lists.newArrayList(MULTI_SELECTION)),

    /**
     * lbs圈选护士
     */
    LBS_STATION_ANGEL_CIRCLE_SELECTION("lbsStationAngelCircleSelectService", "lbs圈选护士组件","circle", "根据预约上门地址的LBS位置进行圈选护士", null),

    /**
     * 护士技能过滤逻辑
     */
    ANGEL_SKILL_FILTER("angelSkillFilter", "护士技能过滤组件","filter", "过滤护士技能匹配", null),

    /**
     * 护士排班过滤逻辑
     */
    ANGEL_SCHEDULE_FILTER("angelScheduleFilter", "护士排班过滤逻辑组件","filter", "过滤护士排班匹配", null),

    /**
     * 护士日程过滤逻辑
     */
    ANGEL_DAILY_WORK_FILTER("angelDailyWorkFilter", "护士日程过滤逻辑组件","filter", "过滤护士日程匹配", null),

    /**
     * 因子计算优先排序逻辑
     */
    FACTOR_PRIORITY_SORT("factorPrioritySort", "距离优先排序逻辑组件","sort", "基于护士到预约上门地址距离排序（升序）", null),

    /**
     * 派单决策逻辑-直接指定派给排名第一的护士，无需护士手动接受
     */
    DISPATCH_ASSIGN_DECISION("dispatchAssignDecision", "派单调度逻辑组件","decision", "选择1名护士派单，系统自动接单", null),

    /**
     * 派单分支条件组件
     */
    DISPATCH_COMMON_CONDITION("dispatchCommonCondition", "派单分支条件组件","condition", "派单分支条件，选择该组件必须添加至少两个子组件，并设置命中规则", null),
    ;

    private String flowCode;

    private String flowName;

    private String flowType;

    private String flowDesc;

    private List<DispatchFlowEnum> dependFlowList;

    private static final Map<String, DispatchFlowEnum> FLOW_MAP = Maps.newHashMap();
    static {
        for (DispatchFlowEnum value : values()) {
            FLOW_MAP.put(value.flowCode, value);
        }
    }

    public static DispatchFlowEnum getByCode(String code){
        if (StringUtils.isBlank(code)){
            return null;
        }
        return FLOW_MAP.get(code);

    }
}