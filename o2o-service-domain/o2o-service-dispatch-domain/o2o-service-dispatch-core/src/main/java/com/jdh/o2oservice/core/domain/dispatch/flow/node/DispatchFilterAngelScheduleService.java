package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.export.angel.dto.AngelScheduleDto;
import com.jdh.o2oservice.export.angel.dto.ScheduleCalendarDto;
import com.jdh.o2oservice.export.angel.dto.ScheduleIntervalDto;
import com.jdh.o2oservice.export.angel.query.AngelScheduleCalendarRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchFilterAngelScheduleService
 * @Description 护士排期过滤
 * <AUTHOR>
 * @Date 2025/3/10 10:08
 **/
@Service("dispatchFilterAngelScheduleService")
@Slf4j
public class DispatchFilterAngelScheduleService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     *
     */
    @Resource
    private DispatchFlowDependRpc dispatchFlowDependRpc;

    /**
     *
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 排班时间：
     * 全职：运营端排期
     * 兼职：自动接单偏好满足，且开了“自动帮我接单”开关（本期无，预留）
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("DispatchFilterAngelScheduleService -> call, 过滤逻辑 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("DispatchFilterAngelScheduleService -> call, 过滤逻辑 context={}", JSON.toJSONString(context));
        //对圈选的护士进行过滤
        List<DispatchAngelBO> angelList = context.getSelectionAngelList();
        if (CollectionUtils.isEmpty(angelList)) {
            log.info("DispatchFilterAngelScheduleService -> call, 过滤逻辑 无可派护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }
        //兼职护士过滤未开“自动帮我接单”开关---本期无，所有兼职护士全部被过滤掉
        angelList.removeIf(dispatchAngelBO -> !Objects.equals(dispatchAngelBO.getJobNature(), 1));

        //全职护士过滤排期不满足
        Map<Long, DispatchAngelBO> angelMap = angelList.stream().collect(Collectors.toMap(DispatchAngelBO::getAngelId, dispatchAngelBO -> dispatchAngelBO, (t, t2) -> t2));
        if (CollectionUtils.isEmpty(angelMap.keySet())) {
            log.info("DispatchFilterAngelScheduleService -> call, 过滤逻辑 无可派自营护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            context.setSelectionAngelList(new ArrayList<>());//护士数据清空
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }
        //预约上门开始时间、预约上门结束时间
        Date startTime = TimeUtils.localDateTimeToDate(context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentStartTime());
        Date endTime = TimeUtils.localDateTimeToDate(context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentEndTime());
        //预约日期
        DateTime dateTime = DateUtil.beginOfDay(startTime);

        AngelScheduleCalendarRequest request = new AngelScheduleCalendarRequest();
        request.setAngelIdList(Lists.newArrayList(angelMap.keySet()));
        request.setStartDate(dateTime);
        request.setScheduleStatus(1);//com.jdh.o2oservice.core.domain.angel.enums.ScheduleStatusEnum
        List<AngelScheduleDto> angelScheduleDtos = dispatchFlowDependRpc.queryAngelScheduleCalendar(request);
        if (CollectionUtils.isEmpty(angelScheduleDtos)) {
            log.info("DispatchFilterAngelScheduleService -> call, 过滤逻辑 无可选有排期的自营护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            context.setSelectionAngelList(new ArrayList<>());//护士数据清空
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }

        List<DispatchAngelBO> result = new ArrayList<>();
        for (AngelScheduleDto angelScheduleDto :angelScheduleDtos) {
            if (Objects.isNull(angelScheduleDto.getAngelId()) || CollectionUtils.isEmpty(angelScheduleDto.getScheduleCalendarDtoList())) {
                continue;
            }
            List<ScheduleCalendarDto> scheduleCalendarDtoList = angelScheduleDto.getScheduleCalendarDtoList();
            for (ScheduleCalendarDto scheduleCalendarDto : scheduleCalendarDtoList) {
                if (!Objects.equals(dateTime, scheduleCalendarDto.getDate()) || CollectionUtils.isEmpty(scheduleCalendarDto.getScheduleIntervalList())) {
                    continue;
                }
                List<ScheduleIntervalDto> scheduleIntervalList = mergeIntervals(scheduleCalendarDto.getScheduleIntervalList());
                for (ScheduleIntervalDto scheduleIntervalDto : scheduleIntervalList) {
                    if(DateUtil.isIn(startTime, scheduleIntervalDto.getStartTime(), scheduleIntervalDto.getEndTime())
                            && DateUtil.isIn(endTime, scheduleIntervalDto.getStartTime(), scheduleIntervalDto.getEndTime())
                            && angelMap.containsKey(angelScheduleDto.getAngelId())) {
                        result.add(angelMap.get(angelScheduleDto.getAngelId()));
                    }
                }

            }
        }
        context.setSelectionAngelList(result);
        log.info("DispatchFilterAngelScheduleService -> call, 过滤排期不匹配的护士后 context={}", JSON.toJSONString(context));
        log.info("DispatchFilterAngelScheduleService -> call, 过滤逻辑 END");
        return new OutputMessage();
    }

    /**
     * 合并护士排期，连续的时间段合并成一个
     * @param intervals
     * @return
     */
    public static List<ScheduleIntervalDto> mergeIntervals(List<ScheduleIntervalDto> intervals) {
        log.info("DispatchFilterAngelScheduleService -> mergeIntervals start, intervals={}", JSON.toJSONString(intervals));
        if (intervals == null || intervals.isEmpty()) {
            return new ArrayList<>();
        }
        // Sort intervals by start time
        intervals.sort(Comparator.comparing(ScheduleIntervalDto::getStartTime));

        List<ScheduleIntervalDto> merged = new ArrayList<>();
        Date currentStart = intervals.get(0).getStartTime();
        Date currentEnd = intervals.get(0).getEndTime();

        for (ScheduleIntervalDto interval : intervals) {
            if (!interval.getStartTime().after(currentEnd)) {
                // Overlapping intervals, move the end if needed
                currentEnd = new Date(Math.max(currentEnd.getTime(), interval.getEndTime().getTime()));
            } else {
                // Non-overlapping interval, add the previous one and reset bounds
                merged.add(ScheduleIntervalDto.builder().startTime(currentStart).endTime(currentEnd).build());
                currentStart = interval.getStartTime();
                currentEnd = interval.getEndTime();
            }
        }
        // Add the last interval
        merged.add(ScheduleIntervalDto.builder().startTime(currentStart).endTime(currentEnd).build());
        log.info("DispatchFilterAngelScheduleService -> mergeIntervals end, merged={}", JSON.toJSONString(merged));
        return merged;
    }

    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    @Override
    public String getCode() {
        return DispatchFlowEnum.ANGEL_SCHEDULE_FILTER.getFlowCode();
    }

    @Override
    public String getName() {
        return DispatchFlowEnum.ANGEL_SCHEDULE_FILTER.getFlowName();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======DispatchFilterAngelScheduleService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}