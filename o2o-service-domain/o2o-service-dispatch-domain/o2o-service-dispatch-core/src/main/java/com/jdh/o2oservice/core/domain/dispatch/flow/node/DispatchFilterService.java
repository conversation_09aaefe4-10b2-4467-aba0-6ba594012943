package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.DispatchFilterConfig;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelSkillRelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchAngelSkillDictParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName FilterService
 * @Description
 * <AUTHOR>
 * @Date 2024/10/21 10:14
 **/
@Service("dispatchFilterService")
@Slf4j
public class DispatchFilterService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     *
     */
    @Resource
    private DispatchFlowDependRpc dispatchFlowDependRpc;

    /**
     *
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     *
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("DispatchFilterService -> call, 过滤逻辑 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("DispatchFilterService -> call, 过滤逻辑 context={}", JSON.toJSONString(context));
        //对圈选的护士进行过滤
        List<DispatchAngelBO> angelList = context.getSelectionAngelList();
        if (CollectionUtils.isEmpty(angelList)) {
            log.info("DispatchFilterService -> call, 过滤逻辑 无可派护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }
        //1.过滤未打开接单开关的护士
        List<DispatchAngelBO> offTakeAngelList = new ArrayList<DispatchAngelBO>();
        angelList.forEach(angel -> {
            if (!Objects.equals(angel.getTakeOrderStatus(), 1)) {
                offTakeAngelList.add(angel);
            }
        });
        angelList.removeAll(offTakeAngelList);
        log.info("DispatchFilterService -> call, 对圈选的护士进行过滤后 context={}", JSON.toJSONString(context));

        //2.过滤技能不匹配的护士
        DispatchFilterConfig filterConfig = context.getFilterConfig();
        log.info("DispatchFilterService -> call, 过滤逻辑 filterConfig={}", JSON.toJSONString(filterConfig));
        //不在配置中的城市，默认为不受护士技能匹配影响（不过滤护士技能）
        if (Objects.isNull(filterConfig)) {
            log.info("DispatchFilterService -> call, 过滤逻辑 不在配置中的城市，默认为不受护士技能匹配影响, 过滤逻辑 END");
            return new OutputMessage();
        }
        Boolean skillFilterDispatch = filterConfig.getSkillFilterDispatch();
        //派单过滤技能开关为空或false，默认为不受护士技能匹配影响（不过滤护士技能）
        if (Objects.isNull(skillFilterDispatch) || Objects.equals(Boolean.FALSE, skillFilterDispatch)) {
            log.info("DispatchFilterService -> call, 过滤逻辑 派单过滤技能开关为空或false，默认为不受护士技能匹配影响, 过滤逻辑 END");
            return new OutputMessage();
        }

        //用户订单所需技能
        List<String> angelSkillCodeList = context.getAngelSkillCodeList();
        List<Long> angelIdList = angelList.stream().map(DispatchAngelBO::getAngelId).collect(Collectors.toList());
        log.info("DispatchFilterService -> call, 护士ID列表 angelIdList={}", JSON.toJSONString(angelIdList));
        List<DispatchAngelSkillRelBO> dispatchAngelSkillRelBOList = dispatchFlowDependRpc.queryAngelSkillListByAngelInfo(DispatchAngelSkillDictParam.builder().angelIdList(angelIdList).build());
        //2.1 根据护士归堆
        Map<Long, Set<String>> angelId2SkillMap = dispatchAngelSkillRelBOList.stream()
                .collect(Collectors.groupingBy(DispatchAngelSkillRelBO::getAngelId,
                        Collectors.mapping(DispatchAngelSkillRelBO::getAngelSkillCode, Collectors.toSet())));

        List<DispatchAngelBO> skillMismatchAngelList = new ArrayList<DispatchAngelBO>();
        angelList.forEach(angel -> {
            Set<String> angelSkillSet = angelId2SkillMap.get(angel.getAngelId());
            if (CollectionUtils.isEmpty(angelSkillSet) || !angelSkillSet.containsAll(angelSkillCodeList)) {
                skillMismatchAngelList.add(angel);
            }
        });
        angelList.removeAll(skillMismatchAngelList);
        log.info("DispatchFilterService -> call, 过滤技能不匹配的护士后 context={}", JSON.toJSONString(context));
        log.info("DispatchFilterService -> call, 过滤逻辑 END");
        return new OutputMessage();
    }

    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    @Override
    public String getCode() {
        return DispatchFlowEnum.FILTER.getFlowCode();
    }

    @Override
    public String getName() {
        return DispatchFlowEnum.FILTER.getFlowName();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======DispatchFilterService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}