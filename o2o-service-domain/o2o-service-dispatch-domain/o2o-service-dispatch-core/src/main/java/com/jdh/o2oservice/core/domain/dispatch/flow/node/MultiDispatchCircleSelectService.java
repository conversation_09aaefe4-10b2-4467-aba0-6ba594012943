package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.DispatchFilterConfig;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchFlowTask;
import com.jdh.o2oservice.core.domain.dispatch.model.ServiceLocation;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelStationBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchQueryAngelParam;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchQueryAngelStationParam;
import com.jdh.o2oservice.core.domain.dispatch.service.JdhDispatchDomainService;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 多轮派单圈选护士
 *
 * @ClassName MultiDispatchCircleSelectService
 * @Description
 * <AUTHOR>
 * @Date 2024/10/18 22:16
 **/
@Service("multiDispatchCircleSelectService")
@Slf4j
public class MultiDispatchCircleSelectService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     * 地址服务
     */
    @Resource
    private AddressRpc addressRpc;

    /**
     *
     */
    @Resource
    private DispatchFlowDependRpc dispatchFlowDependRpc;

    /**
     * jdhDispatchDomainService
     */
    @Resource
    private JdhDispatchDomainService jdhDispatchDomainService;
    @Resource
    private MultiDispatchAngelGroupService multiDispatchAngelGroupService;

    /**
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("MultiDispatchCircleSelectService -> call, 圈选逻辑 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("MultiDispatchCircleSelectService -> call, 圈选逻辑 context={}", JSON.toJSONString(context));
        JdhDispatchFlowTask dispatchFlowTask = context.getDispatchFlowTask();

        /**
         * 1. 如果是意向派单触发的多轮派单，已经派单3次时，本轮是多轮次派单的第一轮，需要圈选护士firstRound；
         * 2、如果是普通的派单，已经派单1次时，本轮是多轮次派单的第一轮，需要圈选护士firstRound；
         * 3、不是多轮派单的首次执行，则无法圈选护士，而是从缓存中获取。
         */
        if (dispatchFlowTask.isIntendedDispatch()) {
            if (dispatchFlowTask.getExecutionNum() == 3) {
                return firstRound(context);
            } else {
                return cacheAngel(context);
            }
        } else if (dispatchFlowTask.isStandardAssignDispatch()) {
            if (dispatchFlowTask.getExecutionNum() == 2) {
                return firstRound(context);
            } else {
                return cacheAngel(context);
            }
        } else if (dispatchFlowTask.isDefaultDispatch()) {
            if (dispatchFlowTask.getExecutionNum() == 1) {
                return firstRound(context);
            } else {
                return cacheAngel(context);
            }
        }

        log.info("MultiDispatchCircleSelectService -> call, 无法匹配流程");
        OutputMessage outputMessage = new OutputMessage();
        outputMessage.setBlock(true);
        return outputMessage;
    }

    private OutputMessage cacheAngel(AngelDispatchContext context) {
        log.info("MultiDispatchCircleSelectService -> cacheAngel start");

        List<DispatchAngelBO> angelBOS = multiDispatchAngelGroupService.findByExecutionNum(context.getDispatchFlowTask());
        context.setSelectionAngelList(angelBOS);
        log.info("MultiDispatchCircleSelectService -> cacheAngel, angels size={}", CollectionUtils.isEmpty(angelBOS) ? 0 : angelBOS.size());
        return new OutputMessage();
    }

    /**
     * 第一轮圈选护士
     *
     * @param context
     * @return
     */
    private OutputMessage firstRound(AngelDispatchContext context) {
        log.info("MultiDispatchCircleSelectService -> firstRound start");
        context.setMultiDispatchSelect(Boolean.TRUE);
        //获取用户上门地址省市区信息
        ServiceLocation serviceLocation = context.getJdhDispatch().getServiceLocation();
        //根据用户上门地址获取京标地址
        if (Objects.isNull(serviceLocation.getServiceLocationProvinceId()) || Objects.isNull(serviceLocation.getServiceLocationCityId())) {
            //上门地址省市区未存，调用地址服务根据详细地址查询省市区信息
            BaseAddressBo baseAddressBo = addressRpc.getJDAddressFromAddress(serviceLocation.getServiceLocationDetail());
            serviceLocation.setServiceLocationProvinceId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getProvinceCode() : null);
            serviceLocation.setServiceLocationCityId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getCityCode() : null);
            serviceLocation.setServiceLocationDistrictId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getDistrictCode() : null);
            serviceLocation.setServiceLocationTownId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getTownCode() : null);
        }
        //未拿到城市，默认圈选护士失败
        if (Objects.isNull(serviceLocation.getServiceLocationProvinceId()) || Objects.isNull(serviceLocation.getServiceLocationCityId())) {
            log.info("MultiDispatchCircleSelectService -> firstRound, 未拿到城市，默认圈选护士失败，流程终止 serviceLocation={}", JSON.toJSONString(serviceLocation));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);
            return outputMessage;
        }
        DispatchFilterConfig filterConfig = jdhDispatchDomainService.parseFilterStrategy(context.getJdhDispatch().getServiceLocation());
        if (Objects.isNull(filterConfig)) {
            //配置为空，默认按照城市id查询护士派单
            filterConfig = DispatchFilterConfig.builder().skillFilterDispatch(false).skillFilterPush(false).provinceCode(serviceLocation.getServiceLocationProvinceId()).cityCode(serviceLocation.getServiceLocationCityId()).build();
        }
        log.info("MultiDispatchCircleSelectService -> firstRound, filterConfig={}", JsonUtil.toJSONString(filterConfig));
        context.setFilterConfig(filterConfig);

        //查询与用户上门地址相同城市的服务站信息
        DispatchQueryAngelStationParam stationParam = DispatchQueryAngelStationParam.builder().angelStationStatus(1)
                .provinceCode(Objects.nonNull(filterConfig.getProvinceCode()) ? filterConfig.getProvinceCode().toString() : null)
                .cityCode(Objects.nonNull(filterConfig.getCityCode()) ? filterConfig.getCityCode().toString() : null)
                .districtCode(Objects.nonNull(filterConfig.getDistrictCode()) ? filterConfig.getDistrictCode().toString() : null)
                .pageNum(1)
                .pageSize(1000)
                .build();
        List<DispatchAngelStationBO> angelStationList = dispatchFlowDependRpc.queryAngelStationList(stationParam);
        if (CollectionUtils.isEmpty(angelStationList)) {
            log.info("MultiDispatchCircleSelectService -> firstRound, 未查到与用户上门地址相同城市的服务站信息，流程终止 stationParam={}", JSON.toJSONString(stationParam));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);
            return outputMessage;
        }
        //根据服务站查询关联的护士信息
        List<Long> stationIdList = angelStationList.stream().map(DispatchAngelStationBO::getAngelStationId).collect(Collectors.toList());
        log.info("MultiDispatchCircleSelectService -> firstRound, 根据服务站查询关联的护士信息 stationIdList={}", JSON.toJSONString(stationIdList));
        List<DispatchAngelBO> angelList = dispatchFlowDependRpc.queryAngelByStationId(DispatchQueryAngelParam.builder().auditProcessStatus(1).stationIdList(stationIdList).build());
        context.setSelectionAngelList(angelList);
        log.info("MultiDispatchCircleSelectService -> firstRound end");
        return new OutputMessage();
    }

    /**
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    /**
     * @return
     */
    @Override
    public String getCode() {
        return DispatchFlowEnum.MULTI_SELECTION.getFlowCode();
    }

    /**
     * @return
     */
    @Override
    public String getName() {
        return DispatchFlowEnum.MULTI_SELECTION.getFlowName();
    }

    /**
     * @param inputMessage
     */
    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======multiDispatchAngelGroupService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}