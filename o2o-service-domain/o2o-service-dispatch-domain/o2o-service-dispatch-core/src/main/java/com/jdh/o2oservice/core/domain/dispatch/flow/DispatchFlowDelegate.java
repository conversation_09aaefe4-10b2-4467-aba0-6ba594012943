package com.jdh.o2oservice.core.domain.dispatch.flow;

import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.fastjson.JSON;
import com.jd.matrix.core.biz.service.trace.ServiceTraceUtils;
import com.jd.matrix.core.domain.flow.AbstractDomainFlow;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.OrderedDomainFlow;
import com.jd.matrix.core.domain.flow.engine.DomainFlowContext;
import com.jd.matrix.core.domain.flow.engine.IDomainFlowEngine;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.DispatchFlowNodeConfig;
import com.jdh.o2oservice.base.ducc.model.DispatchFlowPipelineConfig;
import com.jdh.o2oservice.base.ducc.model.DispatchRoundConfig;
import com.jdh.o2oservice.base.enums.CommonDictEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import cn.hutool.core.bean.BeanUtil;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.ext.O2oBusinessIdentifier;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.context.DispatchCallbackContext;
import com.jdh.o2oservice.core.domain.dispatch.context.RecoverDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchExecuteRouteEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.event.DispatchFlowResultEventBody;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchFlowTask;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchFlowTaskExtend;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchFlowTaskIdentifier;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchFlowTaskRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchRepQuery;
import com.jdh.o2oservice.core.domain.dispatch.rpc.NewNethpDispatchRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.NewNethpDiagOrderPatientBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.NewNethpDiagOrderSubmitBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.*;
import com.jdh.o2oservice.core.domain.dispatch.service.JdhDispatchDomainService;
import com.jdh.o2oservice.core.domain.support.basic.dict.model.CommonDictionary;
import com.jdh.o2oservice.core.domain.support.basic.dict.repository.DictRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.DispatchFlowTaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Maps;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchFlowDelegate
 * @Description
 * <AUTHOR>
 * @Date 2024/10/21 18:30
 **/
@Component
@Slf4j
public class DispatchFlowDelegate {

    /**
     * engine
     */
    @Resource(name = "dispatchFlowEngine")
    private IDomainFlowEngine engine;

    /**
     * domainFlowNode
     */
    @Resource(name = "dispatchFlowNode")
    private AbstractDomainFlow domainFlowNode;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * jdhDispatchDomainService
     */
    @Resource
    private JdhDispatchDomainService jdhDispatchDomainService;

    /**
     * dispatchFlowTaskRepository
     */
    @Resource
    private DispatchFlowTaskRepository dispatchFlowTaskRepository;

    /**
     *
     */
    @Resource
    private DispatchRepository dispatchRepository;

    /**
     * newNethpDispatchRpc
     */
    @Resource
    private NewNethpDispatchRpc newNethpDispatchRpc;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * flowNodeFactory
     */
    @Resource
    private FlowNodeFactory flowNodeFactory;

    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * 默认派单流程
     */
    private final static DispatchFlowPipelineConfig DEFAULT_FLOW_PIPELINE_CONFIG = DispatchFlowPipelineConfig.builder().flowPipelineName("默认执行流程")
            .dispatchRoundConfigList(Lists.newArrayList(
                    DispatchRoundConfig.builder().dispatchRoundName("意向派单轮次").flowNodeConfigList(Lists.newArrayList(
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.INTENDED_CIRCLE_SELECTION.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.FILTER.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.PRIORITY_SORT.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.INTENDED_DISPATCH_DECISION.getFlowCode()).build()
                    )).build(),
                    DispatchRoundConfig.builder().dispatchRoundName("常规派单轮次").flowNodeConfigList(Lists.newArrayList(
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.LBS_STATION_ANGEL_CIRCLE_SELECTION.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.ANGEL_SKILL_FILTER.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.ANGEL_SCHEDULE_FILTER.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.ANGEL_DAILY_WORK_FILTER.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.FACTOR_PRIORITY_SORT.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.DISPATCH_ASSIGN_DECISION.getFlowCode()).build()
                    )).build(),
                    DispatchRoundConfig.builder().dispatchRoundName("小队抢单轮次").flowNodeConfigList(Lists.newArrayList(
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.TEAM_ANGEL_CIRCLE_SELECTION.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.FILTER.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.PRIORITY_SORT.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.DISPATCH_DECISION.getFlowCode()).build()
                    )).build(),
                    DispatchRoundConfig.builder().dispatchRoundName("常规抢单轮次").flowNodeConfigList(Lists.newArrayList(
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.BIZ_CHECK.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.CIRCLE_SELECTION.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.FILTER.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.PRIORITY_SORT.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.DISPATCH_DECISION.getFlowCode()).build()
                    )).build()
//                    DispatchRoundConfig.builder().dispatchRoundName("加价抢单轮次").flowNodeConfigList(Lists.newArrayList(
//                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.MULTI_SELECTION.getFlowCode()).build(),
//                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.FILTER.getFlowCode()).build(),
//                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.PRIORITY_SORT.getFlowCode()).build(),
//                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.MULTI_GROUP.getFlowCode()).build()
//                    )).build()
            ))
            .build();

    /**
     * 重新派单派单流程
     */
    private final static DispatchFlowPipelineConfig DEFAULT_REDISPATCH_FLOW_PIPELINE_CONFIG = DispatchFlowPipelineConfig.builder().flowPipelineName("默认执行流程")
            .dispatchRoundConfigList(Lists.newArrayList(
                    DispatchRoundConfig.builder().dispatchRoundName("常规抢单轮次").flowNodeConfigList(Lists.newArrayList(
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.BIZ_CHECK.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.CIRCLE_SELECTION.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.FILTER.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.PRIORITY_SORT.getFlowCode()).build(),
                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.DISPATCH_DECISION.getFlowCode()).build()
                    )).build()
//                    DispatchRoundConfig.builder().dispatchRoundName("加价抢单轮次").flowNodeConfigList(Lists.newArrayList(
//                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.MULTI_SELECTION.getFlowCode()).build(),
//                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.FILTER.getFlowCode()).build(),
//                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.PRIORITY_SORT.getFlowCode()).build(),
//                            DispatchFlowNodeConfig.builder().flowNodeCode(DispatchFlowEnum.MULTI_GROUP.getFlowCode()).build()
//                    )).build()
            ))
            .build();

    /**
     * 发送事件，异步监听事件后执行
     * @see DispatchFlowDelegate#executeDispatch(AngelDispatchContext)
     * @param param
     * @return
     */
    @LogAndAlarm
    public NewNethpDiagOrderSubmitBO submitOrder(NewNethpDiagOrderParam param){
        log.info("DispatchFlowDelegate -> submitOrder, param={}", JSON.toJSONString(param));
        //获取派单执行路由
        String route = duccConfig.getDispatchExecuteRoute();

        NewNethpDiagOrderSubmitBO result = null;
        try {
            result = newNethpDispatchRpc.submitOrder(param);
        } catch (Throwable e) {
            log.info("DispatchFlowDelegate -> submitOrder error, param={}", JSON.toJSONString(param), e);
        }
        //如果配置走互医派单，直接返回结果
        if (Objects.equals(route, DispatchExecuteRouteEnum.NETHP.getRouteCode())) {
            //如果配置走互医派单，调用必须成功，如果有报错抛出异常。
            if (Objects.isNull(result)) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_NETHP_DIAG_ORDER_SUBMIT_ERROR);
            }
            result.setDispatchExecuteRoute(DispatchExecuteRouteEnum.NETHP.getRouteCode());
            return result;
        }
        //默认走本地方法执行派单
        String paramExtendData = param.getParamExtendData();
        NewNethpDiagOrderPatientBO patientBO = JSON.parseObject(paramExtendData, NewNethpDiagOrderPatientBO.class);
        JdhDispatch dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(Long.valueOf(patientBO.getBusinessId())).build());

        //获取派单需要执行的流程和轮次数据
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(dispatch);
        stringObjectMap.put("eventCode", param.getEventCode());
        DispatchFlowPipelineConfig pipelineConfig = getDispatchFlowPipelineConfig(stringObjectMap);
        if (Objects.isNull(pipelineConfig)) {
            pipelineConfig = new DispatchFlowPipelineConfig();
            BeanUtil.copyProperties(DEFAULT_FLOW_PIPELINE_CONFIG, pipelineConfig);
        }
        pipelineConfig.getDispatchRoundConfigList().forEach(dispatchRoundConfig -> dispatchRoundConfig.setRoundExecuteStatus(0));

        JdhDispatchFlowTask dispatchFlowTask = JdhDispatchFlowTask.builder()
                .verticalCode(dispatch.getVerticalCode())
                .serviceType(dispatch.getServiceType())
                .flowTaskId(SpringUtil.getBean(GenerateIdFactory.class).getId())
                .dispatchId(dispatch.getDispatchId())
                .flowStatus(DispatchFlowTaskStatusEnum.DISPATCH_FLOW_WAIT.getStatus())
                .extendInfo(JdhDispatchFlowTaskExtend.builder().dispatchFlowPipeline(pipelineConfig).build())
                .tags(dispatch.parseTags())
                .executionNum(0)
                .build();
        dispatchFlowTaskRepository.save(dispatchFlowTask);

        //发送事件，异步监听事件后执行
        eventCoordinator.publish(EventFactory.newDefaultEvent(dispatchFlowTask, DispatchEventTypeEnum.DISPATCH_FLOW_EXECUTE, DispatchFlowResultEventBody.builder().dispatchId(dispatch.getDispatchId()).flowId(dispatchFlowTask.getFlowTaskId()).build()));
        //发送延迟消息，3分钟后校验，如果此任务没有执行，则报警
        eventCoordinator.publishDelay(EventFactory.newDelayEvent(dispatchFlowTask, DispatchEventTypeEnum.DISPATCH_FLOW_ALARM_DELAY, DispatchFlowResultEventBody.builder().flowId(dispatchFlowTask.getFlowTaskId()).dispatchId(dispatchFlowTask.getDispatchId()).build(), 30 * 60L));
        return NewNethpDiagOrderSubmitBO.builder().diagId(Objects.isNull(result) ? null : result.getDiagId()).dispatchExecuteRoute(route).flowId(dispatchFlowTask.getFlowTaskId()).build();
    }

    /**
     * 获取要执行的派单流程
     * @param param
     * @return
     */
    public DispatchFlowPipelineConfig getDispatchFlowPipelineConfig(Map<String, Object> param) {
        log.info("DispatchFlowDelegate -> getDispatchFlowPipelineConfig, param={}", JSON.toJSONString(param));
        if (MapUtils.isEmpty(param)) {
            return null;
        }
        List<CommonDictionary> commonDictionaries = SpringUtil.getBean(DictRepository.class).queryCommonDictListFromDB(CommonDictEnum.DISPATCH_FLOW_DICT.getSceneCode(), null);
        if (CollectionUtils.isEmpty(commonDictionaries)) {
            return null;
        }
        for (CommonDictionary dictionaryInfo : commonDictionaries) {
            DispatchFlowPipelineConfig pipelineConfig = com.alibaba.fastjson.JSON.parseObject(dictionaryInfo.getDictValue(), DispatchFlowPipelineConfig.class);
            if (Objects.isNull(pipelineConfig) || StringUtils.isBlank(pipelineConfig.getMatchExpression())) {
                continue;
            }
            Object execute = AviatorEvaluator.compile(pipelineConfig.getMatchExpression(), Boolean.TRUE).execute(param);
            log.info("DispatchFlowDelegate -> getDispatchFlowPipelineConfig, matchExpression={}, execute={}", pipelineConfig.getMatchExpression(), execute);
            if (Boolean.TRUE.equals(execute)) {
                log.info("DispatchFlowDelegate -> getDispatchFlowPipelineConfig, pipelineConfig={}", JSON.toJSONString(pipelineConfig));
                return pipelineConfig;
            }
        }
        log.info("DispatchFlowDelegate -> getDispatchFlowPipelineConfig, pipelineConfig=null");
        return null;
    }

    /**
     * 开始执行派单
     * @param dispatchContext
     * @return
     */
    public Boolean executeDispatch(AngelDispatchContext dispatchContext) {
        log.info("DispatchFlowDelegate -> startDispatch, 工作流开始执行!, dispatchContext={}", JSON.toJSONString(dispatchContext));
        O2oBusinessIdentifier businessIdentifier = O2oBusinessIdentifier.builder()
                .verticalCode(dispatchContext.getVerticalCode())
                .serviceType(dispatchContext.getServiceType())
                .businessMode(dispatchContext.getVerticalBusiness().getBusinessModeCode())
                .build();

        Map<String, Object> header = Maps.newHashMap("context", dispatchContext);
        DomainFlowContext context = new DomainFlowContext(businessIdentifier, dispatchContext, header);

        //新逻辑，获取派单任务执行流程
        //开关打开并且task首次执行，走新逻辑  或者执行新逻辑标记为true
        if ((Objects.equals(true, duccConfig.getDispatchNewPipelineSwitch()) && Objects.equals(0, dispatchContext.getDispatchFlowTask().getExecutionNum()))
                || (Objects.nonNull(dispatchContext.getDispatchFlowTask().getExtendInfo()) && Objects.equals(dispatchContext.getDispatchFlowTask().getExtendInfo().getExecutePipelineFlow(), true))) {
            JdhDispatchFlowTask dispatchFlowTask = dispatchContext.getDispatchFlowTask();
            if (Objects.nonNull(dispatchFlowTask.getExtendInfo()) && Objects.nonNull(dispatchFlowTask.getExtendInfo().getDispatchFlowPipeline())) {
                DispatchFlowPipelineConfig dispatchFlowPipeline = dispatchFlowTask.getExtendInfo().getDispatchFlowPipeline();
                //获取需要当前执行的轮次
                Optional<DispatchRoundConfig> first = dispatchFlowPipeline.getDispatchRoundConfigList().stream().filter(dispatchRoundConfig -> Objects.equals(dispatchRoundConfig.getRoundExecuteStatus(), 0)).findFirst();
                if (!first.isPresent()){
                    log.error("DispatchFlowDelegate -> startDispatch, 工作流已执行完成, 无法继续执行! dispatchFlowTask={}", JSON.toJSONString(dispatchFlowTask));
                    return false;
                }
                DispatchRoundConfig dispatchRoundConfig = first.get();
                dispatchContext.setCurrentDispatchRoundConfig(dispatchRoundConfig);
                OrderedDomainFlow.Builder builder = new OrderedDomainFlow.Builder()
                        .code("transferFlow")
                        .name("护士派单流程");

                List<DomainFlowNode> flowNodes = dispatchRoundConfig.getFlowNodeConfigList().stream().map(dispatchFlowNodeConfig -> flowNodeFactory.getFlowNode(dispatchFlowNodeConfig.getFlowNodeCode())).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(flowNodes)) {
                    log.error("DispatchFlowDelegate -> startDispatch, 当前轮次无节点配置, 无法继续执行! dispatchFlowTask={}", JSON.toJSONString(dispatchFlowTask));
                    return false;
                }
                flowNodes.forEach(builder::then);
                dispatchRoundConfig.setRoundExecuteStatus(2);//设置为已执行
                dispatchFlowTask.getExtendInfo().setExecutePipelineFlow(true);//设置执行新流程标记
                log.info("DispatchFlowDelegate -> startDispatch, 执行新逻辑轮次, dispatchRoundConfig={}", JSON.toJSONString(dispatchRoundConfig));
                try {
                    engine.start(builder.build(), context, (ex, rollBackFlow) -> {
                        log.error("DispatchFlowDelegate -> startDispatch, 执行新逻辑工作流执行异常,开始回滚!", ex);
                        rollBackFlow.rollback(context.getInputMessage());
                    });
                    ServiceTraceUtils.echo();
                    return true;
                } catch (Exception e) {
                    log.error("DispatchFlowDelegate -> startDispatch, 执行新逻辑派单异常!", e);
                    throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
                }
            }
            return false;
        } else {
            try {
                engine.start(domainFlowNode, context, (ex, rollBackFlow) -> {
                    log.error("DispatchFlowDelegate -> startDispatch, 工作流执行异常,开始回滚!", ex);
                    rollBackFlow.rollback(context.getInputMessage());
                });
                ServiceTraceUtils.echo();
                return true;
            } catch (Exception e) {
                log.error("DispatchFlowDelegate -> startDispatch, 派单异常!", e);
                throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
            }
        }
    }

    /**
     * 服务者接单
     * @param param
     * @return
     */
    public Boolean angelReply(NewNethpDiagDoctorReplyParam param) {
        log.info("DispatchFlowDelegate -> angelReply, 服务者接单!, param={}", JSON.toJSONString(param));
        //如果配置走互医派单，调用必须成功，如果有报错抛出异常。
        if (Objects.isNull(param.getDispatchExecuteRoute()) || Objects.equals(param.getDispatchExecuteRoute(), DispatchExecuteRouteEnum.NETHP.getRouteCode())) {
            return newNethpDispatchRpc.doctorReply(param);
        }
        JdhDispatchFlowTask dispatchFlowTask = dispatchFlowTaskRepository.find(JdhDispatchFlowTaskIdentifier.builder().flowTaskId(param.getFlowTaskId()).build());
        if (Objects.nonNull(dispatchFlowTask)) {
            dispatchFlowTask.setFlowStatus(DispatchFlowTaskStatusEnum.DISPATCH_COMPLETE.getStatus());
            dispatchFlowTaskRepository.save(dispatchFlowTask);
        }
        //调用互医接单
        NewNethpOrderTransferByDoctorParam transferByDoctorParam = NewNethpOrderTransferByDoctorParam.builder()
                .diagId(param.getDiagId())
                .operator(CommonConstant.SYSTEM)
                .roleType(NumConstant.NUM_4)
                .targetType(NumConstant.NUM_4)
                .reasonType(9)
                .target(Objects.isNull(param.getDoctorId()) ? "" : param.getDoctorId().toString())
                .reason("运营指定护士派单")
                .dispatchExecuteRoute(DispatchExecuteRouteEnum.FLOW.getRouteCode())
                .flowTaskId(param.getFlowTaskId())
                .tenantType(NewNethpDispatchRpc.TENANT_TYPE)
                .build();
        executorPoolFactory.get(ThreadPoolConfigEnum.DISPATCH_POOL).execute(() -> targetDispatchNethp(transferByDoctorParam));
        return true;
    }

    /**
     * 服务者完成-派单完成
     * @param endDiagParam
     * @return
     */
    public Boolean dispatchComplete(NewNethpTradeEndDiagParam endDiagParam){
        log.info("DispatchFlowDelegate -> dispatchComplete, 派单完成!, param={}", JSON.toJSONString(endDiagParam));
        //如果配置走互医派单，调用必须成功，如果有报错抛出异常。
        if (Objects.isNull(endDiagParam.getDispatchExecuteRoute()) || Objects.equals(endDiagParam.getDispatchExecuteRoute(), DispatchExecuteRouteEnum.NETHP.getRouteCode())) {
            return newNethpDispatchRpc.diagEnd(endDiagParam);
        }
        if (Objects.isNull(endDiagParam.getFlowTaskId())) {
            return false;
        }
        JdhDispatchFlowTask dispatchFlowTask = dispatchFlowTaskRepository.find(JdhDispatchFlowTaskIdentifier.builder().flowTaskId(endDiagParam.getFlowTaskId()).build());
        if (Objects.nonNull(dispatchFlowTask)) {
            dispatchFlowTask.setFlowStatus(DispatchFlowTaskStatusEnum.DISPATCH_COMPLETE.getStatus());
            dispatchFlowTaskRepository.save(dispatchFlowTask);
        }
        executorPoolFactory.get(ThreadPoolConfigEnum.DISPATCH_POOL).execute(() -> newNethpDispatchRpc.diagEnd(endDiagParam));
        return true;
    }

    /**
     * 取消派单
     * @param param
     * @return
     */
    public Boolean cancelDispatch(NewNethpTradeCancelDiagParam param) {
        log.info("DispatchFlowDelegate -> cancelDispatch, 取消派单!, param={}", JSON.toJSONString(param));
        //如果配置走互医派单，调用必须成功，如果有报错抛出异常。
        if (Objects.isNull(param.getDispatchExecuteRoute()) || Objects.equals(param.getDispatchExecuteRoute(), DispatchExecuteRouteEnum.NETHP.getRouteCode())) {
            return newNethpDispatchRpc.cancelDiag(param);
        }
        executorPoolFactory.get(ThreadPoolConfigEnum.DISPATCH_POOL).execute(() -> newNethpDispatchRpc.cancelDiag(param));
        return true;
    }

    /**
     * 指定派单
     * @param param
     * @return
     */
    public Boolean targetDispatch(NewNethpOrderTransferByDoctorParam param){
        log.info("DispatchFlowDelegate -> targetDispatch, 指定派单!, param={}", JSON.toJSONString(param));
        //如果配置走互医派单，调用必须成功，如果有报错抛出异常。
        if (Objects.isNull(param.getDispatchExecuteRoute()) || Objects.equals(param.getDispatchExecuteRoute(), DispatchExecuteRouteEnum.NETHP.getRouteCode())) {
            return targetDispatchNethp(param);
        }
        //派单任务更新完成
        JdhDispatchFlowTask dispatchFlowTask = dispatchFlowTaskRepository.find(JdhDispatchFlowTaskIdentifier.builder().flowTaskId(param.getFlowTaskId()).build());
        if (Objects.nonNull(dispatchFlowTask)) {
            dispatchFlowTask.setFlowStatus(DispatchFlowTaskStatusEnum.DISPATCH_COMPLETE.getStatus());
            dispatchFlowTaskRepository.save(dispatchFlowTask);
        }
        executorPoolFactory.get(ThreadPoolConfigEnum.DISPATCH_POOL).execute(() -> targetDispatchNethp(param));
        return true;
    }

    /**
     *
     * @param param
     * @return
     */
    private Boolean targetDispatchNethp(NewNethpOrderTransferByDoctorParam param) {
        log.info("DispatchFlowDelegate -> targetDispatchNethp, 互医指定派单!, param={}", JSON.toJSONString(param));
        newNethpDispatchRpc.reappointDiagBeforeReplyToDoctor(param);
        return newNethpDispatchRpc.doctorReply(NewNethpDiagDoctorReplyParam.builder()
                .diagId(param.getDiagId())
                .doctorId(Long.valueOf(param.getTarget()))
                .tenantType(NewNethpDispatchRpc.TENANT_TYPE).build());
    }

    /**
     * 接单后重派
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean transferDispatch(NewNethpOrderTransferByDoctorParam param) {
        log.info("DispatchFlowDelegate -> transferDispatch, 接单后重派!, param={}", JSON.toJSONString(param));
        //如果配置走互医派单，调用必须成功，如果有报错抛出异常。
        if (Objects.isNull(param.getDispatchExecuteRoute()) || Objects.equals(param.getDispatchExecuteRoute(), DispatchExecuteRouteEnum.NETHP.getRouteCode())) {
            return newNethpDispatchRpc.diagingTransferDiagFromInitialRound(param);
        }
        //本地方法重派
        JdhDispatch dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(param.getDispatchId()).build());

        //老派单任务作废
        JdhDispatchFlowTask oldFlowTask = dispatchFlowTaskRepository.find(JdhDispatchFlowTaskIdentifier.builder().flowTaskId(dispatch.getServiceInfo().getFlowId()).build());
        if (Objects.nonNull(oldFlowTask)) {
            oldFlowTask.setFlowStatus(DispatchFlowTaskStatusEnum.DISPATCH_INVALID.getStatus());
            dispatchFlowTaskRepository.save(oldFlowTask);
        }

        //获取派单需要执行的流程和轮次数据
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(dispatch);
        stringObjectMap.put("eventCode", param.getEventCode());
        DispatchFlowPipelineConfig pipelineConfig = getDispatchFlowPipelineConfig(stringObjectMap);
        if (Objects.isNull(pipelineConfig)) {
            pipelineConfig = new DispatchFlowPipelineConfig();
            BeanUtil.copyProperties(DEFAULT_REDISPATCH_FLOW_PIPELINE_CONFIG, pipelineConfig);
        }
        pipelineConfig.getDispatchRoundConfigList().forEach(dispatchRoundConfig -> dispatchRoundConfig.setRoundExecuteStatus(0));

        //创建新派单任务
        JdhDispatchFlowTask dispatchFlowTask = JdhDispatchFlowTask.builder()
                .verticalCode(dispatch.getVerticalCode())
                .serviceType(dispatch.getServiceType())
                .flowTaskId(SpringUtil.getBean(GenerateIdFactory.class).getId())
                .dispatchId(dispatch.getDispatchId())
                .flowStatus(DispatchFlowTaskStatusEnum.DISPATCH_FLOW_WAIT.getStatus())
                .extendInfo(JdhDispatchFlowTaskExtend.builder().currentAssignNum(oldFlowTask.getExtendInfo().getCurrentAssignNum()).dispatchFlowPipeline(pipelineConfig).build())
                .executionNum(0)
                .build();
        dispatchFlowTaskRepository.save(dispatchFlowTask);
        //新任务ID保存入派单数据中
        dispatch.getServiceInfo().setFlowId(dispatchFlowTask.getFlowTaskId());
        dispatchRepository.save(dispatch);

        //发送事件，异步监听事件后执行
        eventCoordinator.publish(EventFactory.newDefaultEvent(dispatchFlowTask, DispatchEventTypeEnum.DISPATCH_FLOW_EXECUTE, DispatchFlowResultEventBody.builder().dispatchId(dispatch.getDispatchId()).flowId(dispatchFlowTask.getFlowTaskId()).build()));
        //发送延迟消息，3分钟后校验，如果此任务没有执行，则报警
        eventCoordinator.publishDelay(EventFactory.newDelayEvent(dispatchFlowTask, DispatchEventTypeEnum.DISPATCH_FLOW_ALARM_DELAY, DispatchFlowResultEventBody.builder().flowId(dispatchFlowTask.getFlowTaskId()).dispatchId(dispatchFlowTask.getDispatchId()).build(), 30 * 60L));
        executorPoolFactory.get(ThreadPoolConfigEnum.DISPATCH_POOL).execute(() -> newNethpDispatchRpc.diagingTransferDiagFromInitialRound(param));
        return true;
    }

    /**
     *
     * @param context
     * @return
     */
    @LogAndAlarm
    public Boolean disposeCallBack(DispatchCallbackContext context) {
        JdhDispatch dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(context.getDispatchId()).build());
        log.info("DispatchFlowDelegate -> disposeCallBack, dispatch={}", JSON.toJSONString(dispatch));
        if (Objects.isNull(dispatch)) {
            log.info("DispatchFlowDelegate -> disposeCallBack, 派单数据为空不处理, context={}", JSON.toJSONString(context));
            return false;
        }
        //如果配置互医派单
        if (Objects.isNull(dispatch.getServiceInfo().getDispatchExecuteRoute()) || Objects.equals(dispatch.getServiceInfo().getDispatchExecuteRoute(), DispatchExecuteRouteEnum.NETHP.getRouteCode())) {
            //结果为互医派单数据
            if (Objects.equals(context.getDispatchExecuteRoute(), DispatchExecuteRouteEnum.NETHP.getRouteCode())) {
                return true;
            }
            //结果为本地派单数据
            else {
                return false;
            }
        }
        //如果配置本地派单
        else {
            //结果为互医派单数据
            if (Objects.equals(context.getDispatchExecuteRoute(), DispatchExecuteRouteEnum.NETHP.getRouteCode())) {
                return false;
            }
            //结果为本地派单数据
            else {
                return true;
            }
        }
    }

    /**
     * 派单回收
     * @param context
     * @return
     */
    @LogAndAlarm
    public Boolean recoverDispatch(RecoverDispatchContext context) {
        log.info("DispatchFlowDelegate -> recoverDispatch, context={}", JSON.toJSONString(context));
        JdhDispatch dispatch = context.getJdhDispatch();
        if (Objects.isNull(dispatch)) {
            log.info("DispatchFlowDelegate -> disposeCallBack, 派单数据为空不处理, context={}", JSON.toJSONString(context));
            return false;
        }
        //如果配置互医派单
        if (Objects.isNull(dispatch.getServiceInfo().getDispatchExecuteRoute()) || Objects.equals(dispatch.getServiceInfo().getDispatchExecuteRoute(), DispatchExecuteRouteEnum.NETHP.getRouteCode())) {
            return jdhDispatchDomainService.recoverDispatch(context);
        }else if(Objects.equals(dispatch.getServiceInfo().getDispatchExecuteRoute(), DispatchExecuteRouteEnum.FLOW.getRouteCode())){
            return jdhDispatchDomainService.recoverDispatch(context);
        }
        //如果配置本地派单
        return false;
    }
}