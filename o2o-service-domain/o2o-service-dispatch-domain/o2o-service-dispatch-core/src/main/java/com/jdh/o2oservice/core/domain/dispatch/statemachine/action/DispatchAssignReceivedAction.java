package com.jdh.o2oservice.core.domain.dispatch.statemachine.action;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.AngelDispatchPriceConfig;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.dispatch.context.DispatchCallbackContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.event.DispatchCallbackEventBody;
import com.jdh.o2oservice.core.domain.dispatch.model.DispatchAngelPlanCharge;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchDetail;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchDetailExtend;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchDetailStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import io.vitess.shaded.com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchAssignReceivedAction
 * @Description
 * <AUTHOR>
 * @Date 2025/3/10 20:51
 **/
@Service
@Slf4j
public class DispatchAssignReceivedAction extends AbstractDispatchAction{

    /**
     *
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     *
     * @param from
     * @param to
     * @param event
     * @param stateContext
     */
    @Override
    protected void extendExecute(JdhDispatchStatusEnum from, JdhDispatchStatusEnum to, DispatchEventTypeEnum event, StateContext stateContext) {
        DispatchCallbackContext context = Convert.convert(DispatchCallbackContext.class, stateContext);
        log.info("DispatchAssignReceivedAction -> execute, context={}", JSON.toJSONString(context));
        //处理派单任务状态
        JdhDispatch jdhDispatch = context.getJdhDispatch();
        jdhDispatch.setUpdateUser(CommonConstant.SYSTEM);
        context.setDispatchStatus(to.getStatus());
        jdhDispatch.setDispatchStatus(to.getStatus());
        //获取已接单的服务者，目前最终只会有一个人接单
        JdhDispatchDetail dispatchDetail = context.getAngelDetailList().get(0);
        //记录派单成功两种派单类型的次数（派单类型、抢单类型）
        if (Objects.equals(dispatchDetail.getDispatchDetailType(), 1)) {
            jdhDispatch.getServiceInfo().currentAppointNumIncrease();
        } else {
            jdhDispatch.getServiceInfo().currentGrabNumIncrease();
        }
        //获取护士成本价格
        Map<String, DispatchAngelPlanCharge> id2chargeMap = new HashMap<>();
        List<DispatchAngelPlanCharge> angelPlanCharges = context.getAngelPlanCharges();
        if (CollectionUtils.isNotEmpty(angelPlanCharges)) {
            id2chargeMap = angelPlanCharges.stream().collect(Collectors.toMap(DispatchAngelPlanCharge::getAngelId, angelPlanCharge -> angelPlanCharge, (t, t2) -> t2));
        }

        Map<String, AngelDispatchPriceConfig> angelDispatchPriceConfigMap = duccConfig.getAngelDispatchPriceConfigMap();
        AngelDispatchPriceConfig dispatchPriceConfig = JSON.parseObject(JSON.toJSONString(angelDispatchPriceConfigMap.get("default")), AngelDispatchPriceConfig.class);
        log.info("DispatchAssignReceivedAction -> execute, dispatchPriceConfig={}", JSON.toJSONString(dispatchPriceConfig));

        JdhDispatchDetail detail = dispatchDetail.copyInstance();
        detail.setDispatchDetailId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        detail.setVerticalCode(jdhDispatch.getVerticalCode());
        detail.setServiceType(jdhDispatch.getServiceType());
        detail.setDispatchId(jdhDispatch.getDispatchId());
        detail.setPromiseId(jdhDispatch.getPromiseId());
        detail.setVoucherId(jdhDispatch.getVoucherId());
        detail.setDispatchDetailStatus(JdhDispatchDetailStatusEnum.DISPATCH_RECEIVED.getStatus());
        //将护士成本价放入扩展字段
        DispatchAngelPlanCharge planCharge = id2chargeMap.get(String.valueOf(detail.getAngelId()));
        //未查到护士的结算价，此护士过滤掉不派单
        if (Objects.isNull(planCharge)) {
            log.info("DispatchAssignReceivedAction -> execute, 未查到护士的结算价，此护士过滤掉不派单, angelId={}", detail.getAngelId());
            return;
        }
        //判断服务者结算价是否需要加价
        //即时单：当派单结果类型为抢单时，进行加价；预约单：第二次抢单时，进行加价
        //2024-09-09 修改派单策略，去除派单加价
        planCharge.calculateDispatchMarkupPrice(jdhDispatch.getDispatchType(), jdhDispatch.getServiceInfo(), dispatchPriceConfig);
        detail.setDetailExtend(JdhDispatchDetailExtend.builder().planCharge(planCharge).currentDispatchRoundConfig(context.getCurrentDispatchRoundConfig()).build());
        detail.setDispatchRound(jdhDispatch.getDispatchRound());

        jdhDispatch.setAngelDetailList(Lists.newArrayList(detail));
        jdhDispatch.getServiceInfo().setCurrentDispatchTime(context.getEventTime());
        jdhDispatch.getServiceInfo().setCurrentDispatchExpireTime(context.getExpireDate());
        jdhDispatch.getServiceInfo().setPlanOutTime(context.getPlanOutTime());
        jdhDispatch.getServiceInfo().setPlanFinishTime(context.getPlanFinishTime());

        //如果是非骑手上门的派单任务，护士接单后要创建服务者工单（ps：骑手上门的派单任务，工单在创建派单时就已创建）
        if (!Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())  && !Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST_TRANSPORT.getCode())) {
            context.setCreateAngelWork(true);
        }

        //是否首次接单
        PromiseDto promiseDto = SpringUtil.getBean(DispatchFlowDependRpc.class).findVoucherIdByPromiseId(jdhDispatch.getPromiseId());
        if (Objects.nonNull(promiseDto) && Lists.newArrayList(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus(), JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus()).contains(promiseDto.getPromiseStatus())) {
            context.setIsFirstReceived(Boolean.TRUE);
        }

        //组装事件
        Event newDefaultEvent = EventFactory.newDefaultEvent(jdhDispatch, event, new DispatchCallbackEventBody(context.getSnapshot(), jdhDispatch, detail.getAngelId(), CommonConstant.SYSTEM, detail.getDispatchDetailId(), context.getIsFirstReceived()));
        context.setPublishEvent(newDefaultEvent);

        log.info("DispatchAssignReceivedAction -> execute end, context={}", JSON.toJSONString(context));
    }
}