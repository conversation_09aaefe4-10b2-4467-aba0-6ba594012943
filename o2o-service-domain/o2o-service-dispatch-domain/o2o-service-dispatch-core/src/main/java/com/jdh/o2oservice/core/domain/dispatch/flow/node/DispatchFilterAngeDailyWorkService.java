package com.jdh.o2oservice.core.domain.dispatch.flow.node;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.TimeIntervalIntersection;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchFlowEnum;
import com.jdh.o2oservice.core.domain.dispatch.rpc.JdhAngelWorkRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.AngelWorkBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.AngelWorkDetailBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.QueryAngelWorkParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchFilterAngeDailyWorkService
 * @Description
 * <AUTHOR>
 * @Date 2025/3/10 13:36
 **/
@Service("dispatchFilterAngeDailyWorkService")
@Slf4j
public class DispatchFilterAngeDailyWorkService extends Rollbackable implements DomainFlowNode, MapAutowiredKey {

    /**
     *
     */
    @Resource
    private JdhAngelWorkRpc jdhAngelWorkRpc;

    /**
     *
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 时间可用性
     * plan_start：预约上门开始时间 s - a（配置，将来使用路程时间 promise）
     * plan_end：预约上门开始时间 s + b（配置，将来使用服务时长 promise）
     * 考虑 buffer 时间：允许待排任务的时段和护士目标时段的前后时间有 d 分钟的重叠。
     * @param inputMessage
     * @return
     */
    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("DispatchFilterAngeDailyWorkService -> call, 过滤逻辑 START");
        AngelDispatchContext context = Convert.convert(AngelDispatchContext.class, inputMessage.getBody());
        log.info("DispatchFilterAngeDailyWorkService -> call, 过滤逻辑 context={}", JSON.toJSONString(context));
        //对圈选的护士进行过滤
        List<DispatchAngelBO> angelList = context.getSelectionAngelList();
        if (CollectionUtils.isEmpty(angelList)) {
            log.info("DispatchFilterAngeDailyWorkService -> call, 过滤逻辑 无可派护士，流程终止 context={}", JSON.toJSONString(context));
            OutputMessage outputMessage = new OutputMessage();
            outputMessage.setBlock(true);//此节点后的流程将不再执行
            return outputMessage;
        }
        Map<String, DispatchAngelBO> angelMap = angelList.stream().collect(Collectors.toMap(o -> String.valueOf(o.getAngelId()), dispatchAngelBO -> dispatchAngelBO, (t, t2) -> t2));

        //常规派单轮时间可用性配置
        Map<String, Integer> dispatchTimeBufferDuration = duccConfig.getDispatchTimeBufferDuration();
        Integer planStartBufferMinute = dispatchTimeBufferDuration.getOrDefault("planStartBufferMinute", 60);//a
        Integer planServiceTimeMinute = dispatchTimeBufferDuration.getOrDefault("planServiceTimeMinute", 60);//b
        Integer dailyTimeBufferMinute = dispatchTimeBufferDuration.getOrDefault("dailyTimeBufferMinute", 20);//d

        //预约上门开始时间、预约上门结束时间
        LocalDateTime appointmentStartTime = context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentStartTime();
        LocalDateTime appointmentEndTime = context.getJdhDispatch().getServiceInfo().getAppointmentTime().getAppointmentEndTime();

        //当前时间
        LocalDateTime now = LocalDateTime.now();
        //预计上门时间
        Date planDoorTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(-planStartBufferMinute));
        //预计服务完成时间
        Date planServiceDoneTime = TimeUtils.localDateTimeToDate(appointmentStartTime.plusMinutes(planServiceTimeMinute));

        //如果预计上门时间比当前时间还早，则预计上门时间重新计算（用户预约时间往后+）
        if (now.isAfter(appointmentStartTime.plusMinutes(-planStartBufferMinute))) {
            planDoorTime = TimeUtils.localDateTimeToDate(now);
            planServiceDoneTime = TimeUtils.localDateTimeToDate(now.plusMinutes(planStartBufferMinute).plusMinutes(planServiceTimeMinute));
        }
        context.setPlanOutTime(planDoorTime);
        context.setPlanFinishTime(planServiceDoneTime);

        //查询范围内护士的工单
        AngelWorkBO angelWorkStartBO = jdhAngelWorkRpc.queryAngelWork(QueryAngelWorkParam.builder()
                .angelIds(Lists.newArrayList(angelMap.keySet())).statusList(Lists.newArrayList(0, 1, 2, 3, 4, 5, 6)).planOutTimeStart(planDoorTime).planOutTimeEnd(planServiceDoneTime)
                .build());
        AngelWorkBO angelWorkEndBO = jdhAngelWorkRpc.queryAngelWork(QueryAngelWorkParam.builder()
                .angelIds(Lists.newArrayList(angelMap.keySet())).statusList(Lists.newArrayList(0, 1, 2, 3, 4, 5, 6)).planFinishTimeStart(planDoorTime).planFinishTimeEnd(planServiceDoneTime)
                .build());

        List<AngelWorkDetailBO> angelWorkDetailList = Optional.ofNullable(angelWorkStartBO.getAngelWorkDetailList())
                .filter(CollectionUtils::isNotEmpty)
                .orElseGet(ArrayList::new);

        // 直接使用 Optional 处理 angelWorkEndBO 的列表
        Optional.ofNullable(angelWorkEndBO.getAngelWorkDetailList())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(angelWorkDetailList::addAll);

        log.info("DispatchFilterAngeDailyWorkService -> call, 过滤逻辑 angelWorkDetailList={}", JSON.toJSONString(angelWorkDetailList));
        //有护士派单时间被工单预占，需要过滤
        List<DispatchAngelBO> removeAngelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(angelWorkDetailList)) {
            //相同的工单按workId去重
            angelWorkDetailList = angelWorkDetailList.stream()
                    .collect(Collectors.toMap(
                            AngelWorkDetailBO::getWorkId, // 使用workId作为键
                            detail -> detail,             // 保留整个对象作为值
                            (existing, replacement) -> existing)) // 如果有重复，保留现有对象
                    .values().stream().collect(Collectors.toList());
            log.info("DispatchFilterAngeDailyWorkService -> call, 过滤逻辑 去重后的 angelWorkDetailList={}", JSON.toJSONString(angelWorkDetailList));


            //按照护士进行工单分组
            Map<String, List<AngelWorkDetailBO>> angelId2WorkMap = angelWorkDetailList.stream().collect(Collectors.groupingBy(AngelWorkDetailBO::getAngelId));
            //计算护士已预占的时间，判断是否可派当前订单
            for (Map.Entry<String, List<AngelWorkDetailBO>> entry : angelId2WorkMap.entrySet()) {
                String angelId = entry.getKey();
                List<AngelWorkDetailBO> angelWorkDetails = entry.getValue();
                if (CollectionUtils.isEmpty(angelWorkDetails)) {
                    continue;
                }
                Long intersectionTime = 0L;
                for (AngelWorkDetailBO workDetailBO : angelWorkDetails) {
                    intersectionTime += TimeIntervalIntersection.calculateIntersectionMinutes(planDoorTime, planServiceDoneTime, workDetailBO.getPlanOutTime(), workDetailBO.getPlanFinishTime());
                }
                log.info("DispatchFilterAngeDailyWorkService -> call, 过滤逻辑 护士ID={}, 日程已预占时间={}", angelId, intersectionTime);
                if (intersectionTime.compareTo(dailyTimeBufferMinute.longValue()) > 0 && angelMap.containsKey(angelId)) {
                    removeAngelList.add(angelMap.get(angelId));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(removeAngelList)) {
            angelList.removeAll(removeAngelList);
        }
        log.info("DispatchFilterAngeDailyWorkService -> call, 过滤日程不匹配的护士后 context={}", JSON.toJSONString(context));
        log.info("DispatchFilterAngeDailyWorkService -> call, 过滤逻辑 END");
        return new OutputMessage();
    }

    @Override
    public OutputMessage enhanceCall(InputMessage inputMessage) {
        return DomainFlowNode.super.enhanceCall(inputMessage);
    }

    @Override
    public String getCode() {
        return DispatchFlowEnum.ANGEL_DAILY_WORK_FILTER.getFlowCode();
    }

    @Override
    public String getName() {
        return DispatchFlowEnum.ANGEL_DAILY_WORK_FILTER.getFlowName();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======DispatchFilterAngeDailyWorkService rollback biz=======");
    }

    @Override
    public String getMapKey() {
        return this.getCode();
    }
}