package com.jdh.o2oservice.core.domain.dispatch.rpc.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName NewNethpOrderTransferByDoctorParam
 * @Description
 * <AUTHOR>
 * @Date 2024/4/25 21:49
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewNethpOrderTransferByDoctorParam {

    private Long diagId;
    private Long orderId;
    private String operator;
    private Integer roleType;
    private Integer targetType;
    private String target;
    private Integer reasonType;
    private String reason;
    private String tenantType;

    /**
     * 派单执行路由：flow nethp
     */
    private String dispatchExecuteRoute;

    /**
     * 派单任务Id
     */
    private Long dispatchId;

    /**
     *
     */
    private Long flowTaskId;

    /**
     * 事件code
     */
    private String eventCode;
}