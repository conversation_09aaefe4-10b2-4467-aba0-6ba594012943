package com.jdh.o2oservice.core.domain.dispatch.rpc.param;

import com.jd.medicine.b2c.base.export.domain.NhpClientInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * @ClassName NewNethpDiagOrderParam
 * @Description
 * <AUTHOR>
 * @Date 2024/4/25 16:59
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewNethpDiagOrderParam {

    /**
     * 用户的京东pin
     */
    private String userPin;

    /**
     * 场景值
     */
    private String businessScene;

    /**
     * 租户
     */
    private String tenantType;

    private int channel;
    private int source;
    private int payType;
    private Long patientId;
    private String diseaseDesc;
    private String diseasePic;
    private Long productId;
    private String paramExtendData;
    private String businessExtendData;
    private Long venderId;

    private String jdv;

    private String beneficialId;
    private Map gwParams;
    private Integer needRx;
    private Long doctorId;
    private NhpClientInfo nhpClientInfo;
    private String picInfo;
    private Integer abTestStatus;
    private Long orderId;
    private Date orderTime;
    /**
     * 事件编码
     */
    private String eventCode;
}