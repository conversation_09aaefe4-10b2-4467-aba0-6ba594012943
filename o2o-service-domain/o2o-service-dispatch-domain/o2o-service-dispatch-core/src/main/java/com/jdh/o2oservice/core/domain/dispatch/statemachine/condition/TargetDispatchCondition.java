package com.jdh.o2oservice.core.domain.dispatch.statemachine.condition;

import cn.hutool.core.convert.Convert;
import com.jdh.o2oservice.application.promise.PromiseExtApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.dispatch.context.TargetDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.enums.RiskStrategyEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.AppointmentPatient;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchServiceInfo;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchServiceItemSkillRel;
import com.jdh.o2oservice.core.domain.dispatch.model.ServiceItem;
import com.jdh.o2oservice.core.domain.dispatch.rpc.RiskQueryServiceRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelSkillRelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchRiskStrategyParam;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName TargetDispatchCondition
 * @Description
 * <AUTHOR>
 * @Date 2024/4/26 15:29
 **/
@Service
@Slf4j
public class TargetDispatchCondition extends AbstractDispatchCondition {

    @Resource
    private RiskQueryServiceRpc riskQueryServiceRpc;
    @Resource
    private PromiseExtApplication promiseExtApplication;

    @Override
    protected boolean extendSatisfied(StateContext context) {
        TargetDispatchContext targetDispatchContext = Convert.convert(TargetDispatchContext.class, context);
        //派单任务冻结状态，无法绑定条码
        if (Objects.equals(targetDispatchContext.getJdhDispatch().getFreeze(), 1)) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
        }
        //骑手上门无法指定派单
        if (Objects.equals(targetDispatchContext.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
        }

        //判断护士技能与服务项目所需技能项目
        if (!CommonConstant.SYSTEM.equals(targetDispatchContext.getOperator())) {
            checkAngelSkillMatchItemSkill(targetDispatchContext);
        }

        // 命中风控策略
        if (hitRisk(targetDispatchContext)) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_CHECK_RISK_FAIL);
        }
        return true;
    }

    private void checkAngelSkillMatchItemSkill(TargetDispatchContext targetDispatchContext) {
        //护士技能
        List<DispatchAngelSkillRelBO> angelSkillRelList = targetDispatchContext.getTargetAngelSkillRelList();
        Set<String> angelSkillCodeSet = angelSkillRelList.stream().map(DispatchAngelSkillRelBO::getAngelSkillCode).collect(Collectors.toSet());

        //项目技能
        Set<String> itemSkillCodeSet = new HashSet<>();

        JdhDispatchServiceInfo serviceInfo = targetDispatchContext.getServiceInfo();
        List<AppointmentPatient> patients = serviceInfo.getPatients();
        patients.forEach(patient -> {
            List<ServiceItem> serviceItems = patient.getServiceItems();
            serviceItems.forEach(serviceItem -> {
                Set<String> itemSkillCodeList = Optional.ofNullable(serviceItem.getItemSkillList()).orElseGet(Collections::emptyList).stream().map(JdhDispatchServiceItemSkillRel::getAngelSkillCode).collect(Collectors.toSet());
                itemSkillCodeSet.addAll(itemSkillCodeList);
            });
        });

        //判断项目技能集合，是否是护士技能集合的子集
        if (CollectionUtils.isNotEmpty(itemSkillCodeSet) && !angelSkillCodeSet.containsAll(itemSkillCodeSet)) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_ANGEL_SKILL_NOT_MATCH_ITEM_SKILL);
        }
    }

    /**
     *  指定派单时候，需要进行风控判断。基于操作人的角色进行判断
     *  （1）运营派单或者“system”系统派单，调风控接口，记录日志，但是不卡流程；
     *  （2）站长操作派单，调风控接口，风控不通过卡流程，根据操作人是否包含.ext判断是否为站长。
     */
    private Boolean hitRisk(TargetDispatchContext targetDispatchContext) {

        PromiseDto promiseDto = promiseExtApplication.findVoucherIdByPromiseId(targetDispatchContext.getPromiseId());
        DomainAppointmentTime appointmentTime = new DomainAppointmentTime();
        appointmentTime.setAppointmentStartTime(TimeUtils.dateToLocalDateTime(promiseDto.getAppointmentTime().getAppointmentStartTime()));
        appointmentTime.setAppointmentEndTime(TimeUtils.dateToLocalDateTime(promiseDto.getAppointmentTime().getAppointmentEndTime()));

        DispatchRiskStrategyParam param = DispatchRiskStrategyParam.builder()
                .userPin(promiseDto.getUserPin())
                .verticalCode(promiseDto.getVerticalCode())
                .provinceCode(promiseDto.getStore().getProvinceCode())
                .cityCode(promiseDto.getStore().getCityCode())
                .districtCode(promiseDto.getStore().getDistrictCode())
                .townCode(promiseDto.getStore().getTownCode())
                .addressDetail(promiseDto.getStore().getStoreAddr())
                .appointmentTime(appointmentTime)
                .angelId(targetDispatchContext.getAngelId())
                .angelPhone(targetDispatchContext.getAngelPhone())
                .angelPin(targetDispatchContext.getAngelPin())
                .aggregateId(targetDispatchContext.getPromiseId())
                .operator(targetDispatchContext.getOperator())
                .build();
        RiskStrategyEnum riskStrategy = riskQueryServiceRpc.queryStrategy(param);

        String operator = targetDispatchContext.getOperator();
        // 命中风控，且是站长操作
//        return Objects.equals(riskStrategy, RiskStrategyEnum.RISK) && operator.startsWith("ext.");
        return Objects.equals(riskStrategy, RiskStrategyEnum.RISK) && operator.startsWith("zhangyuchen29");
    }
}