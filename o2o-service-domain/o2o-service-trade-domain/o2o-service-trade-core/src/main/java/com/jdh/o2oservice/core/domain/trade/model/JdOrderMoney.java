package com.jdh.o2oservice.core.domain.trade.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName:JdOrderMoney
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2023/12/27 16:09
 * @Vserion: 1.0
 **/
@Data
public class JdOrderMoney implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 订单item编号
     */
    private Long orderItemId;

    /**
     * 商品sku
     */
    private Long skuId;

    /**
     * 金额类型，礼品卡，京豆，白条免息。。
     */
    private Integer moneyType;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 有效标志 0 无效 1 有效
     */
    private Integer yn;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 排序
     */
    private Integer sort;
}
