package com.jdh.o2oservice.core.domain.trade.vo;

import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.patient.model.Patient;
import lombok.Data;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 订单预约信息值对象
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Data
public class OrderAppointmentInfoValueObject {

    /**
     * 预约时间
     */
    private OrderAppointmentTimeValueObject appointmentTime;

    /**
     * 地址信息
     */
    private AddressInfoValueObject addressInfo;

    /**
     * 患者信息
     */
    private List<Patient> patients;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag = false;

    /**
     * 预约时间矫正
     * @param payTime
     */
    public void correctionTime(Date payTime){

        if (Objects.isNull(appointmentTime)){
            return;
        }
        // isImmediately为空或者预约单不需要矫正
        if (Objects.isNull(appointmentTime.getIsImmediately()) || !appointmentTime.getIsImmediately()){
            return;
        }
        payTime = Objects.nonNull(payTime) ? payTime : new Date();

        /**
         * 即时单的预约开始时间和结束时间取值规则：时段以半小时为单位，往后追加N小时，再取整作为结束时间。其中N根据服务类型的差异不同而不同
         * 矫正时间逻辑：计算选择的预约开始时间到预约结束时间的长度L1，按照30分钟的刻度进行划分L1/30作为支付时间需要追加的时间，payTime + N * 30;
         * 最后再往后取整到半点
         */
        LocalDateTime startTime = TimeUtils.timeStrToLocalDate(appointmentTime.getAppointmentStartTime(), TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS);
        LocalDateTime endTime = TimeUtils.timeStrToLocalDate(appointmentTime.getAppointmentEndTime(), TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS);
        long minutes = Duration.between(startTime, endTime).toMinutes();
        int step = (int)minutes / 30;

        LocalDateTime newEndTime = TimeUtils.dateToLocalDateTime(payTime).plusMinutes(step * 30);
        // 获取结束时间距离下一个半点的差值
        int add = 30 - (newEndTime.getMinute() % 30);
        appointmentTime.setAppointmentStartTime(TimeUtils.dateTimeToStr(payTime, TimeFormat.LONG_PATTERN_LINE_NO_S));
        appointmentTime.setAppointmentEndTime(TimeUtils.localDateTimeToStr(newEndTime.plusMinutes(add), TimeFormat.LONG_PATTERN_LINE_NO_S));

    }
}