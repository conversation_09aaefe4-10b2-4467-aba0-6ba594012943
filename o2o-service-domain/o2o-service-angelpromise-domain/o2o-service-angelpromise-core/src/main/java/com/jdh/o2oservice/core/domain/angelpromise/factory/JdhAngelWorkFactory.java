package com.jdh.o2oservice.core.domain.angelpromise.factory;

import com.jd.common.util.StringUtils;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.angelpromise.assembler.JdhAngelWorkAssembler;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelWorkStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.SaveAngelWorkContext;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkHistory;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelWorkHistoryExtVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:JdhAngelWorkFactory
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/18 18:08
 * @Vserion: 1.0
 **/
@Slf4j
public class JdhAngelWorkFactory {

    /**
     * 创建服务者工单
     *
     * @param angelWorkContext
     * @return
     */
    public static AngelWork create(SaveAngelWorkContext angelWorkContext){
        AngelWork angelWork = JdhAngelWorkAssembler.ins.convertToAngelWork(angelWorkContext);
        angelWork.setWorkId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        angelWork.initNormalField();

        List<AngelTask> angelTasks = angelWork.getAngelTasks();
        if(CollectionUtils.isNotEmpty(angelTasks)){
            angelTasks.stream().forEach(item -> {
                item.setWorkId(angelWork.getWorkId());
                item.setTaskId(SpringUtil.getBean(GenerateIdFactory.class).getId());
                item.setCreator(angelWork.getCreator());
                item.setUpdater(angelWork.getUpdater());
                item.initNormalField();
            });
        }
        return angelWork;
    }


    public static AngelWorkHistory createHistory(AngelWorkStatusContext workContext) {
        AngelWorkHistory history = AngelWorkHistory.builder()
                .workId(workContext.getAngelWork().getWorkId())
                .operateEvent(workContext.getEventCode())
                .beforeStatus(workContext.getAngelWork().getWorkStatus())
                .afterStatus(Objects.isNull(workContext.getWorkStatus()) ? workContext.getAngelWork().getWorkStatus() : workContext.getWorkStatus())
                .beforeStopStatus(workContext.getAngelWork().getStopStatus())
                .afterStopStatus(workContext.getWorkStopStatus())
                .operateTime(new Date())
                .yn(YnStatusEnum.YES.getCode())
                .createTime(new Date())
                .build();


        JdhAngelWorkHistoryExtVo jdhAngelWorkHistoryExtVo = null;
        if(StringUtils.isNotBlank(workContext.getReason())){
            if(Objects.isNull(jdhAngelWorkHistoryExtVo)) {
                jdhAngelWorkHistoryExtVo = JdhAngelWorkHistoryExtVo.builder().reason(workContext.getReason()).build();
            }
            history.setJdhAngelWorkHistoryExtVo(jdhAngelWorkHistoryExtVo);
        }

        if(Objects.nonNull(workContext.getAngelLocationBo())) {
            if(Objects.isNull(jdhAngelWorkHistoryExtVo)) {
                jdhAngelWorkHistoryExtVo = JdhAngelWorkHistoryExtVo.builder().angelLocationBo(workContext.getAngelLocationBo()).build();
            }else {
                jdhAngelWorkHistoryExtVo.setAngelLocationBo(workContext.getAngelLocationBo());
            }
            history.setJdhAngelWorkHistoryExtVo(jdhAngelWorkHistoryExtVo);
        }

        if(StringUtils.isNotBlank(workContext.getAngelId())){
            history.setCreator(workContext.getAngelId());
        }
        if(StringUtils.isNotBlank(workContext.getCPin())){
            history.setCreator(workContext.getCPin());
        }

        return history;
    }
}
