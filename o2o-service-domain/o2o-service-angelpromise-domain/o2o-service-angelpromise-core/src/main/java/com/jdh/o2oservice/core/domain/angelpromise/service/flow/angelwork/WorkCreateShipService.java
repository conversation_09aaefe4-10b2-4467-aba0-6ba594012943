package com.jdh.o2oservice.core.domain.angelpromise.service.flow.angelwork;

import cn.hutool.core.convert.Convert;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.matrix.core.domain.flow.OutputMessage;
import com.jd.matrix.core.domain.flow.Rollbackable;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.UavConfig;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.bo.ServiceItemQueryBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelWorkShipCreateContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.SaveAngelWorkContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkFlowEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelShipCreateFailBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.rpc.ServiceItemRpc;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.vo.ServiceItemMaterialPackageVo;
import com.jdh.o2oservice.core.domain.angelpromise.vo.ServiceItemVo;
import com.jdh.o2oservice.core.domain.angelpromise.vo.ShipTask;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.dto.JdhMedicalPromiseQueryDto;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.param.MedicalPromiseQueryParam;
import com.jdh.o2oservice.core.domain.support.medicalpromsie.rpc.MedicalPromiseDispatchRpc;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseDeliveryStepDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName:WorkCreateDipatchStationService
 * @Description: 创建工单派实验室
 * @Author: yaoqinghai
 * @Date: 2024/5/26 18:34
 * @Vserion: 1.0
 **/
@Service("workCreateCreateShipService")
@Slf4j
public class WorkCreateShipService extends Rollbackable implements DomainFlowNode {

    @Resource
    private AngelShipDomainService angelShipDomainService;

    @Resource
    private MedicalPromiseDispatchRpc medicalPromiseDispatchRpc;

    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private ServiceItemRpc serviceItemRpc;

    @Resource
    private DuccConfig duccConfig;

    @Override
    public OutputMessage call(InputMessage inputMessage) {
        log.info("[WorkCreateShipService.call],业务参数检查 START");
        AngelWork angelWork = Convert.convert(AngelWork.class, inputMessage.getHeader("angelWork"));
        List<AngelTask> angelTaskList = Convert.convert(List.class, inputMessage.getHeader("angelTask"));
        SaveAngelWorkContext workContext = (SaveAngelWorkContext) inputMessage.getHeader("context");
        if(Objects.isNull(angelWork)){
            log.error("[WorkCreateDipatchStationService.call],服务者工单信息不存在");
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }

        try{
            //任务分组
            Map<String, AngelTask> angelMap = angelTaskList.stream().collect(Collectors.toMap(AngelTask::getPatientId, Function.identity(), (k1, k2) -> k1));
            log.info("[WorkCreateBizCheckService -> call],angelMap={}", JSON.toJSONString(angelMap));

            //查询派出的实验室
            MedicalPromiseQueryParam medicalPromiseQueryBo = new MedicalPromiseQueryParam();
            medicalPromiseQueryBo.setPromiseId(angelWork.getPromiseId());
            medicalPromiseQueryBo.setInvalid(Boolean.FALSE);
            List<JdhMedicalPromiseQueryDto> jdhMedicalPromiseQueryVos = medicalPromiseDispatchRpc.queryMedicalPromiseList(medicalPromiseQueryBo);
            Map<String, List<JdhMedicalPromiseQueryDto>> promiseGroup = Optional.ofNullable(jdhMedicalPromiseQueryVos).map(List::stream).orElseGet(Stream::empty)
                    .collect(Collectors.groupingBy(JdhMedicalPromiseQueryDto::getStationId));
            log.info("[WorkCreateBizCheckService -> call],promiseGroup={}", JSON.toJSONString(promiseGroup));
            //呼叫运力
            promiseGroup.forEach((k, v) -> {
                //运单扩展信息
                List<ShipTask> shipTaskList = Lists.newArrayList();
                Map<Long, JdhMedicalPromiseQueryDto> promisePatientMap = v.stream().collect(Collectors.toMap(JdhMedicalPromiseQueryDto::getPromisePatientId, Function.identity(), (k1, k2) -> k1));
                promisePatientMap.values().stream().forEach(item -> {
                    ShipTask shipTask = new ShipTask();
                    AngelTask angelTask = angelMap.get(String.valueOf(item.getPromisePatientId()));
                    shipTask.setTaskId(angelTask.getTaskId());
                    shipTask.setPromisePatientId(item.getPromisePatientId());
                    shipTaskList.add(shipTask);
                });
                log.info("[WorkCreateBizCheckService -> call],shipTaskList={}", JSON.toJSONString(shipTaskList));

                //组装运单备注信息
                StringBuffer buff = new StringBuffer();
                Integer cargoNum = 1;
                try{
                    //处理采样管的数量
                    Map<String, Long> medicalPromiseItemGroup = jdhMedicalPromiseQueryVos.stream().collect(Collectors.groupingBy(JdhMedicalPromiseQueryDto::getServiceItemId, Collectors.counting()));

                    Map<String, Integer> info = Maps.newHashMap();
                    medicalPromiseItemGroup.forEach((key, value) -> {
                        ServiceItemQueryBo serviceItemQueryBo = new ServiceItemQueryBo();
                        serviceItemQueryBo.setItemIds(Sets.newHashSet(Long.valueOf(key)));
                        serviceItemQueryBo.setStationId(k);
                        List<ServiceItemVo> serviceItemVos = serviceItemRpc.queryServiceItemList(serviceItemQueryBo);
                        Optional.ofNullable(serviceItemVos).map(List::stream).orElseGet(Stream::empty)
                                .forEach(materialL -> {
                                    List<ServiceItemMaterialPackageVo> materialList = materialL.getMaterialList();
                                    for (ServiceItemMaterialPackageVo serviceItemMaterialPackageVo : materialList) {
                                        if (info.containsKey(serviceItemMaterialPackageVo.getMaterialPackageName())) {
                                            info.put(serviceItemMaterialPackageVo.getMaterialPackageName(),
                                                    info.get(serviceItemMaterialPackageVo.getMaterialPackageName()) + medicalPromiseItemGroup.get(String.valueOf(materialL.getItemId())).intValue());
                                        } else {
                                            info.put(serviceItemMaterialPackageVo.getMaterialPackageName(),
                                                    medicalPromiseItemGroup.get(String.valueOf(materialL.getItemId())).intValue());
                                        }
                                    }
                                });
                    });

                    info.forEach((name, material) -> buff.append(name).append(":").append(material).append("件;"));
                    cargoNum = info.keySet().size();
                    if("xfylHomeSelfTestTransport".equals(angelWork.getVerticalCode())){
                        cargoNum = v.size();
                    }
                    buff.append("订单号:").append(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getOrderId());
                    log.info("[WorkCreateBizCheckService -> call], 骑手备注:{}", buff.toString());
                }catch (Exception ex) {
                    log.error("[WorkCreateBizCheckService -> call],查询项目的耗材信息异常!");
                }

                Integer deliveryType = workContext.getAngelDeliveryType().get(k);
                if(Objects.isNull(deliveryType)){
                    log.error("[WorkCreateBizCheckService -> call],无骑手供应商,workContext={}!", JSON.toJSONString(workContext));
                    throw new BusinessException(AngelPromiseBizErrorCode.RIDER_PROVIDER_EMPTY_ERROR);
                }

                AngelWorkShipCreateContext shipCreateContext = new AngelWorkShipCreateContext();
                shipCreateContext.setVerticalCode(workContext.getVerticalCode());
                shipCreateContext.setServiceType(workContext.getServiceType());
                shipCreateContext.setWorkId(angelWork.getWorkId());
                shipCreateContext.setShopNo(mappingShopNo(v.get(0).getAngelStationId()));
                shipCreateContext.setProviderShopNo(k);
                shipCreateContext.setIsPrepay(CommonConstant.ZERO);
                if(CollectionUtils.isEmpty(v.get(0).getDeliveryStepFlow())||v.get(0).getDeliveryStepFlow().size()<=1){
                    log.info("[WorkCreateBizCheckService -> call], 不走无人机逻辑,只有达达运单");
                    shipCreateContext.setReceiverAddress(v.get(0).getStationAddress());
                    shipCreateContext.setReceiverName(v.get(0).getStationName());
                    shipCreateContext.setReceiverPhone(v.get(0).getStationPhone());
                }else {
                    //无人机
                    log.info("[WorkCreateBizCheckService -> call], 走无人机逻辑,传接驳点地址");
                    shipCreateContext.setReceiverAddress(v.get(0).getDeliveryStepFlow().get(0).getEndAddress());
                    log.info("[WorkCreateBizCheckService -> uavConfig={}",JsonUtil.toJSONString(duccConfig.getUavConfig()));
                    log.info("[WorkCreateBizCheckService -> medicalPromise={}",JsonUtil.toJSONString(v.get(0)));
                    UavConfig.UavFlightInfo uavFlightInfo = duccConfig.getUavConfig().getUavFlightInfoByFlightId(v.get(0).getDeliveryStepFlow().get(1).getThirdStationId(),v.get(0).getDeliveryStepFlow().get(1).getThirdStationTargetId());
                    shipCreateContext.setReceiverName(uavFlightInfo.getReceiverName());
                    shipCreateContext.setReceiverPhone(uavFlightInfo.getReceiverPhone());
                }
                shipCreateContext.setCargoWeight(0.1);
                shipCreateContext.setCargoNum(cargoNum);
                shipCreateContext.setSupplierAddress(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getFullAddress());
                shipCreateContext.setSupplierPhone(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getAppointPhone());
                shipCreateContext.setSupplierName(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getAppointName());
                shipCreateContext.setAngelType(workContext.getAngelType());
                shipCreateContext.setDeliveryType(deliveryType);
                shipCreateContext.setAngelDetailType(DeliveryTypeEnum.getEnumByType(deliveryType).getAngelDetailTypeEnum().getType());
                shipCreateContext.setIsDirectDelivery(CommonConstant.ZERO);
                shipCreateContext.setAngelWork(angelWork);
                shipCreateContext.setInfo(buff.toString());
                shipCreateContext.setShipTaskList(shipTaskList);
                shipCreateContext.setAngelStationId(v.get(0).getAngelStationId());
                shipCreateContext.setMedicalPromiseIds(v.stream().map(JdhMedicalPromiseQueryDto::getMedicalPromiseId).collect(Collectors.toList()));
                AngelShip angelShip = angelShipDomainService.createAngelShip(shipCreateContext);
                angelWork.setPlanCallTime(angelShip.getPlanCallTime());
            });
        }catch (Exception ex) {
            log.error("[WorkCreateCreateShipService.call],呼叫运单失败!", ex);
            throw new BusinessException(AngelPromiseBizErrorCode.CREATE_DADA_SHIP_ERROR);
        }
        log.info("[WorkCreateShipService.call],业务参数检查 END");
        return new OutputMessage();
    }

    public static void main(String[] args) {

        String s = " [\n" +
                "                {\n" +
                "                    \"deliveryStepType\": 1,\n" +
                "                    \"endAddress\": \"北京大兴区西红门地区京港城生活广场\",\n" +
                "                    \"endAddressId\": \"172756014465041\",\n" +
                "                    \"sort\": 1,\n" +
                "                    \"startAddress\": \"北京大兴区西红门地区王府井奥莱\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"deliveryStepType\": 2,\n" +
                "                    \"endAddress\": \"北京市亦庄经济开发区******\",\n" +
                "                    \"endAddressId\": \"S168634579157002\",\n" +
                "                    \"sort\": 2,\n" +
                "                    \"startAddress\": \"北京大兴区西红门地区京港城生活广场\",\n" +
                "                    \"startAddressId\": \"172756014465041\",\n" +
                "                    \"thirdStationId\": \"10814\",\n" +
                "                    \"thirdStationTargetId\": \"10815\"\n" +
                "                }\n" +
                "            ]";

        List<MedPromiseDeliveryStepDTO> list = JsonUtil.parseArray(s, MedPromiseDeliveryStepDTO.class);
        String config = "{\n" +
                "    \"key\": \"beb1138cd24843a680230a1d61f8012b\",\n" +
                "    \"privateKeyStr\": \"MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALO+C8A2svTJTBqpuTjkzChOHY4RqyJw1grNu6EmNfKKQlFc+QyPG3kYtuvd8ZLxLodhRGetTL1E5NCbkTIOobmtb6aruXSW8HLdYTy7qQY8ii89RVvZAh3/8viV+4O+kUIYG3tjIyGTIng5FbDD0r5LXk7nKsWp4JoYDl1bTaevAgMBAAECgYEAl9u2/dy48Yuo2tYOgXz86Aine8J9vglrLZGINqyb46DgSvGsEOpPoc45ranEUgum7gZFzvph3X75ey4UTCCEjXrTgB1n1uqCGLLdwmTurROzJ5QF+B6DD3X5uvlYLDWbplIpkzgT3IqQ+uJFv9mWEON7v/VaflSsNreflop5yDECQQDdBdwjuHqsSokH/WmiLAF1BJkVNO1XZ5xfGobzb0416sME8fEG3n0zCK66kjG7gaTdjOSA5UlRb6nujBrRkI0LAkEA0C/RbN1KgGftDAly8CGfVt2vWUv0BRH+ppBVEPy30GzjfNYNnx48pq81xPjUUc+5VmIrP1cHTWEemcrWhAEObQJAZp8a6Gb2ZlqxJ0mNK2QdRRTecw9BB+0umKW7dPoAKV6YMAqZ66OQJArq/et1NedrAgcx7XsIQMyE7SKWLSldowJAFnSh9UklSRZspji6shYeVsNQr6QWrRlUy2iUFGH9/bhRDV0VWQ1s41nQxe9FD5IFXsD2Az4C5qDMKTCM1O48PQJAOMET3cbLUOE4MAfCQHAtpN0AvYdAWS7oSbWZbnXgt+9XIEiZbntCK96tDpESKiw7lvHNOJhYO7EgH0Sf9fs9jA==\",\n" +
                "    \"uavFlightInfos\": [\n" +
                "        {\n" +
                "            \"startFlightId\": \"10814\",\n" +
                "            \"endFlightId\": \"10815\",\n" +
                "            \"receiverName\": \"高星亮\",\n" +
                "            \"receiverPhone\": \"15353743512\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        UavConfig uavConfig = JSON.parseObject(config,UavConfig.class);
        UavConfig.UavFlightInfo uavFlightInfo = uavConfig.getUavFlightInfoByFlightId(list.get(1).getThirdStationId(),list.get(1).getThirdStationTargetId());

        System.out.println(JsonUtil.toJSONString(uavFlightInfo));

    }

    @Override
    public String getCode() {
        return AngelWorkFlowEnum.CREATE_WORK_SHIP.getFlowCode();
    }

    @Override
    public String getName() {
        return AngelWorkFlowEnum.CREATE_WORK_SHIP.getFlowDesc();
    }

    @Override
    public void rollBack(InputMessage inputMessage) {
        log.info("FLOW ROLLBACK: ======WorkCreateCreateShipService rollback biz。执行呼叫运力失败回滚流程=======");
        AngelWork angelWork = Convert.convert(AngelWork.class, inputMessage.getHeader("angelWork"));
        //运单创建失败事件
        AngelShipCreateFailBody angelShipCreateFailBody = new AngelShipCreateFailBody();
        angelShipCreateFailBody.setOrderId(angelWork.getJdOrderId());
        angelShipCreateFailBody.setPromiseId(angelWork.getPromiseId());
        angelShipCreateFailBody.setSourceId(angelWork.getSourceId());
        angelShipCreateFailBody.setRefundType(1);
        angelShipCreateFailBody.setRefundReason("创建运单失败");
        angelShipCreateFailBody.setRefundSource("3");
        angelShipCreateFailBody.setOperator("system");

        eventCoordinator.publish(EventFactory.newDefaultEvent(angelWork, AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CREATE_FAIL, angelShipCreateFailBody));
    }

    /**
     * 映射服务站id
     *
     * @param angelStationId
     * @return
     */
    private String mappingShopNo(String angelStationId) {
        Map<String, String> angelStationMap = duccConfig.getAngelStationMap();
        if(MapUtils.isEmpty(angelStationMap)) {
            return angelStationId;
        }
        return StringUtils.isBlank(angelStationMap.get(angelStationId)) ? angelStationId : angelStationMap.get(angelStationId);
    }
}
