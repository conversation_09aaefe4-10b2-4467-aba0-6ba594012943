package com.jdh.o2oservice.core.domain.angelpromise.service.impl;

import com.jd.fastjson.JSON;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.statemachine.StateMachine;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.common.ext.O2oBusinessIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelTaskExtStateBo;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelTaskStateBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskExtStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelTaskStatusEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.factory.JdhAngelTaskFactory;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTaskHistory;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelTaskBizStatusDbCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelTaskStatusCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelTaskDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.service.ability.TaskExtStatusHandelAbility;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author:lichen55
 * @createTime: 2024-04-18 18:07
 * @Description:
 */
@Slf4j
@Service
public class AngelTaskDomainServiceImpl implements AngelTaskDomainService {

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelTaskRepository angelTaskRepository;

    @Resource
    private AngelTaskHistoryRepository angelTaskHistoryRepository;

    /**
     * angelWorkStateMachine
     */
    @Resource
    private StateMachine<AngelPromiseStatus, AngelTaskEventTypeEnum, StateContext> angelTaskStatemachine;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private TaskExtStatusHandelAbility taskExtStatusHandelAbility;

    /**
     * 执行任务单
     *
     * @param angelTaskStatusContext
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeTask(AngelTaskStatusContext angelTaskStatusContext) {
        log.info("[AngelTaskDomainServiceImpl.executeTask],angelTaskStatusContext={}", JSON.toJSONString(angelTaskStatusContext));
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelTaskStatusContext.getWorkId()).build());
        angelTaskStatusContext.setAngelWork(angelWork);
        if(Objects.isNull(angelWork)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }

        List<AngelTaskStateBo> angelTaskStateBoList = angelTaskStatusContext.getAngelTaskStateBoList();
        Map<Long, AngelTaskStateBo> taskMap = angelTaskStateBoList.stream().collect(Collectors.toMap(AngelTaskStateBo::getTaskId, Function.identity(), (k1, k2) -> k1));
        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setWorkId(angelTaskStatusContext.getWorkId());
        angelTaskDBQuery.setTaskIds(Lists.newArrayList(taskMap.keySet()));
        List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
        log.info("[AngelTaskDomainServiceImpl.executeTask],angelTaskList={}", JSON.toJSONString(angelTaskList));
        List<AngelTask> list;
        if(!AngelTaskEventTypeEnum.checkInvalidEvent(angelTaskStatusContext.getAngelTaskEventTypeEnum().getCode())){
            list = angelTaskList.stream().filter(task -> task.checkValidStatus(true, true)).collect(Collectors.toList());
        }else {
            list = angelTaskList;
        }
        log.info("[AngelTaskDomainServiceImpl.executeTask],list={}", JSON.toJSONString(list));
        if(CollectionUtils.isEmpty(list)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_TASK_NOT_EXIST);
        }

        //任务幂等检查
        List<AngelTask> executeList = list.stream().filter(task -> {
            AngelTaskStateBo angelTaskStateBo = taskMap.get(task.getTaskId());
            if (Objects.isNull(angelTaskStateBo)) {
                return Boolean.FALSE;
            }
            return angelTaskStateBo.getTaskStatus() > task.getTaskStatus();
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(executeList)){
            log.info("[AngelTaskDomainServiceImpl.executeTask],没有需要处理的任务!");
            return Boolean.TRUE;
        }

        //检查状态是否被暂停，是否可以继续执行
        executeList.forEach(task -> {
            AngelTaskStateBo angelTaskStateBo = taskMap.get(task.getTaskId());
            if (!Objects.equals(AngelTaskStopStatusEnum.INIT.getStatus(), task.getStopStatus())
                    && !AngelTaskStopStatusEnum.getTargetStatus(task.getStopStatus()).contains(angelTaskStateBo.getTaskStatus())) {
                log.error("[AngelWorkDomainServiceImpl.executeWork],当前工单状态处于暂停状态,不能执行状态变更!");
                throw new BusinessException(AngelPromiseBizErrorCode.WORK_STOP);
            }
        });

        angelTaskStatusContext.setAngelTaskList(executeList);

        //执行状态机
        AngelTaskStatusEnum enumByCode = AngelTaskStatusEnum.getEnumByCode(executeList.get(0).getTaskStatus());
        angelTaskStatemachine.fireEvent(enumByCode, angelTaskStatusContext.getAngelTaskEventTypeEnum(), angelTaskStatusContext);

        //修改状态
        Map<Long, Integer> versionMap = executeList.stream().collect(Collectors.toMap(AngelTask::getTaskId, AngelTask::getVersion, (k1, k2) -> k1));
        List<AngelTaskStatusCmd> taskStatusCmdList = angelTaskStatusContext.getAngelTaskStateBoList().stream().map(item -> {
            AngelTaskStatusCmd taskStatusCmd = new AngelTaskStatusCmd();
            taskStatusCmd.setTaskStatus(item.getTaskStatus());
            taskStatusCmd.setStopStatus(item.getStopStatus());
            taskStatusCmd.setTaskId(item.getTaskId());
            taskStatusCmd.setVersion(versionMap.get(item.getTaskId()));
            taskStatusCmd.setOperator(angelTaskStatusContext.getOperator());
            return taskStatusCmd;
        }).collect(Collectors.toList());

        angelTaskHistoryRepository.batchSave(JdhAngelTaskFactory.createHistory(angelTaskStatusContext, angelTaskStateBoList.get(0).getTaskStatus()));
        if(angelTaskRepository.updateStatus(taskStatusCmdList) != taskStatusCmdList.size()){
            throw new BusinessException(AngelPromiseBizErrorCode.SHARED_LOCK_EXECUTE_ERROR);
        }
        log.info("[AngelTaskDomainServiceImpl.executeTask],angelTaskStatusContext2={}", JSON.toJSONString(angelTaskStatusContext));
        log.info("[AngelTaskDomainServiceImpl.executeTask],executeList2={}", JSON.toJSONString(executeList));
        if(Objects.nonNull(angelTaskStatusContext.getInPlaceTaskEvent())){
            executeList.forEach(task -> {
                AngelTaskStatusEventBody eventBody = new AngelTaskStatusEventBody();
                eventBody.setTaskId(task.getTaskId());
                eventBody.setWorkId(task.getWorkId());
                eventBody.setTaskStatus(angelTaskStateBoList.get(0).getTaskStatus());
                Event publishEvent = EventFactory.newDefaultEvent(angelWork, angelTaskStatusContext.getInPlaceTaskEvent(), eventBody);
                eventCoordinator.publish(publishEvent);
            });
        }
        return true;
    }

    /**
     * 执行任务单
     *
     * @param angelTaskExtStatusContext
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeTaskExt(AngelTaskExtStatusContext angelTaskExtStatusContext) {
        AssertUtils.nonNull(angelTaskExtStatusContext.getWorkId(), "工单id不能为空");
        AssertUtils.isNotEmpty(angelTaskExtStatusContext.getAngelTaskExtStateBoList(), "任务列表不能为空");

        List<AngelTaskExtStateBo> angelTaskExtStateBoList = angelTaskExtStatusContext.getAngelTaskExtStateBoList();
        Map<Long, AngelTaskExtStateBo> angelTaskMap = angelTaskExtStateBoList.stream().collect(Collectors.toMap(AngelTaskExtStateBo::getTaskId, Function.identity(), (k1, k2) -> k1));
        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setWorkId(angelTaskExtStatusContext.getWorkId());
        angelTaskDBQuery.setTaskIds(Lists.newArrayList(angelTaskMap.keySet()));
        List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
        List<AngelTask> list = angelTaskList.stream()
                .filter(angel -> angel.checkValidStatus(true, true))
                .filter(angel -> {
                    AngelTaskExtStateBo taskExtStateBo = angelTaskMap.get(angel.getTaskId());
                    // todo 这里使用大于符号判断是什么意思？
                    return taskExtStateBo.getTaskExtStatus() > angel.getBizExtStatus();
                })
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            log.info("[AngelTaskDomainServiceImpl.executeTaskExt],没有要处理状态的任务单!angelTaskExtStatusContext={}", JSON.toJSONString(angelTaskExtStatusContext));
            return Boolean.TRUE;
        }
        angelTaskExtStatusContext.setAngelTaskList(list);

        //修改状态
        List<AngelTaskBizStatusDbCmd> taskBizStatusDbCmdList = list.stream().map(item -> {
            AngelTaskBizStatusDbCmd angelTaskBizStatusDbCmd = new AngelTaskBizStatusDbCmd();
            angelTaskBizStatusDbCmd.setNewStatus(angelTaskMap.get(item.getTaskId()).getTaskExtStatus());
            angelTaskBizStatusDbCmd.setTaskId(item.getTaskId());
            angelTaskBizStatusDbCmd.setOperator(angelTaskExtStatusContext.getOperator());
            return angelTaskBizStatusDbCmd;
        }).collect(Collectors.toList());

        angelTaskRepository.updateExtStatus(taskBizStatusDbCmdList);
        // todo 服务完成时候 此时的taskExtStatus状态是几？所有的task状态是一致的？
        AngelTaskBizExtEventTypeEnum taskExtEvent = AngelBizExtStatusEnum.getTaskExtEvent(
                angelTaskExtStatusContext.getAngelTaskExtStateBoList().get(0).getTaskExtStatus());

        //记录历史
        AngelTaskBizExtEventTypeEnum extEventTypeEnum = Objects.isNull(taskExtEvent) ? angelTaskExtStatusContext.getAngelTaskEventTypeEnum() : taskExtEvent;
        List<AngelTaskHistory> eventHistory = JdhAngelTaskFactory.createEventHistory(list, angelTaskExtStatusContext.getAngelTaskExtStateBoList().get(0).getTaskExtStatus(), extEventTypeEnum);
        angelTaskHistoryRepository.batchSave(eventHistory);

        //发送事件
        if(Objects.nonNull(taskExtEvent)){
            list.forEach(task -> {
                Event publishEvent = EventFactory.newDefaultEvent(task, taskExtEvent, null);
                eventCoordinator.publish(publishEvent);
            });
        }
        return true;
    }

    /**
     * 获取业务垂直身份业务扩展状态
     *
     * @param businessContext
     * @return
     */
    @Override
    public Integer getVerticalStatus(BusinessContext businessContext) {
        //检查业务身份参数
        businessContext.initVertical();

        //查询垂直业务身份下开始服务的扩展状态
        O2oBusinessIdentifier businessIdentifier = O2oBusinessIdentifier.builder()
                .verticalCode(businessContext.getVerticalCode())
                .serviceType(businessContext.getServiceType())
                .businessMode(businessContext.getVerticalBusiness().getBusinessModeCode())
                .build();
        ExtResponse<Integer> startVerticalStatus = taskExtStatusHandelAbility.getStartVerticalStatus(businessIdentifier);
        return startVerticalStatus.getData();
    }
}
