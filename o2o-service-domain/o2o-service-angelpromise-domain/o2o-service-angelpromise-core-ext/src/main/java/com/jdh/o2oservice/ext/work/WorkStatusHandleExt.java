package com.jdh.o2oservice.ext.work;

import com.jd.matrix.sdk.annotation.DomainAbilityExtension;
import com.jd.matrix.sdk.base.IDomainAbilityExtension;
import com.jdh.o2oservice.common.ext.ExtBusinessIdentifierCode;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.ext.work.param.AngelTaskStatusParam;
import com.jdh.o2oservice.ext.work.param.CheckTaskStatusAlreadyParam;
import com.jdh.o2oservice.ext.work.response.AngelTaskStatusResponse;

/**
 * @InterfaceName:WorkStatusHandleExt
 * @Description: 工单状态处理扩展点
 * @Author: yaoqinghai
 * @Date: 2024/5/16 23:49
 * @Vserion: 1.0
 **/
public interface WorkStatusHandleExt extends IDomainAbilityExtension {


    /**
     * 检查任务单的状态是否都到达了当前发布的新状态
     *
     * @param alreadyParam
     * @return
     */
    @DomainAbilityExtension(
            code = ExtBusinessIdentifierCode.ANGEL_PROMISE_WORK_STATUS_HANDLE_EXT,
            name = "工单状态处理检查任务单的状态是否都到达"
    )
    ExtResponse<Boolean> checkTaskStatusAlready(CheckTaskStatusAlreadyParam alreadyParam);

    /**
     * 查询工单任务状态
     *
     * @param angelTaskStatusParam
     * @return
     */
    @DomainAbilityExtension(
            code = ExtBusinessIdentifierCode.ANGEL_TASK_STATUS_HANDLE_EXT,
            name = "查询当前工单状态下任务单的状态"
    )
    ExtResponse<AngelTaskStatusResponse> getTaskStatus(AngelTaskStatusParam angelTaskStatusParam);
}
