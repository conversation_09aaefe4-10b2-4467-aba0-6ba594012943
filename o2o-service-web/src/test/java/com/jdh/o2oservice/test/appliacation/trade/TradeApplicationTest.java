package com.jdh.o2oservice.test.appliacation.trade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.StartApplication;
import com.jdh.o2oservice.application.angel.service.AngelQueryApplication;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.riskassessment.service.RiskAssessmentApplication;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleApplication;
import com.jdh.o2oservice.application.support.service.FeeConfigurationApplication;
import com.jdh.o2oservice.application.support.service.PatientApplication;
import com.jdh.o2oservice.application.support.util.BusinessModeUtil;
import com.jdh.o2oservice.application.trade.convert.JdOrderConverter;
import com.jdh.o2oservice.application.trade.convert.ServiceFeeApplicationConverter;
import com.jdh.o2oservice.application.trade.convert.TradeApplicationConverter;
import com.jdh.o2oservice.application.trade.convert.TradeOrderRefundConverter;
import com.jdh.o2oservice.application.trade.service.InspectionSheetApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderRefundApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.application.via.ViaConfigApplication;
import com.jdh.o2oservice.base.config.AbTestConfiguration;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.*;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.model.AbilityCode;
import com.jdh.o2oservice.base.model.BusinessModeParam;
import com.jdh.o2oservice.base.model.DomainAbility;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.statemachine.AbilityExecutor;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.context.QueryAvailableTimeContext;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.product.model.JdhAreaFeeConfig;
import com.jdh.o2oservice.core.domain.product.model.JdhAreaFeeConfigdentifier;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhAreaFeeConfigRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.JdhAreaFeeConfigQuery;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseAppointmentDraftRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.service.JdhPromiseDomainService;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.*;
import com.jdh.o2oservice.base.model.UserName;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.rpc.CompanyCalendarRpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.CompanyCalendarBo;
import com.jdh.o2oservice.core.domain.support.rpc.bo.QueryCompanyCalendarBo;
import com.jdh.o2oservice.core.domain.support.ship.param.CommonCheckPreCreateOrderBo;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.bo.OrderRefundDetailBo;
import com.jdh.o2oservice.core.domain.trade.bo.RefundSku;
import com.jdh.o2oservice.core.domain.trade.context.CalcOrderServiceFeeContext;
import com.jdh.o2oservice.core.domain.trade.context.OrderInfoQueryContext;
import com.jdh.o2oservice.core.domain.trade.context.OrderRefundContext;
import com.jdh.o2oservice.core.domain.trade.enums.*;
import com.jdh.o2oservice.core.domain.trade.model.*;
import com.jdh.o2oservice.core.domain.trade.repository.db.*;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.core.domain.trade.rpc.SelfAppointRecordExportServiceRpc;
import com.jdh.o2oservice.core.domain.trade.rpc.VtpOrderInfoRpc;
import com.jdh.o2oservice.core.domain.trade.service.JdhOrderDomainService;
import com.jdh.o2oservice.core.domain.trade.service.SettleUrlService;
import com.jdh.o2oservice.core.domain.trade.service.TradeDomainService;
import com.jdh.o2oservice.core.domain.trade.vo.AddressInfoValueObject;
import com.jdh.o2oservice.export.angel.dto.SkuInventoryResultDto;
import com.jdh.o2oservice.export.angel.query.AddressDetail;
import com.jdh.o2oservice.export.angel.query.QueryBatchSkuInventoryRequest;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuAreaFeeQuery;
import com.jdh.o2oservice.export.promise.cmd.*;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.enums.VoucherOpEnum;
import com.jdh.o2oservice.export.promise.query.PromiseListRequest;
import com.jdh.o2oservice.export.support.dto.DictInfoDto;
import com.jdh.o2oservice.export.support.dto.TimeRangeDate;
import com.jdh.o2oservice.export.trade.TradeJsfExport;
import com.jdh.o2oservice.export.trade.cmd.CalcTradeServiceFeeCmd;
import com.jdh.o2oservice.export.trade.dto.*;
import com.jdh.o2oservice.export.trade.query.*;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePatientPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePatientPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author:
 * @date: 2024/1/5 9:46 上午
 * @version: 1.0
 */

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = StartApplication.class)
public class TradeApplicationTest {

    /**
     * conditions
     */
    private final Map<String, DomainAbility> conditions = Maps.newConcurrentMap();
    /**
     * actions
     */
    private final Map<String, DomainAbility> actions = Maps.newConcurrentMap();
    @Resource
    private TradeApplication tradeApplication;
    @Resource
    private TradeJsfExport tradeJsfExport;
    @Resource
    private OrderInfoRpc orderInfoRpc;
    /**
     * jdhPromiseDomainService
     */
    @Resource
    private JdhPromiseDomainService jdhPromiseDomainService;
    /**
     * promiseAppointmentDraftRepository
     */
    @Resource
    private PromiseAppointmentDraftRepository promiseAppointmentDraftRepository;
    /**
     * settleUrlService
     */
    @Resource
    private SettleUrlService settleUrlService;
    /**
     * voucherRepository
     */
    @Resource
    private VoucherRepository voucherRepository;
    /**
     * jdOrderItemRepository
     */
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;
    /**
     * jdOrderExtRepository
     */
    @Resource
    private JdOrderExtRepository jdOrderExtRepository;
    /**
     * jdOrderExtRepository
     */
    @Resource
    private JdServiceSettleApplication jdServiceSettleApplication;
    /**
     * jdOrderMoneyRepository
     */
    @Resource
    private JdOrderMoneyRepository jdOrderMoneyRepository;
    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;
    /**
     * applicationContext
     */
    @Resource
    private ApplicationContext applicationContext;

    /**
     * jdOrderRefundApplication
     */
    @Resource
    private JdOrderRefundApplication jdOrderRefundApplication;

    /**
     * 页面配置应用层服务
     */
    @Resource
    private ViaConfigApplication viaConfigApplication;

    /**
     * medicalPromiseApplication
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;
    /**
     * promiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private TradeDomainService tradeDomainService;

    @Resource
    private TradeApplicationConverter tradeApplicationConverter;
    /**
     * feeConfigurationApplication
     */
    @Resource
    private FeeConfigurationApplication feeConfigurationApplication;
    @Resource
    private Cluster jimClient;
    /**
     * redisLockUtil
     */
    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    private JdOrderApplication jdOrderApplication;
    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * jdhOrderDomainService
     */
    @Autowired
    private JdhOrderDomainService jdhOrderDomainService;

    /**
     * serviceFeeApplicaitonConverter
     */
    @Resource
    private ServiceFeeApplicationConverter serviceFeeApplicationConverter;

    @Resource
    private ProductApplication productApplication;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * promiseApplication
     */
    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * inspectionSheetApplication
     */
    @Resource
    private InspectionSheetApplication inspectionSheetApplication;

    @Resource
    private SkuInfoRpc skuInfoRpc;

    /**
     * jdOrderRefundTaskRepository
     */
    @Autowired
    private JdOrderRefundTaskRepository jdOrderRefundTaskRepository;

    @Resource
    private JdhSkuRepository jdhSkuRepository;

    @Autowired
    private JdhAreaFeeConfigRepository jdhAreaFeeConfigRepository;

    @Autowired
    private StationApplication stationApplication;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * 线程池
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Autowired
    private VoucherApplication voucherApplication;

    @Autowired
    private AngelWorkRepository angelWorkRepository;

    @Autowired
    private PromiseHistoryRepository promiseHistoryRepository;

    private VtpOrderInfoRpc vtpOrderInfoRpc;

    /**
     * promisePatientPoMapper
     */
    @Autowired
    private JdhPromisePatientPoMapper promisePatientPoMapper;

    @Autowired
    private SelfAppointRecordExportServiceRpc selfAppointRecordExportServiceRpc;

    /**
     * promiseGoRpcService
     */
    @Autowired
    private PromiseGoRpcService promiseGoRpcService;

    /**
     * 门店信息
     */
    @Autowired
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    @Resource
    private AbTestConfiguration abTestConfiguration;
    @Autowired
    private AngelQueryApplication angelQueryApplication;

    /**
     * 健康地址rpc
     */
    @Resource
    private JdhAddressRpc jdhAddressRpc;

    @Resource
    private AngelShipDomainService angelShipDomainService;

    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * 服务者履约工单
     */
    @Resource
    private AngelWorkApplication angelWorkApplication;

    @Resource
    private PatientApplication patientApplication;

    @Resource
    private RiskAssessmentApplication riskAssessmentApplication;


    @Resource
    @Lazy
    private AngelPromiseApplication angelPromiseApplication;

    @Resource
    private CompanyCalendarRpc companyCalendarRpc;

    public static void main(String[] args) {
        /*List<Date> avaiableInventoryList = JSON.parseArray("[1744246800000,1744250400000,1744250400000,1744254000000,1744254000000,1744257600000,1744257600000,1744261200000,1744261200000,1744264800000,1744264800000,1744268400000,1744268400000,1744272000000,1744272000000,1744275600000,1744275600000,1744279200000,1744279200000,1744282800000,1744282800000,1744286400000,1744286400000,1744290000000,1744082071000,1744085671000,1744084800000,1744088400000,1744088400000,1744092000000,1744092000000,1744095600000,1744095600000,1744099200000,1744099200000,1744102800000,1744102800000,1744106400000,1744106400000,1744110000000,1744110000000,1744113600000,1744113600000,1744117200000,1744160400000,1744164000000,1744164000000,1744167600000,1744167600000,1744171200000,1744171200000,1744174800000,1744174800000,1744178400000,1744178400000,1744182000000,1744182000000,1744185600000,1744185600000,1744189200000,1744189200000,1744192800000,1744192800000,1744196400000,1744196400000,1744200000000,1744200000000,1744203600000]",Date.class);
        avaiableInventoryList = avaiableInventoryList.stream().sorted().collect(Collectors.toList());
        AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO = new AvaiableAppointmentTimeDTO(
                avaiableInventoryList.stream().min(Date::compareTo).orElse(null),
                avaiableInventoryList.stream().max(Date::compareTo).orElse(null),
                "00:00", "24:00", 0,null,null);
        System.out.println(avaiableAppointmentTimeDTO);
        TradeApplicationImpl tradeApplication = new TradeApplicationImpl();
        Map<String, Map<String, List<AppointmentTimeRangeDTO>>> displayDate =tradeApplication.getDisplayDate(avaiableAppointmentTimeDTO, avaiableInventoryList,false, null, false);
        System.out.println(JSON.toJSONString(displayDate));*/


        Date d = TimeUtils.strToDate("2025-04-10");
        System.out.println(d);
    }

    /**
     * 组装buildOrderPromiseDto
     *
     * @param promise
     * @param orderDetail
     * @param vouMap
     * @return
     */
    @Test
    public static OrderPromiseDto buildOrderPromiseDto(JdhPromise promise, JdOrder orderDetail, Map<Long, JdhVoucher> vouMap) {
        OrderPromiseDto promiseDto = new OrderPromiseDto();
        promiseDto.setPromiseId(promise.getPromiseId());
        promiseDto.setAppointmentId(promise.getAppointmentId());
        promiseDto.setServiceType(promise.getServiceType());
        promiseDto.setVerticalCode(promise.getVerticalCode());
        promiseDto.setVenderId(orderDetail.getVenderId());
        promiseDto.setOrderStatus(orderDetail.getOrderStatus());
        promiseDto.setPaymentTime(orderDetail.getPaymentTime());
        if (Objects.nonNull(promise.getStore())) {
            promiseDto.setStoreId(promise.getStore().getStoreId());
            promiseDto.setStoreName(promise.getStore().getStoreName());
            promiseDto.setStoreAddr(promise.getStore().getStoreAddr());
        }
        JdhVoucher voucher = vouMap.get(promise.getVoucherId());
        promiseDto.setOrderId(voucher.getExtend().getOrderId());
        if (Objects.nonNull(promise.getAppointmentTime())) {
            String day = promise.getAppointmentTime().formatAppointDate();
            promiseDto.setAppointmentDate(day);
            if (Objects.equals(DateTypeEnum.SCHEDULE_BY_DATE.getType(), promise.getAppointmentTime().getDateType())) {
                promiseDto.setTimeDesc(promise.getAppointmentTime().formatAppointDate());
            } else {
                String desc = promise.getAppointmentTime().formatAppointTimeDesc();
                promiseDto.setTimeDesc(desc);
            }
            promiseDto.setDateType(promise.getAppointmentTime().getDateType());
        }

        //用户信息
        if (Objects.nonNull(promise.findOnlyPatient())) {
            promiseDto.setUserName(Objects.nonNull(promise.findOnlyPatient().getUserName()) ? promise.findOnlyPatient().getUserName().getName() : null);
            promiseDto.setUserPhone(Objects.nonNull(promise.findOnlyPatient().getPhoneNumber()) ? promise.findOnlyPatient().getPhoneNumber().mask() : null);
            promiseDto.setUserCredentialNo(Objects.nonNull(promise.findOnlyPatient().getCredentialNum()) ? promise.findOnlyPatient().getCredentialNum().mask() : null);
        }
        promiseDto.setAppointmentStatus(promise.getPromiseStatus());
        JdhPromiseStatusEnum statusEnum = JdhPromiseStatusEnum.convert(promise.getPromiseStatus());
        promiseDto.setAppointmentStatusName(Objects.nonNull(statusEnum) ? statusEnum.getDesc() : null);

        //将服务商品信息填入返回值
        List<PromiseService> services = promise.getServices();
        if (CollectionUtil.isNotEmpty(services)) {
            PromiseService basicService = null;
            if (services.size() == 1) {
                basicService = services.get(0);
            } else {
                Optional<PromiseService> optional = services.stream().filter(e -> Objects.equals(e.getTags(), ServiceTagEnum.BASIC.getType())).findFirst();
                basicService = optional.orElse(null);
            }
            if (Objects.nonNull(basicService)) {
                promiseDto.setSkuId(String.valueOf(basicService.getServiceId()));
                promiseDto.setSkuName(basicService.getServiceName());
            }
        }
        promiseDto.setVerticalCode(promise.getVerticalCode());
        return promiseDto;
    }

    @Test
    public void getDraftSettleUrl() {
        SubmitAppointmentDraftCmd cmd = new SubmitAppointmentDraftCmd();
        cmd.setVerticalCode("xfyl_pop");
        cmd.setUserPin("草稿保存");
        cmd.setStoreId("199990");
        cmd.setServiceType(ServiceTypeEnum.ORAL_CAVITY.getServiceType());
        String userJson = "{\"addItemSkuNos\":[],\"appointmentBeginTime\":\"\",\"appointmentDate\":\"2024-01-19\",\"appointmentEndTime\":\"\",\"channelNo\":5879687598,\"companyNo\":5673073154,\"goodsId\":\"2123023198\",\"isCheckSmsCode\":true,\"isCheckYYmb\":true,\"orderAppointType\":10,\"name\":\"马慧\",\"relativesType\":1,\"skuName\":\"米其林入职体检套餐包-女\",\"skuNo\":\"100054659104\",\"storeId\":\"EN\",\"userBirth\":\"1998-10-11\",\"credentialNo\":\"h7mq9C8p46RgM1a60xFVj1YCFQ6WiTpqEfSYh/gqZs6glrbVHxOZWnP3iabukBfG+/N3TYBXV5GUElGtUhAbXaaWBl8wOu3WYuLLIHB1IhS2nQEEFJkkmndl+lNpxCJLtS4HtRloDWb6nBX5XJqx/FsXgSufqvKJN0Ek6x/177I=\",\"credentialType\":1,\"gender\":2,\"userId\":\"149604034674698\",\"marriage\":1,\"phone\":\"NrfweZDvNr0mmRaS5ek3R7881V8NH0PCo8vMZ9cvOOiKHxwRb9ZPkAsuBHl6AhgTEjcPpHZuk9V3oSfXXdcOItC+os7Iy40PRpM7dlmkkf0z621ejjd+eQfy9QsMv69SJsSjM4a/Iv/sdxTlJ/FZohkvnlUW27ld9vxqDCyyIfo=\",\"userPin\":\"筱筱小妖\"}";
        SubmitUser user = JSON.parseObject(userJson, SubmitUser.class);
        user.setPatientId(12345L);
        user.setCredentialType(CredentialEnum.CID.getType());
        UserName name = new UserName();
        name.setName("杨曦宇");
        user.setName(name.encrypt());
        cmd.setUser(user);

        cmd.setSmsCode("907365");

        AppointmentTime appointmentTime = new AppointmentTime();
        appointmentTime.setAppointmentStartTime("2024-01-19 09:00");
        appointmentTime.setAppointmentEndTime("2024-01-19 10:00");
        appointmentTime.setDateType(DateTypeEnum.SCHEDULE_BY_TIME.getType());
        cmd.setAppointmentTime(appointmentTime);

        List<PromiseServiceItem> services = Lists.newArrayList();
        PromiseServiceItem service = new PromiseServiceItem();
        service.setServiceId(123435L);
        service.setOutServiceId("123435");
        services.add(service);
        cmd.setServices(services);
        cmd.setVerticalCode("xfylPop");
        cmd.setServiceType(ServiceTypeEnum.ORAL_CAVITY.getServiceType());

        SettleUrlItem settleUrlItem = new SettleUrlItem();
        settleUrlItem.setStoreId("shopId");
        settleUrlItem.setPt("pt");
        settleUrlItem.setBybtSceneStart(Boolean.TRUE);
        settleUrlItem.setPreSaleStart(Boolean.TRUE);
        cmd.setSettleUrlItem(settleUrlItem);

        cmd.setEnvType(EnvTypeEnum.JD_APP.getCode());
        String medicalLocId = tradeApplication.saveDraft(cmd);
        System.out.println(medicalLocId + "-------------");
    }

    @Test
    public void testSave() {
        String json = "{\"serviceType\":\"oralCavity\",\"pt\":\"\",\"preSaleStart\":false,\"services\":[{\"outServiceId\":\"10096330197185\",\"outServiceName\":\"测试口腔的商品-单次券\",\"serviceId\":\"10096330197185\"}],\"verticalCode\":\"xfylPop\",\"serviceId\":\"10096330197185\",\"bybtSceneStart\":false,\"storeId\":\"**********\",\"user\":{\"phone\":\"Obbhj5qS1TMwhtRP6egisCrxa5Z/Nejmz6bxtm7LAMLmkmz1JcIZK8h9cxBjhImwz0njFYkcnUjYbW2TrgplVxbSFqVoF+R2KnT3fs4OQJMi9NYHen4UZ5vNA1iQlJxx1L3rm11l9LD9Vwy8Cs2uWGMChwMtplP339vmi8DVMuU=\"},\"appointmentTime\":{\"dateType\":\"2\",\"appointmentStartTime\":\"2024-02-12 01:00\",\"appointmentEndTime\":\"2024-02-12 02:00\"},\"settleUrlItem\":{\"storeId\":\"**********\",\"pt\":\"\",\"preSaleStart\":\"false\",\"bybtSceneStart\":\"false\"},\"envType\":\"jdapp\"}";
        SubmitAppointmentDraftCmd cmd = JSON.parseObject(json, SubmitAppointmentDraftCmd.class);
        String medicalLocId = tradeApplication.saveDraft(cmd);

    }

    @Test
    public void testExecuteActionByBuyNow() {
        CartInfoParam cartInfoParam = new CartInfoParam();
        cartInfoParam.setMethodId(74);
        cartInfoParam.setCartType(3);
        AddressUpdateParam addressUpdateParam = new AddressUpdateParam();
        addressUpdateParam.setId(1800242589L);
        List<SkuInfoParam> skuInfoParamList = new ArrayList<>();
        SkuInfoParam skuInfoParam = new SkuInfoParam();
        skuInfoParam.setSkuId(String.valueOf(100002642795L));
        skuInfoParam.setBuyNum(1);
        skuInfoParamList.add(skuInfoParam);

        OrderUserActionParam orderUserActionParam = new OrderUserActionParam();
        orderUserActionParam.setCartInfoParam(cartInfoParam);
        orderUserActionParam.setUa(2);
        orderUserActionParam.setUserActionType(UserActionEnum.BUY_NOW.getUserActionType());
        orderUserActionParam.setAddressUpdateParam(addressUpdateParam);
        orderUserActionParam.setSkuInfoParamList(skuInfoParamList);
        orderUserActionParam.setUserPin("jiesuantest-dpf");

        orderUserActionParam.setAppointmentTimeParam(new AppointmentTimeParam("2024-04-29 16:15", "2024-04-29 18:00", 2, true));
        orderUserActionParam.setUsp("true");

        System.out.println(JSON.toJSONString(tradeJsfExport.executeAction(orderUserActionParam)));
    }

    @Test
    public void testExecuteAction() {
        String actionParamStr = "{\"cartInfoQuery\":{\"cartExtInfo\":{\"cartType\":3,\"language\":\"zh_CN\",\"methodId\":74,\"siteId\":301,\"terminal\":1,\"tokenKey\":\"1\",\"tokenValue\":\"cart\",\"webOriginId\":2},\"cartExtInfoMap\":{\"tokenKey\":\"1\",\"cartType\":3,\"siteId\":301,\"methodId\":74,\"language\":\"zh_CN\",\"terminal\":1,\"tokenValue\":\"cart\",\"webOriginId\":2},\"tags\":[\"aloneMerge\",\"otcMerge\",\"overseaMerge\",\"preSaleOrder\",\"cfyhg\",\"djrh\",\"includeUnusableProduct\",\"showYuYueNoBuy\",\"yydsMerge\",\"useLsService\",\"mrCoupon\",\"stock\",\"zydp\",\"vsuitToMan\",\"useJdHomeService\",\"addPromotionLadders\",\"showYuYueNoBuy\",\"lbs\",\"limit\"]},\"paymentInfo\":{},\"ua\":2,\"userActionType\":\"PAYMENT_QUERY_LIST\",\"userPin\":\"jd_70d872cb293a4\"}";
        OrderUserActionParam orderUserActionParam = JSON.parseObject(actionParamStr, OrderUserActionParam.class);
        orderUserActionParam.setUserPin("jiesuantest-dpf");
        if (orderUserActionParam.getAddressUpdateParam() != null) {
            orderUserActionParam.getAddressUpdateParam().setId(1800242589L);
        }

        System.out.println(JSON.toJSONString(tradeJsfExport.executeAction(orderUserActionParam)));
    }

    @Test
    public void testSubmitOrder() {
        SubmitOrderParam submitOrderParam = new SubmitOrderParam();
        submitOrderParam.setUserPin("jiesuantest-dpf");
        submitOrderParam.setAppointmentTimeParam(new AppointmentTimeParam("2024-04-29 16:15", "2024-04-29 18:00", 2, true));
        submitOrderParam.setUsp("true");
        System.out.println(JSON.toJSONString(tradeJsfExport.submitOrder(submitOrderParam)));
    }

    @Test
    public void testQueryOrderInfo() {
        OrderInfoQueryContext orderInfoQueryContext = new OrderInfoQueryContext();
        orderInfoQueryContext.setUserPin("jiesuantest-dpf");
        orderInfoQueryContext.setOrderId("56357468002");
        System.out.println(JSON.toJSONString(orderInfoRpc.queryOrderInfo(orderInfoQueryContext)));
    }

    /**
     * 取消待支付订单
     *
     * @param cancelOrderParam
     * @return
     */
    @Test
    public Boolean cancelPayOrder(CancelOrderParam cancelOrderParam) {
        AssertUtils.nonNull(cancelOrderParam.getOrderId(), TradeErrorCode.ORDER_ID_NULL);
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(cancelOrderParam.getOrderId()).userPin(cancelOrderParam.getUserPin()).build());
        AssertUtils.nonNull(jdOrder, TradeErrorCode.ORDER_IS_NULL);

//        JdOrderContext jdOrderContext = TradeOrderConverter.INSTANCE.convertToJdOrderContext(jdOrder);
//        jdOrderContext.setJdOrder(jdOrder);
//        jdOrderContext.init(TradeEventTypeEnum.SELF_CANCEL_ORDER);
//        orderStatemachine.fireEvent(ResolveOrderConditionEnum.ORDER_WAIT_PAY, TradeEventTypeEnum.SELF_CANCEL_ORDER, jdOrderContext);
        // 更新订单状态
        jdOrder.setOrderStatus(OrderStatusEnum.ORDER_CANCEL.getStatus());
        jdOrderRepository.cancalOrderByOrderId(jdOrder);

        return Boolean.TRUE;
    }

    @Test
    public JdOrderDTO getOrderDetail(OrderDetailParam orderDetailParam) {
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(orderDetailParam.getOrderId())).userPin(orderDetailParam.getPin()).build());
        if (Objects.isNull(jdOrder)) {
            return null;
        }
        String orderId = orderDetailParam.getOrderId();
        Boolean isAddSkuItemLogic = jdOrder.getParentId() != 0L && Objects.nonNull(orderDetailParam.getQuerySource()) && orderDetailParam.getQuerySource().equals("C");
        //处理加项逻辑 传入的肯定是子单 先判断是否有父单
        if (isAddSkuItemLogic) {
            JdOrder jdOrderParent = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(jdOrder.getParentId()).userPin(orderDetailParam.getPin()).build());
            jdOrder.setOrderId(jdOrderParent.getOrderId());
            orderId = String.valueOf(jdOrderParent.getOrderId());
            log.info("TradeApplicationImpl.getOrderDetail.jdOrderParent={}", JSON.toJSONString(jdOrderParent));
            jdOrder.setOrderAmount(jdOrderParent.getOrderAmount());
            jdOrder.setOrderCoupon(jdOrderParent.getOrderCoupon());
            jdOrder.setOrderDiscount(jdOrderParent.getOrderDiscount());
        }
        List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(Long.valueOf(orderId));
        log.info("TradeApplicationImpl.getOrderDetail.jdOrderItems={}", JSON.toJSONString(jdOrderItems));
        JdOrderExt jdOrderExtDetail = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderDetailParam.getOrderId()), OrderExtTypeEnum.APPOINTMENT_INFO.getType());
        JdOrderExt jdOrderExtDetailFee = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderId), OrderExtTypeEnum.SERVICE_FEE_INFO.getType());
        //非快检模式-查询用户收货地址
        JdOrderExt orderAddressExtDetail = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderId), OrderExtTypeEnum.ORDER_ADDRESS.getType());
        List<JdOrderRefundTask> jdOrderRefundTaskList = new ArrayList<>();
        //处理加项逻辑
        if (isAddSkuItemLogic) {
            List<JdOrder> orders = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(Long.valueOf(orderId)).build());
            log.info("TradeApplicationImpl.getOrderDetail.orders={}", JSON.toJSONString(orders));
            for (JdOrder order : orders) {
                JdOrderRefundTask jdOrderRefundTask = new JdOrderRefundTask();
                jdOrderRefundTask.setOrderId(order.getOrderId());
                List<JdOrderRefundTask> jdOrderRefundTaskChildList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
                log.info("TradeApplicationImpl.getOrderDetail.order={},jdOrderRefundTaskChildList={}", JSON.toJSONString(order), JSON.toJSONString(jdOrderRefundTaskChildList));
                jdOrderRefundTaskList.addAll(jdOrderRefundTaskChildList);
            }
        } else {
            JdOrderRefundTask jdOrderRefundTask = new JdOrderRefundTask();
            jdOrderRefundTask.setOrderId(Long.valueOf(orderId));
            jdOrderRefundTaskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
        }
        log.info("TradeApplicationImpl.getOrderDetail.jdOrderRefundTaskList={}", JSON.toJSONString(jdOrderRefundTaskList));
        jdOrder.setJdOrderItemList(jdOrderItems);
        jdOrder.setJdOrderExtList(Arrays.asList(jdOrderExtDetail, orderAddressExtDetail));
        JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
        jdOrderDTO.setAddressInfo(JdOrderConverter.INSTANCE.getAddressInfoDTO(jdOrderExtDetail));
        jdOrderDTO.setAppointmentTimeDto(JdOrderConverter.INSTANCE.getAppointmentTimeDto(jdOrderExtDetail));
        jdOrderDTO.setJdOrderServiceFeeInfos(JdOrderConverter.INSTANCE.getJdOrderFeeDTO(jdOrderExtDetailFee));
        jdOrderDTO.setRefundAmount(JdOrderConverter.INSTANCE.getRefundAmount(jdOrderRefundTaskList));
        if (isAddSkuItemLogic) {
            jdOrderDTO.setOrderId(Long.valueOf(orderDetailParam.getOrderId()));
        }
        return jdOrderDTO;
    }

    /**
     * @param orderDetailParam
     * @return
     */
    @Test
    public JdOrderDTO getOrderItemAndFeeInfo(OrderDetailParam orderDetailParam) {
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(orderDetailParam.getOrderId())).userPin(orderDetailParam.getPin()).build());
        if (Objects.isNull(jdOrder)) {
            return null;
        }
        String orderId = orderDetailParam.getOrderId();
        Boolean isAddSkuItemLogic = jdOrder.getParentId() != 0L && Objects.nonNull(orderDetailParam.getQuerySource()) && orderDetailParam.getQuerySource().equals("C");
        //处理加项逻辑 传入的肯定是子单 先判断是否有父单
        if (isAddSkuItemLogic) {
            JdOrder jdOrderParent = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(jdOrder.getParentId()).userPin(orderDetailParam.getPin()).build());
            jdOrder.setOrderId(jdOrderParent.getOrderId());
            orderId = String.valueOf(jdOrderParent.getOrderId());
            log.info("TradeApplicationImpl.getOrderDetail.jdOrderParent={}", JSON.toJSONString(jdOrderParent));
            jdOrder.setOrderAmount(jdOrderParent.getOrderAmount());
        }
        List<JdOrderItem> jdOrderItems = jdOrderItemRepository.itemListByOrderId(Long.valueOf(orderId));
        log.info("TradeApplicationImpl.getOrderDetail.jdOrderItems={}", JSON.toJSONString(jdOrderItems));
        JdOrderExt jdOrderExtDetail = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderDetailParam.getOrderId()), OrderExtTypeEnum.APPOINTMENT_INFO.getType());
        JdOrderExt jdOrderExtDetailFee = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderId), OrderExtTypeEnum.SERVICE_FEE_INFO.getType());
        List<JdOrderRefundTask> jdOrderRefundTaskList = new ArrayList<>();
        //处理加项逻辑
        if (isAddSkuItemLogic) {
            List<JdOrder> orders = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(Long.valueOf(orderId)).build());
            log.info("TradeApplicationImpl.getOrderDetail.orders={}", JSON.toJSONString(orders));
            for (JdOrder order : orders) {
                JdOrderRefundTask jdOrderRefundTask = new JdOrderRefundTask();
                jdOrderRefundTask.setOrderId(order.getOrderId());
                List<JdOrderRefundTask> jdOrderRefundTaskChildList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
                log.info("TradeApplicationImpl.getOrderDetail.order={},jdOrderRefundTaskChildList={}", JSON.toJSONString(order), JSON.toJSONString(jdOrderRefundTaskChildList));
                jdOrderRefundTaskList.addAll(jdOrderRefundTaskChildList);
            }
        } else {
            JdOrderRefundTask jdOrderRefundTask = new JdOrderRefundTask();
            jdOrderRefundTask.setOrderId(Long.valueOf(orderId));
            jdOrderRefundTaskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(jdOrderRefundTask);
        }
        log.info("TradeApplicationImpl.getOrderDetail.jdOrderRefundTaskList={}", JSON.toJSONString(jdOrderRefundTaskList));
        jdOrder.setJdOrderItemList(jdOrderItems);
        jdOrder.setJdOrderExtList(Arrays.asList(jdOrderExtDetail));
        JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
        jdOrderDTO.setAddressInfo(JdOrderConverter.INSTANCE.getAddressInfoDTO(jdOrderExtDetail));
        jdOrderDTO.setAppointmentTimeDto(JdOrderConverter.INSTANCE.getAppointmentTimeDto(jdOrderExtDetail));
        jdOrderDTO.setJdOrderServiceFeeInfos(JdOrderConverter.INSTANCE.getJdOrderFeeDTO(jdOrderExtDetailFee));
        jdOrderDTO.setRefundAmount(JdOrderConverter.INSTANCE.getRefundAmount(jdOrderRefundTaskList));
        if (isAddSkuItemLogic) {
            jdOrderDTO.setOrderId(Long.valueOf(orderDetailParam.getOrderId()));
        }
        return jdOrderDTO;
    }

    /**
     * @param orderDetailParam
     * @return
     */
    @Test
    public JdOrderDTO getOrderSettleDetail(OrderDetailParam orderDetailParam) {
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(orderDetailParam.getOrderId())).userPin(orderDetailParam.getPin()).build());
        if (Objects.isNull(jdOrder)) {
            return null;
        }
        List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(jdOrder.getOrderId());
        if (CollUtil.isNotEmpty(jdOrderMoneyList)) {
            jdOrder.setJdOrderMoneyList(jdOrderMoneyList);
        } else {
        }
        JdOrderItem jdOrderItem = jdOrderItemRepository.findJdOrderItemDetail(jdOrder.getOrderId(), Long.parseLong(orderDetailParam.getServiceId()));
        jdOrder.setJdOrderItemList(Arrays.asList(jdOrderItem));
        JdOrderExt jdOrderExtDetailFee = jdOrderExtRepository.findJdOrderExtDetail(Long.valueOf(orderDetailParam.getOrderId()), OrderExtTypeEnum.SERVICE_FEE_SNAPSHOT.getType());
        JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
        if (Objects.nonNull(jdOrderExtDetailFee)) {
            jdOrderDTO.setServiceFeeSnapshot(jdOrderExtDetailFee.getExtContext());
        }
        return jdOrderDTO;
    }

    /**
     * @param orderId
     * @return
     */
    @Test
    public String findJdOrderExtContext(Long orderId) {
        JdOrderExt jdOrderExtDetailFee = jdOrderExtRepository.findJdOrderExtDetail(orderId, OrderExtTypeEnum.SERVICE_FEE_SNAPSHOT.getType());
        return jdOrderExtDetailFee == null ? "" : jdOrderExtDetailFee.getExtContext();
    }

    /**
     * @param orderDetailParam
     * @return
     */
    @Test
    public JdOrderDTO getSplitOrderSettleDetail(OrderDetailParam orderDetailParam) {
        List<JdOrder> jdOrderList = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(orderDetailParam.getParentId()).build());
        if (CollUtil.isEmpty(jdOrderList)) {
            JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderDetailParam.getParentId()).build());
            jdOrderList = Arrays.asList(jdOrder);
        }
        String settleSnapshot = "";
        if (Objects.nonNull(orderDetailParam.getPromiseId())) {
            settleSnapshot = jdServiceSettleApplication.findAngelWorkSettleSnapshot(orderDetailParam.getPromiseId());
        }
        for (JdOrder jdOrder : jdOrderList) {
            JdOrderItem jdOrderItem = jdOrderItemRepository.findJdOrderItemDetail(jdOrder.getOrderId(), Long.parseLong(orderDetailParam.getServiceId()));
            if (Objects.nonNull(jdOrderItem)) {
                jdOrder.setJdOrderItemList(Arrays.asList(jdOrderItem));
                List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(jdOrder.getOrderId());
                if (CollUtil.isEmpty(jdOrderMoneyList)) {
                }
                if (CollUtil.isNotEmpty(jdOrderMoneyList)) {
                    jdOrder.setJdOrderMoneyList(jdOrderMoneyList);
                }
                JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
                if (StringUtil.isNotBlank(settleSnapshot)) {
                    jdOrderDTO.setServiceFeeSnapshot(settleSnapshot);
                }
                return jdOrderDTO;
            }
        }
        return null;
    }

    /**
     * @param orderId
     * @return
     */
    @Test
    public List<JdOrderMoneyDTO> getJdOrderMoneyList(Long orderId) {
        List<JdOrder> jdOrderList = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(orderId).build());
        if (CollUtil.isEmpty(jdOrderList)) {
            JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderId).build());
            jdOrderList = Arrays.asList(jdOrder);
        }
        List<JdOrderMoney> jdOrderMoneyList = new ArrayList<>();
        for (JdOrder jdOrder : jdOrderList) {
            List<JdOrderMoney> childOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(jdOrder.getOrderId());
            if (CollUtil.isEmpty(childOrderMoneyList)) {
            } else {
                jdOrderMoneyList.addAll(childOrderMoneyList);
            }
        }

        return JdOrderConverter.INSTANCE.JdOrderMoneyToDTOList(jdOrderMoneyList);
    }

    /**
     * 可约时段
     *
     * @param param
     * @return
     */
    @Test
    public AvaiableAppointmentTimeDTO queryAvailableAppointmentTime(AvaiableAppointmentTimeParam param) {
        return this.queryAvaiableAppointmentTime(param);
    }

    /**
     * @param skuIds
     * @param areaFeeConfigId
     * @return
     */
    @Test
    public String getUpgrageAngelFee(Set<String> skuIds, Long areaFeeConfigId) {
        JdhAreaFeeConfig jdhAreaFeeConfig = jdhAreaFeeConfigRepository.find(new JdhAreaFeeConfigdentifier(areaFeeConfigId));
        String upgrageAngelFee = "";
        if (Objects.nonNull(jdhAreaFeeConfig) && StringUtil.isNotBlank(jdhAreaFeeConfig.getUpgrageAngelFee())) {
            String upgrageSkuList = jdhAreaFeeConfig.getUpgrageSkuList();
            if (StringUtils.isBlank(upgrageSkuList)) {
                upgrageAngelFee = jdhAreaFeeConfig.getUpgrageAngelFee();
            } else {
                for (String skuId : skuIds) {
                    if (!upgrageSkuList.contains(skuId)) {
                        return "";
                    }
                    upgrageAngelFee = jdhAreaFeeConfig.getUpgrageAngelFee();
                }
            }
        }
        return upgrageAngelFee;
    }

    /**
     * @param jdhSkuAreaFeeQuery
     * @return
     */
    @Test
    public String getUpgrageAngelFeeByParam(JdhSkuAreaFeeQuery jdhSkuAreaFeeQuery) {
        JdhAreaFeeConfigQuery jdhAreaFeeConfigQuery = new JdhAreaFeeConfigQuery();
        jdhAreaFeeConfigQuery.setProvinceCode(jdhSkuAreaFeeQuery.getProvinceCode());
        jdhAreaFeeConfigQuery.setChannelId(jdhSkuAreaFeeQuery.getChannelId());
        jdhAreaFeeConfigQuery.setServiceType(String.valueOf(jdhSkuAreaFeeQuery.getServiceType()));
        List<JdhAreaFeeConfig> jdhAreaFeeConfigList = jdhAreaFeeConfigRepository.queryJdhAreaFeeConfigList(jdhAreaFeeConfigQuery);
        if (CollUtil.isNotEmpty(jdhAreaFeeConfigList)) {
            Set<String> skuIds = jdhSkuAreaFeeQuery.getSkuIds();
            JdhAreaFeeConfig jdhAreaFeeConfig = jdhAreaFeeConfigList.get(0);
            for (String skuId : skuIds) {
                String upgrageSkuList = jdhAreaFeeConfig.getUpgrageSkuList();
                if (upgrageSkuList.contains(skuId)) {
                    return jdhAreaFeeConfig.getUpgrageAngelFee();
                }
            }
        }
        return "";
    }

    /**
     * 查询订单详情
     *
     * @param orderDetailParam
     */
    @Test
    public JdOrderDTO queryJdOrderDTO(OrderDetailParam orderDetailParam) {
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(orderDetailParam.getOrderId())).userPin(orderDetailParam.getPin()).build());
        if (Objects.isNull(jdOrder)) {
            return null;
        }
        JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
        return jdOrderDTO;
    }

    @Test
    public AvaiableAppointmentTimeDTO queryAvaiableAppointmentTime(AvaiableAppointmentTimeParam param) {
        buildParamByScene(param);
        Map<Long, JdhSkuDto> skuInfoMap = productApplication.queryJdhSkuInfoList(tradeApplicationConverter.convertJdhSkuListRequest(param));
        if (skuInfoMap == null || skuInfoMap.size() == 0) {
            log.error("TradeApplicationImpl queryAvaiableAppointmentTime 商品不存在 avaiableAppointmentTimeParam:{} ", JSONObject.toJSONString(param));
            return null;
        }
        /**判断业务类型，返回是否为骑手*/
        Boolean isKnight = isKnightLogicBySku(skuInfoMap);
        Boolean isCare = isCareLogicBySku(skuInfoMap);
        AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO = calcAppointmentTime(new ArrayList<>(skuInfoMap.values()), isCare);
        log.info("TradeApplicationImpl.queryAvaiableAppointmentTime.avaiableAppointmentTimeDTO={}", JSON.toJSONString(avaiableAppointmentTimeDTO));
        /**库存逻辑：step1.获取库存可预约时间列表*/
        List<TimeRangeDate> avaiableInventoryList = new ArrayList<>();
        Boolean isSelfTestTransport = this.isSelfTestTransport(skuInfoMap);
        if (isSelfTestTransport) {
            //非快检模式
            avaiableInventoryList = handleJdLogisticsDateLogic(param);
            if (CollectionUtils.isEmpty(avaiableInventoryList)) {
                log.error("TradeApplicationImpl queryAvaiableAppointmentTime 非快检模式 查询物流接口 返回可约时段为空!!!");
                return null;
            }
            //排序处理
            avaiableInventoryList = avaiableInventoryList.stream().sorted(Comparator.comparing(TimeRangeDate::getStartTime)).collect(Collectors.toList());
            TimeRangeDate maxAvailableTime = avaiableInventoryList.stream().max(Comparator.comparing(TimeRangeDate::getEndTime)).orElse(null);
        } else {
            log.info("TradeApplicationImpl.queryAvaiableAppointmentTime.avaiableInventoryList={} isSelfTestTransport={}", JSON.toJSONString(avaiableInventoryList), isSelfTestTransport);
            avaiableInventoryList = handleInventoryDateLogic(param, getServiceType(skuInfoMap, param.getUserPin()));
        }
        log.info("TradeApplicationImpl.queryAvaiableAppointmentTime.avaiableInventoryList={} avaiableInventoryList={}", JSON.toJSONString(avaiableInventoryList), JSON.toJSONString(avaiableAppointmentTimeDTO));

        // 不可约周期、节假日不可约等进行过滤
        filterUnableAppointmentList(avaiableInventoryList, skuInfoMap);
        log.info("unableAppointment avaiableInventoryList={}", JSON.toJSONString(avaiableInventoryList));

        /**step1.5.获取夜间服务费项数据*/
        AppointTimeServiceFeeDetail serviceFeeDetail = getAppointTimeServiceFeeDetail(param, skuInfoMap);
        log.info("serviceFeeDetail={}", JSON.toJSONString(serviceFeeDetail));
        /**step2.获取前端展示时间并与库存可预约时间取交集-进行打标*/
        Map<String, Map<String, List<AppointmentTimeRangeDTO>>> displayDate = getDisplayDate(avaiableAppointmentTimeDTO, avaiableInventoryList, isKnight, serviceFeeDetail, isCare);
        log.info("displayDate={}", JSON.toJSONString(displayDate));
        /**step3.过滤displayDate的map中时间key没有库存的日期*/
        filterDisplayDateByInventory(displayDate, avaiableInventoryList);
        log.info("filter displayDate={}", JSON.toJSONString(displayDate));
        /**step4.兼容老版本排期列表*/
        List<XfylAppointDateDTO> compatibleGroupDTO = new ArrayList<>();
        log.info("compatibleGroupDTO={} displayDate={}", JSON.toJSONString(compatibleGroupDTO), JSON.toJSONString(displayDate));
        /**step5.赋值返回结果*/
        avaiableAppointmentTimeDTO.setDisplayDate(displayDate);
        avaiableAppointmentTimeDTO.setCompatibleGroupDTO(compatibleGroupDTO);
//        log.info("TradeApplicationImpl queryAvaiableAppointmentTime.after.avaiableAppointmentTimeDTO={}", JsonUtil.toJSONString(avaiableAppointmentTimeDTO));
        if (param.getShowTimeType() != null && param.getShowTimeType().equals(CommonConstant.ONE)) {
            //非快检新增逻辑,根据前端出来的标识,过滤不可约时间
            avaiableAppointmentTimeDTO.filterShowTime(param.getShowTimeType());
        }
        buildRetByScene(param, avaiableAppointmentTimeDTO);
        return avaiableAppointmentTimeDTO;
    }

    /**
     * 通过场景构建必传参数
     *
     * @param param
     */
    @Test
    public void buildParamByScene(AvaiableAppointmentTimeParam param) {
        if (StringUtils.isBlank(param.getScene())) {
            return;
        }
        if (AvailableAppointmentTimeSceneEnum.ANGEL_MODIFY_DATE.getName().equalsIgnoreCase(param.getScene())) {
            String angelPin = UserPinContext.get();
            AssertUtils.hasText(param.getAngelWorkId(), "工单ID不允许为空");
            AngelWork work = angelWorkRepository.authorityFind(new AngelWorkIdentifier(Long.parseLong(param.getAngelWorkId())), angelPin);
            if (Objects.isNull(work)) {
                throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
            }
            JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(work.getPromiseId()).build());

            List<JdOrderItem> jdOrderItems = jdOrderItemRepository.listByOrderId(Long.valueOf(promise.getSourceVoucherId()));
            if (CollUtil.isNotEmpty(jdOrderItems)) {
                List<Long> lt = jdOrderItems.stream().filter(s -> (s != null && s.getSkuId() != null && s.getIsAdded() != null && CommonConstant.ZERO == s.getIsAdded())).map(JdOrderItem::getSkuId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(lt)) {
                    param.setSkuIds(lt);
                }
            } else {
                if (CollUtil.isNotEmpty(promise.getServices())) {
                    List<Long> lt = promise.getServices().stream().map(PromiseService::getServiceId).filter(Objects::nonNull).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(lt)) {
                        param.setSkuIds(lt);
                    }
                }
            }
            param.setAddressId(promise.getStore().getStoreId());
            param.setFullAddress(promise.getStore().getStoreAddr());
            param.setUserPin(promise.getUserPin());
            param.setAngelPin(angelPin);
        }
    }

    /**
     * 通过场景构建必传参数
     *
     * @param param
     */
    @Test
    public void buildRetByScene(AvaiableAppointmentTimeParam param, AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO) {
        if (StringUtils.isBlank(param.getScene())) {
            return;
        }
        if (AvailableAppointmentTimeSceneEnum.ANGEL_MODIFY_DATE.getName().equalsIgnoreCase(param.getScene())) {
            if (CollectionUtils.isNotEmpty(avaiableAppointmentTimeDTO.getCompatibleGroupDTO())) {
                // 查询护士排期
                AngelWorkDBQuery req = new AngelWorkDBQuery();
                req.setAngelPin(param.getAngelPin());
                req.setServiceStartTimeBegin(new Date());
                List<AngelWork> result = angelWorkRepository.findList(req);
                if (CollectionUtils.isEmpty(result)) {
                    return;
                }

                Map<String, List<AngelWork>> angelWorkAppointMap = new HashMap<>();
                DateFormat dayFormat = getAppointDateFormat();

                for (AngelWork angelWork : result) {
                    String startTime = dayFormat.format(angelWork.getWorkStartTime());
                    if (angelWorkAppointMap.containsKey(startTime)) {
                        angelWorkAppointMap.get(startTime).add(angelWork);
                    } else {
                        List<AngelWork> list = new ArrayList<>();
                        list.add(angelWork);
                        angelWorkAppointMap.put(startTime, list);
                    }
                }
                log.info("buildRetByScene angelWorkAppointMap={}", JSON.toJSONString(angelWorkAppointMap));
                for (XfylAppointDateDTO xfylAppointDateDTO : avaiableAppointmentTimeDTO.getCompatibleGroupDTO()) {
                    if (!angelWorkAppointMap.containsKey(xfylAppointDateDTO.getAppointDateDesc())) {
                        log.info("buildRetByScene !angelWorkAppointMap.containsKey {}", xfylAppointDateDTO.getAppointDateDesc());
                        continue;
                    }
                    for (XfylAppointDateTimeGroupDTO groupDTO : xfylAppointDateDTO.getAppointDateTimeGroupDTOList()) {
                        if (CollUtil.isEmpty(groupDTO.getDateTimeDTOList())) {
                            continue;
                        }
                        for (XfylAppointDateTimeDTO xfylAppointDateTimeDTO : groupDTO.getDateTimeDTOList()) {
                            Date startTime = TimeUtils.timeStrToDate(xfylAppointDateTimeDTO.getAppointmentStartTime(), TimeFormat.LONG_PATTERN_LINE_NO_S);
                            Date endTime = TimeUtils.timeStrToDate(xfylAppointDateTimeDTO.getAppointmentEndTime(), TimeFormat.LONG_PATTERN_LINE_NO_S);
                            log.info("buildRetByScene startTime {} endTime {}", startTime, endTime);
                            for (AngelWork angelWork : angelWorkAppointMap.get(xfylAppointDateDTO.getAppointDateDesc())) {
                                log.info("buildRetByScene angelWorkStartTime {} angelWorkEndTime {}", angelWork.getWorkStartTime(), angelWork.getWorkEndTime());
                                if ((angelWork.getWorkStartTime().compareTo(startTime) >= 0 && angelWork.getWorkStartTime().compareTo(endTime) < 0) || angelWork.getWorkEndTime().compareTo(startTime) > 0 && angelWork.getWorkEndTime().compareTo(endTime) <= 0) {
                                    xfylAppointDateTimeDTO.setTimeDescAppend("(已被预约)");
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 查询京东物流可约时间
     *
     * @param param
     * @return
     */
    @Test
    public List<TimeRangeDate> handleJdLogisticsDateLogic(AvaiableAppointmentTimeParam param) {
        List<TimeRangeDate> dates = new ArrayList<>();

        QueryAvailableTimeContext queryAvailableTimeContext = new QueryAvailableTimeContext();
        queryAvailableTimeContext.setSenderFullAddress(param.getFullAddress());

        if (param.getPromiseId() != null) {
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(param.getPromiseId()).build());
            if (CollectionUtils.isNotEmpty(medicalPromises)) {
                queryAvailableTimeContext.setReceiverFullAddress(medicalPromises.get(0).getStationAddress());
            }
        }
        CommonCheckPreCreateOrderBo commonCheckPreCreateOrderBo = angelShipDomainService.queryAvailableTime(queryAvailableTimeContext);
        if (commonCheckPreCreateOrderBo == null || CollectionUtils.isEmpty(commonCheckPreCreateOrderBo.getPickupSliceTimes())) {
            log.info("handleJdLogisticsDateLogic commonCheckPreCreateOrderBo为空");
            return dates;
        }
        commonCheckPreCreateOrderBo.getPickupSliceTimes().forEach(t -> {
            if (CollectionUtils.isEmpty(t.getPickupSliceTimes())) {
                return;
            }
            t.getPickupSliceTimes().forEach(m -> {
                Date dayOfTomorrow = TimeUtils.getDateStart(TimeUtils.add(new Date(), 2, Calendar.DAY_OF_MONTH));
                Date d = TimeUtils.strToDate(t.getDateKey());
                if (d.before(dayOfTomorrow)) {
                    //按产品要求,排除掉后天的时间
                    dates.add(TimeRangeDate.builder().startTime(TimeUtils.timeStrToDate(t.getDateKey() + " " + m.getStartTime(), TimeFormat.LONG_PATTERN_LINE)).endTime(TimeUtils.timeStrToDate(t.getDateKey() + " " + m.getEndTime(), TimeFormat.LONG_PATTERN_LINE)).build());
                }
            });
        });
        return dates;
    }

    /**
     * 是否非快检模式
     *
     * @param skuInfoMap
     * @return
     */
    @Test
    public Boolean isSelfTestTransport(Map<Long, JdhSkuDto> skuInfoMap) {
        List<JdhSkuDto> collect = new ArrayList<>(skuInfoMap.values());
        JdhSkuDto jdhSkuDto = collect.get(0);
        return ServiceTypeNewEnum.TRANSPORT_TEST.getType().equals(jdhSkuDto.getServiceType());

    }

    /**
     * @param param
     * @param skuInfoMap
     * @return
     */
    @Test
    public AppointTimeServiceFeeDetail getAppointTimeServiceFeeDetail(AvaiableAppointmentTimeParam param, Map<Long, JdhSkuDto> skuInfoMap) {
        if (StringUtils.isBlank(param.getUserPin()) || StringUtils.isBlank(param.getAddressId())) {
            return null;
        }
        try {
            CalcOrderServiceFeeContext context = new CalcOrderServiceFeeContext();
            // 获取地址信息
            AddressDetailBO addressDetail = new AddressDetailBO();
            AddressInfoValueObject addressInfo = new AddressInfoValueObject();
            addressInfo.setTownId(addressDetail.getTownId());
            addressInfo.setCountyId(addressDetail.getCountyId());
            addressInfo.setCityId(addressDetail.getCityId());
            addressInfo.setProvinceId(addressDetail.getProvinceId());
            context.setAddressInfo(addressInfo);
            context.setFeeType(JdOrderFeeTypeEnum.NIGHT_SERVICE_FEE.getType());
            //增强垂直业务身份以及用于动态计算价格
            AppointTimeServiceFeeDetail serviceFeeDetail = jdhOrderDomainService.calcOrderAppointTimeServiceFee(context);
            return serviceFeeDetail;
        } catch (Exception e) {
            log.warn("TradeApplicationImpl getAppointTimeServiceFeeDetail error", e);
            return null;
        }
    }

    @Test
    public Boolean isKnightLogicBySku(Map<Long, JdhSkuDto> skuInfoMap) {
        List<JdhSkuDto> collect = new ArrayList<>(skuInfoMap.values());
        JdhSkuDto jdhSkuDto = collect.get(0);
        if (duccConfig.getAngelStationInventoryConfig() != null && CollectionUtils.isNotEmpty(duccConfig.getAngelStationInventoryConfig().getServiceType())) {
            return duccConfig.getAngelStationInventoryConfig().getServiceType().contains(jdhSkuDto.getServiceType());
        } else {
            return ServiceTypeNewEnum.KNIGHT_TEST.getType().equals(jdhSkuDto.getServiceType());
        }
    }

    @Test
    public Boolean isCareLogicBySku(Map<Long, JdhSkuDto> skuInfoMap) {
        List<JdhSkuDto> collect = new ArrayList<>(skuInfoMap.values());
        JdhSkuDto jdhSkuDto = collect.get(0);
        return ServiceTypeNewEnum.isHomeCare(jdhSkuDto.getServiceType());
    }

    @Test
    public Integer getServiceType(Map<Long, JdhSkuDto> skuInfoMap, String userPin) {
        List<JdhSkuDto> collect = new ArrayList<>(skuInfoMap.values());
        List<Long> skuIds = collect.stream().map(JdhSkuDto::getSkuId).collect(Collectors.toList());
        if (StringUtils.isNotBlank(userPin)) {
            String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.EXECUTE_ACTION_BUY_NOW_PIN_SKU_LIST_KEY, userPin);
            OrderUserActionDTO orderUserActionDTO = JsonUtil.parseObject(jimClient.get(redisKey), OrderUserActionDTO.class);
            if (Objects.nonNull(orderUserActionDTO) && Objects.nonNull(orderUserActionDTO.getServiceType())) {
                List<SkuItemDTO> skuItemDtoList = orderUserActionDTO.getBundleInfoDTOList().get(0).getSkuItemDTOList();
                if (CollectionUtils.isNotEmpty(skuItemDtoList) && skuItemDtoList.stream().map(s -> Long.parseLong(s.getId())).collect(Collectors.toList()).containsAll(skuIds)) {
                    return Integer.parseInt(orderUserActionDTO.getServiceType());
                }
            }
        }
        JdhSkuDto jdhSkuDto = collect.get(0);
        return jdhSkuDto.getServiceType();
    }

    /**
     * 过滤map中时间key没有库存的日期
     *
     * @param displayDate
     * @param availableInventoryList
     */
    @Test
    public void filterDisplayDateByInventory(Map<String, Map<String, List<AppointmentTimeRangeDTO>>> displayDate, List<TimeRangeDate> availableInventoryList) {
        Set<String> availableInventorySet = new HashSet<>();
        DateFormat dayFormat = getAppointDateFormat();
        Optional.ofNullable(availableInventoryList).map(List::stream).orElseGet(Stream::empty).forEach(date -> {
            availableInventorySet.add(dayFormat.format(date.getStartTime()));
        });
        if (CollectionUtils.isNotEmpty(availableInventorySet)) {
            List<String> planRemoveKey = new ArrayList<>();
            for (String dateString : displayDate.keySet()) {
                if (!availableInventorySet.contains(dateString)) {
                    planRemoveKey.add(dateString);
                }
            }
            planRemoveKey.forEach(s -> {
                displayDate.remove(s);
            });
        } else {
            displayDate.clear();
        }
    }

    /**
     * 不可约周期、节假日不可约
     *
     * @param avaiableInventoryList
     * @param skuInfoMap
     */
    @Test
    public void filterUnableAppointmentList(List<TimeRangeDate> avaiableInventoryList, Map<Long, JdhSkuDto> skuInfoMap) {
        try {
            if (CollectionUtils.isEmpty(avaiableInventoryList)) {
                log.info("filterUnableAppointmentList avaiableInventoryList empty");
                return;
            }
            // 不可约周期
            Set<String> unableAppointmentPeriodSet = new HashSet<>();
            // 节假日不可约
            Set<String> unableAppointmentHolidaySet = new HashSet<>();
            for (JdhSkuDto sku : new ArrayList<>(skuInfoMap.values())) {
                if (CollectionUtils.isNotEmpty(sku.getUnableAppointmentPeriod())) {
                    unableAppointmentPeriodSet.addAll(sku.getUnableAppointmentPeriod());
                }
                if (CollectionUtils.isNotEmpty(sku.getUnableAppointmentHoliday())) {
                    unableAppointmentHolidaySet.addAll(sku.getUnableAppointmentHoliday());
                }
            }
            log.info("filterUnableAppointmentList unableAppointmentPeriodSet={}, unableAppointmentHolidaySet={}", JSON.toJSONString(unableAppointmentPeriodSet), JSON.toJSONString(unableAppointmentHolidaySet));

            // 不可约周期过滤
            if (CollectionUtils.isNotEmpty(unableAppointmentPeriodSet)) {
                Iterator<TimeRangeDate> iterator = avaiableInventoryList.iterator();
                while (iterator.hasNext()) {
                    TimeRangeDate date = iterator.next();
                    String dayOfWeekEn = TimeUtils.getDayOfWeekEn(date.getStartTime());
                    if (unableAppointmentPeriodSet.contains(dayOfWeekEn)) {
                        log.info("filterUnableAppointmentList unableAppointmentPeriod remove date={}, dayOfWeekEn={}", date.getStartTime(), dayOfWeekEn);
                        iterator.remove();
                    }
                }
                log.info("filterUnableAppointmentList period iterator avaiableInventoryList={}", JSON.toJSONString(avaiableInventoryList));
            }

            // 节假日不可约
            if (CollectionUtils.isNotEmpty(unableAppointmentHolidaySet)) {
                // 获取排期日历
                List<CompanyCalendarBo> companyCalendarList = this.companyCalendarInfo();
                List<String> unableHolidaySet = JSON.parseArray(duccConfig.getUnableAppointmentHoliday(), DictInfoDto.class).stream().filter(u -> unableAppointmentHolidaySet.contains(u.getValue())).map(DictInfoDto::getLabel).collect(Collectors.toList());
                log.info("filterUnableAppointmentList unableHolidaySet={}", JSON.toJSONString(unableHolidaySet));
                List<String> companyCalendarDateList = companyCalendarList.stream().filter(c -> StringUtils.isNotBlank(c.getHolidayName()) && unableHolidaySet.contains(c.getHolidayName()) && Arrays.asList(HolidayTypeNewEnum.PUBLIC_HOLIDAY.getType(), HolidayTypeNewEnum.REST_DAY.getType()).contains(c.getType())).map(CompanyCalendarBo::getDate).collect(Collectors.toList());
                log.info("filterUnableAppointmentList companyCalendarDateList={}", JSON.toJSONString(companyCalendarDateList));

                Iterator<TimeRangeDate> holidayIterator = avaiableInventoryList.iterator();
                while (holidayIterator.hasNext()) {
                    TimeRangeDate date = holidayIterator.next();
                    String dateStr = TimeUtils.dateTimeToStr(date.getStartTime(), TimeFormat.SHORT_PATTERN_LINE);
                    if (companyCalendarDateList.contains(dateStr)) {
                        log.info("filterUnableAppointmentList date={}, companyCalendarDateList={}", date.getStartTime(), JSON.toJSONString(companyCalendarDateList));
                        holidayIterator.remove();
                    }
                }
            }
        } catch (Exception e) {
            log.error("TradeApplicationImpl filterUnableAppointmentList error e", e);
        }
    }

    /**
     * 获取排期日历
     *
     * @return
     */
    @Test
    public List<CompanyCalendarBo> companyCalendarInfo() {
        RedisKeyEnum redisKeyEnum = RedisKeyEnum.O2O_TRADE_AVAILABLE_APPOINTMENT_CALENDAR;
        String key = redisKeyEnum.getRedisKeyPrefix();
        if (StringUtils.isNotBlank(jimClient.get(key))) {
            log.info("TradeApplicationImpl companyCalendarInfo cache");
            return JSON.parseArray(jimClient.get(key), CompanyCalendarBo.class);
        }

        List<CompanyCalendarBo> companyCalendarList = new ArrayList<>();

        // 根据考勤组、工作计划、日期查询工作日程表
        QueryCompanyCalendarBo currYearCompanyCalendarBo = new QueryCompanyCalendarBo();
        // 获取当年第一天
        currYearCompanyCalendarBo.setStartDate(LocalDate.of(LocalDate.now().getYear(), 1, 1).format(DateTimeFormatter.ISO_DATE));
        // 获取当年最后一天
        currYearCompanyCalendarBo.setEndDate(LocalDate.of(LocalDate.now().getYear(), 12, 31).format(DateTimeFormatter.ISO_DATE));
        List<CompanyCalendarBo> currYearCompanyCalendarList = companyCalendarRpc.getCalendarByGroupAndTime(currYearCompanyCalendarBo);
        companyCalendarList.addAll(currYearCompanyCalendarList);


        QueryCompanyCalendarBo nextYearCompanyCalendarBo = new QueryCompanyCalendarBo();
        nextYearCompanyCalendarBo.setStartDate(LocalDate.of(LocalDate.now().getYear() + 1, 1, 1).format(DateTimeFormatter.ISO_DATE));
        nextYearCompanyCalendarBo.setEndDate(LocalDate.of(LocalDate.now().getYear() + 1, 12, 31).format(DateTimeFormatter.ISO_DATE));
        List<CompanyCalendarBo> nextYearCompanyCalendarList = companyCalendarRpc.getCalendarByGroupAndTime(nextYearCompanyCalendarBo);
        companyCalendarList.addAll(nextYearCompanyCalendarList);


        List<CompanyCalendarBo> totalList = new ArrayList<>();
        for (CompanyCalendarBo companyCalendar : companyCalendarList) {
            // 国庆节、中秋节
            if (companyCalendar.getHolidayName().contains("、")) {
                String[] split = companyCalendar.getHolidayName().split("、");
                for (String spl : split) {
                    CompanyCalendarBo targetCompanyCalendar = JSON.parseObject(JSON.toJSONString(companyCalendar), CompanyCalendarBo.class);
                    targetCompanyCalendar.setHolidayName(spl);
                    totalList.add(targetCompanyCalendar);
                }
            } else {
                totalList.add(companyCalendar);
            }
        }
        log.info("TradeApplicationImpl companyCalendarInfo totalList={}", JSON.toJSONString(totalList));

        jimClient.setEx(key, JSON.toJSONString(totalList), redisKeyEnum.getExpireTime(), TimeUnit.DAYS);

        //totalList.addAll(JSON.parseArray(duccConfig.getFestivalHolidayConfig(), CompanyCalendarBo.class));
        return totalList;
    }

    /**
     * 排期格式化
     *
     * @return
     */
    @Test
    public DateFormat getAppointDateFormat() {
        return new SimpleDateFormat("M月d日[E]");
    }

    @Test
    public Map<String, Map<String, List<AppointmentTimeRangeDTO>>> getDisplayDate(AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO, List<TimeRangeDate> availableInventoryList, Boolean isKnight, AppointTimeServiceFeeDetail serviceFeeDetail, Boolean isCare) {
        //查询可用列表的时间窗口
        Integer o2oAvailableTimeWindow = duccConfig.getO2oAvailableTimeWindow();
        Map<String, Map<String, List<AppointmentTimeRangeDTO>>> result = new LinkedHashMap<>();
        List<AppointmentTimeRangeDTO> avaiableDateList = avaiableAppointmentTimeDTO.getAvaiableDateList(isCare, o2oAvailableTimeWindow);
        Calendar calendar = Calendar.getInstance();
        DateFormat dayFormat = getAppointDateFormat();
        DateFormat ampmFormat = new SimpleDateFormat("a");
        Map<String, List<AppointmentTimeRangeDTO>> dayGroup = avaiableDateList.stream().collect(Collectors.groupingBy(s -> dayFormat.format(s.getBeginTime()), LinkedHashMap::new, Collectors.toList()));
        dayGroup.forEach((k1, v1) -> {
            Map<String, List<AppointmentTimeRangeDTO>> ampmMap = new LinkedHashMap<>();
            v1.stream().collect(Collectors.groupingBy(s -> ampmFormat.format(s.getBeginTime()), LinkedHashMap::new, Collectors.toList())).forEach((k2, v2) -> {
                List<AppointmentTimeRangeDTO> ampmList = new ArrayList<>();
                v2.forEach(e -> {
                    calendar.setTime(e.getBeginTime());
                    // 整点场景的结束时间为下一个小时整点，非整点场景的结束时间为下两个小时整点
                    if (isCare) {
                        if (Objects.nonNull(e.getIsImmediately()) && e.getIsImmediately()) {
                            calendar.add(Calendar.HOUR_OF_DAY, 2);
                        } else {
                            calendar.add(Calendar.HOUR_OF_DAY, 1);
                        }
                    } else {
                        calendar.add(Calendar.HOUR_OF_DAY, 1);
                        if (calendar.get(Calendar.MINUTE) != 0) {
                            calendar.set(Calendar.MINUTE, 0);
                            calendar.set(Calendar.SECOND, 0);
                        }
                    }
                    //2025-02-19 可下单时间优化需求改动。如果结束时间跨天，在第二天0-1点之内，默认设置为前一天23:59
                    if (calendar.get(Calendar.HOUR_OF_DAY) == 0) {
                        calendar.set(Calendar.HOUR_OF_DAY, 0);
                        calendar.set(Calendar.MINUTE, 0);
                        calendar.set(Calendar.SECOND, 0);
                        calendar.add(Calendar.SECOND, -1);
                    }
                    e.setEndTime(calendar.getTime());
                    //增强库存是否支持预约
                    enhanceInventoryIsAvailable(availableInventoryList, e);
                    //夜间服务费
                    if (Objects.nonNull(serviceFeeDetail)) {
                        LocalTime beginTime = LocalTime.parse(e.getBeginTimeLocalTimeStr(), DateTimeFormatter.ofPattern("HH:mm"));
                        LocalTime endTime = beginTime.plusHours(1).withMinute(0).withSecond(0).withNano(0);
                        //处理开始时间为23:00的情况，加一小时后endtime变为0:00，需改为23:59
                        if (endTime.getHour() < beginTime.getHour() && endTime.getHour() == 0) {
                            endTime = endTime.plusMinutes(-1);
                        }
                        Optional<List<TimeIntervalIntersection.TimeInterval>> optional = TimeIntervalIntersection.groupCalculateIntersection(new TimeIntervalIntersection.TimeInterval(beginTime, endTime), serviceFeeDetail.getTimeIntervalList());
                        e.setNightDoorFee(serviceFeeDetail.getFee());
                        e.setIsNightService(optional.isPresent());
                    }
                    ampmList.add(e);
                });
                ampmMap.put(k2, ampmList);
            });
            result.put(k1, ampmMap);
        });
//        log.info("tradeApplicationImpl.getDisplayDate.result={}",JsonUtil.toJSONString(result));
        return result;
    }

    /***
     * 库存是否支持预约
     * @param availableInventoryList
     * @param appointmentTimeRangeDTO
     * @return
     */
    @Test
    public void enhanceInventoryIsAvailable(List<TimeRangeDate> availableInventoryList, AppointmentTimeRangeDTO appointmentTimeRangeDTO) {
        if (CollectionUtils.isEmpty(availableInventoryList)) {
            appointmentTimeRangeDTO.setIsAvailable(false);
        }
        if (log.isDebugEnabled()) {
            log.debug("TradeApplicationImpl.enhanceInventoryIsAvailable.appointmentTimeRangeDTO={}", JsonUtil.toJSONString(appointmentTimeRangeDTO));
        }
        if (Objects.nonNull(appointmentTimeRangeDTO)) {
            //需要注意如果不是立即预约的话，需要将date类型向上取整秒 否则会出现 2024-06-19 19:00:00.018 毫秒值 无法进行时间范围的框定 开始时间向上取整 结束时间向下取整
            if (Objects.nonNull(appointmentTimeRangeDTO.getIsImmediately()) && !appointmentTimeRangeDTO.getIsImmediately()) {
                appointmentTimeRangeDTO.setBeginTime(TimeUtils.roundUpToSecond(appointmentTimeRangeDTO.getBeginTime()));
            }
            appointmentTimeRangeDTO.setEndTime(TimeUtils.floorDateToMinute(appointmentTimeRangeDTO.getEndTime()));

            if (log.isDebugEnabled()) {
                log.debug("TradeApplicationImpl.enhanceInventoryIsAvailable.appointmentTimeRangeDTO.before={}", JsonUtil.toJSONString(appointmentTimeRangeDTO));
            }
            appointmentTimeRangeDTO.setIsAvailable(TimeUtils.isWithinTimeRanges(tradeApplicationConverter.convertTimeRangeList(availableInventoryList), appointmentTimeRangeDTO.getBeginTime(), appointmentTimeRangeDTO.getEndTime()));
            if (log.isDebugEnabled()) {
                log.debug("TradeApplicationImpl.enhanceInventoryIsAvailable.appointmentTimeRangeDTO.after={}", JsonUtil.toJSONString(appointmentTimeRangeDTO));
            }
        }

    }

    /**
     * 处理库存逻辑
     *
     * @param param
     * @return
     */
    @Test
    public List<TimeRangeDate> handleInventoryDateLogic(AvaiableAppointmentTimeParam param, Integer serviceType) {
        QueryBatchSkuInventoryRequest request = new QueryBatchSkuInventoryRequest();
        request.setAngelType(AngelTypeEnum.DELIVERY.getType());
        request.setSkuNos(new HashSet<>(param.getSkuIds()));
        List<AddressDetail> addressList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getAddressId()) || StringUtils.isNotBlank(param.getFullAddress())) {
            AddressDetail addressDetail = new AddressDetail();
            addressDetail.setAddressId(param.getAddressId());
            addressDetail.setFullAddress(param.getFullAddress());
            addressList.add(addressDetail);
        }
        request.setAddressList(addressList);
        if (Integer.valueOf("1").equals(serviceType)) {
            request.setInventoryChannelNo(1);
        } else if (Integer.valueOf("2").equals(serviceType)) {
            request.setInventoryChannelNo(2);
        } else if (Integer.valueOf("3").equals(serviceType)) {
            request.setInventoryChannelNo(3);
        } else if (Integer.valueOf("5").equals(serviceType)) {
            request.setInventoryChannelNo(3);
        }
        log.info("TradeApplicationImpl.handleInventoryLogic.request={}", JSON.toJSONString(request));
        SkuInventoryResultDto skuInventoryResultDto;
        skuInventoryResultDto = stationApplication.querySkuInventory(request);
        log.info("TradeApplicationImpl.handleInventoryLogic.skuInventoryResultDto={}", JSON.toJSONString(skuInventoryResultDto));
        List<TimeRangeDate> availableDates = skuInventoryResultDto.getAvailableDates();
        log.info("TradeApplicationImpl.handleInventoryLogic.availableDates={}", JSON.toJSONString(availableDates));
        return availableDates;
    }

    /**
     * 计算服务费
     *
     * @param cmd cmd
     * @return {@link List}<{@link TradeServiceFeeInfoDTO}>
     */
    @Test
    public List<TradeServiceFeeInfoDTO> calcServiceFee(CalcTradeServiceFeeCmd cmd) {
        log.info("TradeApplicationImpl calcServiceFee cmd={}", JSON.toJSONString(cmd));
        CalcOrderServiceFeeContext context = serviceFeeApplicationConverter.calcServiceFeeCmd2Context(cmd);
        //增强垂直业务身份以及用于动态计算价格
        List<JdOrderServiceFeeInfo> jdOrderServiceFeeInfos = jdhOrderDomainService.calcOrderServiceFee(context);
        return serviceFeeApplicationConverter.feeList2DtoList(jdOrderServiceFeeInfos);
    }

    /**
     * 申请退款
     *
     * @param param
     * @return
     */
    @Test
    public Boolean xfylOrderRefund(RefundOrderParam param) {
        log.info("TradeApplicationImpl xfylOrderRefund RefundOrderParam:{} ", JSON.toJSONString(param));
        //查询订单信息
        JdOrder orderDetail = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(Long.valueOf(param.getOrderId())).userPin(param.getUserPin()).build());
        AssertUtils.nonNull(orderDetail, TradeErrorCode.ORDER_IS_NULL);
        //判断当前订单是子单 还是父单 如果是加项场景，非小程序直接查询订单传入的是父单，其余场景传入的是子单
        boolean noParentOrder = orderDetail.getParentId().intValue() == 0;
        //判断当前订单来源是C端还是运营端
        boolean isRequestByC = Objects.nonNull(param.getRefundSource()) && (param.getRefundSource().equals(CommonConstant.ONE_STR) || param.getRefundSource().equals(CommonConstant.THREE_STR));
        // (C端发起退款 且 订单包含加项)时，将父单下其他子单一并申请退款
        if (isRequestByC) {
            List<JdOrder> childOrders;
            if (noParentOrder) {//不拆单
                childOrders = this.getChildOrderList(orderDetail.getOrderId());
                if (HasAddedEnum.HAS_ADDED.getValue().equals(orderDetail.getHasAdded())) {
                    addItemOrderRefund(param, childOrders, !isRequestByC);
                } else {
                    childOrders.forEach(s -> {
                        param.setOrderId(s.getOrderId());
                        buildRefundTask(s, param);
                    });
                }
            } else {
                //拆单有子单
                childOrders = this.getChildOrderList(orderDetail.getParentId());
                JdOrder parentOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderDetail.getParentId()).userPin(param.getUserPin()).build());
                if (HasAddedEnum.HAS_ADDED.getValue().equals(parentOrder.getHasAdded())) {
                    addItemOrderRefund(param, childOrders, isRequestByC);
                    return true;
                } else {
                    this.buildRefundTask(orderDetail, param);
                }
            }

        } else {//如果是运营端 传啥就退啥
            if (!noParentOrder) {
                JdOrder parentOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderDetail.getParentId()).userPin(param.getUserPin()).build());
                if (HasAddedEnum.HAS_ADDED.getValue().equals(parentOrder.getHasAdded())) {
                    List<JdOrder> childOrders = this.getChildOrderList(orderDetail.getParentId());
                    addItemOrderRefund(param, childOrders, isRequestByC);
                    return true;
                } else {
                    // 非加项包正常退款
                    this.buildRefundTask(orderDetail, param);
                }
            } else {
                // 非加项包正常退款
                this.buildRefundTask(orderDetail, param);
            }
        }
        return true;
    }

    @Test
    public void addItemOrderRefund(RefundOrderParam param, List<JdOrder> childOrders, Boolean isRequestByC) {
        BigDecimal refundAmount = param.getRefundAmount();
        AtomicReference<BigDecimal> subtractAmount = new AtomicReference<>(refundAmount);
        param.setHasAdded(Boolean.TRUE);
        Iterator<JdOrder> iterator = childOrders.iterator();
        while (iterator.hasNext()) {
            JdOrder childOrder = iterator.next();
            BigDecimal orderAmount = childOrder.getOrderAmount();
            if (HasAddedEnum.HAS_ADDED.getValue().equals(childOrder.getHasAdded())) {
                param.setFreezeAndInvalid(Boolean.FALSE);
            } else {
                param.setFreezeAndInvalid(Boolean.TRUE);
            }
            if (!isRequestByC) {
                if (subtractAmount.get().compareTo(orderAmount) > 0) {
                    param.setRefundAmount(orderAmount);
                    subtractAmount.set(subtractAmount.get().subtract(orderAmount));
                } else {
                    param.setRefundAmount(subtractAmount.get());
                    subtractAmount.set(BigDecimal.ZERO);
                }
            }
            if (!iterator.hasNext()) {
                param.setLastChildOrder(Boolean.TRUE);
            }
            param.setOrderId(childOrder.getOrderId());
            buildRefundTask(childOrder, param);
        }
    }

    /**
     * @param orderId
     * @return
     */
    @Test
    public List<JdOrder> getChildOrderList(Long orderId) {
        List<JdOrder> childOrders = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(orderId).build());
        if (CollectionUtils.isEmpty(childOrders)) {
            childOrders = jdOrderRepository.findOrderListByOrderId(JdOrderIdentifier.builder().orderId(orderId).build());
        }
        return childOrders;
    }

    /**
     * 生成退款任务
     *
     * @param orderDetail
     * @param param
     */
    @Test
    public void buildRefundTask(JdOrder orderDetail, RefundOrderParam param) {
        String lockKey = MessageFormat.format(RedisKeyEnum.JDH_ORDER_REFUND_LOCK_KEY.getRedisKeyPrefix(), orderDetail.getOrderId());
        try {
            log.info("[TradeApplicationImpl.buildRefundTask],param={}", JSON.toJSONString(param));
            boolean lockFlag = redisLockUtil.tryLock(lockKey, cn.hutool.core.lang.UUID.fastUUID().toString(), RedisKeyEnum.JDH_ORDER_REFUND_LOCK_KEY.getExpireTime(), RedisKeyEnum.JDH_ORDER_REFUND_LOCK_KEY.getExpireTimeUnit());
            if (!lockFlag) {
                throw new BusinessException(TradeErrorCode.REFUND_SUBMIT);
            }
            checkOrderMoneyDetail(orderDetail);
            calcPromiseRefundFreeze(param);
            selfOrderRefund(param, orderDetail);
        } catch (Exception e) {
            log.error("[TradeApplicationImpl.buildRefundTask],Exception=", e);
            throw e;
        } finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * @param orderDetail
     */
    @Test
    public void checkOrderMoneyDetail(JdOrder orderDetail) {
        if (orderDetail.getOrderAmount().compareTo(BigDecimal.ZERO) > 0) {
            Long orderId = orderDetail.getOrderId();
            List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findJdOrderMoneyList(orderId);
            if (CollUtil.isEmpty(jdOrderMoneyList)) {
                jdOrderMoneyRepository.batchSave(jdOrderMoneyList);
            }
            AssertUtils.isNotEmpty(jdOrderMoneyList, TradeErrorCode.QUERY_REFUND_PAY_TYPE_NULL);
        }
    }

    /**
     * @param param
     * @return
     */
    @Test
    public Boolean calcPromiseRefundFreeze(RefundOrderParam param) {
        List<Long> promisePatientIdList = new ArrayList<>();
        List<String> serviceIdList = new ArrayList<>();
        Integer refundType = param.getRefundType();
        if (RefundTypeEnum.ORDER_REFUND.getType().equals(refundType)) {
            if (param.getHasAdded()) {
                List<OrderRefundTaskDto> list = jdOrderRefundApplication.queryAddOrderRefundRecord(Long.valueOf(param.getOrderId()));
                if (CollUtil.isNotEmpty(list)) {
                    throw new BusinessException(TradeErrorCode.ORDER_REFUND_EXIST);
                }
            } else {
                List<OrderRefundTaskDto> list = jdOrderRefundApplication.queryOrderRefundRecord(Long.valueOf(param.getOrderId()));
                if (CollUtil.isNotEmpty(list)) {
                    throw new BusinessException(TradeErrorCode.ORDER_REFUND_EXIST);
                }
            }
            param.setLastChildOrder(true);
        } else if (RefundTypeEnum.AMOUNT_REFUND.getType().equals(refundType)) {
            BigDecimal refundAmount = param.getRefundAmount();
            if (Objects.isNull(refundAmount) || refundAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BusinessException(TradeErrorCode.REFUND_AMOUNT_NULL);
            }
            if (param.getFreezeAndInvalid()) {
                List<RefundOrderSku> refundOrderSkuList = param.getRefundOrderSkuList();
                AssertUtils.isNotEmpty(refundOrderSkuList, TradeErrorCode.ORDER_REFUND_PROMISEPATIENTID);
                promisePatientIdList = refundOrderSkuList.stream().map(RefundOrderSku::getPromisePatientId).collect(Collectors.toList());
                serviceIdList = refundOrderSkuList.stream().map(RefundOrderSku::getServiceId).collect(Collectors.toList());
                String serviceId = serviceIdList.get(0);
                MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
                medicalPromiseListRequest.setPromiseId(param.getPromiseId());
                List<MedicalPromiseDTO> medicalPromises = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
                List<Long> finalPromisePatientIdList1 = promisePatientIdList;

                List<MedicalPromiseDTO> selfMedicalPromisesList = medicalPromises.stream().filter(medical -> serviceId.equals(medical.getServiceId().toString()) && finalPromisePatientIdList1.contains(medical.getPromisePatientId())).collect(Collectors.toList());

                selfMedicalPromisesList = selfMedicalPromisesList.stream().filter(medical -> VoucherOpEnum.FREEZE.getStatus().equals(medical.getFreeze()) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(medical.getStatus())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(selfMedicalPromisesList)) {
                    throw new BusinessException(TradeErrorCode.ORDER_REFUND_EXIST);
                }

                selfMedicalPromisesList = medicalPromises.stream().filter(medical -> serviceId.equals(medical.getServiceId().toString()) && !finalPromisePatientIdList1.contains(medical.getPromisePatientId())).collect(Collectors.toList());
                if (CollUtil.isEmpty(selfMedicalPromisesList)) {
                    param.setLastChildOrder(true);
                } else {
                    List<MedicalPromiseDTO> medicalPromisesOther = selfMedicalPromisesList.stream().filter(medical -> !MedicalPromiseStatusEnum.INVALID.getStatus().equals(medical.getStatus()) && CommonConstant.ZERO == medical.getFreeze()).collect(Collectors.toList());
                    log.info("PromiseLastRefundServiceAbility calcPromiseRefundFreeze medicalPromisesServiceList={}, medicalPromisesOther={}", JSON.toJSONString(selfMedicalPromisesList), JSON.toJSONString(medicalPromisesOther));
                    if (CollUtil.isEmpty(medicalPromisesOther)) {
                        param.setLastChildOrder(true);
                    }
                }
            }
        }
        return Boolean.TRUE;
    }

    /**
     * @param param
     * @param orderDetail
     */
    @Test
    public OrderRefundContext selfOrderRefund(RefundOrderParam param, JdOrder orderDetail) {
        log.info("TradeApplicationImpl selfOrderRefund param={}, orderDetail={}", JSON.toJSONString(param), JSON.toJSONString(orderDetail));
        OrderRefundContext context = TradeOrderRefundConverter.INSTANCE.convertToOrderRefundContext(param);
        if (CollUtil.isNotEmpty(param.getRefundOrderSkuList())) {
            List<RefundSku> refundSkuList = TradeOrderRefundConverter.INSTANCE.convertRefundSkuList(param.getRefundOrderSkuList());
            context.setRefundSkuList(refundSkuList);
        }

        context.setOrderDetail(orderDetail);
        context.setOrderUserPhone(orderDetail.getOrderUserPhone());
        context.setUserPin(orderDetail.getUserPin());
        context.setVerticalCode(orderDetail.getVerticalCode());
        context.setServiceType(orderDetail.getServiceType());
        context.setParentId(orderDetail.getParentId());
        log.info("TradeApplicationImpl.selfOrderRefund.context={}", JSON.toJSONString(context));
        /**退款原因 王雨*/
        context.setRefundReason(param.getRefundReason());
        context.setRefundReasonCode(param.getRefundReasonCode());

        TradeEventTypeEnum tradeEventTypeEnum = RefundTypeEnum.getTradeEventTypeEnum(param.getRefundType());
        context.init(tradeEventTypeEnum);
        AbilityExecutor executor = context.getExecutor();
        execute(executor, context);

        saveRefundTaskAndDetail(context);
        return context;
    }

    /**
     * 只想配置的condition和action
     * 这个execute和状态机调用的差异是，executor不由履约单状态控制
     *
     * @param executor
     * @param context
     */
    @Test
    public void execute(AbilityExecutor executor, BusinessContext context) {
        // 未查询到当前状态+事件配置的执行器
        if (Objects.isNull(executor)) {
            log.error("TradeApplicationImpl submitDraft 未找到状态机执行器");
            throw new SystemException(SystemErrorCode.CONFIG_ERROR);
        }
        List<String> validConditions = executor.getConditionCodes();
        for (String code : validConditions) {
            DomainAbility condition = conditions.get(code);
            condition.execute(context);
            log.info("TradeApplicationImpl submitDraft conditionCode={}, context={}", code, JSON.toJSONString(context));
        }
        List<String> actionCodes = executor.getActionCodes();
        for (String code : actionCodes) {
            DomainAbility action = actions.get(code);
            action.execute(context);
            log.info("TradeApplicationImpl submitDraft cationCode={}, context={}", code, JSON.toJSONString(context));
        }
    }

    /**
     * init
     */
    @Test
    public void init() {
        Map<String, DomainAbility> map = applicationContext.getBeansOfType(DomainAbility.class);
        for (DomainAbility ability : map.values()) {
            if (!Objects.equals(ability.getAbilityCode().domainType().getCode(), DomainEnum.TRADE.getCode())) {
                log.info("TradeApplicationImpl init continue code={}", ability.getAbilityCode().getCode());
                continue;
            }
            if (Objects.equals(ability.getAbilityCode().getAbilityType(), AbilityCode.ACTION)) {
                actions.put(ability.getAbilityCode().getCode(), ability);
            } else if (Objects.equals(ability.getAbilityCode().getAbilityType(), AbilityCode.CONDITION)) {
                conditions.put(ability.getAbilityCode().getCode(), ability);
            }
        }
    }

    /**
     * 获取订单退款原因列表
     *
     * @return
     */
    @Test
    public List<OrderCancelReasonDto> getOrderCancelReasons(RefundOrderParam param) {
        String orderCancelReasons = tradeDomainService.getOrderCancelReasons(param.getServiceType());
        if (StringUtils.isNotBlank(orderCancelReasons)) {
            return JsonUtil.parseArray(orderCancelReasons, OrderCancelReasonDto.class);
        }
        return null;
    }

    /**
     * findCompleteOrder
     *
     * @param request 请求
     * @return {@link CompleteOrderDto}
     */
    @Test
    public CompleteOrderDto findCompleteOrder(CompleteOrderRequest request) {
        JdOrderDTO orderDetail = this.getOrderDetail(OrderDetailParam.builder().orderId(request.getOrderId()).build());
        List<PromiseDto> promiseList = Collections.emptyList();
        if (Objects.nonNull(orderDetail)) {

            // 填充业务模式
            if (StringUtils.isNotBlank(orderDetail.getVerticalCode())) {
                JdhVerticalBusiness business = verticalBusinessRepository.find(orderDetail.getVerticalCode());
                orderDetail.setBusinessMode(business.getBusinessModeCode());
            } else {
                try {
                    // 解决订单还没有业务身份时，通过商品解析。
                    Optional<JdOrderItemDTO> orderItem = orderDetail.getJdOrderItemList().stream().findFirst();
                    if (orderItem.isPresent()) {
                        Long skuId = orderItem.get().getSkuId();
                        JdhSku sku = jdhSkuRepository.find(new JdhSkuIdentifier(skuId));
                        BusinessModeEnum businessModeEnum = BusinessModeUtil.getBusinessModeEnum(BusinessModeParam.builder().skuServiceType(sku.getServiceType()).businessProcessType(sku.getBusinessProcessType()).build());
                        orderDetail.setBusinessMode(Objects.nonNull(businessModeEnum) ? businessModeEnum.getCode() : "");
                    }
                } catch (Exception e) {
                    log.error("TradeApplicationImpl->findCompleteOrder find businessMode error", e);
                }
            }
            Long orderId = Objects.nonNull(orderDetail.getParentId()) && orderDetail.getParentId() > 0 ? orderDetail.getParentId() : orderDetail.getOrderId();
            List<Long> voucherIds = parseVoucherIds(orderId, orderDetail.getVerticalCode());
            if (CollUtil.isEmpty(voucherIds)) {
                return CompleteOrderDto.builder().jdOrder(orderDetail).build();
            }


            promiseList = promiseApplication.findByPromiseList(PromiseListRequest.builder().voucherIds(voucherIds).build());
        } else {
            JdOrderSaveParam jdOrderSaveParam = new JdOrderSaveParam();
            jdOrderSaveParam.setOrderId(String.valueOf(request.getOrderId()));
            jdOrderSaveParam.setUserPin(request.getPin());
            jdOrderSaveParam.setPartnerSource(PartnerSourceEnum.JDH_XFYL.getCode());
            JdOrder jdOrder = jdOrderApplication.saveJdOrder(jdOrderSaveParam);
            orderDetail = JdOrderConverter.INSTANCE.JdOrderToDTO(jdOrder);
        }

        return CompleteOrderDto.builder().jdOrder(orderDetail).promiseList(promiseList).build();
    }

    /**
     * 查询运营端退款提示信息
     *
     * @param param param
     * @return {@link List}<{@link ManRefundTipsInfoDto}>
     */
    @Test
    public ManRefundTipsInfoDto queryManRefundTipsInfo(ManRefundTipsParam param) {
        //参数校验
        if (Objects.isNull(param)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        if (Objects.isNull(param.getPromiseId())) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        List<ManRefundTipsOrderParam> tipsOrderList = param.getOrderList();
        if (CollUtil.isEmpty(tipsOrderList)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        tipsOrderList.forEach(ele -> {
            if (Objects.isNull(ele.getOrderId()) || Objects.isNull(ele.getPromisePatientId())) {
                throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
            }
        });

        checkPromiseId(param);

        ManRefundTipsOrderParam manRefundTipsOrderParam = tipsOrderList.get(0);
        Long orderId = manRefundTipsOrderParam.getOrderId();
        JdOrderDTO orderDetail = this.getOrderItemAndFeeInfo(OrderDetailParam.builder().orderId(orderId.toString()).build());
        JdOrderItemDTO orderItemDTO = orderDetail.getJdOrderItemList().get(0);
        List<JdOrderServiceFeeInfoDTO> jdOrderServiceFeeInfos = orderDetail.getJdOrderServiceFeeInfos();
        BigDecimal timePeriodAmount = null;
        BigDecimal homeVisitAmount = null;
        BigDecimal dynamicAmount = null;
//        BigDecimal upgraeFeeAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(jdOrderServiceFeeInfos)) {
            Map<Integer, List<JdOrderServiceFeeInfoDTO>> feeList = jdOrderServiceFeeInfos.stream().collect(Collectors.groupingBy(JdOrderServiceFeeInfoDTO::getAggregateSubType));
            List<JdOrderServiceFeeInfoDTO> timePeriodList = feeList.get(FeeAggregateTypeEnum.TIME_PERIOD_FEE.getSubType());
            if (CollUtil.isNotEmpty(timePeriodList)) {
                timePeriodAmount = timePeriodList.stream().map(JdOrderServiceFeeInfoDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                //maxRefundAmount = maxRefundAmount.add(timePeriodAmount);
            }
            List<JdOrderServiceFeeInfoDTO> dynamicList = feeList.get(FeeAggregateTypeEnum.DYNAMIC_FEE.getSubType());
            if (CollUtil.isNotEmpty(dynamicList)) {
                dynamicAmount = dynamicList.stream().map(JdOrderServiceFeeInfoDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
//            List<JdOrderServiceFeeInfoDTO> upgraeFeeList = feeList.get(FeeAggregateTypeEnum.UPGRADE_ANGEL_FEE.getSubType());
//            if (CollUtil.isNotEmpty(upgraeFeeList)) {
//                upgraeFeeAmount = upgraeFeeList.stream().map(JdOrderServiceFeeInfoDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
//            }
//            if(Objects.nonNull(dynamicAmount)){
//                upgraeFeeAmount = upgraeFeeAmount.add(dynamicAmount);
//            }

            List<JdOrderServiceFeeInfoDTO> homeVisitList = feeList.get(FeeAggregateTypeEnum.HOME_VISIT_FEE.getSubType());
            if (CollUtil.isNotEmpty(homeVisitList)) {
                homeVisitAmount = homeVisitList.stream().map(JdOrderServiceFeeInfoDTO::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                //maxRefundAmount = maxRefundAmount.add(homeVisitAmount);
            }
        }

        BigDecimal maxRefundAmount = orderDetail.getOrderAmount();
        //BigDecimal maxRefundAmount = new BigDecimal("0");
        BigDecimal sugRefundAmount = null;

        QueryRefundAmountParam refundOrderParam = new QueryRefundAmountParam();
        refundOrderParam.setOrderId(orderId);
        refundOrderParam.setPromiseId(param.getPromiseId());
        refundOrderParam.setQueryAmountType(3);
        refundOrderParam.setPromisePatientIdList(param.getOrderList().stream().map(ManRefundTipsOrderParam::getPromisePatientId).collect(Collectors.toList()));
        OrderRefundAmountDto refundAmountDto = jdOrderRefundApplication.queryOrderRefundAmount(refundOrderParam);
        if (Objects.nonNull(refundAmountDto)) {
            sugRefundAmount = refundAmountDto.getSuggestRefundAmount();
            maxRefundAmount = refundAmountDto.getMostRefundAmount();
        }

        return ManRefundTipsInfoDto.builder().orderId(orderId).skuId(orderItemDTO.getSkuId().toString()).refundSkuNum(tipsOrderList.size()).skuAmount(orderItemDTO.getItemAmount()).timePeriodAmount(timePeriodAmount).homeVisitAmount(homeVisitAmount).dynamicAmount(dynamicAmount).sugRefundAmount(sugRefundAmount).maxRefundAmount(maxRefundAmount).refundTips("最大退款金额" + maxRefundAmount + "元").build();
    }

    /**
     * checkPromiseId
     *
     * @param param param
     */
    @Test
    public void checkPromiseId(ManRefundTipsParam param) {
        List<Long> promisePatientIdList = param.getOrderList().stream().map(ManRefundTipsOrderParam::getPromisePatientId).collect(Collectors.toList());
        LambdaQueryWrapper<JdhPromisePatientPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(JdhPromisePatientPo::getPromisePatientId, promisePatientIdList);
        List<JdhPromisePatientPo> jdhPromisePatientPos = promisePatientPoMapper.selectList(queryWrapper);
        Set<Long> promiseIdSet = jdhPromisePatientPos.stream().map(JdhPromisePatientPo::getPromiseId).collect(Collectors.toSet());
        if (promiseIdSet.size() > 1) {
            throw new BusinessException(TradeErrorCode.PROMISE_ID_MULTI_ERROR);
        }
    }

    /**
     * @param context
     */
    @Test
    public void saveRefundTaskAndDetail(OrderRefundContext context) {
        log.info("TradeApplicationImpl saveRefundTaskAndDetail context={}", JSON.toJSONString(context));
        JdOrderRefundTask jdOrderRefundTask = TradeOrderRefundConverter.INSTANCE.convertToJdOrderRefundTask(context);
        Long taskId = generateIdFactory.getId();
        jdOrderRefundTask.setTaskId(taskId);
        jdOrderRefundTask.setPatientId(context.getPatientId());
        jdOrderRefundTask.setOrderId(context.getOrderId());
        jdOrderRefundTask.setParentId(context.getParentId());
        jdOrderRefundTask.setVoucherId(context.getVoucherId());
        jdOrderRefundTask.setPromiseId(context.getPromiseId());
        OrderRefundDetailBo orderRefundDetailBo = context.getOrderRefundDetailBo();
        if (Objects.nonNull(orderRefundDetailBo)) {
            orderRefundDetailBo.setFreezeStatus(context.getFreezeStatus());
            jdOrderRefundTask.setRefundDetail(JsonUtil.toJSONString(context.getOrderRefundDetailBo()));
            orderRefundDetailBo.setFreezeAndInvalid(context.getFreezeAndInvalid());
        }
        jdOrderRefundTask.setOperator(context.getOperator());
        jdOrderRefundTask.setServiceId(context.getServiceId());
        jdOrderRefundTask.setRefundAmount(context.getRefundAmount());
        jdOrderRefundTask.setRefundStatus(RefundStatusEnum.NO_REFUND.getType());
        jdOrderRefundTask.setLastRefundTask(context.getLastPromise() ? 1 : 0);
        jdOrderRefundTask.setLastOrderTask(context.getLastChildOrder() ? 1 : 0);
        jdOrderRefundTask.setRefundReason(context.getRefundReason());
        jdOrderRefundTask.setRefundReasonCode(context.getRefundReasonCode());
        List<JdOrderRefundDetail> jdOrderRefundDetailList = new ArrayList<>();
        Map<Integer, BigDecimal> lastRefundAmountAndTypeMap = context.getLastRefundAmountAndTypeMap();
        for (Map.Entry<Integer, BigDecimal> entry : lastRefundAmountAndTypeMap.entrySet()) {
            Integer refundType = ReFundAmountTypeEnum.getRefundTypeOfType(entry.getKey());
            if (Objects.nonNull(refundType)) {
                JdOrderRefundDetail jdOrderRefundDetail = JdOrderRefundDetail.builder().build();
                jdOrderRefundDetail.setTaskId(taskId);
                Long taskDetailId = generateIdFactory.getId();
                jdOrderRefundDetail.setTaskDetailId(taskDetailId);
                jdOrderRefundDetail.setUserPin(context.getUserPin());
                jdOrderRefundDetail.setOrderUserPhone(context.getOrderUserPhone());
                jdOrderRefundDetail.setAmountType(entry.getKey());
                jdOrderRefundDetail.setKtRefundType(refundType);
                jdOrderRefundDetail.setTransactionNum(taskDetailId + "_" + refundType);
                jdOrderRefundDetail.setOrderId(context.getOrderId());
                jdOrderRefundDetail.setRefundAmount(entry.getValue());
                jdOrderRefundDetail.setRefundStatus(RefundStatusEnum.NO_REFUND.getType());
                jdOrderRefundDetailList.add(jdOrderRefundDetail);
            }
        }
        log.info("TradeApplicationImpl saveRefundTaskAndDetail jdOrderRefundTask={}, jdOrderRefundDetailList={}", JSON.toJSONString(jdOrderRefundTask), JSON.toJSONString(jdOrderRefundDetailList));
        jdOrderRefundApplication.saveJdOrderRefundTask(jdOrderRefundTask, jdOrderRefundDetailList);
        if (RefundTypeEnum.ORDER_REFUND.getType().equals(context.getRefundType()) || context.getLastChildOrder() || context.getHasAdded()) {
            JdOrder jdOrder = JdOrder.builder().orderId(context.getOrderId()).orderStatus(OrderStatusEnum.ORDER_REFUNDING.getStatus()).build();
            jdOrderRepository.updateOrderStatusByOrderId(jdOrder);
        }
    }

    /**
     * 根据SKU配置的需提前预约时间、未来可约天数、每天可预约时间段计算出可约时间
     *
     * @param jdhSkuDtoList
     * @param isCare
     * @return
     */
    @Test
    public AvaiableAppointmentTimeDTO calcAppointmentTime(List<JdhSkuDto> jdhSkuDtoList, Boolean isCare) {
        // 多SKU交集 最小可预约天数
        Integer maxScheduleDays = jdhSkuDtoList.stream().min(Comparator.comparing(JdhSkuDto::getMaxScheduleDays)).get().getMaxScheduleDays();
        if (maxScheduleDays == null || maxScheduleDays.intValue() == 0) {
            return null;
        }
        // 多SKU交集 最大需提前预约时间
        Integer advanceAppointTime = jdhSkuDtoList.stream().max(Comparator.comparing(JdhSkuDto::getAdvanceAppointTime, Comparator.nullsFirst(Comparator.naturalOrder()))).get().getAdvanceAppointTime();
        if (advanceAppointTime == null) {
            advanceAppointTime = 0;
        }
        // 多SKU交集 最大开始时段
        String beginTimeRange = jdhSkuDtoList.stream().max(Comparator.comparing(JdhSkuDto::getDayTimeFrameBegin)).get().getDayTimeFrameBegin();
        if (StringUtils.isBlank(beginTimeRange)) {
            beginTimeRange = "00:00";
        }
        // 多SKU交集 最小结算时段
        String endTimeRange = jdhSkuDtoList.stream().min(Comparator.comparing(JdhSkuDto::getDayTimeFrameEnd)).get().getDayTimeFrameEnd();
        if (StringUtils.isBlank(endTimeRange)) {
            endTimeRange = "24:00";
        }

        Date now = new Date();
        Calendar c = Calendar.getInstance();
        // 可预约开始时间
        c.setTime(now);
        // 提前大于0小时预约，不展示立即预约
        if (advanceAppointTime > 0) {
            if (isCare) {
                c.add(Calendar.HOUR_OF_DAY, advanceAppointTime);
                c.set(Calendar.SECOND, 0);
            } else {
                //非整点，去下一个整点时段
                if (c.get(Calendar.MINUTE) > 0) {
                    c.add(Calendar.HOUR_OF_DAY, advanceAppointTime + 1);
                } else {
                    c.add(Calendar.HOUR_OF_DAY, advanceAppointTime);
                }
                c.set(Calendar.MINUTE, 0);
                c.set(Calendar.SECOND, 0);
            }
        }
        Date avaiableTimeStart = c.getTime();
        // 可预约结束时间
        c.setTime(now);
        c.add(Calendar.DATE, maxScheduleDays - 1);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        Date avaiableTimeEnd = c.getTime();
        return new AvaiableAppointmentTimeDTO(avaiableTimeStart, avaiableTimeEnd, beginTimeRange, endTimeRange, advanceAppointTime, null, null, 30);
    }

    /**
     * 根据orderId查询服务单，再根据服务单查询履约单
     *
     * @param orderId
     * @return
     */
    @Test
    public List<Long> parseVoucherIds(Long orderId, String verticalCode) {
        List<JdhVoucher> vouchers = voucherRepository.listBySourceVoucherId(Arrays.asList(orderId.toString()), verticalCode);
        log.info("TradeApplicationImpl->parseVoucherIds 根据orderId查询 vouchers={}", JSON.toJSONString(vouchers));
        if (CollUtil.isEmpty(vouchers)) {
            List<JdOrderItem> items = jdOrderItemRepository.listByOrderId(orderId);
            log.info("TradeApplicationImpl->parseVoucherIds items={}", JSON.toJSONString(items));
            if (CollectionUtil.isEmpty(items)) {
                return Collections.emptyList();
            }
            List<Long> itemIds = items.stream().map(JdOrderItem::getOrderItemId).collect(Collectors.toList());
            List<String> sourceVoucherId = Lists.newArrayList();
            for (Long itemId : itemIds) {
                sourceVoucherId.add(String.valueOf(itemId));
            }
            vouchers = voucherRepository.listBySourceVoucherId(sourceVoucherId, verticalCode);
            log.info("TradeApplicationImpl->parseVoucherIds vouchers={}", JSON.toJSONString(vouchers));
        }

        if (CollectionUtil.isEmpty(vouchers)) {
            return Collections.emptyList();
        }
        return vouchers.stream().map(JdhVoucher::getVoucherId).collect(Collectors.toList());
    }
}
