<?xml version='1.0' encoding='UTF-8' ?>

<configuration>

    <!-- 日志存放路径  -->
    <property name="log.path" value="/export/Logs/Domains/jdh-o2o-service.jd.com/server1/logs/"/>
    <!-- 日志输出格式 -->
    <property name="log.pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%p] [%C{0}:%L,%M\\(\\)] [%X{PFTID}] [%X{logid}] [%thread] -%m%n"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <appender name="LOGGER-DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/debug.%d{yyyy-MM-dd_HH}.%i.log</fileNamePattern>
            <maxFileSize>1024MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="com.jdh.o2oservice.base.config.TruncatingLogEncoder">
            <charset>UTF-8</charset>
            <pattern>${log.pattern}</pattern>
            <!-- 其他 encoder 配置 -->
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="LOGGER-INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/info.%d{yyyy-MM-dd_HH}.%i.log</fileNamePattern>
            <maxFileSize>1024MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>

        <encoder class="com.jdh.o2oservice.base.config.TruncatingLogEncoder">
            <charset>UTF-8</charset>
            <pattern>${log.pattern}</pattern>
            <!-- 其他 encoder 配置 -->
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <appender name="LOGGER-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/error.%d{yyyy-MM-dd_HH}.%i.log</fileNamePattern>
            <maxFileSize>1024MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="com.jdh.o2oservice.base.config.TruncatingLogEncoder">
            <charset>UTF-8</charset>
            <pattern>${log.pattern}</pattern>
            <!-- 其他 encoder 配置 -->
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="LOGGER-SQL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sqlmap.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/sqlmap.%d{yyyy-MM-dd_HH}.%i.log</fileNamePattern>
            <maxFileSize>1024MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="com.jdh.o2oservice.base.config.TruncatingLogEncoder">
            <charset>UTF-8</charset>
            <pattern>${log.pattern}</pattern>
            <!-- 其他 encoder 配置 -->
        </encoder>
    </appender>


    <appender name="DRUID" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/druidFilter.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/druidFilter.%d{yyyy-MM-dd_HH}.%i.log</fileNamePattern>
            <maxFileSize>1024MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="com.jdh.o2oservice.base.config.TruncatingLogEncoder">
            <charset>UTF-8</charset>
            <pattern>${log.pattern}</pattern>
            <!-- 其他 encoder 配置 -->
        </encoder>
    </appender>

    <appender name="DruidStatFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/druidStateFilter.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/druidStatFilter.%d{yyyy-MM-dd_HH}.%i.log</fileNamePattern>
            <maxFileSize>1024MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="com.jdh.o2oservice.base.config.TruncatingLogEncoder">
            <charset>UTF-8</charset>
            <pattern>${log.pattern}</pattern>
            <!-- 其他 encoder 配置 -->
        </encoder>
    </appender>
    <!-- sql -->
    <logger name="com.jdh.o2oservice.infrastructure.repository.db.dao"  additivity="false" level="debug">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="LOGGER-SQL"/>
    </logger>

    <logger name="com.jd.jim.cli.springcache" additivity="false" level="debug">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="LOGGER-DEBUG"/>
    </logger>

    <logger name="druid.sql.Connection" level="DEBUG" additivity="false">
        <appender-ref ref="DRUID"/>
        <appender-ref ref="LOGGER-SQL"/>
    </logger>
    <logger name="druid.sql.Statement" level="DEBUG" additivity="false">
        <appender-ref ref="DRUID"/>
        <appender-ref ref="LOGGER-SQL"/>
    </logger>
    <logger name="druid.sql.DataSource" level="DEBUG" additivity="false">
        <appender-ref ref="DRUID"/>
        <appender-ref ref="LOGGER-SQL"/>
    </logger>

    <!-- StatFilter日志 -->
    <Logger name="com.alibaba.druid.filter.stat.StatFilter" level="DEBUG" additivity="false">
        <appender-ref ref="DruidStatFile"/>
    </Logger>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="LOGGER-DEBUG"/>
        <appender-ref ref="LOGGER-INFO"/>
        <appender-ref ref="LOGGER-ERROR"/>
    </root>

</configuration>
