package com.jdh.o2oservice;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.jd.security.configsec.spring.config.JDSecurityPropertySourceFactory;
import com.jdh.o2oservice.base.factory.CustomYamlPropertySourceFactory;
import com.jdh.o2oservice.common.config.UimConfig;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.*;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 启动类入口ReachStoreStartApp
 * <AUTHOR>
 * @date 2023-11-23 11:36
 */
@Slf4j
@EnableRetry
@EnableAsync
//直接打开EnableCaching 即可以使用缓存，关于序列化，反序列化需要多测试，详细配置见：com.jdh.o2oservice.base.config.RedisConfig.cacheManager
//@EnableCaching
@PropertySources(value = {
        @PropertySource(value = {"classpath:important.properties"}, encoding = "utf-8", factory = JDSecurityPropertySourceFactory.class, ignoreResourceNotFound = true),
        @PropertySource(value = {"classpath:config.properties"}, encoding = "utf-8", ignoreResourceNotFound = true),
        @PropertySource(value = {"classpath:jmq.yml"}, encoding = "utf-8", factory= CustomYamlPropertySourceFactory.class),
})
@ImportResource(locations = {"classpath:jsf-provider.xml", "classpath:jsf-consumer.xml" , "classpath:spring-config-matrix.xml"})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = "com.jdh.o2oservice",// 指定要扫描的基础包
        excludeFilters = @ComponentScan.Filter(
                type = FilterType.REGEX, // 使用正则表达式过滤
                pattern = "com\\.jdh\\.o2oservice\\.common\\..*" // 替换为你想排除的实际包路径正则表达式
        )
)
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@MapperScan("com.jdh.o2oservice.infrastructure.repository.db.dao")
@EnableTransactionManagement
@EnableConfigurationProperties({UimConfig.class}) // 添加这一行，注册配置属性类
@EnableMethodCache(basePackages = "com.jdh.o2oservice")
@EnableCreateCacheAnnotation
public class StartApplication extends SpringBootServletInitializer {

    /**
     * 配置springboot打war情况下启动问题
     *
     * @param application 应用程序
     * @return {@link SpringApplicationBuilder}
     */
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(StartApplication.class);
    }

    /**
     * 启动入口
     *
     * @param args args
     */
    public static void main(String[] args) {
        SpringApplication.run(StartApplication.class, args);
    }

}
