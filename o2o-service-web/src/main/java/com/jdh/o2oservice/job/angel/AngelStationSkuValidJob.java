package com.jdh.o2oservice.job.angel;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.model.JdhStationSkuRel;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationSkuRelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelStationPageQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.StationSkuRelDbQuery;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuItemRel;
import com.jdh.o2oservice.core.domain.product.model.StationSkuItemErrorExcel;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.file.context.PutFileResult;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 服务站商品是否有效(可分配到实验室，仅针对骑手上门sku)定时任务
 * @date 2025/2/20
 */
@Slf4j
@Component
public class AngelStationSkuValidJob  implements SimpleJob {

    /**
     * jdhStationRepository
     */
    @Resource
    @Lazy
    private JdhStationRepository jdhStationRepository;
    /**
     *
     */
    @Resource
    @Lazy
    private JdhStationSkuRelRepository jdhStationSkuRelRepository;

    /**
     *
     */
    @Resource
    @Lazy
    private JdhSkuRepository jdhSkuRepository;

    /**
     * 供应商门店
     */
    @Autowired
    @Lazy
    private ProviderStoreApplication providerStoreApplication;

    /**
     *
     */
    @Autowired
    @Lazy
    private FileManageService fileManageService;

    @Autowired
    @Lazy
    private DongDongRobotRpc dongDongRobotRpc;

    @Autowired
    @Lazy
    private DuccConfig duccConfig;


    /**
     * @param shardingContext
     */
    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("AngelStationSkuValidJob->start");

        //如果开关关闭
        if (!duccConfig.getAngelStationSkuValidJobSwitch()){
            log.info("AngelStationSkuValidJob->开关关闭");
        }

        AngelStationPageQuery query = new AngelStationPageQuery();
        query.setPageSize(10);
        query.setAngelTypeSet(com.google.common.collect.Sets.newHashSet(1,3));
        query.setStationStatus(1);
        if (CollectionUtils.isNotEmpty(duccConfig.getAngelStationIdsJob())){
            query.setAngelStationIds(duccConfig.getAngelStationIdsJob());
        }

        List<StationSkuItemErrorExcel> errorExcels = Lists.newArrayList();
        //1.查询服务站列表
        for (int i = 1; i < 2; i++) {
            query.setPageNum(i);
            Page<JdhStation> pageList = jdhStationRepository.findPageList(query);
            log.info("AngelStationSkuValidJob->pageList={}",JsonUtil.toJSONString(pageList));
            if (Objects.isNull(pageList) || org.apache.commons.collections4.CollectionUtils.isEmpty(pageList.getRecords())) {
                break;
            }
            List<JdhStation> records = pageList.getRecords();

            Set<Long> angelStationIds = records.stream().map(JdhStation::getAngelStationId).collect(Collectors.toSet());

            List<JdhStationSkuRel> jdhStationSkuRels = jdhStationSkuRelRepository.findList(StationSkuRelDbQuery.builder().angelStationIds(angelStationIds).build());
            log.info("AngelStationSkuValidJob->jdhStationSkuRels={}",JsonUtil.toJSONString(jdhStationSkuRels));

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(jdhStationSkuRels)) {
                continue;
            }

            //去掉不售卖的商品
            jdhStationSkuRels.removeIf(p->Objects.equals(0,p.getSaleStatus()));
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(jdhStationSkuRels)) {
                continue;
            }
            log.info("AngelStationSkuValidJob->jdhStationSkuRels,去掉不售卖的商品={}",JsonUtil.toJSONString(jdhStationSkuRels));


            Set<Long> skuIds = jdhStationSkuRels.stream().map(JdhStationSkuRel::getSkuId).collect(Collectors.toSet());
            List<JdhSku> jdhSkus = jdhSkuRepository.querySkuList(skuIds);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(jdhSkus)) {
                continue;
            }

            //去掉非主品,非骑手上门的品,非售卖状态的品
            Set<Long> illegalSku = jdhSkus.stream()
                    .filter(p -> Objects.equals(0, p.getSaleStatus()) || !Objects.equals(0, p.getSkuType()) || !Objects.equals(1, p.getServiceType()))
                    .map(JdhSku::getSkuId)
                    .collect(Collectors.toSet());

            jdhStationSkuRels.removeIf(p->illegalSku.contains(p.getSkuId()));

            log.info("AngelStationSkuValidJob->jdhStationSkuRels,去掉非主品,非骑手上门的品,非售卖状态的品={}",JsonUtil.toJSONString(jdhStationSkuRels));
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(jdhStationSkuRels)) {
                continue;
            }
            //sku:项目
            JdhSkuItemRel jdhSkuItemRel = new JdhSkuItemRel();
            Set<Long> skuIdsAfter = jdhStationSkuRels.stream().map(JdhStationSkuRel::getSkuId).collect(Collectors.toSet());
            jdhSkuItemRel.setSkuIds(skuIdsAfter);
            List<JdhSkuItemRel> jdhSkuItemRels = jdhSkuRepository.queryJdhSkuItemRelList(jdhSkuItemRel);
            log.info("AngelStationSkuValidJob->jdhSkuItemRels={}",JsonUtil.toJSONString(jdhSkuItemRels));
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(jdhSkuItemRels)) {
                continue;
            }
            Map<Long, List<JdhSkuItemRel>> skuToItem = jdhSkuItemRels.stream().collect(Collectors.groupingBy(JdhSkuItemRel::getSkuId));

            //实验室：项目
            //查询实验室对应项目
            Set<String> stationId = records.stream().map(JdhStation::getStationId).collect(Collectors.toSet());
            List<JdhStationServiceItemRelDto> jdhStationServiceItemRelDtos = providerStoreApplication.queryStationServiceItemRelList(JdhStationServiceItemRelRequest.builder().queryStationDetail(Boolean.TRUE).stationIdSet(stationId).build());
            log.info("AngelStationSkuValidJob->jdhStationServiceItemRelDtos={}",JsonUtil.toJSONString(jdhStationServiceItemRelDtos));

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(jdhStationServiceItemRelDtos)) {
                Set<Long> collect = records.stream().map(JdhStation::getAngelStationId).collect(Collectors.toSet());
                StationSkuItemErrorExcel build = StationSkuItemErrorExcel.builder()
                        .angelStationId(JsonUtil.toJSONString(collect))
                        .stationId(JsonUtil.toJSONString(stationId))
                        .errorReason("实验室没有绑定项目")
                        .build();
                errorExcels.add(build);
                continue;
            }
            Map<String, List<JdhStationServiceItemRelDto>> storeToItem = jdhStationServiceItemRelDtos.stream().collect(Collectors.groupingBy(JdhStationServiceItemRelDto::getStationId));


            //服务站：实验室
            //服务站：服务站station
            Map<Long, JdhStation> angelStationIdToStation = records.stream().collect(Collectors.toMap(JdhStation::getAngelStationId, p -> p));
            //关联关系按服务站分组
            Map<Long, List<JdhStationSkuRel>> stationIdToRel = jdhStationSkuRels.stream().collect(Collectors.groupingBy(JdhStationSkuRel::getStationId));

            //sku:obj
            Map<Long, JdhSku> skuNameToObj = jdhSkus.stream().collect(Collectors.toMap(JdhSku::getSkuId, p -> p));

            stationIdToRel.forEach((angelStationId,rels)->{
                //实验室
                String store = angelStationIdToStation.get(angelStationId).getStationId();
                //实验室可做项目
                List<JdhStationServiceItemRelDto> storeItemRels = storeToItem.get(store);
                String stationName = storeItemRels.get(0).getStationName();
                String angelStationName = angelStationIdToStation.get(angelStationId).getAngelStationName();
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(storeItemRels)) {
                    Set<String> storeCanServiceItem = storeItemRels.stream().map(p->String.valueOf(p.getServiceItemId())).collect(Collectors.toSet());
                    log.info("checkStationSkuItemConfig->store={},storeCanServiceItem={}",store,JsonUtil.toJSONString(storeCanServiceItem));
                    //遍历关联sku
                    for (JdhStationSkuRel jdhStationSkuRel : rels) {
                        List<JdhSkuItemRel> skuItems = skuToItem.get(jdhStationSkuRel.getSkuId());
                        Set<String> skuNeedItem = skuItems.stream().map(JdhSkuItemRel::getSkuItemId).collect(Collectors.toSet());
                        log.info("checkStationSkuItemConfig,sku={},skuNeedItem={}",jdhStationSkuRel.getSkuId(),JsonUtil.toJSONString(skuNeedItem));
                        if (org.apache.commons.collections4.CollectionUtils.isEmpty(skuItems)) {
                            StationSkuItemErrorExcel build = StationSkuItemErrorExcel.builder()
                                    .angelStationId(String.valueOf(angelStationId))
                                    .angelStationName(angelStationName)
                                    .skuId(jdhStationSkuRel.getSkuId().toString())
                                    .skuName(skuNameToObj.get(jdhStationSkuRel.getSkuId()).getItemName())
                                    .stationId(store)
                                    .stationName(stationName)
                                    .needServiceItem(JsonUtil.toJSONString(skuNeedItem))
                                    .canServiceItem(JsonUtil.toJSONString(storeCanServiceItem))
                                    .errorReason("sku没有绑定项目")
                                    .build();
                            errorExcels.add(build);
                            continue;
                        }

                        //如果这个实验室都可以做sku下的项目，continue
                        if (storeCanServiceItem.containsAll(skuNeedItem)){
                            continue;
                        }

                        Set<String> diff = org.assertj.core.util.Sets.newHashSet(skuNeedItem);
                        diff.removeAll(storeCanServiceItem);
                        Set<String> common = org.assertj.core.util.Sets.newHashSet(skuNeedItem);
                        common.retainAll(storeCanServiceItem);
                        //否则配置错误
                        //服务站：angelStationId，实验室：store，sku:jdhStationSkuRel.getSkuId(),对应项目：skuNeedItem，可做项目：storeCanServiceItem
                        StationSkuItemErrorExcel build = StationSkuItemErrorExcel.builder()
                                .angelStationId(String.valueOf(angelStationId))
                                .skuId(jdhStationSkuRel.getSkuId().toString())
                                .stationId(store)
                                .needServiceItem(JsonUtil.toJSONString(skuNeedItem))
                                .canServiceItem(JsonUtil.toJSONString(common))
                                .cantServiceItem(JsonUtil.toJSONString(diff))
                                .errorReason("实验室绑定项目无法包含sku所有项目")
                                .build();
                        errorExcels.add(build);
                    }
                }else {
                    StationSkuItemErrorExcel build = StationSkuItemErrorExcel.builder()
                            .angelStationId(String.valueOf(angelStationId))
                            .stationId(store)
                            .errorReason("实验室没有绑定项目")
                            .build();
                    errorExcels.add(build);
                }
            });
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(errorExcels)) {
            log.info("AngelStationSkuValidJob->end 配置有误，发送咚咚告警");
            createExcel(errorExcels);
            log.info("AngelStationSkuValidJob->end 配置有误，发送咚咚告警成功");
            return;
        }

        log.info("AngelStationSkuValidJob->end 配置全部正确");
    }

    private void createExcel(List<StationSkuItemErrorExcel> errorExcels){
        // 上传文件名称
        String uploadFileName = "stationSkuStoreItemError" + cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".csv";
        //创建流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        //生成easyexcel的流
        EasyExcelFactory.write(outputStream).head(StationSkuItemErrorExcel.class).sheet("配置错误项").doWrite(errorExcels);
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        PutFileResult putFileResult = fileManageService.put(uploadFileName, inputStream, FileManageServiceImpl.FolderPathEnum.FILE_MANAGE, null,Boolean.FALSE);
        Date jdkDate = cn.hutool.core.date.DateUtil.offsetDay(new Date(), 1).toJdkDate();
        String publicUrl = fileManageService.getPublicUrl(putFileResult.getFilePath(), Boolean.TRUE, jdkDate);
        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get("配置巡检");
        dongDongRobotRpc.sendDongDongRobotMessage(String.format("骑手上门，服务站-sku-实验室-项目巡检发现可能存在错误的配置项，详情点击链接查看：%s",publicUrl),
                jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));


    }
}
