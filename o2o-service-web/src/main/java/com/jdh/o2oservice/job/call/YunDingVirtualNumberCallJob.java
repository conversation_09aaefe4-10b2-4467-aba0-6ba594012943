package com.jdh.o2oservice.job.call;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.export.support.dto.CallBillingUserDataDto;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import com.jdh.o2oservice.export.support.dto.CallRecordingRetrievalDto;
import com.jdh.o2oservice.export.support.query.QueryCallRecordRequest;
import com.wangyin.schedule.client.job.ScheduleContext;
import com.wangyin.schedule.client.job.ScheduleFlowTask;
import com.wangyin.schedule.client.job.TaskResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: maoxianglin1
 * @Date: 2025/9/3 20:00
 * @Description:
 **/
@Component
@Slf4j
public class YunDingVirtualNumberCallJob implements ScheduleFlowTask {

    @Resource
    private CallRecordApplication callRecordApplication;

    @Override
    @LogAndAlarm
    public TaskResult doTask(ScheduleContext scheduleContext) throws Exception {
        try {
            log.info("YunDingVirtualNumberCallJob -->> start");
            Map<String, String> parameters = scheduleContext.getParameters();
            // 默认查询当前时间-120分钟
            int addTime = CommonConstant.ONE_HUNDRED_AND_TWENTY;
            if (MapUtils.isNotEmpty(parameters) && parameters.containsKey("addTime")) {
                addTime = Integer.parseInt(parameters.get("addTime"));
            }
            Date queryStartTime = TimeUtils.add(new Date(), Calendar.MINUTE, -addTime);

            int pageNum = CommonConstant.ONE;
            int pageSize = CommonConstant.ONE_HUNDRED;
            QueryCallRecordRequest queryCallRecordRequest = new QueryCallRecordRequest();
            queryCallRecordRequest.setPageNum(pageNum);
            queryCallRecordRequest.setPageSize(pageSize);
            queryCallRecordRequest.setQueryStartTime(queryStartTime);
            List<CallRecordDto> callRecordDtoList = callRecordApplication.queryNoAudioUrlCallRecordPage(queryCallRecordRequest);
            int count = CommonConstant.ZERO;
            while (CollectionUtils.isNotEmpty(callRecordDtoList)) {
                for (CallRecordDto callRecordDto : callRecordDtoList) {
                    try {
                        CallRecordingRetrievalDto callRecordingRetrievalDto = new CallRecordingRetrievalDto();
                        callRecordingRetrievalDto.setPromiseId(callRecordDto.getPromiseId());
                        callRecordingRetrievalDto.setBindId(callRecordDto.getBindId());
                        callRecordingRetrievalDto.setCallId(callRecordDto.getCallId());
                        if (StringUtils.isNotBlank(callRecordDto.getUserData())) {
                            CallBillingUserDataDto userDataDto = JSON.parseObject(callRecordDto.getUserData(), CallBillingUserDataDto.class);
                            callRecordingRetrievalDto.setBuId(userDataDto.getBuId());
                        }
                        if (Boolean.TRUE.equals(callRecordApplication.callRecordingRetrieval(callRecordingRetrievalDto))) {
                            count++;
                        }
                    } catch (Exception e) {
                        log.error("YunDingVirtualNumberCallJob callRecordingRetrieval has error", e);
                    }
                }
                pageNum++;
                queryCallRecordRequest.setPageNum(pageNum);
                callRecordDtoList = callRecordApplication.queryNoAudioUrlCallRecordPage(queryCallRecordRequest);
            }
            log.info("YunDingVirtualNumberCallJob -->> end, 成功的promise数量等于={}", count);
            return TaskResult.success();
        } catch (Exception e) {
            log.error("YunDingVirtualNumberCallJob Exception=", e);
            return TaskResult.fail(e.getMessage());
        }
    }
}

