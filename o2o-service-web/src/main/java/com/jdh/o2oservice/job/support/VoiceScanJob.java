package com.jdh.o2oservice.job.support;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.file.model.VoiceConvertRecord;
import com.jdh.o2oservice.core.domain.support.file.model.VoiceTask;
import com.jdh.o2oservice.core.domain.support.file.repository.db.VoiceConvertRecordRepository;
import com.jdh.o2oservice.infrastructure.repository.db.dao.VoiceConvertRecordPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.VoiceConvertRecordPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * 扫描录音转换任务,扫描refreshTime为最近24小时的，解析中的数据，查询录音解析结果
 *
 * @author: yangxiyu
 * @date: 2024/7/29 16:33
 * @version: 1.0
 */
@Slf4j
@Component
public class VoiceScanJob implements SimpleJob {

    /**
     *
     */
    @Resource
    private VoiceConvertRecordPoMapper voiceConvertRecordPoMapper;
    @Resource
    @Lazy
    private FileManageApplication fileManageApplication;
    @Resource
    private VoiceConvertRecordRepository voiceConvertRecordRepository;
    @Resource
    @Lazy
    private ExecutorPoolFactory executorPoolFactory;


    @Override
    public void execute(ShardingContext shardingContext) {

        try {
            LambdaQueryWrapper<VoiceConvertRecordPo> queryWrapper = Wrappers.lambdaQuery();

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime start = now.plusDays(-1);


            queryWrapper.eq(VoiceConvertRecordPo::getStatus, 1)
                    .gt(VoiceConvertRecordPo::getRefreshTime, TimeUtils.localDateTimeToDate(start))
                    .lt(VoiceConvertRecordPo::getRefreshTime, now);

            List<VoiceConvertRecord> records = voiceConvertRecordRepository.list(1, TimeUtils.localDateTimeToDate(start), TimeUtils.localDateTimeToDate(now));

            if (CollectionUtils.isEmpty(records)) {
                log.error("VoiceScanJob->recordPos is empty now={}", now);
                return;
            }

            records.forEach(e -> {

                VoiceTask task = e.findRunningTask();
                if (Objects.nonNull(task)) {
                    ExecutorService executorService = executorPoolFactory.get(ThreadPoolConfigEnum.VOICE_JOB_POOL);
                    try {
                        CompletableFuture.runAsync(() -> {
                            fileManageApplication.processVoiceText(task);
                        }, executorService);
                    } catch (Exception exception) {
                        log.error("VoiceScanJob->processVoiceText error e={}", JSON.toJSONString(e), exception);
                    }
                } else {
                    log.warn("VoiceScanJob->processVoiceText error task is null voiceId={}", e.getVoiceId());
                }
            });
        } catch (Exception ex) {
            log.error("VoiceScanJob->execute error", ex);
        }


    }


}
