package com.jdh.o2oservice.web.controller.ztools;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.support.service.AutoBotsApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.report.bo.MedReportIndicatorQueryBO;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportIndicatorRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsRequestBO;
import com.jdh.o2oservice.core.domain.support.autobos.bo.AutoBotsResultBO;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import com.jdh.o2oservice.export.report.AbnormalTypeEnum;
import com.jdh.o2oservice.export.report.dto.StructQuickReportContentDTO;
import com.jdh.o2oservice.export.report.dto.StructQuickReportResultIndicatorDTO;
import com.jdh.o2oservice.export.ztools.query.AiReportReadRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description 研发人员使用
 */
@Slf4j
@RestController
@RequestMapping("/tools/dev")
public class DevToolController {

    @Autowired
    private ProductServiceItemApplication productServiceItemApplication;

    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    @Autowired
    private MedicalReportApplication medicalReportApplication;

    @Autowired
    private MedicalReportRepository medicalReportRepository;

    @Autowired
    private MedicalReportIndicatorRepository medicalReportIndicatorRepository;

    @Resource
    private FileManageService fileManageService;

    @Resource
    private Cluster jimClient;

    @Resource
    private AutoBotsApplication autoBotsApplication;

    private String exceptionGroupKey = "exceptionGroupKey";


    /**
     * 清洗报告数据,ai解读报告结构化数据,生成ai小结
     * @param aiReportReadRequest
     * @return
     */
    @ResponseBody
    @LogAndAlarm
    @RequestMapping(method = RequestMethod.POST, value = "/medicalreport/airead")
    public Response<String> aiRead(@RequestBody AiReportReadRequest aiReportReadRequest) {

        this.aiRead("12联",aiReportReadRequest.getVersion(),aiReportReadRequest.getKeyWordFomart());
        this.aiRead("16联",aiReportReadRequest.getVersion(),aiReportReadRequest.getKeyWordFomart());
        return ResponseUtil.buildSuccResponse("");
    }

    /**
     * 处理12联的标品数据,生成报告ai解读
     */
    private void aiRead(String itemName,Integer version,String keyWordFomart){
        jimClient.del(exceptionGroupKey+itemName+version);

        //查询12联服务项目
        ServiceItemQuery serviceItemQuery = ServiceItemQuery.builder().build();
        serviceItemQuery.setItemName(itemName);
        serviceItemQuery.setPageSize(100);
        PageDto<ServiceItemDto> pageDto = productServiceItemApplication.queryServiceItemPage(serviceItemQuery);
        List<ServiceItemDto> serviceItemDtos = pageDto.getList();

        //构造查询参数
        List<Long> itemIds = serviceItemDtos.stream().map(ServiceItemDto::getItemId).collect(Collectors.toList());
        MedicalPromiseListQuery medicalPromiseListQuery = MedicalPromiseListQuery.builder().build();
        medicalPromiseListQuery.setServiceItemIds(itemIds);
        medicalPromiseListQuery.setReportStatus(1);
        medicalPromiseListQuery.setPageNum(1);
        medicalPromiseListQuery.setPageSize(50);

        //查询已出报告的检测单
        PageDto<MedicalPromise> medicalPromisePageDto = medicalPromiseRepository.queryMedicalPromisePage(medicalPromiseListQuery);
        log.info("DevToolController aiRead 共计: {}页",medicalPromisePageDto.getTotalPage());
        for (int i = 1; i <= medicalPromisePageDto.getTotalPage(); i++) {
            log.info("DevToolController aiRead 当前第{}页",i);
            medicalPromiseListQuery.setPageNum(i);
            PageDto<MedicalPromise> loopMedPromisePageDto = medicalPromiseRepository.queryMedicalPromisePage(medicalPromiseListQuery);
            if(CollectionUtils.isEmpty(loopMedPromisePageDto.getList())){
                return;
            }
            loopMedPromisePageDto.getList().forEach(loopMedPromiseDto->{
                try {

                    log.info("DevToolController aiRead loopMedPromiseDto={}", JSON.toJSONString(loopMedPromiseDto));
                    MedicalReport medicalReport = medicalReportRepository.getByMedicalPromiseId(loopMedPromiseDto.getMedicalPromiseId());
                    log.info("DevToolController aiRead medicalReport={}", JSON.toJSONString(medicalReport));
                    if(medicalReport==null){
                        log.info("DevToolController aiRead medicalReport为空,终止逻辑");
                        return;
                    }
                    if(StringUtils.isEmpty(medicalReport.getStructReportOss())){
                        log.info("DevToolController aiRead 非结构化报告,终止逻辑");
                        return;
                    }
                    MedReportIndicatorQueryBO medReportIndicatorQueryBO = new MedReportIndicatorQueryBO();
                    medReportIndicatorQueryBO.setReportIds(Collections.singleton(medicalReport.getId()+""));

                    String structReportStr = this.getStructReportStr(medicalReport.getStructReportOss());
                    List<StructQuickReportContentDTO> structQuickReportContentDTOS = JSON.parseArray(structReportStr, StructQuickReportContentDTO.class);
                    List<StructQuickReportResultIndicatorDTO> collect = structQuickReportContentDTOS.stream().flatMap(s -> s.getReportResult().stream()).flatMap(s -> s.getIndicators().stream()).filter(s -> !Objects.equals(s.getAbnormalType(), AbnormalTypeEnum.NORMAL.getType())).collect(Collectors.toList());

                    int abnormalCount =collect.size();
                    if(abnormalCount<1){
                        log.info("DevToolController aiRead 检测指标没有异常项,终止逻辑");
                        return;
                    }
                    log.info("DevToolController aiRead 有异常项 medicalReportIndicators={}", JSON.toJSONString(loopMedPromiseDto));
                    List<String> indicatorNames = collect.stream().map(StructQuickReportResultIndicatorDTO::getIndicatorName).collect(Collectors.toList());
                    indicatorNames.add(version+"");
                    //排序(升序)
                    Collections.sort(indicatorNames);
                    String cacheKey = Joiner.on(",").join(indicatorNames).trim();

                    if(StringUtils.isNotEmpty(jimClient.get(cacheKey))){
                        log.info("异常情况已解读,无须重复解读,逻辑终止");
                        return;
                    }

                    //TODO 调用鹏飞AI解读接口
                    jimClient.setEx(cacheKey,"1", 1, TimeUnit.DAYS);

                    AutoBotsRequestBO bo = new AutoBotsRequestBO();
                    bo.setErp("zhangcan37");
                    //异常报告数据
                    bo.setKeyword(String.format(keyWordFomart,structReportStr));
                    bo.setExtendId("1");
                    bo.setReqId(String.valueOf(System.currentTimeMillis()));
                    bo.setTraceId(UUID.randomUUID().toString()+"-"+medicalReport.getId());
                    bo.setCallbackUrl("https://jdh-o2oservice-beta.jd.com/autobots/callbackResult");
                    AutoBotsResultBO autoBotsResultBO = autoBotsApplication.searchAiRequest(
                            "17934","a39bb6d7523144aaa7d82aee01131f51",bo
                    );
                    log.info("报告ID:{}  bo={} autoBotsResultBO={}",medicalReport.getId(), JSON.toJSONString(bo),JSON.toJSONString(autoBotsResultBO));
                    jimClient.sAdd(exceptionGroupKey+itemName+version,cacheKey);
                    Thread.sleep(10000L);
                } catch (Exception e) {
                    log.info("DevToolController aiRead 异常",e);
                }
            });
        }

    }

    /**
     * 下载结构化报告字符串
     *
     * @param jssUrl
     * @return
     */
    private String getStructReportStr(String jssUrl) {
        if (StringUtils.isBlank(jssUrl)) {
            return null;
        }
        log.info("MedicalReportApplicationImpl.getStructReportStr -> jssUrl={}", jssUrl);
        InputStream inputStream = fileManageService.get(jssUrl);
        String result = new BufferedReader(new InputStreamReader(inputStream))
                .lines().collect(Collectors.joining("\n"));
        log.info("MedicalReportApplicationImpl.getStructReportStr -> result={}", result);
        return result;
    }

    public static void main(String[] args) {
        Long s= (long) (2<<24);
        System.out.println(s/1024/1024);
        System.out.println(s);

        String cacheKey = Joiner.on(",").join(Arrays.asList("甲流,嗜血")).trim();
        System.out.println(cacheKey);
    }
}
