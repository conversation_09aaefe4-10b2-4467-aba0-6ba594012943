package com.jdh.o2oservice.web.controller.ztools;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseApplicationConverter;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.convert.ProductServiceIndicatorConvertor;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.trade.convert.JdOrderConverter;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.application.ztools.AngelWorkToolsApplication;
import com.jdh.o2oservice.application.ztools.MedicalPromiseToolsApplication;
import com.jdh.o2oservice.application.ztools.convert.ZtoolsConvert;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.common.enums.SupplierTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationModeTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelShipCallBackContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipCancelCodeStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.product.context.JdhItemIndicatorRelQueryContext;
import com.jdh.o2oservice.core.domain.product.context.ServiceIndicatorQueryContext;
import com.jdh.o2oservice.core.domain.product.model.Indicator;
import com.jdh.o2oservice.core.domain.product.model.ServiceItemIndicatorRel;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceIndicatorRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.angel.dto.AngelStationDto;
import com.jdh.o2oservice.export.angelpromise.cmd.AngelWorkCancelShipCmd;
import com.jdh.o2oservice.export.angelpromise.cmd.ShipInfoForCallBackRequest;
import com.jdh.o2oservice.export.angelpromise.dto.AngelShipDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailForManDto;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseReportCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.ResetReportStatusCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.product.dto.ServiceIndicatorDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.provider.cmd.ListLaboratoryByStoreNameCmd;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.report.dto.*;
import com.jdh.o2oservice.export.report.query.MedicalReportRequest;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.ztools.cmd.*;
import com.jdh.o2oservice.export.ztools.query.AngelWorkPageRequest;
import com.jdh.o2oservice.export.ztools.query.ManMedicalPromiseRequest;
import com.jdh.o2oservice.export.ztools.query.QueryAngelStationRequest;
import com.jdh.o2oservice.export.ztools.query.ServiceItemIndicatorRequest;
import com.jdh.o2oservice.export.ztools.dto.PromiseForManDTO;
import com.jdh.o2oservice.export.ztools.query.*;
import com.jdh.o2oservice.vertical.enums.ShunFengShipStatusEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName AngelWorkManToolController
 * @Description
 * <AUTHOR>
 * @Date 2024/9/3 20:32
 */
@Slf4j
@RestController
@RequestMapping("/tools/manageAngelWork")
public class AngelWorkManToolController {

    @Resource
    private AngelWorkToolsApplication angelWorkToolsApplication;

    @Resource
    private ProviderStoreApplication providerStoreApplication;

    @Resource
    private StationApplication stationApplication;

    @Resource
    private MedicalPromiseToolsApplication medicalPromiseToolsApplication;

    /**
     * 派单 application
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     *
     */
    @Resource
    private PromiseApplication promiseApplication;

    @Resource
    private AngelShipRepository angelShipRepository;

    /**
     * 工单
     */
    @Resource
    private AngelWorkApplication angelWorkApplication;

    @Resource
    private JdhServiceItemRepository jdhServiceItemRepository;

    @Resource
    private JdhServiceIndicatorRepository jdhServiceIndicatorRepository;

    @Resource
    private MedicalReportApplication medicalReportApplication;

    @Resource
    private FileManageService fileManageService;

    @Resource
    private JdOrderApplication jdOrderApplication;

    @ResponseBody
    @LogAndAlarm(jKey = "AngelWorkManToolController.queryAngelWorkPage")
    @RequestMapping(method = RequestMethod.POST, value = "/queryAngelWorkPage")
    public Response<PageDto<AngelWorkDetailForManDto>> queryAngelWorkPage(AngelWorkPageRequest angelWorkPageRequest) {
        log.info("[AngelWorkManToolController -> queryAngelWorkPage],查询工单列表入参!angelWorkPageRequest={}", JSON.toJSONString(angelWorkPageRequest));
        PageDto<AngelWorkDetailForManDto> angelWorkDtoPageDto = angelWorkToolsApplication.queryAngelWorkPage(angelWorkPageRequest);
        return ResponseUtil.buildSuccResponse(angelWorkDtoPageDto);
    }

    @ResponseBody
    @LogAndAlarm(jKey = "AngelWorkManToolController.manBindSpecimenCode")
    @RequestMapping(method = RequestMethod.POST, value = "/manBindSpecimenCode")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具绑码", recordParamBizIdExpress = {"args[0].medicalPromiseId", "args[0].specimenCode"})
    public Response<Boolean> manBindSpecimenCode(ManBindSpecimenCodeCmd manBindSpecimenCodeCmd) {
        log.info("[AngelWorkManToolController -> manBindSpecimenCode],运营工具绑码!manBindSpecimenCodeCmd={}", JSON.toJSONString(manBindSpecimenCodeCmd));
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.manBindSpecimenCode(manBindSpecimenCodeCmd));
    }

    @ResponseBody
    @LogAndAlarm(jKey = "AngelWorkManToolController.queryMedicalPromise")
    @RequestMapping(method = RequestMethod.POST, value = "/queryMedicalPromise")
    public Response<List<MedicalPromiseDTO>> queryMedicalPromise(ManMedicalPromiseRequest manMedicalPromiseRequest) {
        log.info("[AngelWorkManToolController -> queryMedicalPromise],运营工具绑码!manBindSpecimenCodeCmd={}", JSON.toJSONString(manMedicalPromiseRequest));
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.queryMedicalPromise(manMedicalPromiseRequest));
    }

    @ResponseBody
    @LogAndAlarm(jKey = "AngelWorkManToolController.syncMedicalStation")
    @RequestMapping(method = RequestMethod.POST, value = "/syncMedicalStation")
    public Response<Boolean> syncMedicalStation(ManSyncStationCmd manSyncStationCmd) {
        log.info("[AngelWorkManToolController -> syncStation],运营工具绑码!manSyncStationCmd={}", JSON.toJSONString(manSyncStationCmd));
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.syncMedicalStation(manSyncStationCmd));
    }

    @ResponseBody
    @LogAndAlarm(jKey = "AngelWorkManToolController.syncPromiseStation")
    @RequestMapping(method = RequestMethod.POST, value = "/syncPromiseStation")
    public Response<Boolean> syncPromiseStation(ManSyncStationCmd manSyncStationCmd) {
        log.info("[AngelWorkManToolController -> syncPromiseStation],运营工具绑码!manSyncStationCmd={}", JSON.toJSONString(manSyncStationCmd));
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.syncPromiseStation(manSyncStationCmd));
    }

    /**
     * 查询实验室列表
     *
     * @param storeNameCmd
     * @return
     */
    @ResponseBody
    @LogAndAlarm(jKey = "AngelWorkManToolController.queryStation")
    @RequestMapping(method = RequestMethod.POST, value = "/queryStation")
    public Response<List<StoreInfoDto>> queryStation(ListLaboratoryByStoreNameCmd storeNameCmd) {
        storeNameCmd.setBusinessType(16);
        storeNameCmd.setPageSize(CommonConstant.TWENTY);
        storeNameCmd.setPageNum(CommonConstant.ONE);
        List<StoreInfoDto> result = providerStoreApplication.listLaboratoryByStoreName(storeNameCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询服务站列表
     *
     * @param angelStationRequest
     * @return
     */
    @ResponseBody
    @LogAndAlarm(jKey = "AngelWorkManToolController.queryAngelStation")
    @RequestMapping(method = RequestMethod.POST, value = "/queryAngelStation")
    public Response<List<AngelStationDto>> queryAngelStation(QueryAngelStationRequest angelStationRequest) {
        angelStationRequest.setPageNum(CommonConstant.ONE);
        angelStationRequest.setPageSize(CommonConstant.TWENTY);
        angelStationRequest.setStationModeType(AngelStationModeTypeEnum.FULL_TIME.getCode());
        List<AngelStationDto> angelStationDtoList = stationApplication.queryAngelStationList(angelStationRequest);
        return ResponseUtil.buildSuccResponse(angelStationDtoList);
    }

    /**
     * 绑定实验室
     *
     * @param manBindStationCmd
     * @return
     */
    @ResponseBody
    @LogAndAlarm(jKey = "AngelWorkManToolController.manBindStation")
    @RequestMapping(method = RequestMethod.POST, value = "/manBindStation")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具绑定实验室", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> manBindStation(ManBindStationCmd manBindStationCmd) {
        TargetStationCmd targetStationCmd = new TargetStationCmd();
        targetStationCmd.setStationId(manBindStationCmd.getStationId());
        targetStationCmd.setAngelStationId(StringUtils.isNotBlank(manBindStationCmd.getAngelStationId()) ? Long.valueOf(manBindStationCmd.getAngelStationId()) : null);
        targetStationCmd.setMedicalPromiseId(StringUtils.isNotBlank(manBindStationCmd.getMedicalPromiseId()) ? Long.valueOf(manBindStationCmd.getMedicalPromiseId()) : null);
        return ResponseUtil.buildSuccResponse(medicalPromiseToolsApplication.targetStation(targetStationCmd));
    }

    /**
     * 重置检测单报告状态，供应商可以重推报告
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping("/resetReportStatus")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具报告重置", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> resetReportStatus(@RequestBody ResetReportStatusCmd cmd){
        cmd.setOperator(cmd.getOperator());
        cmd.setRoleType(NumConstant.NUM_4);
        medicalPromiseApplication.resetReportStatus(cmd);
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * 查询履约单
     * @param promiseIdRequest
     * @return
     */
    @RequestMapping("/queryPromise")
    @LogAndAlarm
    public Response<PromiseDto> queryPromise(@RequestBody PromiseIdRequest promiseIdRequest){
        PromiseDto byPromiseId = promiseApplication.findPromiseByPromiseId(promiseIdRequest);
        return Response.buildSuccessResult(byPromiseId);
    };

    /**
     * 查询运单
     * @param angelShipDBQuery
     * @return
     */
    @RequestMapping("/queryAngelShipDtoList")
    @LogAndAlarm
    public Response<List<AngelShipDto>> queryAngelShipDtoList(@RequestBody AngelShipDBQuery angelShipDBQuery){
        List<AngelShipDto> angelShipDtos = AngelPromiseApplicationConverter.instance.convertToAngelShipDtoList(angelShipRepository.findList(angelShipDBQuery));
        return  Response.buildSuccessResult(angelShipDtos);
    }

    /**
     * 推送报告
     * @param pushMedicalReportCmd
     * @return
     */
    @RequestMapping("/pushReport")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具推送报告", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> pushReport(@RequestBody PushMedicalReportCmd pushMedicalReportCmd){
        MedicalPromiseReportCmd medicalPromiseReportCmd = new MedicalPromiseReportCmd();
        BeanUtils.copyProperties(pushMedicalReportCmd,medicalPromiseReportCmd);
        MedicalPromiseRequest medicalPromiseRequest = new MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(pushMedicalReportCmd.getMedicalPromiseId());
        medicalPromiseRequest.setPatientDetail(Boolean.TRUE);
        MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(medicalPromiseRequest);

        //组装用户信息
        StructQuickReportUserInfoDTO reportUserInfoDTO = new StructQuickReportUserInfoDTO();
        reportUserInfoDTO.setAge(medicalPromiseDTO.getAge());
        reportUserInfoDTO.setMarriage(medicalPromiseDTO.getMarriage());
        reportUserInfoDTO.setName(medicalPromiseDTO.getName());
        reportUserInfoDTO.setGender(medicalPromiseDTO.getGender());
        reportUserInfoDTO.setPhone(medicalPromiseDTO.getPhone());
        pushMedicalReportCmd.getStructReport().setUserInfo(reportUserInfoDTO);

        pushMedicalReportCmd.getStructReport().getReportResult().get(0).setSampleBarcode(medicalPromiseDTO.getSpecimenCode());

        //组装报告信息
        StructQuickReportBaseInfoDTO structQuickReportBaseInfoDTO = new StructQuickReportBaseInfoDTO();
        structQuickReportBaseInfoDTO.setReportCreateTime(DateUtil.format(new Date(),CommonConstant.YMDHMS));
        structQuickReportBaseInfoDTO.setReceivedSampleTime(DateUtil.format(medicalPromiseDTO.getCheckTime(),CommonConstant.YMDHMS));
        structQuickReportBaseInfoDTO.setTestingMethod("system");
        structQuickReportBaseInfoDTO.setTestingOperator("system");
        structQuickReportBaseInfoDTO.setTestingApprover("system");
        structQuickReportBaseInfoDTO.setSampleCharacteristics("无肉眼可见异常");
        pushMedicalReportCmd.getStructReport().setReportBasicInfo(structQuickReportBaseInfoDTO);



        medicalPromiseReportCmd.setJdStructReportStr(JsonUtil.toJSONString(pushMedicalReportCmd.getStructReport()));
        medicalPromiseApplication.pushMedicalPromiseReport(medicalPromiseReportCmd);
        return Response.buildSuccessResult(Boolean.TRUE);
    }

    /**
     * 取消运单
     * @param shipStatusCmd
     * @return
     */
    @LogAndAlarm
    @RequestMapping("/changeShipStatus")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具修改运单状态", recordParamBizIdExpress = {"args[0].orderId"})
    public Response<Boolean> changeShipStatus(@RequestBody ShipStatusCmd shipStatusCmd){


        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setShipIds(Collections.singletonList(Long.parseLong(shipStatusCmd.getOrderId())));
        AngelShipDto angelShipDto = angelWorkApplication.getAngelShipByShipInfo(angelShipDBQuery);

        if(DeliveryTypeEnum.RIDER_DELIVERY.getType().equals(angelShipDto.getType())){
            ShipInfoForCallBackRequest shipInfoForCallBackRequest = new ShipInfoForCallBackRequest();
            shipInfoForCallBackRequest.setCancelFrom(shipStatusCmd.getCancelFrom());
            shipInfoForCallBackRequest.setClientId(shipStatusCmd.getClientId());
            shipInfoForCallBackRequest.setDmId(Integer.valueOf(shipStatusCmd.getDmId()));
            shipInfoForCallBackRequest.setDmMobile(shipStatusCmd.getDmMobile());
            shipInfoForCallBackRequest.setOrderStatus(shipStatusCmd.getOrderStatus());
            shipInfoForCallBackRequest.setDmName(shipStatusCmd.getDmName());
            shipInfoForCallBackRequest.setOrderStatus(shipStatusCmd.getOrderStatus());
            shipInfoForCallBackRequest.setSignature(shipStatusCmd.getSignature());
            shipInfoForCallBackRequest.setUpdateTime( DateUtil.current()/1000);
            shipInfoForCallBackRequest.setOrderId(shipStatusCmd.getOrderId());
            shipInfoForCallBackRequest.setFinishCode(shipStatusCmd.getFinishCode());
//        BeanUtils.copyProperties(shipStatusCmd,shipInfoForCallBackRequest);
            shipInfoForCallBackRequest.setLongitude(116.414409);
            shipInfoForCallBackRequest.setLatitude(39.840511);
            angelWorkApplication.shipStatusCallback(shipInfoForCallBackRequest);
        }else{
            AngelShipCallBackContext angelShipCallBackContext = AngelShipCallBackContext.builder().build();

            angelShipCallBackContext.setLongitude(116.414409);
            angelShipCallBackContext.setLatitude(39.840511);
            angelShipCallBackContext.setOrderStatus(shipStatusCmd.getOrderStatus());
            angelShipCallBackContext.setOrderId(Long.parseLong(shipStatusCmd.getOrderId()));
            angelShipCallBackContext.setDmId(shipStatusCmd.getDmId());
            angelShipCallBackContext.setDmName(shipStatusCmd.getDmName());
            angelShipCallBackContext.setDmMobile(shipStatusCmd.getDmMobile());
            angelShipCallBackContext.setTime(TimeUtils.dateTimeToStr(new Date()));
            angelWorkApplication.shipStatusCallback(angelShipCallBackContext);
        }

        return Response.buildSuccessResult(Boolean.TRUE);
    }

    /**
     * 取消运单
     * @param shipStatusCmd
     * @return
     */
    @LogAndAlarm
    @RequestMapping("/cancelShip")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具取消运单", recordParamBizIdExpress = {"args[0].orderId"})
    public Response<Boolean> cancelShip(@RequestBody ShipStatusCmd shipStatusCmd){
        AngelWorkCancelShipCmd angelWorkCancelShipCmd = new AngelWorkCancelShipCmd();
        angelWorkCancelShipCmd.setWorkId(shipStatusCmd.getWorkId());
        angelWorkCancelShipCmd.setShipId(shipStatusCmd.getOrderId());
        angelWorkCancelShipCmd.setOperateSource(CommonConstant.ONE);
        angelWorkCancelShipCmd.setStandCancelCode(AngelShipCancelCodeStatusEnum.DEV_CANCEL.getType());
        angelWorkApplication.cancelShip(angelWorkCancelShipCmd);
        return Response.buildSuccessResult(Boolean.TRUE);
    }


    /**
     * 修改履约单状态
     * @return
     */
    @LogAndAlarm
    @RequestMapping("/changePromiseStatus")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具修改履约单状态", recordParamBizIdExpress = {"args[0].promiseId"})
    public Response<Boolean> changePromiseStatus(@RequestBody PromiseStatusCmd promiseStatusCmd){
//        promiseApplication.freeze()
        return Response.buildSuccessResult(Boolean.TRUE);
    }

    /**
     * 修改工单状态
     * @return
     */
    @LogAndAlarm
    @RequestMapping("/changeWorkStatus")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具修改工单状态", recordParamBizIdExpress = {"args[0].workId"})
    public Response<Boolean> changeWorkStatus(@RequestBody WorkStatusCmd workStatusCmd){

        return Response.buildSuccessResult(Boolean.TRUE);
    }


    /**
     * 查询项目
     * @param serviceItemList
     * @return
     */
    @LogAndAlarm
    @RequestMapping("/queryServiceItemIndicator")
    public Response<List<ServiceIndicatorDto>> queryServiceItemIndicator(@RequestBody ServiceItemIndicatorRequest serviceItemList){
        JdhItemIndicatorRelQueryContext relQueryContext = JdhItemIndicatorRelQueryContext.builder().serviceItemIds(Sets.newHashSet(Long.valueOf(serviceItemList.getItemId()))).build();
        List<ServiceItemIndicatorRel> relList = jdhServiceItemRepository.queryServiceItemIndicatorRel(relQueryContext);
        //查询指标信息
        if (CollectionUtils.isNotEmpty(relList)) {
            Set<Long> indicatorIds = relList.stream().map(ServiceItemIndicatorRel::getIndicatorId).collect(Collectors.toSet());
            ServiceIndicatorQueryContext indicatorQueryContext = ServiceIndicatorQueryContext.builder().indicatorIds(indicatorIds).build();
            List<Indicator> indicators = jdhServiceIndicatorRepository.queryIndicatorList(indicatorQueryContext);
//            ItemDomainServiceConvert.ins.packIndicators(serviceItemList, relList, indicators);
            List<ServiceIndicatorDto> serviceIndicatorDtos = ProductServiceIndicatorConvertor.ins.convertToServiceIndicatorDtoList(indicators);
            return Response.buildSuccessResult(serviceIndicatorDtos);

        }
        return Response.buildSuccessResult(null);

    }

    /**
     * 查询报告
     * @param medicalReportRequest
     * @return
     */
    @LogAndAlarm
    @RequestMapping("/queryReportForMan")
    public Response<MedicalReportForManDTO> queryReportForMan(@RequestBody MedicalReportRequest medicalReportRequest){
        com.jdh.o2oservice.export.report.query.MedicalPromiseRequest medicalPromiseRequest = new com.jdh.o2oservice.export.report.query.MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(medicalReportRequest.getMedicalPromiseId());
        MedicalReportDTO byMedicalPromiseId = medicalReportApplication.getByMedicalPromiseId(medicalPromiseRequest);
        String structReportStr = getStructReportStr(byMedicalPromiseId.getStructReportOss());
        StructQuickReportContentDTO structQuickReportContentDTO = JsonUtil.parseObject(structReportStr, StructQuickReportContentDTO.class);
        MedicalReportForManDTO medicalReportForManDTO = new MedicalReportForManDTO();
        medicalReportForManDTO.setId(byMedicalPromiseId.getId());
        medicalReportForManDTO.setMedicalPromiseId(byMedicalPromiseId.getMedicalPromiseId());
        medicalReportForManDTO.setStructQuickReportContentDTO(structQuickReportContentDTO);
        String publicUrl = fileManageService.getPublicUrl(byMedicalPromiseId.getReportOss(), Boolean.TRUE, DateUtil.offsetMinute(new Date(), 30));
        medicalReportForManDTO.setReportUrl(publicUrl);
        return ResponseUtil.buildSuccResponse(medicalReportForManDTO);
    }


    /**
     * 订单取消
     * @param orderForManCmd
     * @return
     */
    @LogAndAlarm
    @RequestMapping("/cancelOrder")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具取消订单", recordParamBizIdExpress = {"args[0].orderId"})
    public Response<Boolean> cancelOrder(@RequestBody OrderForManCmd orderForManCmd){

        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * 查询履约单
     * @param promiseForManRequest
     * @return
     */
    @LogAndAlarm
    @RequestMapping("/queryPromiseForMan")
    public Response<PromiseForManDTO> queryPromiseForMan(@RequestBody PromiseForManRequest promiseForManRequest){
        PromiseDto promiseByPromiseId = promiseApplication.findPromiseByPromiseId(PromiseIdRequest.builder().promiseId(Long.valueOf(promiseForManRequest.getPromiseId())).build());
        PromiseForManDTO convert = ZtoolsConvert.ins.convert(promiseByPromiseId);
        return ResponseUtil.buildSuccResponse(convert);
    }

    /**
     * 查询订单信息
     * @param orderForManRequest
     * @return
     */
    @LogAndAlarm
    @RequestMapping("/queryJdOrderForMan")
    public Response<JdOrderDTO> queryJdOrderForMan(@RequestBody OrderForManRequest orderForManRequest){
        JdOrder order = jdOrderApplication.queryJdOrderByOrderId(Long.valueOf(orderForManRequest.getOrderId()));
        JdOrderDTO jdOrderDTO = JdOrderConverter.INSTANCE.JdOrderToDTO(order);
        return ResponseUtil.buildSuccResponse(jdOrderDTO);
    }

    /**
     *
     * @param medicalReportRequest
     * @return
     */
    @LogAndAlarm
    @RequestMapping("/anewStructQuickReportFromSourceOss")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具重新解析结构化报告", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<String> anewStructQuickReportFromSourceOss(@RequestBody MedicalReportRequest medicalReportRequest){
        String url = medicalPromiseApplication.anewStructQuickReportFromSourceOss(medicalReportRequest.getMedicalPromiseId());
        return ResponseUtil.buildSuccResponse(url);
    }

    /**
     * 下载结构化报告字符串
     *
     * @param jssUrl
     * @return
     */
    private String getStructReportStr(String jssUrl) {
        if (StringUtils.isBlank(jssUrl)) {
            return null;
        }
        log.info("MedicalReportApplicationImpl.getStructReportStr -> jssUrl={}", jssUrl);
        InputStream inputStream = fileManageService.get(jssUrl);
        String result = new BufferedReader(new InputStreamReader(inputStream))
                .lines().collect(Collectors.joining("\n"));
        log.info("MedicalReportApplicationImpl.getStructReportStr -> result={}", result);
        return result;
    }


}
