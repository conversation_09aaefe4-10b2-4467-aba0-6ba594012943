package com.jdh.o2oservice.listener.dts;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.shorturl.exceptions.BussinessException;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.ServiceHomeNewTypeEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.base.model.Birthday;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.UserName;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.function.Predicate;

/**
 * BinlakeAppointmentEventListener xfyl_appointment_info表binlake
 *
 * <AUTHOR>
 * @version 2024/7/2 18:49
 **/
@Slf4j
@Component
public class BinlakeAppointmentEventListener extends BinlakeBaseEventListener implements MessageListener {

    /**
     * 序列化器
     */
    private static final MessageDeserialize<Message> DESERIALIZE = new JMQMessageDeserialize();
    /**
     * 表名称
     */
    private static final String TABLE_NAME = "xfyl_appointment_info";

    @Resource
    VoucherRepository voucherRepository;

    @Resource
    PromiseRepository promiseRepository;

    @Resource
    MedicalPromiseRepository medicalPromiseRepository;

    @JmqListener(id = "dtsConsumer", topics = {"${topics.dts.appointment}"})
    @LogAndAlarm(jKey = "com.jdh.o2oservice.listener.dts.BinlakeAppointmentEventListener.onMessage")
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        List<EntryMessage> entryMessages = DESERIALIZE.deserialize(messages);
        for (EntryMessage entryMessage : entryMessages) {
            if (null == entryMessage.getRowChange()) {
                continue;
            }
            if (!entryMessage.getEntryType().equals(WaveEntry.EntryType.ROWDATA)) {
                continue;
            }
            if (!TABLE_NAME.equals(entryMessage.getHeader().getTableName())) {
                continue;
            }
            List<WaveEntry.RowData> rowDatas = entryMessage.getRowChange().getRowDatasList();
            log.info("BinlakeAppointmentEventListener -> onMessage 消息数量, rowDatasListSize={}", entryMessage.getRowChange().getRowDatasList().size());
            WaveEntry.EventType eventType = entryMessage.getHeader().getEventType();
            log.info("BinlakeAppointmentEventListener -> onMessage eventType={}", eventType);
            for (WaveEntry.RowData rowData : rowDatas) {
                try {
                    // 不符合同步要求的行数据
                    if (filterDiscardRow(rowData.getAfterColumnsList())) {
                        continue;
                    }
                    processRowData(rowData, eventType);
                } catch (BusinessException e) {
                    log.error("BinlakeAppointmentEventListener -> onMessage business error msg={}", e.getMessage());
                    throw e;
                } catch (Exception e) {
                    log.error("BinlakeAppointmentEventListener -> onMessage Exception ", e);
                    throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
                } catch (Throwable e) {
                    log.error("BinlakeAppointmentEventListener -> onMessage Throwable ", e);
                    throw new BusinessException(SystemErrorCode.UNKNOWN_ERROR);
                }
            }
        }
    }

    /**
     * 处理单行数据
     *
     * @param rowData
     * @param eventType
     * @throws BussinessException
     */
    private void processRowData(WaveEntry.RowData rowData, WaveEntry.EventType eventType) throws ParseException {
        JdhVoucher jdhVoucher = buildJdhVoucher(rowData, eventType);
        if (Objects.isNull(jdhVoucher)) {
            return;
        }
        Long voucherId = voucherRepository.insertOrUpdateBySourceVoucherId(jdhVoucher);
        if (Objects.isNull(voucherId)) {
            return;
        }
        jdhVoucher.setVoucherId(voucherId);

        JdhPromise jdhPromise = buildJdhPromise(rowData, eventType, jdhVoucher);
        if (Objects.isNull(jdhPromise)) {
            return;
        }
        Map<Long, List<Long>> promiseIdAndPatientIds = promiseRepository.insertOrUpdateBySourceVoucherId(jdhPromise);
        if (Objects.isNull(promiseIdAndPatientIds) || promiseIdAndPatientIds.isEmpty()) {
            return;
        }
        jdhPromise.setPromiseId(promiseIdAndPatientIds.keySet().stream().findFirst().get());

        List<Long> promisePatientIdList = promiseIdAndPatientIds.get(jdhPromise.getPromiseId());
        // 存在promisePatientId时才保存medicalPromise
        if (CollectionUtil.isNotEmpty(promisePatientIdList)) {
            MedicalPromise jdhMedicalPromise = buildJdhMedicalPromise(rowData, eventType, jdhPromise, promisePatientIdList.get(0));
            medicalPromiseRepository.insertOrUpdateByPromisePatientAndServiceItem(jdhMedicalPromise);
        }

    }

    /**
     * 构建voucher
     *
     * @param rowData
     * @param eventType
     * @return
     * @throws ParseException
     */
    private JdhVoucher buildJdhVoucher(WaveEntry.RowData rowData, WaveEntry.EventType eventType) throws ParseException {
        Long skuId = null;
        JdhVoucher jdhVoucher = new JdhVoucher();
        jdhVoucher.setSourceType(0);
        jdhVoucher.setPromiseNum(1);
        jdhVoucher.setFreeze(0);
        jdhVoucher.setBranch(BRANCH_PRODUCTION);
        jdhVoucher.setVersion(SYNC_VERSION);
        jdhVoucher.setUpdateUser(SYNC_USER);
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("order_id".equalsIgnoreCase(column.getName())) {
                jdhVoucher.setSourceVoucherId(column.getValue());
            } else if ("user_pin".equalsIgnoreCase(column.getName())) {
                jdhVoucher.setUserPin(column.getValue());
                jdhVoucher.setCreateUser(column.getValue());
            } else if ("expire_date".equalsIgnoreCase(column.getName())) {
                jdhVoucher.setExpireDate(DateUtil.parseDate(column.getValue(), "yyyyMMddHHmmss"));
            } else if ("yn".equalsIgnoreCase(column.getName())) {
                jdhVoucher.setYn(Integer.valueOf(column.getValue()));
            } else if ("create_time".equalsIgnoreCase(column.getName())) {
                jdhVoucher.setCreateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("update_time".equalsIgnoreCase(column.getName())) {
                jdhVoucher.setUpdateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("order_status".equalsIgnoreCase(column.getName())) {
                jdhVoucher.setStatus(convertVoucherStatus(Integer.valueOf(column.getValue())));
            } else if ("sku_no".equalsIgnoreCase(column.getName())) {
                skuId = Long.valueOf(column.getValue());
            }
        }
        // 补充垂直身份
        ServiceHomeNewTypeEnum verticalEnum = super.getVertical(skuId);
        jdhVoucher.setVerticalCode(verticalEnum.getVerticalCode());
        jdhVoucher.setServiceType(verticalEnum.getServiceType());

        jdhVoucher.setVoucherItemList(buildJdhVoucherItem(rowData, eventType, jdhVoucher));
        return jdhVoucher;
    }

    /**
     * 构建voucher明细
     *
     * @param rowData
     * @param eventType
     * @return
     * @throws ParseException
     */
    private List<JdhVoucherItem> buildJdhVoucherItem(WaveEntry.RowData rowData, WaveEntry.EventType eventType, JdhVoucher voucher) throws ParseException {
        List<JdhVoucherItem> jdhVoucherItemList = new ArrayList<>();
        JdhVoucherItem jdhVoucherItem = new JdhVoucherItem();
        jdhVoucherItemList.add(jdhVoucherItem);
        jdhVoucherItem.setVerticalCode(voucher.getVerticalCode());
        jdhVoucherItem.setServiceType(voucher.getServiceType());
        jdhVoucherItem.setVoucherId(voucher.getVoucherId());
        jdhVoucherItem.setBranch(BRANCH_PRODUCTION);
        jdhVoucherItem.setVersion(SYNC_VERSION);
        jdhVoucherItem.setUpdateUser(SYNC_USER);
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("sku_no".equalsIgnoreCase(column.getName())) {
                jdhVoucherItem.setServiceId(Long.valueOf(column.getValue()));
            } else if ("yn".equalsIgnoreCase(column.getName())) {
                jdhVoucherItem.setYn(Integer.valueOf(column.getValue()));
            } else if ("user_pin".equalsIgnoreCase(column.getName())) {
                jdhVoucherItem.setCreateUser(column.getValue());
            } else if ("create_time".equalsIgnoreCase(column.getName())) {
                jdhVoucherItem.setCreateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("update_time".equalsIgnoreCase(column.getName())) {
                jdhVoucherItem.setUpdateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        return jdhVoucherItemList;
    }

    /**
     * 构建JdhPromise
     *
     * @param rowData
     * @param eventType
     * @return
     * @throws ParseException
     */
    private JdhPromise buildJdhPromise(WaveEntry.RowData rowData, WaveEntry.EventType eventType, JdhVoucher jdhVoucher) throws ParseException {
        PromiseAppointmentTime appointmentTime = new PromiseAppointmentTime();
        appointmentTime.setDateType(2);
        appointmentTime.setIsImmediately(false);
        JdhPromise jdhPromise = new JdhPromise();
        jdhPromise.setVerticalCode(jdhVoucher.getVerticalCode());
        jdhPromise.setServiceType(jdhVoucher.getServiceType());
        jdhPromise.setVoucherId(jdhVoucher.getVoucherId());
        jdhPromise.setSourceVoucherId(jdhVoucher.getSourceVoucherId());
        jdhPromise.setSerialNum(0);
        jdhPromise.setPromiseType(1);
        jdhPromise.setFreeze(0);
        jdhPromise.setBranch(BRANCH_PRODUCTION);
        jdhPromise.setVersion(SYNC_VERSION);
        jdhPromise.setUpdateUser(SYNC_USER);
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("expire_date".equalsIgnoreCase(column.getName())) {
                jdhPromise.setExpireDate(cn.hutool.core.date.DateUtil.toLocalDateTime(DateUtil.parseDate(column.getValue(), "yyyyMMddHHmmss")));
            } else if ("yn".equalsIgnoreCase(column.getName())) {
                jdhPromise.setYn(Integer.valueOf(column.getValue()));
            } else if ("user_pin".equalsIgnoreCase(column.getName())) {
                jdhPromise.setUserPin(column.getValue());
                jdhPromise.setCreateUser(column.getValue());
            } else if ("create_time".equalsIgnoreCase(column.getName())) {
                jdhPromise.setCreateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("update_time".equalsIgnoreCase(column.getName())) {
                jdhPromise.setUpdateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("appointment_start_time".equalsIgnoreCase(column.getName())) {
                appointmentTime.setAppointmentStartTime(cn.hutool.core.date.DateUtil.toLocalDateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss")));
            } else if ("appointment_end_time".equalsIgnoreCase(column.getName())) {
                appointmentTime.setAppointmentEndTime(cn.hutool.core.date.DateUtil.toLocalDateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss")));
            } else if ("detail_addr".equalsIgnoreCase(column.getName())) {
                PromiseStation store = new PromiseStation();
                store.setStoreAddr(column.getValue());
                jdhPromise.setStore(store);
            } else if ("order_status".equalsIgnoreCase(column.getName())) {
                jdhPromise.setPromiseStatus(convertPromiseStatus(Integer.valueOf(column.getValue())));
            }
        }
        jdhPromise.setAppointmentTime(appointmentTime);

        jdhPromise.setPatients(buildJdhPromisePatientList(rowData, eventType, jdhPromise));
        jdhPromise.setServices(buildJdhPromiseServiceDetailList(rowData, eventType, jdhPromise));
        jdhPromise.setPromiseExtends(buildJdhPromiseExtendList(rowData, eventType, jdhPromise));
        return jdhPromise;
    }


    /**
     * 构建JdhPromiseServiceDetail
     *
     * @param rowData
     * @param eventType
     * @param jdhPromise
     * @return
     * @throws ParseException
     */
    private List<PromiseService> buildJdhPromiseServiceDetailList(WaveEntry.RowData rowData, WaveEntry.EventType eventType, JdhPromise jdhPromise) throws ParseException {
        List<PromiseService> serviceDetailList = new ArrayList<>();
        PromiseService serviceDetail = new PromiseService();
        serviceDetailList.add(serviceDetail);
        serviceDetail.setPromiseId(jdhPromise.getPromiseId());
        serviceDetail.setTags(0);
        serviceDetail.setBranch(BRANCH_PRODUCTION);
        serviceDetail.setVersion(SYNC_VERSION);
        serviceDetail.setUpdateUser(SYNC_USER);
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("sku_no".equalsIgnoreCase(column.getName())) {
                serviceDetail.setServiceId(Long.valueOf(column.getValue()));
            } else if ("sku_name".equalsIgnoreCase(column.getName())) {
                serviceDetail.setServiceName(column.getValue());
            } else if ("yn".equalsIgnoreCase(column.getName())) {
                serviceDetail.setYn(Integer.valueOf(column.getValue()));
            } else if ("user_pin".equalsIgnoreCase(column.getName())) {
                serviceDetail.setCreateUser(column.getValue());
            } else if ("create_time".equalsIgnoreCase(column.getName())) {
                serviceDetail.setCreateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("update_time".equalsIgnoreCase(column.getName())) {
                serviceDetail.setUpdateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        return serviceDetailList;
    }

    /**
     * 构建JdhPromiseExtend
     *
     * @param rowData
     * @param eventType
     * @param jdhPromise
     * @return
     * @throws ParseException
     */
    private List<JdhPromiseExtend> buildJdhPromiseExtendList(WaveEntry.RowData rowData, WaveEntry.EventType eventType, JdhPromise jdhPromise) throws ParseException {
        List<JdhPromiseExtend> promiseExtendList = new ArrayList<>();
        JdhPromiseExtend promiseExtend = new JdhPromiseExtend();
        promiseExtendList.add(promiseExtend);
        promiseExtend.setPromiseId(jdhPromise.getPromiseId());
        promiseExtend.setYn(1);
        promiseExtend.setCreateTime(jdhPromise.getCreateTime());
        promiseExtend.setUpdateTime(jdhPromise.getUpdateTime());
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("order_user_phone".equalsIgnoreCase(column.getName())) {
                promiseExtend.setOrderPhone(column.getValue());
            }
        }
        return promiseExtendList;
    }

    /**
     * 构建JdhPromisePatient
     *
     * @param rowData
     * @param eventType
     * @param jdhPromise
     * @return
     * @throws ParseException
     */
    private List<JdhPromisePatient> buildJdhPromisePatientList(WaveEntry.RowData rowData, WaveEntry.EventType eventType, JdhPromise jdhPromise) throws ParseException {
        List<JdhPromisePatient> promisePatientList = new ArrayList<>();
        JdhPromisePatient promisePatient = new JdhPromisePatient();
        promisePatientList.add(promisePatient);
        promisePatient.setVerticalCode(jdhPromise.getVerticalCode());
        promisePatient.setServiceType(jdhPromise.getServiceType());
        promisePatient.setPromiseId(jdhPromise.getPromiseId());
        promisePatient.setVoucherId(jdhPromise.getVoucherId());
        promisePatient.setSourceVoucherId(jdhPromise.getSourceVoucherId());
        promisePatient.setBranch(BRANCH_PRODUCTION);
        promisePatient.setVersion(SYNC_VERSION);
        promisePatient.setUpdateUser(SYNC_USER);
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("user_pin".equalsIgnoreCase(column.getName())) {
                promisePatient.setUserPin(column.getValue());
            } else if ("patient_id".equalsIgnoreCase(column.getName())) {
                promisePatient.setPatientId(Long.valueOf(column.getValue()));
            } else if ("user_marriage".equalsIgnoreCase(column.getName())) {
                promisePatient.setMarriage(Integer.valueOf(column.getValue()));
            } else if ("user_gender".equalsIgnoreCase(column.getName())) {
                promisePatient.setGender(Integer.valueOf(column.getValue()));
            } else if ("birthday".equalsIgnoreCase(column.getName())) {
                promisePatient.setBirthday(new Birthday(column.getValue()));
            } else if ("relatives_type".equalsIgnoreCase(column.getName())) {
                promisePatient.setRelativesType(Integer.valueOf(column.getValue()));
            } else if ("user_name".equalsIgnoreCase(column.getName())) {
                promisePatient.setUserName(new UserName(column.getValue()));
            } else if ("user_phone".equalsIgnoreCase(column.getName())) {
                promisePatient.setPhoneNumber(new PhoneNumber(column.getValue()));
            } else if ("yn".equalsIgnoreCase(column.getName())) {
                promisePatient.setYn(Integer.valueOf(column.getValue()));
            } else if ("user_pin".equalsIgnoreCase(column.getName())) {
                promisePatient.setCreateUser(column.getValue());
            } else if ("create_time".equalsIgnoreCase(column.getName())) {
                promisePatient.setCreateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("update_time".equalsIgnoreCase(column.getName())) {
                promisePatient.setUpdateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        // 在用户扫码后才存储，如果没有采集到patientId，则后期update进去
        if (Objects.isNull(promisePatient.getPatientId())) {
            promisePatientList.remove(0);
        }
        return promisePatientList;
    }

    /**
     * 构建MedicalPromise
     *
     * @param rowData
     * @param eventType
     * @param jdhPromise
     * @return
     * @throws ParseException
     */
    private MedicalPromise buildJdhMedicalPromise(WaveEntry.RowData rowData, WaveEntry.EventType eventType, JdhPromise jdhPromise, Long promisePatientId) throws ParseException {
        MedicalPromise medicalPromise = new MedicalPromise();
        medicalPromise.setVerticalCode(jdhPromise.getVerticalCode());
        medicalPromise.setServiceType(jdhPromise.getServiceType());
        medicalPromise.setPromiseId(jdhPromise.getPromiseId());
        medicalPromise.setVoucherId(jdhPromise.getVoucherId());
        medicalPromise.setPromisePatientId(promisePatientId);
        medicalPromise.setFreeze(0);
        medicalPromise.setSettleStatus(0);
        medicalPromise.setBranch(BRANCH_PRODUCTION);
        medicalPromise.setVersion(SYNC_VERSION);
        medicalPromise.setUpdateUser(SYNC_USER);
        List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
        for (WaveEntry.Column column : columns) {
            if (StringUtils.isEmpty(column.getValue())) {
                continue;
            }
            if ("user_pin".equalsIgnoreCase(column.getName())) {
                medicalPromise.setUserPin(column.getValue());
                medicalPromise.setCreateUser(column.getValue());
            } else if ("sku_no".equalsIgnoreCase(column.getName())) {
                medicalPromise.setServiceId(column.getValue());
            } else if ("random_code".equalsIgnoreCase(column.getName())) {
                medicalPromise.setSpecimenCode(column.getValue());
            } else if ("sku_info_extend".equalsIgnoreCase(column.getName())) {
                try {
                    JSONObject skuInfoExtend = JSON.parseArray(column.getValue()).getJSONObject(0);
                    medicalPromise.setServiceItemId(skuInfoExtend.getString("itemNo"));
                    medicalPromise.setServiceItemName(skuInfoExtend.getString("itemName"));
                } catch (Exception e) {
                }
            } else if ("channel_no".equalsIgnoreCase(column.getName())) {
                medicalPromise.setProviderId(Long.valueOf(column.getValue()));
            } else if ("store_id".equalsIgnoreCase(column.getName())) {
                medicalPromise.setStationId(column.getValue());
            } else if ("store_addr".equalsIgnoreCase(column.getName())) {
                medicalPromise.setStationAddress(column.getValue());
            } else if ("store_name".equalsIgnoreCase(column.getName())) {
                medicalPromise.setStationName(column.getValue());
            } else if ("yn".equalsIgnoreCase(column.getName())) {
                medicalPromise.setYn(Integer.valueOf(column.getValue()));
            } else if ("report_status".equalsIgnoreCase(column.getName())) {
                medicalPromise.setReportStatus(Integer.valueOf(column.getValue()));
            } else if ("check_status".equalsIgnoreCase(column.getName())) {
                medicalPromise.setCheckStatus(Integer.valueOf(column.getValue()));
            } else if ("check_time".equalsIgnoreCase(column.getName())) {
                medicalPromise.setCheckTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("report_time".equalsIgnoreCase(column.getName())) {
                medicalPromise.setReportTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("create_time".equalsIgnoreCase(column.getName())) {
                medicalPromise.setCreateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("update_time".equalsIgnoreCase(column.getName())) {
                medicalPromise.setUpdateTime(DateUtil.parseDate(column.getValue(), "yyyy-MM-dd HH:mm:ss"));
            } else if ("order_status".equalsIgnoreCase(column.getName())) {
                Integer medicalPromiseStatus = convertMedicalPromiseStatus(Integer.valueOf(column.getValue()));
                if(Objects.nonNull(medicalPromiseStatus)){
                    medicalPromise.setStatus(medicalPromiseStatus);
                }
            }
        }

        return medicalPromise;
    }

    /**
     * 将老预约单状态映射成voucher状态
     *
     * @param oldOrderStatus
     * @return
     */
    private Integer convertVoucherStatus(Integer oldOrderStatus) {
        switch (oldOrderStatus.intValue()) {
            //待绑定预约人
            case 0:
                return 1;
            //绑完条码，同步到实验室（异常）
            case 2:
                return 1;
            //同步实验室返回成功
            case 3:
                return 1;
            //退款中
            case 6:
                return 4;
            //已退款
            case 8:
                return 4;
            //实验室签收，check_status=1
            case 14:
                return 1;
            //订单完成，报告已出
            case 15:
                return 2;
        }
        return null;
    }

    /**
     * 将老预约单状态映射成promise状态
     *
     * @param oldOrderStatus
     * @return
     */
    private Integer convertPromiseStatus(Integer oldOrderStatus) {
        switch (oldOrderStatus.intValue()) {
            //待绑定预约人
            case 0:
                return 1;
            //绑完条码，同步到实验室（异常）
            case 2:
                return 16;
            //同步实验室返回成功
            case 3:
                return 17;
            //退款中
            case 6:
                return 13;
            //已退款
            case 8:
                return 13;
            //实验室签收，check_status=1
            case 14:
                return 17;
            //订单完成，报告已出
            case 15:
                return 11;
        }
        return null;
    }

    /**
     * 将老预约单状态映射成mecicalPromise状态
     * @param oldOrderStatus
     * @return
     */
    private Integer convertMedicalPromiseStatus(Integer oldOrderStatus) {
        switch (oldOrderStatus.intValue()) {
            //待绑定预约人
            case 0:
                return 1;
            //绑完条码，同步到实验室（异常）
            case 2:
                return 2;
            //同步实验室返回成功
            case 3:
                return 3;
            //退款中
            case 6:
                return 8;
            //已退款
            case 8:
                return 8;
            //实验室签收，check_status=1
            case 14:
                return 4;
            //订单完成，报告已出
            case 15:
                return 6;
        }
        return null;
    }

    /**
     * 过滤数据必要条件  order_appoint_type=16 and payment_time is not null
     *
     * @return
     */
    @Override
    protected List<Predicate<WaveEntry.Column>> filterNeedCondition() {
        return Arrays.asList(
                s -> "order_appoint_type".equalsIgnoreCase(s.getName()) && "16".equals(s.getValue()),
                s -> "store_id".equalsIgnoreCase(s.getName()) && StringUtil.isNotEmpty(s.getValue())
        );
    }
}