package com.jdh.o2oservice.listener.support;


import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.annotation.LogAndUmp;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.enums.VoiceTypeEnum;
import com.jdh.o2oservice.export.support.command.ParseVoiceCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 触达任务监听处理
 * @author: yang<PERSON><PERSON>
 * @date: 2022/4/20 2:26 下午
 * @version: 1.0
 */
@Service("soundFileListener")
@Slf4j
public class SoundFileListener implements MessageListener {

    /** 序列化器 */
    private static final MessageDeserialize<Message> DESERIALIZE = new JMQMessageDeserialize();

    @Resource
    @Lazy
    private FileManageApplication fileManageApplication;


    @LogAndUmp(jKey = "reachTaskListener.onMessage", errorReturnJsfResult = false)
    @JmqListener(id= "jdhReachStoreConsumer", topics = {"${topics.reach.file.submit.topicyfb}"})
    @Override
    public void onMessage(List<Message> messages) throws Exception {

        for (Message message : messages) {
            String text = message.getText();
            try {
                ParseVoiceCmd cmd = JSON.parseObject(text, ParseVoiceCmd.class);
                if(Objects.isNull(cmd.getVoiceType())){
                    cmd.setVoiceType(VoiceTypeEnum.ANGEL_SERVICE_SOUND.getType());
                }
                fileManageApplication.parseVoice(cmd);
            }catch (Exception e){
                log.error("SoundFileListener->onMessage error text={}", text, e);
            }
        }
    }
}
