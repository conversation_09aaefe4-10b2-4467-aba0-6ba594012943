package com.jdh.o2oservice.listener.product;

import cn.hutool.core.collection.CollectionUtil;
import com.jd.fastjson.JSON;
import com.jd.fastjson.JSONObject;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.core.domain.product.context.ServiceIndicatorQueryContext;
import com.jdh.o2oservice.core.domain.product.model.Indicator;
import com.jdh.o2oservice.core.domain.product.model.ServiceItem;
import com.jdh.o2oservice.core.domain.product.model.ServiceItemIdentifier;
import com.jdh.o2oservice.core.domain.product.model.ServiceItemIndicatorRel;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceIndicatorRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhStationServiceItemRelPoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/13
 */
@Slf4j
@Component
public class JdhServiceItemListener  implements MessageListener {

    /**
     * serviceItemRepository
     */
    @Autowired
    @Lazy
    private JdhServiceItemRepository serviceItemRepository;

    /**
     * 指标仓储
     */
    @Autowired
    @Lazy
    private JdhServiceIndicatorRepository jdhServiceIndicatorRepository;

    @Autowired
    @Lazy
    private ProviderStoreRepository providerStoreRepository;

    /**
     * @param list
     * @throws Exception
     */
    @Override
    @JmqListener(id= "jdhReachStoreConsumer", topics = {"${topics.event.jdhServiceItemToYfTopic}"})
    public void onMessage(List<Message> list) throws Exception {

        log.info("JdhServiceItemListener->onMessage start");
        if (CollectionUtil.isEmpty(list) ) {
            return;
        }
        for(Message message : list){
            this.processMessage(message);
        }

    }

    private void processMessage(Message message) {
        log.info("JdhServiceItemListener->processMessage start,indicatorBody={}",JsonUtil.toJSONString(message));

        JSONObject jsonObject = JSON.parseObject(message.getText());

        String serviceItem = jsonObject.getString("serviceItem");

        String indicators = jsonObject.getString("indicators");

        String stationServiceItemRel = jsonObject.getString("stationServiceItemRel");

        ServiceItem item= JsonUtil.parseObject(serviceItem, ServiceItem.class);


        if (StringUtil.isNotBlank(indicators)){
            List<Indicator> indicatorList = JsonUtil.parseArray(indicators, Indicator.class);
            Set<Long> indicatorIdSet = indicatorList.stream().map(Indicator::getIndicatorId).collect(Collectors.toSet());
            List<Indicator> indicatorExit = jdhServiceIndicatorRepository.queryIndicatorList(ServiceIndicatorQueryContext.builder().indicatorIds(indicatorIdSet).build());
            log.info("JdhServiceItemListener->processMessage indicatorExit={}",JsonUtil.toJSONString(indicatorExit));
            if (CollectionUtils.isEmpty(indicatorExit)) {
                for (Indicator indicator : indicatorList) {
                    try {
                        indicator.setId(null);
                        jdhServiceIndicatorRepository.save(indicator);
                    }catch (Exception e){
                        log.info("JdhServiceItemListener->indicatorExit,null,save,error,indicator={}",JsonUtil.toJSONString(indicator),e);
                    }
                }
            }else {
                Map<Long, Indicator> indicatorIdToObj = indicatorExit.stream().collect(Collectors.toMap(Indicator::getIndicatorId, p -> p));
                for (Indicator indicator : indicatorList) {
                    try {
                        if (indicatorIdToObj.containsKey(indicator.getIndicatorId())){
                            indicator.setId(indicatorIdToObj.get(indicator.getIndicatorId()).getId());
                            indicator.setVersion(indicatorIdToObj.get(indicator.getIndicatorId()).getVersion());
                        }else {
                            indicator.setId(null);
                        }
                        jdhServiceIndicatorRepository.save(indicator);
                    }catch (Exception e){
                        log.info("JdhServiceItemListener->indicatorExit,save,error,indicator={}",JsonUtil.toJSONString(indicator),e);
                    }
                }
            }
        }

        if (StringUtil.isNotBlank(serviceItem)){
            ServiceItem serviceItemExist = serviceItemRepository.find(ServiceItemIdentifier.builder().itemId(item.getItemId()).build());
            log.info("JdhServiceItemListener->processMessage serviceItemExist={}",JsonUtil.toJSONString(serviceItemExist));

            if (Objects.nonNull(serviceItemExist)) {
                item.setId(serviceItemExist.getId());
                item.setVersion(serviceItemExist.getVersion());
            }else {
                item.setId(null);
                item.setAllowInsertItemId(Boolean.TRUE);
            }
            if (CollectionUtils.isNotEmpty(item.getIndicatorRelList())){

                List<Indicator> list = Lists.newArrayList();
                item.getIndicatorRelList().forEach(p->{
                    Indicator indicator = new Indicator();
                    indicator.setIndicatorId(p.getIndicatorId());
                    list.add(indicator);
                });

                item.setIndicatorList(list);
            }

            log.info("JdhServiceItemListener->processMessage item={}",JsonUtil.toJSONString(item));
            serviceItemRepository.save(item);
        }

        if (StringUtil.isNotBlank(stationServiceItemRel)){
            JdhStationServiceItemRel jdhStationServiceItemRel = JsonUtil.parseObject(stationServiceItemRel, JdhStationServiceItemRel.class);

            JdhStationServiceItemRel rel = providerStoreRepository.queryStationServiceItem(
                    JdhStationServiceItemRel.builder()
                            .stationId(jdhStationServiceItemRel.getStationId())
                            .serviceItemId(jdhStationServiceItemRel.getServiceItemId())
                            .build()
            );

            if (Objects.isNull(rel)) {
                jdhStationServiceItemRel.setId(null);
                providerStoreRepository.saveStationServiceItem(jdhStationServiceItemRel);
            }

        }

        return;

    }


}
