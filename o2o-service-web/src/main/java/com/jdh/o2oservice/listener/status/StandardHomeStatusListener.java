package com.jdh.o2oservice.listener.status;


import com.alibaba.fastjson.JSON;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.common.enums.VerticalEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkAggregateEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.rpc.B2bEnterpriseServiceRpc;
import com.jdh.o2oservice.core.domain.trade.enums.TradeAggregateEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.vo.JdOrderExtendVo;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.trade.dto.OrderEventMessageDTO;
import com.jdh.o2oservice.export.trade.enums.StandardHomeStatusEnum;
import com.jdh.o2oservice.listener.handler.AbstractHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 预约回调接口监听
 *
 * @author: yangxiyu
 * @date: 2022/4/20 2:26 下午
 * @version: 1.0
 */
@Slf4j
@Service("standardHomeStatusListener")
public class StandardHomeStatusListener extends AbstractHandler<Event> implements MapAutowiredKey {


    @Value("${topics.event.o2oCoreEventTopic}")
    private String handlerTopic;

    /**
     * promiseApplication
     */
    @Resource
    @Lazy
    private PromiseApplication promiseApplication;

    @Resource
    @Lazy
    private TradeApplication tradeApplication;

    @Resource
    @Lazy
    private JdOrderApplication orderApplication;

    @Resource
    @Lazy
    private AngelPromiseApplication angelPromiseApplication;

    @Resource
    @Lazy
    private MedicalPromiseApplication medicalPromiseApplication;

    @Resource
    private B2bEnterpriseServiceRpc b2bEnterpriseServiceRpc;

    @Autowired
    @Lazy
    private DuccConfig duccConfig;

    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Value("${topics.order.standardHomeStatusTopic}")
    private String standardHomeStatusTopic;

    @Resource
    private Cluster jimClient;

    private static final String ORDER_MAP_KEY = "order";
    private static final String PROMISE_MAP_KEY = "promise";
    private static final String WORK_MAP_KEY = "work";
    private static final String MEDICAL_PROMISE_MAP_KEY = "medical_promise";

    private static final String CACHE_HOME_STATUS_PREFIX_KEY = "standard_home_status_{0}";
    private static final String LOCK_PREFIX_KEY = "standard_home_status_lock_{0}";
    private static final Long CACHE_EXPIRE_SECOND = 3L * 24L * 60L * 60L;
    private static final Long LOCK_EXPIRE_SECOND = 30L; // 分布式锁过期时间30秒
    private static final String LOCK_VALUE = "locked"; // 锁的值

    @Override
    public String getMapKey() {
        return handlerTopic;
    }

    /**
     * 在解析消息体前进行消息处理的判断，不需要的消息直接过滤点或者不进行处理（使用场景消息处理限流等）
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param message
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean preFilterOfDiscardMessage(Message message) {
        log.info("StandardHomeStatusListener->preFilterOfDiscardMessage message={}", JSON.toJSONString(message));
        //消息检查
        if (Objects.isNull(message) || StringUtils.isBlank(message.getText())) {
            return true;
        }
        return false;
    }

    /**
     * 解析消息
     *
     * @param message
     * @return
     */
    @Override
    public Event analysisMessage(Message message) {
        return JSON.parseObject(message.getText(), Event.class);
    }

    /**
     * 根据接到的mq消息解析出的对象，丢弃业务不需要的消息
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param event
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean filterOfDiscardMessage(Event event) {
        if (Objects.isNull(event)) {
            return true;
        }

        if (Objects.isNull(event.getAggregateCode())) {
            return true;
        }
        // 过滤掉非订单、履约、护士工单、实验室检测单的消息
        if (event.getAggregateCode().equalsIgnoreCase(TradeAggregateEnum.ORDER.getCode())
                || event.getAggregateCode().equalsIgnoreCase(PromiseAggregateEnum.PROMISE.getCode())
                || event.getAggregateCode().equalsIgnoreCase(AngelWorkAggregateEnum.WORK.getCode())
                || event.getAggregateCode().equalsIgnoreCase(MedPromiseAggregateEnum.MED_PROMISE.getCode())
        ) {
            return false;
        }

        return true;
    }

    /**
     * 转投环境
     *
     * @param message
     * @param event
     * @return
     */
    @Override
    public boolean transferToYf(Message message, Event event) {
        return false;
    }

    /**
     * 业务处理
     *
     * @param event
     */
    @Override
    public void dealMessage(Event event) {
        Map<String, Object> aggregateDtoMap = getAggregateDtoMap(event);
        if (MapUtils.isEmpty(aggregateDtoMap)) {
            log.info("StandardHomeStatusListener->dealMessage aggregateDtoMap is empty");
            return;
        }
        // 安全地从聚合数据中提取各类型对象
        AggregateDataExtractor extractor = new AggregateDataExtractor(aggregateDtoMap);
        List<JdOrder> orderList = extractor.extractOrderList();
        PromiseDto promiseDto = extractor.extractPromiseDto();
        AngelWorkDetailDto angelWorkDetailDto = extractor.extractAngelWorkDetailDto();
        List<MedicalPromiseDTO> medicalPromiseDTOList = extractor.extractMedicalPromiseList();

        StandardHomeStatusEnum standardHomeStatusEnum = getStandardHomeStatusEnum(orderList, promiseDto, angelWorkDetailDto, medicalPromiseDTOList);

        // 使用分布式锁确保并发安全的状态检查和处理
        String businessId = extractBusinessId(orderList, promiseDto);
        if (StringUtils.isBlank(businessId)) {
            return;
        }

        if (!processWithDistributedLock(standardHomeStatusEnum, orderList, promiseDto, businessId, event)) {
            log.info("StandardHomeStatusListener->dealMessage status duplicated or lock failed, skip processing; businessId:{}", businessId);
            return;
        }
        // 注意：消息发送逻辑已经移动到processWithDistributedLock方法中
    }

    /**
     * 使用分布式锁确保并发安全的状态检查和处理
     *
     * @param standardHomeStatusEnum 标准化状态枚举
     * @param orderList              订单列表
     * @param promiseDto             履约单信息
     * @param businessId             业务ID
     * @param event                  事件对象
     * @return true-处理成功，false-状态重复或锁获取失败
     */
    private boolean processWithDistributedLock(StandardHomeStatusEnum standardHomeStatusEnum,
                                               List<JdOrder> orderList,
                                               PromiseDto promiseDto,
                                               String businessId,
                                               Event event) {
        String lockKey = MessageFormat.format(LOCK_PREFIX_KEY, businessId);
        String statusValue = String.valueOf(standardHomeStatusEnum.getStatus());

        try {
            // 尝试获取分布式锁，使用SET NX EX命令实现原子性
            boolean lockAcquired = jimClient.setNX(lockKey, LOCK_VALUE);
            jimClient.expire(lockKey, LOCK_EXPIRE_SECOND, TimeUnit.SECONDS);

            if (!lockAcquired) {
                log.warn("StandardHomeStatusListener->processWithDistributedLock failed to acquire lock, businessId={}, status={}",
                        businessId, statusValue);
                return false;
            }

            try {
                // 在锁保护下再次检查状态是否重复
                if (isStatusDuplicated(businessId, statusValue)) {
                    return false;
                }

                // 先更新缓存状态（关键：在发送消息前更新）
                updateStatusCacheInLock(standardHomeStatusEnum, businessId);

                // 然后发送消息
                return sendMessage(standardHomeStatusEnum, orderList, promiseDto, event);

            } finally {
                // 释放分布式锁
                releaseLock(lockKey);
            }

        } catch (Exception e) {
            log.error("StandardHomeStatusListener->processWithDistributedLock error, businessId={}, status={}",
                    businessId, statusValue, e);
            return false;
        }
    }

    /**
     * 检查状态是否重复
     * 优化点：
     * 1. 消除代码重复，提取通用缓存检查逻辑
     * 2. 提前转换状态值，避免重复转换
     * 3. 增加异常处理，提高系统稳定性
     * 4. 优化逻辑流程，提高可读性
     *
     * @param standardHomeStatusEnum 标准化状态枚举
     * @param orderList              订单列表
     * @param promiseDto             履约单信息
     * @return true-状态重复，false-状态不重复
     */
    private boolean checkStatusDuplication(StandardHomeStatusEnum standardHomeStatusEnum, List<JdOrder> orderList, PromiseDto promiseDto) {
        // 参数校验
        if (Objects.isNull(standardHomeStatusEnum)) {
            log.debug("StandardHomeStatusListener->checkStatusDuplication standardHomeStatusEnum is null");
            return false;
        }

        // 提前转换状态值，避免重复转换
        String targetStatusValue = String.valueOf(standardHomeStatusEnum.getStatus());

        // 确定缓存键的业务ID
        String businessId = extractBusinessId(orderList, promiseDto);
        if (StringUtils.isBlank(businessId)) {
            log.debug("StandardHomeStatusListener->checkStatusDuplication businessId is blank");
            return false;
        }

        // 统一的缓存检查逻辑
        return isStatusDuplicated(businessId, targetStatusValue);
    }

    /**
     * 提取业务ID用于缓存键生成
     * 优先级：订单ID > 履约单ID
     *
     * @param orderList  订单列表
     * @param promiseDto 履约单信息
     * @return 业务ID字符串，如果都为空则返回null
     */
    private String extractBusinessId(List<JdOrder> orderList, PromiseDto promiseDto) {
        // 优先使用订单ID
        if (CollectionUtils.isNotEmpty(orderList)) {
            JdOrder jdOrder = orderList.get(0);
            if (Objects.nonNull(jdOrder) && Objects.nonNull(jdOrder.getOrderId())) {
                return String.valueOf(jdOrder.getOrderId());
            }
        }

        // 其次使用履约单ID
        if (Objects.nonNull(promiseDto) && Objects.nonNull(promiseDto.getPromiseId())) {
            return String.valueOf(promiseDto.getPromiseId());
        }

        return null;
    }

    /**
     * 检查缓存中的状态是否与目标状态重复
     *
     * @param businessId        业务ID
     * @param targetStatusValue 目标状态值
     * @return true-状态重复，false-状态不重复
     */
    private boolean isStatusDuplicated(String businessId, String targetStatusValue) {
        try {
            String cacheKey = MessageFormat.format(CACHE_HOME_STATUS_PREFIX_KEY, businessId);
//            String cachedStatusValue = jimClient.get(cacheKey);
//
//            boolean isDuplicated = StringUtils.isNotBlank(cachedStatusValue) &&
//                                 cachedStatusValue.equals(targetStatusValue);

            boolean isDuplicated = jimClient.sIsMember(cacheKey, targetStatusValue);

            if (isDuplicated) {
                log.warn("StandardHomeStatusListener->checkStatusDuplication status duplicated, businessId={}, status={}",
                        businessId, targetStatusValue);
            }

            return isDuplicated;

        } catch (Exception e) {
            log.error("StandardHomeStatusListener->checkStatusDuplication cache operation failed, businessId={}, status={}",
                    businessId, targetStatusValue, e);
            // 缓存异常时返回false，不阻塞业务流程
            return false;
        }
    }

    /**
     * 在锁保护下更新状态缓存
     *
     * @param standardHomeStatusEnum 标准化状态枚举
     * @param businessId             业务ID
     */
    private void updateStatusCacheInLock(StandardHomeStatusEnum standardHomeStatusEnum, String businessId) {
        try {
            String cacheKey = MessageFormat.format(CACHE_HOME_STATUS_PREFIX_KEY, businessId);
            String statusValue = String.valueOf(standardHomeStatusEnum.getStatus());

            // 使用Redis Set存储状态，支持多个状态值
            jimClient.sAdd(cacheKey, statusValue);
            jimClient.expire(cacheKey, CACHE_EXPIRE_SECOND, TimeUnit.SECONDS);

            log.debug("StandardHomeStatusListener->updateStatusCacheInLock cache updated, businessId={}, status={}",
                    businessId, statusValue);

        } catch (Exception e) {
            log.error("StandardHomeStatusListener->updateStatusCacheInLock cache update failed, businessId={}, status={}",
                    businessId, standardHomeStatusEnum.getStatus(), e);
            throw new RuntimeException("Cache update failed", e);
        }
    }

    /**
     * 发送消息
     *
     * @param standardHomeStatusEnum 标准化状态枚举
     * @param orderList              订单列表
     * @param promiseDto             履约单信息
     * @param event                  事件对象
     * @return true-发送成功，false-发送失败
     */
    private boolean sendMessage(StandardHomeStatusEnum standardHomeStatusEnum,
                                List<JdOrder> orderList,
                                PromiseDto promiseDto,
                                Event event) {
        try {
            // 构建消息对象
            OrderEventMessageDTO orderEventMessageDTO = buildOrderEventMessage(standardHomeStatusEnum, orderList, promiseDto, event);

            log.info("StandardHomeStatusListener->sendMessage orderEventMessageDTO={}", JSON.toJSONString(orderEventMessageDTO));

            String businessId = String.valueOf(orderEventMessageDTO.getOrderId()) + "_" +
                    String.valueOf(orderEventMessageDTO.getPromiseId()) + "_" +
                    orderEventMessageDTO.getMessageId();

            Message message = new Message(standardHomeStatusTopic, JSON.toJSONString(orderEventMessageDTO), businessId);
            reachStoreProducer.send(message);

            log.info("StandardHomeStatusListener->sendMessage message sent successfully, businessId={}", businessId);
            return true;

        } catch (JMQException e) {
            log.error("StandardHomeStatusListener->sendMessage reachStoreProducer send error", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 构建订单事件消息对象
     */
    private OrderEventMessageDTO buildOrderEventMessage(StandardHomeStatusEnum standardHomeStatusEnum,
                                                        List<JdOrder> orderList,
                                                        PromiseDto promiseDto,
                                                        Event event) {
        OrderEventMessageDTO orderEventMessageDTO = new OrderEventMessageDTO();
        orderEventMessageDTO.setMessageId(String.valueOf(event.getEventId()));
        orderEventMessageDTO.setStatus(standardHomeStatusEnum.getStatus());
        orderEventMessageDTO.setStatusCode(standardHomeStatusEnum.getCode());
        orderEventMessageDTO.setStatusTitle(standardHomeStatusEnum.getDesc());

        if (CollectionUtils.isNotEmpty(orderList)) {
            JdOrder jdOrder = orderList.get(0);
            orderEventMessageDTO.setOrderId(jdOrder.getOrderId());
            orderEventMessageDTO.setOrderType(jdOrder.getOrderType());
            orderEventMessageDTO.setPartnerSourceOrderId(jdOrder.getPartnerSourceOrderId());
            orderEventMessageDTO.setPartnerSource(jdOrder.getPartnerSource());
            orderEventMessageDTO.setUserPin(jdOrder.getUserPin());
            orderEventMessageDTO.setVerticalCode(jdOrder.getVerticalCode());

            String jdOrderExtend = jdOrder.getExtend();
            if (StringUtils.isNotBlank(jdOrderExtend)) {
                JdOrderExtendVo jdOrderExtendVo = JSON.parseObject(jdOrderExtend, JdOrderExtendVo.class);
                if (Objects.nonNull(jdOrderExtendVo)) {
                    orderEventMessageDTO.setSaleChannelId(jdOrderExtendVo.getSaleChannelId());
                }
            }
        }

        if (Objects.nonNull(promiseDto)) {
            orderEventMessageDTO.setPromiseId(Collections.singletonList(promiseDto.getPromiseId()));
            orderEventMessageDTO.setUserPin(promiseDto.getUserPin());
            orderEventMessageDTO.setSourceVoucherId(promiseDto.getSourceVoucherId());
            orderEventMessageDTO.setVerticalCode(promiseDto.getVerticalCode());
        }

        if (StringUtils.isNotEmpty(orderEventMessageDTO.getVerticalCode())) {
            // B2B订单查询对应的enterpriseId
            if (VerticalEnum.EXTERNAL_B.getCode().equalsIgnoreCase(orderEventMessageDTO.getVerticalCode())) {
                if (StringUtils.isNotBlank(orderEventMessageDTO.getSourceVoucherId())
                        && StringUtils.isNumeric(orderEventMessageDTO.getSourceVoucherId())) {
                    Long enterpriseId = b2bEnterpriseServiceRpc.queryEnterpriseId(Long.parseLong(orderEventMessageDTO.getSourceVoucherId()));
                    orderEventMessageDTO.setEnterpriseId(enterpriseId);
                }
            }
        }

        return orderEventMessageDTO;
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey 锁的键
     */
    private void releaseLock(String lockKey) {
        try {
            jimClient.del(lockKey);
            log.debug("StandardHomeStatusListener->releaseLock lock released, lockKey={}", lockKey);
        } catch (Exception e) {
            log.error("StandardHomeStatusListener->releaseLock failed to release lock, lockKey={}", lockKey, e);
            // 锁释放失败不影响主流程，锁会自动过期
        }
    }

    /**
     * 更新状态缓存（保留原方法，用于向后兼容）
     *
     * @deprecated 使用 updateStatusCacheInLock 替代
     */
    @Deprecated
    private void updateStatusCache(StandardHomeStatusEnum standardHomeStatusEnum, List<JdOrder> orderList, PromiseDto promiseDto) {
        if (Objects.isNull(standardHomeStatusEnum)) {
            return;
        }

        String businessId = extractBusinessId(orderList, promiseDto);
        if (StringUtils.isBlank(businessId)) {
            return;
        }

        try {
            String cacheKey = MessageFormat.format(CACHE_HOME_STATUS_PREFIX_KEY, businessId);
            String statusValue = String.valueOf(standardHomeStatusEnum.getStatus());

            jimClient.sAdd(cacheKey, statusValue);
            jimClient.expire(cacheKey, CACHE_EXPIRE_SECOND, TimeUnit.SECONDS);

            log.debug("StandardHomeStatusListener->updateStatusCache cache updated, businessId={}, status={}",
                    businessId, statusValue);

        } catch (Exception e) {
            log.error("StandardHomeStatusListener->updateStatusCache cache update failed, businessId={}, status={}",
                    businessId, standardHomeStatusEnum.getStatus(), e);
        }
    }

    /**
     * 解析消息体获取聚合数据
     *
     * @param event
     * @return
     */
    private Map<String, Object> getAggregateDtoMap(Event event) {
        Map<String, Object> result = new HashMap<>();
        // 入口是订单
        if (event.getAggregateCode().equalsIgnoreCase(TradeAggregateEnum.ORDER.getCode())) {
            JdOrder jdOrder = orderApplication.queryJdOrderByOrderId(Long.parseLong(event.getAggregateId()));
            if (Objects.nonNull(jdOrder)) {
                result.put(ORDER_MAP_KEY, jdOrder);
                List<PromiseDto> promiseDtoList = promiseApplication.findJdhPromiseList(PromiseRepQuery.builder().sourceVoucherId(String.valueOf(jdOrder.getOrderId())).build());
                if (CollectionUtils.isNotEmpty(promiseDtoList)) {
                    PromiseDto promiseDto = promiseDtoList.get(0);
                    result.put(PROMISE_MAP_KEY, promiseDto);
                    AngelWorkDetailDto angelWorkDetailDto = angelPromiseApplication.queryWorkListOrRecently(AngelWorkQuery.builder().promiseId(promiseDto.getPromiseId()).build());
                    if (Objects.nonNull(angelWorkDetailDto)) {
                        result.put(WORK_MAP_KEY, angelWorkDetailDto);
                    }
                    List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseListRequest.builder().promiseId(promiseDto.getPromiseId()).build());
                    if (CollectionUtils.isNotEmpty(medicalPromiseDTOList)) {
                        result.put(MEDICAL_PROMISE_MAP_KEY, medicalPromiseDTOList);
                    }
                }
            }
            // 入口是履约单
        } else if (event.getAggregateCode().equalsIgnoreCase(PromiseAggregateEnum.PROMISE.getCode())) {
            List<PromiseDto> promiseDtoList = promiseApplication.findJdhPromiseList(PromiseRepQuery.builder().promiseIds(Collections.singletonList(Long.parseLong(event.getAggregateId()))).build());
            if (CollectionUtils.isNotEmpty(promiseDtoList)) {
                PromiseDto promiseDto = promiseDtoList.get(0);
                result.put(PROMISE_MAP_KEY, promiseDto);
                AngelWorkDetailDto angelWorkDetailDto = angelPromiseApplication.queryWorkListOrRecently(AngelWorkQuery.builder().promiseId(promiseDto.getPromiseId()).build());
                if (Objects.nonNull(angelWorkDetailDto)) {
                    result.put(WORK_MAP_KEY, angelWorkDetailDto);
                }
                List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseListRequest.builder().promiseId(promiseDto.getPromiseId()).build());
                if (CollectionUtils.isNotEmpty(medicalPromiseDTOList)) {
                    result.put(MEDICAL_PROMISE_MAP_KEY, medicalPromiseDTOList);
                }
                JdOrder jdOrder = orderApplication.queryJdOrderByOrderId(Long.parseLong(promiseDto.getSourceVoucherId()));
                if (Objects.nonNull(jdOrder)) {
                    result.put(ORDER_MAP_KEY, jdOrder);
                }
            }
            // 入口是工单
        } else if (event.getAggregateCode().equalsIgnoreCase(AngelWorkAggregateEnum.WORK.getCode())) {
            AngelWorkDetailDto angelWorkDetailDto = angelPromiseApplication.queryWorkListOrRecently(AngelWorkQuery.builder().workId(Long.parseLong(event.getAggregateId())).build());
            if (Objects.nonNull(angelWorkDetailDto)) {
                result.put(WORK_MAP_KEY, angelWorkDetailDto);
                List<PromiseDto> promiseDtoList = promiseApplication.findJdhPromiseList(PromiseRepQuery.builder().promiseIds(Collections.singletonList(angelWorkDetailDto.getPromiseId())).build());
                if (CollectionUtils.isNotEmpty(promiseDtoList)) {
                    PromiseDto promiseDto = promiseDtoList.get(0);
                    result.put(PROMISE_MAP_KEY, promiseDto);
                    List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseListRequest.builder().promiseId(promiseDto.getPromiseId()).build());
                    if (CollectionUtils.isNotEmpty(medicalPromiseDTOList)) {
                        result.put(MEDICAL_PROMISE_MAP_KEY, medicalPromiseDTOList);
                    }
                    JdOrder jdOrder = orderApplication.queryJdOrderByOrderId(Long.parseLong(promiseDto.getSourceVoucherId()));
                    if (Objects.nonNull(jdOrder)) {
                        result.put(ORDER_MAP_KEY, jdOrder);
                    }
                }
            }
            // 入口是检测单
        } else if (event.getAggregateCode().equalsIgnoreCase(MedPromiseAggregateEnum.MED_PROMISE.getCode())) {
            List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseListRequest.builder().medicalPromiseId(Long.parseLong(event.getAggregateId())).build());
            if (CollectionUtils.isNotEmpty(medicalPromiseDTOList)) {
                result.put(MEDICAL_PROMISE_MAP_KEY, medicalPromiseDTOList);
                List<PromiseDto> promiseDtoList = promiseApplication.findJdhPromiseList(PromiseRepQuery.builder().promiseIds(Collections.singletonList(medicalPromiseDTOList.get(0).getPromiseId())).build());
                if (CollectionUtils.isNotEmpty(promiseDtoList)) {
                    PromiseDto promiseDto = promiseDtoList.get(0);
                    result.put(PROMISE_MAP_KEY, promiseDto);
                    AngelWorkDetailDto angelWorkDetailDto = angelPromiseApplication.queryWorkListOrRecently(AngelWorkQuery.builder().promiseId(promiseDto.getPromiseId()).build());
                    if (Objects.nonNull(angelWorkDetailDto)) {
                        result.put(WORK_MAP_KEY, angelWorkDetailDto);
                    }
                    JdOrder jdOrder = orderApplication.queryJdOrderByOrderId(Long.parseLong(promiseDto.getSourceVoucherId()));
                    if (Objects.nonNull(jdOrder)) {
                        result.put(ORDER_MAP_KEY, jdOrder);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 计算映射状态
     *
     * @param jdOrderList           子单列表
     * @param promiseDto            履约单信息
     * @param angelWorkDetailDto    工单信息
     * @param medicalPromiseDTOList 检测单列表
     * @return 标准化状态枚举
     */
    @LogAndAlarm
    private StandardHomeStatusEnum getStandardHomeStatusEnum(List<JdOrder> jdOrderList, PromiseDto promiseDto, AngelWorkDetailDto angelWorkDetailDto, List<MedicalPromiseDTO> medicalPromiseDTOList) {
        // 获取主要状态信息
        Integer orderStatus = null;
        Integer promiseStatus = null;
        Integer workStatus = null;
        Integer medicalPromiseStatus = null;
        Integer medPromiseFreeze = null;
        String serviceType = null;

        // 从订单列表中获取状态信息 - 考虑多个订单的情况
        if (CollectionUtils.isNotEmpty(jdOrderList)) {
            // 优先级：退款状态 > 待支付状态 > 其他状态
            JdOrder priorityOrder = jdOrderList.stream()
                    .filter(Objects::nonNull)
                    .min((o1, o2) -> Integer.compare(
                            getOrderStatusPriority(o1.getOrderStatus()),
                            getOrderStatusPriority(o2.getOrderStatus())
                    ))
                    .orElse(jdOrderList.get(0));

            orderStatus = priorityOrder.getOrderStatus();
            serviceType = priorityOrder.getServiceType();
        }

        // 从履约单中获取状态信息
        if (Objects.nonNull(promiseDto)) {
            promiseStatus = promiseDto.getPromiseStatus();
        }

        // 从工单中获取状态信息
        if (Objects.nonNull(angelWorkDetailDto)) {
            workStatus = angelWorkDetailDto.getStatus();
        }

        // 从检测单中获取状态信息 - 考虑多个检测单的情况
        if (CollectionUtils.isNotEmpty(medicalPromiseDTOList)) {
            // 检测单状态优先级：作废 > 冻结 > 已完成 > 检测中 > 送检中 > 采样完成 > 其他
            MedicalPromiseDTO priorityMedical = medicalPromiseDTOList.stream()
                    .filter(Objects::nonNull)
                    .min((m1, m2) -> Integer.compare(
                            getMedicalStatusPriority(m1.getStatus(), m1.getFreeze()),
                            getMedicalStatusPriority(m2.getStatus(), m2.getFreeze())
                    ))
                    .orElse(medicalPromiseDTOList.get(0));

            medicalPromiseStatus = priorityMedical.getStatus();
            medPromiseFreeze = priorityMedical.getFreeze();
        }

        // 按照优先级判断状态
        if ((Objects.nonNull(orderStatus) && OrderStatusEnum.ORDER_CANCEL.getStatus().equals(orderStatus))) {
            return StandardHomeStatusEnum.ORDER_CANCEL;
        }

        // 1. 退款相关状态（最高优先级）
        if (Objects.nonNull(medicalPromiseStatus) && Objects.nonNull(medPromiseFreeze) &&
                !MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseStatus) &&
                JdhFreezeEnum.FREEZE.getStatus().equals(medPromiseFreeze)) {
            return StandardHomeStatusEnum.ORDER_REFUNDING;
        }

        if ((Objects.nonNull(orderStatus) && OrderStatusEnum.ORDER_REFUND.getStatus().equals(orderStatus)) ||
                (Objects.nonNull(medicalPromiseStatus) && MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseStatus))) {
            return StandardHomeStatusEnum.ORDER_REFUNDED;
        }

        // 2. 预约取消相关状态
        if (Objects.nonNull(promiseStatus)) {
            if (JdhPromiseStatusEnum.CANCEL_ING.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.PROMISE_WAIT_APPOINT; // 取消预约中，回到待预约状态
            }
            if (JdhPromiseStatusEnum.CANCEL_SUCCESS.getStatus().equals(promiseStatus) ||
                    JdhPromiseStatusEnum.CANCEL_FAIL.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.PROMISE_WAIT_APPOINT; // 取消成功或失败，回到待预约状态
            }
        }

        // 4. 工单状态
        if (Objects.nonNull(workStatus) && AngelWorkStatusEnum.COMPLETED.getType().equals(workStatus)) {
            return StandardHomeStatusEnum.MEDICAL_PROMISE_DELIVERED;
        }

        // 3. 检测流程状态
        if (Objects.nonNull(medicalPromiseStatus)) {
            if (MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus().equals(medicalPromiseStatus)) {
                return StandardHomeStatusEnum.MEDICAL_PROMISE_GO_LAB;
            }
            if (MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus().equals(medicalPromiseStatus)) {
                return StandardHomeStatusEnum.MEDICAL_PROMISE_TESTING;
            }
            if (MedicalPromiseStatusEnum.COLLECTED.getStatus().equals(medicalPromiseStatus)) {
                return StandardHomeStatusEnum.WORK_SAMPLED;
            }
            if (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(medicalPromiseStatus)) {
                if (ServiceTypeEnum.TEST.getServiceType().equals(serviceType)) {
                    return StandardHomeStatusEnum.MEDICAL_PROMISE_REPORTED;
                } else if (ServiceTypeEnum.CARE.getServiceType().equals(serviceType)) {
                    return StandardHomeStatusEnum.WORK_FINISHED;
                }
            }
        }

        // 5. 履约单状态
        if (Objects.nonNull(promiseStatus)) {
            if (JdhPromiseStatusEnum.WAIT_PROMISE.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.PROMISE_WAIT_APPOINT;
            }
            if (JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus().equals(promiseStatus) ||
                    JdhPromiseStatusEnum.MODIFY_ING.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.DISPATCH_WAIT_RECEIVE;
            }
            if (JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus().equals(promiseStatus) ||
                    JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.WORK_STARTED;
            }
            if (JdhPromiseStatusEnum.SERVICE_READY.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.WORK_GO_HOME;
            }
            if (JdhPromiseStatusEnum.SERVICING.getStatus().equals(promiseStatus)) {
                return StandardHomeStatusEnum.WORK_SERVICING;
            }
        }

        // 6. 默认状态 - 如果订单状态是待支付
        if (Objects.nonNull(orderStatus) && OrderStatusEnum.ORDER_WAIT_PAY.getStatus().equals(orderStatus)) {
            return StandardHomeStatusEnum.ORDER_WAIT_PAY;
        }

        // 默认返回待预约状态
        return StandardHomeStatusEnum.PROMISE_WAIT_APPOINT;
    }

    /**
     * 获取订单状态优先级
     * 数值越小优先级越高
     *
     * @param orderStatus 订单状态
     * @return 优先级数值
     */
    private int getOrderStatusPriority(Integer orderStatus) {
        if (Objects.isNull(orderStatus)) return Integer.MAX_VALUE;

        if (OrderStatusEnum.ORDER_REFUND.getStatus().equals(orderStatus)) return 1;
        if (OrderStatusEnum.ORDER_WAIT_PAY.getStatus().equals(orderStatus)) return 2;
        return 999; // 其他状态
    }

    /**
     * 获取检测单状态优先级
     * 数值越小优先级越高
     *
     * @param medicalStatus 检测单状态
     * @param freeze        冻结状态
     * @return 优先级数值
     */
    private int getMedicalStatusPriority(Integer medicalStatus, Integer freeze) {
        if (Objects.isNull(medicalStatus)) return Integer.MAX_VALUE;

        if (MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalStatus)) return 1;
        if (JdhFreezeEnum.FREEZE.getStatus().equals(freeze)) return 2;
        if (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(medicalStatus)) return 3;
        if (MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus().equals(medicalStatus)) return 4;
        if (MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus().equals(medicalStatus)) return 5;
        if (MedicalPromiseStatusEnum.COLLECTED.getStatus().equals(medicalStatus)) return 6;
        return 999; // 其他状态
    }

    /**
     * 统一处理状态返回和日志记录
     *
     * @param result               返回的状态枚举
     * @param reason               返回原因
     * @param orderStatus          订单状态
     * @param promiseStatus        履约单状态
     * @param workStatus           工单状态
     * @param medicalPromiseStatus 检测单状态
     * @param medPromiseFreeze     冻结状态
     * @return 状态枚举
     */
    private StandardHomeStatusEnum logAndReturn(StandardHomeStatusEnum result, String reason,
                                                Integer orderStatus, Integer promiseStatus, Integer workStatus,
                                                Integer medicalPromiseStatus, Integer medPromiseFreeze) {
        log.info("StandardHomeStatusListener->getStandardHomeStatusEnum end, result={}, reason={}, orderStatus={}, promiseStatus={}, workStatus={}, medicalStatus={}, freeze={}",
                result.getCode(), reason, orderStatus, promiseStatus, workStatus, medicalPromiseStatus, medPromiseFreeze);
        return result;
    }


    /**
     * 聚合数据提取器 - 封装数据提取逻辑
     * 提供类型安全的数据提取方法
     */
    private static class AggregateDataExtractor {
        private final Map<String, Object> aggregateDtoMap;

        public AggregateDataExtractor(Map<String, Object> aggregateDtoMap) {
            this.aggregateDtoMap = aggregateDtoMap;
        }

        /**
         * 提取订单列表
         */
        public List<JdOrder> extractOrderList() {
            return extractList(ORDER_MAP_KEY, JdOrder.class);
        }

        /**
         * 提取履约单信息
         */
        public PromiseDto extractPromiseDto() {
            return extractSingle(PROMISE_MAP_KEY, PromiseDto.class);
        }

        /**
         * 提取工单详情
         */
        public AngelWorkDetailDto extractAngelWorkDetailDto() {
            return extractSingle(WORK_MAP_KEY, AngelWorkDetailDto.class);
        }

        /**
         * 提取检测单列表
         */
        public List<MedicalPromiseDTO> extractMedicalPromiseList() {
            return extractList(MEDICAL_PROMISE_MAP_KEY, MedicalPromiseDTO.class);
        }

        /**
         * 通用的单对象提取方法
         */
        @SuppressWarnings("unchecked")
        private <T> T extractSingle(String key, Class<T> clazz) {
            if (MapUtils.isEmpty(aggregateDtoMap)) {
                return null;
            }

            try {
                Object obj = aggregateDtoMap.get(key);
                if (clazz.isInstance(obj)) {
                    return (T) obj;
                }
            } catch (Exception e) {
                log.warn("AggregateDataExtractor->extractSingle failed for key={}, class={}, error={}",
                        key, clazz.getSimpleName(), e.getMessage());
            }

            return null;
        }

        /**
         * 通用的列表提取方法
         */
        @SuppressWarnings("unchecked")
        private <T> List<T> extractList(String key, Class<T> clazz) {
            if (MapUtils.isEmpty(aggregateDtoMap)) {
                return Collections.emptyList();
            }

            try {
                Object obj = aggregateDtoMap.get(key);
                if (obj instanceof List) {
                    List<?> rawList = (List<?>) obj;
                    if (rawList.isEmpty() || clazz.isInstance(rawList.get(0))) {
                        return (List<T>) rawList;
                    }
                } else if (clazz.isInstance(obj)) {
                    // 如果是单个对象，包装成列表
                    return Collections.singletonList((T) obj);
                }
            } catch (Exception e) {
                log.warn("AggregateDataExtractor->extractList failed for key={}, class={}, error={}",
                        key, clazz.getSimpleName(), e.getMessage());
            }

            return Collections.emptyList();
        }
    }
}
