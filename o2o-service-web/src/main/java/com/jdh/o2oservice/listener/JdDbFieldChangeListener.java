package com.jdh.o2oservice.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchRepQuery;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.support.businessMode.ServiceHomeTypeDomainService;
import com.jdh.o2oservice.core.domain.support.event.model.DbFieldChangeEventBody;
import com.jdh.o2oservice.core.domain.trade.context.OrderInfoQueryContext;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.export.angelpromise.dto.AngelShipDto;
import com.jdh.o2oservice.export.angelpromise.query.GetDetailByPromiseIdQuery;
import com.jdh.o2oservice.export.trade.query.CustomOrderDetailParam;
import com.jdh.o2oservice.export.trade.query.CustomOrderParam;
import com.jdh.o2oservice.export.trade.query.QueryCustomOrderListParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 监听数据变更刷新缓存
 * <AUTHOR>
 * @date 2025-06-04 13:26
 */
@Slf4j
@Service("jdDbFieldChangeListener")
public class JdDbFieldChangeListener implements MessageListener {

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;

    /**
     * jdhVoucherRepository
     */
    @Resource
    private VoucherRepository jdhVoucherRepository;

    /**
     * jdOrderRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    /**
     * 检测单仓储层
     */
    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * angelShipRepository
     */
    @Resource
    private AngelShipRepository angelShipRepository;

    /**
     * angelWorkRepository
     */
    @Resource
    private AngelWorkRepository angelWorkRepository;

    /**
     * orderInfoRpc
     */
    @Resource
    private OrderInfoRpc orderInfoRpc;

    /**
     * tradeApplication
     */
    @Resource
    private TradeApplication tradeApplication;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * 服务者履约工单
     */
    @Resource
    private AngelWorkApplication angelWorkApplication;

    /**
     * 缓存
     */
    @Resource
    private Cluster jimClient;

    /**
     * 派单域
     */
    @Resource
    DispatchRepository dispatchRepository;

    /**
     * 任务单
     */
    @Resource
    AngelTaskRepository angelTaskRepository;

    @Resource
    private ServiceHomeTypeDomainService serviceHomeTypeDomainService;

    /**
     * ducc
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * onMessage
     *
     * @param list 消息
     * @throws Exception Exception
     */
    @Override
    @JmqListener(id= "jdhReachStoreConsumer", topics = {"${topics.jdhReachStoreConsumer.dbFieldChangeEventTopic}"})
    public void onMessage(List<Message> list) throws Exception {
        log.info("JdDbFieldChangeListener -> onMessage start");
        if (CollectionUtil.isEmpty(list) ) {
            return;
        }
        for(Message message : list){
            this.processMessage(message);
        }
    }

    /**
     * 消费消息
     * 1. 解析MQ，限时防重
     * 2. 发送短信平台
     * @param message
     */
    private void processMessage(Message message) {
        String msg = message.getText();
        try {
            log.info("JdDbFieldChangeListener -> processMessage start, msg={}", msg);
            this.dbFieldChangeFreshCache(msg);
        } catch (Exception e) {
            log.error("JdDbFieldChangeListener -> processMessage exception, error={}", e.getMessage(), e);
        }
    }

    /**
     * 订单列表、订详刷缓存
     */
    private void dbFieldChangeFreshCache(String messageText) {
        if (StringUtils.isBlank(messageText)) {
            return;
        }
        DbFieldChangeEventBody eventBody = JSON.parseObject(messageText, DbFieldChangeEventBody.class);
        if (eventBody == null) {
            log.info("JdDbFieldChangeListener eventBody is null");
            return;
        }
        Long orderId = null;
        if (StringUtils.isBlank(eventBody.getTableName())) {
            return;
        }

        JSONObject jsonObject = JSON.parseObject(duccConfig.getJdAppOrderFloorCacheRule());
        if (jsonObject == null) {
            return;
        }
        AngelWork angelWork = null;
        switch (eventBody.getTableName()) {
            case "jd_order":
                orderId = queryOrderFromOrder(jsonObject, eventBody);
                break;
            case "jdh_voucher":
                orderId = queryOrderFromVoucher(jsonObject, eventBody);
                break;
            case "jdh_promise":
                orderId = queryOrderFromPromise(jsonObject, eventBody);
                break;
            case "jdh_medical_promise":
                orderId = queryOrderFromMedicalPromise(jsonObject, eventBody);
                break;
            case "jdh_angel_ship":
                if (cacheFieldList(jsonObject, eventBody.getTableName()).contains(eventBody.getChangeFieldName())) {
                    Long shipId = Long.parseLong(eventBody.getPrimaryKeyFieldValue());
                    AngelShip angelShip = angelShipRepository.find(new AngelShipIdentifier(shipId));
                    if (angelShip != null && angelShip.getWorkId() != null) {
                        angelWork = angelWorkRepository.find(new AngelWorkIdentifier(angelShip.getWorkId()));
                        orderId = angelWork.getJdOrderId();
                    }
                }
                break;
            case "jdh_dispatch":
                orderId = queryOrderFromDispatch(jsonObject, eventBody);
                break;
            case "jdh_angel_work":
                orderId = queryOrderFromAngelWork(jsonObject, eventBody);
                break;
            case "jdh_angel_task":
                orderId = queryOrderFromAngelTask(jsonObject, eventBody);
                break;
            default:
                return;
        }
        if (orderId == null) {
            return;
        }

        log.info("[JdDbFieldChangeListener->dbFieldChangeFreshCache],orderId={}", orderId);

        JdOrder orderEntity = queryOrder(orderId);
        if (orderEntity == null) {
            return;
        }
        cacheOrderDetail(orderEntity);
        cacheOrderList(orderEntity);
        cacheShip(orderEntity, angelWork);
    }

    /**
     * 查询订单
     * @param jsonObject
     * @param eventBody
     * @return
     */
    private Long queryOrderFromOrder(JSONObject jsonObject, DbFieldChangeEventBody eventBody) {
        log.info("[JdDbFieldChangeListener->queryOrderFromOrder],eventBody={}", JSON.toJSONString(eventBody));
        if (cacheFieldList(jsonObject, eventBody.getTableName()).contains(eventBody.getChangeFieldName())) {
            log.info("[JdDbFieldChangeListener->queryOrderFromOrder],ACCESS,eventBody={}", JSON.toJSONString(eventBody));
            return Long.parseLong(eventBody.getPrimaryKeyFieldValue());
        }
        return null;
    }

    /**
     * 查询订单
     * @param jsonObject
     * @param eventBody
     * @return
     */
    private Long queryOrderFromVoucher(JSONObject jsonObject, DbFieldChangeEventBody eventBody) {
        log.info("[JdDbFieldChangeListener->queryOrderFromVoucher],eventBody={}", JSON.toJSONString(eventBody));
        if (cacheFieldList(jsonObject, eventBody.getTableName()).contains(eventBody.getChangeFieldName())) {
            log.info("[JdDbFieldChangeListener->queryOrderFromVoucher],ACCESS,eventBody={}", JSON.toJSONString(eventBody));
            Long voucherId = Long.parseLong(eventBody.getPrimaryKeyFieldValue());
            JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(voucherId).build());
            if (jdhVoucher != null && StringUtils.isNotBlank(jdhVoucher.getSourceVoucherId())) {
                return Long.parseLong(jdhVoucher.getSourceVoucherId());
            }
        }
        return null;
    }

    /**
     * 查询订单
     * @param jsonObject
     * @param eventBody
     * @return
     */
    private Long queryOrderFromPromise(JSONObject jsonObject, DbFieldChangeEventBody eventBody) {
        log.info("[JdDbFieldChangeListener->queryOrderFromPromise],eventBody={}", JSON.toJSONString(eventBody));
        if (cacheFieldList(jsonObject, eventBody.getTableName()).contains(eventBody.getChangeFieldName())) {
            log.info("[JdDbFieldChangeListener->queryOrderFromPromise],ACCESS,eventBody={}", JSON.toJSONString(eventBody));
            JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(Long.parseLong(eventBody.getPrimaryKeyFieldValue())));
            if (promise != null && StringUtils.isNotBlank(promise.getSourceVoucherId())) {
                return Long.parseLong(promise.getSourceVoucherId());
            }
        }
        return null;
    }

    /**
     * 查询订单
     * @param jsonObject
     * @param eventBody
     * @return
     */
    private Long queryOrderFromMedicalPromise(JSONObject jsonObject, DbFieldChangeEventBody eventBody) {
        log.info("[JdDbFieldChangeListener->queryOrderFromMedicalPromise],eventBody={}", JSON.toJSONString(eventBody));
        if (cacheFieldList(jsonObject, eventBody.getTableName()).contains(eventBody.getChangeFieldName())) {
            log.info("[JdDbFieldChangeListener->queryOrderFromMedicalPromise],ACCESS,eventBody={}", JSON.toJSONString(eventBody));
            Long medicalPromiseId = Long.parseLong(eventBody.getPrimaryKeyFieldValue());
            MedicalPromise medicalPromise = medicalPromiseRepository.find(new MedicalPromiseIdentifier(medicalPromiseId));
            if (medicalPromise != null && medicalPromise.getPromiseId() != null) {
                JdhPromise promise = promiseRepository.find(new JdhPromiseIdentifier(medicalPromise.getPromiseId()));
                return Long.parseLong(promise.getSourceVoucherId());
            }
        }
        return null;
    }

    /**
     * 查询订单
     * @param jsonObject
     * @param eventBody
     * @return
     */
    private Long queryOrderFromAngelWork(JSONObject jsonObject, DbFieldChangeEventBody eventBody) {
        log.info("[JdDbFieldChangeListener->queryOrderFromAngelWork],eventBody={}", JSON.toJSONString(eventBody));
        if (cacheFieldList(jsonObject, eventBody.getTableName()).contains(eventBody.getChangeFieldName())) {
            log.info("[JdDbFieldChangeListener->queryOrderFromAngelWork],ACCESS,eventBody={}", JSON.toJSONString(eventBody));
            AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(Long.parseLong(eventBody.getPrimaryKeyFieldValue())));
            if (angelWork != null && angelWork.getJdOrderId() != null) {
                return angelWork.getJdOrderId();
            }
        }
        return null;
    }

    /**
     * 查询订单
     * @param jsonObject
     * @param eventBody
     * @return
     */
    private Long queryOrderFromAngelTask(JSONObject jsonObject, DbFieldChangeEventBody eventBody) {
        log.info("[JdDbFieldChangeListener->queryOrderFromAngelTask],eventBody={}", JSON.toJSONString(eventBody));
        if (cacheFieldList(jsonObject, eventBody.getTableName()).contains(eventBody.getChangeFieldName())) {
            log.info("[JdDbFieldChangeListener->queryOrderFromAngelTask],ACCESS,eventBody={}", JSON.toJSONString(eventBody));
            AngelTask angelTask = angelTaskRepository.find(AngelTaskIdentifier.builder().taskId(Long.parseLong(eventBody.getPrimaryKeyFieldValue())).build());
            if (angelTask != null && angelTask.getTaskId() != null) {
                AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(angelTask.getWorkId()));
                return angelWork.getJdOrderId();
            }
        }
        return null;
    }

    /**
     * 查询订单
     * @param jsonObject
     * @param eventBody
     * @return
     */
    private Long queryOrderFromDispatch(JSONObject jsonObject, DbFieldChangeEventBody eventBody) {
        log.info("[JdDbFieldChangeListener->queryOrderFromDispatch],eventBody={}", JSON.toJSONString(eventBody));
        if (cacheFieldList(jsonObject, eventBody.getTableName()).contains(eventBody.getChangeFieldName())) {
            log.info("[JdDbFieldChangeListener->queryOrderFromDispatch],ACCESS,eventBody={}", JSON.toJSONString(eventBody));
            JdhDispatch jdhDispatch = dispatchRepository.findDispatch(DispatchRepQuery.builder().build());
            if (jdhDispatch != null && jdhDispatch.getDispatchId() != null) {
                JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(jdhDispatch.getVoucherId()).build());
                return Long.parseLong(jdhVoucher.getSourceVoucherId());
            }
        }
        return null;
    }

    /**
     * 变更触发缓存更新的字段
     * @param jsonObject
     * @param tableName
     * @return
     */
    private List<String> cacheFieldList(JSONObject jsonObject, String tableName) {
        JSONObject rule = jsonObject.getJSONObject(tableName);
        if (rule == null) {
            return Collections.emptyList();
        }
        String fieldListKey = "fieldList";
        if (!rule.containsKey(fieldListKey)) {
            return Collections.emptyList();
        }
        JSONArray jsonArray = rule.getJSONArray(fieldListKey);
        if (CollUtil.isEmpty(jsonArray)) {
            return Collections.emptyList();
        }
        return JSONObject.parseArray(jsonArray.toJSONString(), String.class);
    }

    /**
     * 查询订单信息
     * @param orderId
     * @return
     */
    private JdOrder queryOrder(Long orderId) {
        JdOrder orderEntity = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(orderId).build());
        if (orderEntity == null) {
            return null;
        }
        Map<String, String> sendPayMap = orderEntity.getSendPayMap();
        if (CollUtil.isEmpty(sendPayMap)) {
            OrderInfoQueryContext context = new OrderInfoQueryContext();
            context.setOrderId(String.valueOf(orderEntity.getOrderId()));
            context.setUserPin(orderEntity.getUserPin());
            JdOrder jdOrder = orderInfoRpc.queryOrderInfo(context);
            if (jdOrder != null) {
                sendPayMap = jdOrder.getSendPayMap();
                orderEntity.setSendPayMap(sendPayMap);
            }
        }
        return orderEntity;
    }

    /**
     * 缓存订详楼层
     * @param orderEntity
     */
    private void cacheOrderDetail(JdOrder orderEntity) {
        CustomOrderDetailParam customOrderDetailParam = new CustomOrderDetailParam();
        customOrderDetailParam.setPin(orderEntity.getUserPin());
        customOrderDetailParam.setOrderId(orderEntity.getOrderId());
        customOrderDetailParam.setOrderType(orderEntity.getOrderType());
        customOrderDetailParam.setSendPayMap(orderEntity.getSendPayMap());
        customOrderDetailParam.setSendPay(orderEntity.getSendPay());
        customOrderDetailParam.setOrderStatusId(orderEntity.getOrderStatus());
        customOrderDetailParam.setFreshCache(true);
        tradeApplication.queryCustomOrderDetailFloor(customOrderDetailParam);
    }

    /**
     * 缓存订列楼层
     * @param orderEntity
     */
    private void cacheOrderList(JdOrder orderEntity) {
        QueryCustomOrderListParam queryCustomOrderListParam = new QueryCustomOrderListParam();
        queryCustomOrderListParam.setPin(orderEntity.getUserPin());
        List<CustomOrderParam> customOrderParamList = new ArrayList<>();
        CustomOrderParam customOrderParam = new CustomOrderParam();
        customOrderParam.setOrderId(orderEntity.getOrderId());
        customOrderParam.setSendPay(orderEntity.getSendPay());
        customOrderParam.setOrderStatusId(orderEntity.getOrderStatus());
        customOrderParam.setOrderType(orderEntity.getOrderType() == null ? null : String.valueOf(orderEntity.getOrderType()));
        customOrderParam.setSendPayMap(CollUtil.isEmpty(orderEntity.getSendPayMap()) ? null : JSON.toJSONString(orderEntity.getSendPayMap()));
        customOrderParamList.add(customOrderParam);
        queryCustomOrderListParam.setCustomOrderParamList(customOrderParamList);
        queryCustomOrderListParam.setFreshCache(true);
        tradeApplication.queryCustomOrderList(queryCustomOrderListParam);
    }

    /**
     * 缓存订列楼层
     * @param orderEntity
     */
    private void cacheShip(JdOrder orderEntity, AngelWork angelWork) {
        // 京东物流无ETA通过ship标准字段缓存物流节点数据
        boolean verticalCodeBool = serviceHomeTypeDomainService.getServiceHomeVerticalCode("XFYL_HOME_SELF_TEST_TRANSPORT").equalsIgnoreCase(orderEntity.getVerticalCode());
        if (!verticalCodeBool || angelWork == null) {
            return;
        }

        freshShipMessageCache(angelWork.getPromiseId());
    }


    /**
     * 刷新运单信息缓存,京东物流未接入ETA数据,需要查询ship表中物流信息
     */
    private void freshShipMessageCache(Long promiseId) {
        if (promiseId == null) {
            return;
        }
        try {
            String promiseIdStr = String.valueOf(promiseId);
            GetDetailByPromiseIdQuery getDetailByPromiseIdQuery = new GetDetailByPromiseIdQuery();
            getDetailByPromiseIdQuery.setPromiseId(promiseId);
            AngelShipDto angelShipDto = angelWorkApplication.getDetailByPromiseId(getDetailByPromiseIdQuery);
            if (angelShipDto != null && StringUtils.isNotBlank(angelShipDto.getLogisticsMessage())) {
                String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STORE_QUERY_MEDICAL_LIST_SHIP_INFO, promiseIdStr);
                jimClient.setEx(cacheKey, angelShipDto.getLogisticsMessage(), RedisKeyEnum.STORE_QUERY_MEDICAL_LIST_SHIP_INFO.getExpireTime(), RedisKeyEnum.STORE_QUERY_MEDICAL_LIST_SHIP_INFO.getExpireTimeUnit());
                log.info("[JdDbFieldChangeListener->freshShipMessageCache],promiseId={}", promiseId);
            }
        }catch (Throwable e){
            log.error("刷新运单信息缓存异常,promiseId:{}", promiseId, e);
        }
    }
}
