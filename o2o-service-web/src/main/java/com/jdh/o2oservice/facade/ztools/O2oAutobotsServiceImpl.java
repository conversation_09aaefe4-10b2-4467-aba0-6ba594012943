package com.jdh.o2oservice.facade.ztools;

import cn.hutool.core.bean.BeanUtil;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.ztools.O2oAutobotsService;
import com.jdh.o2oservice.export.ztools.dto.ProductProgramAutoBotsDto;
import com.jdh.o2oservice.export.ztools.query.ProductProgramAutoBotParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName O2oAutobotsServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/4/2 16:34
 */
@Service
@Slf4j
public class O2oAutobotsServiceImpl implements O2oAutobotsService {

    /**
     * productApplication
     */
    @Resource
    private ProductApplication productApplication;


    /**
     * 商品查询工具
     *
     * @param autoBotParam
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<ProductProgramAutoBotsDto> autoBotsQueryProductInfo(ProductProgramAutoBotParam autoBotParam) {
        AssertUtils.nonNull(autoBotParam, "入参不能为空");
        AssertUtils.hasText(autoBotParam.getSkuNo(), "商品编码不能为空");

        JdhSkuRequest request = new JdhSkuRequest();
        request.setSkuId(Long.valueOf(autoBotParam.getSkuNo()));
        JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(request);

        ProductProgramAutoBotsDto result = new ProductProgramAutoBotsDto();
        BeanUtil.copyProperties(jdhSkuDto, result);
        return ResponseUtil.buildSuccResponse(result);
    }
}
