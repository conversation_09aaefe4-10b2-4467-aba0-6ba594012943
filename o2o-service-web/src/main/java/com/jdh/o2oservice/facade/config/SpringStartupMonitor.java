package com.jdh.o2oservice.facade.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 项目启动耗时分析小工具
 */
@Component
@Slf4j
public class SpringStartupMonitor implements BeanPostProcessor, ApplicationListener<ContextRefreshedEvent> {
    // 存储Bean初始化耗时
    private final Map<String, Long> beanInitTimeMap = new ConcurrentHashMap<>(64);
    // 项目启动开始时间
    private final long appStartTime = System.currentTimeMillis();

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        beanInitTimeMap.put(beanName, System.currentTimeMillis());
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Long startTime = beanInitTimeMap.get(beanName);
        if (startTime != null) {
            long cost = System.currentTimeMillis() - startTime;
            beanInitTimeMap.put(beanName, cost);
        }
        return bean;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // 输出耗时分析报告
        log.info("========== Spring启动耗时分析 ==========");
        log.info("总启动耗时: {}ms", System.currentTimeMillis() - appStartTime);

        // 按耗时排序输出Bean初始化时间
        log.info("开始输出超过30ms的所有Bean的初始化时间");
        beanInitTimeMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 30 && entry.getValue() < 10000000) // 过滤耗时>30ms的Bean
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .forEach(entry -> log.info("[{}] 初始化耗时: {}ms", entry.getKey(), entry.getValue()));
    }
}