package com.jdh.o2oservice.facade.trade;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherExtend;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.export.trade.TradeDevJsfExport;
import com.jdh.o2oservice.export.trade.cmd.OrderMockCreateCmd;
import com.jdh.o2oservice.export.trade.cmd.OrderMockCreatePromiseCmd;
import com.jdh.o2oservice.export.trade.cmd.OrderMockModifyPromiseCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("tradeDevJsfExport")
public class TradeDevJsfExportImpl implements TradeDevJsfExport {

    /**
     * 订单rpc
     */
    @Resource
    OrderInfoRpc orderInfoRpc;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;

    /**
     * voucherApplication
     */
    @Resource
    private VoucherApplication voucherApplication;

    /**
     * jdhVoucherRepository
     */
    @Resource
    private VoucherRepository jdhVoucherRepository;

    /**
     * 商品
     */
    @Resource
    private ProductApplication productApplication;
    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    /**
     * 检测单仓储层
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository jdhPromiseRepository;

    /** */
    @Resource
    private AngelWorkRepository angelWorkRepository;
    /** */
    @Resource
    private AngelShipRepository angelShipRepository;

    /**
     * 生成订单数据
     *
     * @return
     */
    @Override
    public Response<Boolean> mockGeneralOrder(OrderMockCreateCmd cmd) {
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 生成订单数据
     *
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> mockGeneralPromise(OrderMockCreatePromiseCmd cmd) {
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 生成订单数据
     *
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> mockModifyPromise(OrderMockModifyPromiseCmd cmd) {
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 作废订单下数据
     *
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> mockInvalidOrderService(OrderMockModifyPromiseCmd cmd) {
        return null;
    }

    /**
     * 作废订单下数据
     *
     * @param voucherId
     * @param type
     * @return
     */
    @Override
    public Response<Boolean> mockInsertVoucherExtend(Long voucherId, Integer type) {
        JdhVoucher voucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(voucherId).build());
        if (type == null) {
            type = 1;
        }
        if (voucher == null) {
            return ResponseUtil.buildSuccResponse(false);
        }
        JdhVoucherExtend jdhVoucherExtend = voucher.getExtend();
        if (jdhVoucherExtend == null){
            jdhVoucherExtend = new JdhVoucherExtend();
        }
        if (type == 1) {
            Map<String, Object> st = new HashMap<String, Object>();
            st.put("userId", "1");
            st.put("orderId", 1000000L);
            jdhVoucherExtend.setOuterPatientId(JSON.toJSONString(st));
        } else if (type == 2) {
            List<String> list = new ArrayList<>();
            list.add("1");
            jdhVoucherExtend.setOuterPatientId(JSON.toJSONString(list));
        } else {
            jdhVoucherExtend.setOuterPatientId("test0001");
        }
        jdhVoucherRepository.insertOrUpdateBySourceVoucherId(voucher);
        return null;
    }

}
