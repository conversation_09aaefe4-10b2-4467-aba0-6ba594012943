package com.jdh.o2oservice.facade.config;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * @Author: maoxianglin1
 * @Date: 2025/9/19 16:51
 * @Description:
 **/
@Component
public class LogLevelInitializer implements ApplicationListener<ApplicationReadyEvent> {

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        // 应用启动完成后，将根日志级别调整为INFO
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger rootLogger = loggerContext.getLogger(org.slf4j.Logger.ROOT_LOGGER_NAME);
        rootLogger.setLevel(Level.INFO);

        // 特定包保持DEBUG级别
        Logger appLogger = loggerContext.getLogger("com.jdh.o2oservice");
        appLogger.setLevel(Level.DEBUG);

        System.out.println("日志级别已调整完成");
    }
}