import java.util.*;

/**
 * 地址ID生成器测试和演示
 */
public class AddressIdGeneratorTest {
    
    /**
     * 根据完整地址生成唯一的数字ID
     * 相同的地址会生成相同的ID，确保一致性
     * @param fullAddress 完整地址
     * @return 地址ID字符串
     */
    public static String generateAddressId(String fullAddress) {
        if (isBlank(fullAddress)) {
            return String.valueOf(System.currentTimeMillis());
        }
        
        try {
            // 对地址进行标准化处理
            String normalizedAddress = normalizeAddress(fullAddress);
            
            // 使用哈希算法生成一致的数字ID
            int hashCode = normalizedAddress.hashCode();
            
            // 确保生成正数，并转换为合理范围的数字
            long addressId = Math.abs((long) hashCode);
            
            // 如果需要更长的ID，可以结合地址长度等信息
            if (normalizedAddress.length() > 10) {
                addressId = addressId * 1000L + (normalizedAddress.length() % 1000);
            }
            
            // 确保ID在合理范围内（避免过大的数字）
            addressId = addressId % 999999999L + 100000000L; // 9位数字
            
            return String.valueOf(addressId);
            
        } catch (Exception e) {
            System.err.println("生成地址ID失败: " + e.getMessage());
            // 降级方案：使用时间戳
            return String.valueOf(System.currentTimeMillis());
        }
    }

    /**
     * 地址标准化处理
     * 去除空格、统一字符等，确保相同地址的一致性
     * @param address 原始地址
     * @return 标准化后的地址
     */
    public static String normalizeAddress(String address) {
        if (isBlank(address)) {
            return "";
        }
        
        return address.trim()
                .replaceAll("\\s+", "") // 去除所有空格
                .replaceAll("　", "") // 去除全角空格
                .toLowerCase() // 转小写（如果有英文）
                .replace("（", "(") // 统一括号
                .replace("）", ")")
                .replace("，", ",") // 统一逗号
                .replace("。", ".") // 统一句号
                .replace("－", "-") // 统一连字符
                .replace("—", "-");
    }
    
    /**
     * 简单的空字符串检查
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    public static void main(String[] args) {
        System.out.println("=== 地址ID生成器测试 ===\n");
        
        // 测试用例
        String[] testAddresses = {
            "北京市朝阳区建国门外大街1号",
            "北京市朝阳区建国门外大街1号", // 相同地址
            "北京市 朝阳区 建国门外大街 1号", // 有空格的相同地址
            "北京市朝阳区建国门外大街1号（国贸大厦）",
            "北京市朝阳区建国门外大街1号(国贸大厦)", // 括号不同
            "上海市浦东新区陆家嘴环路1000号",
            "广州市天河区珠江新城花城大道85号",
            "深圳市南山区科技园南区深南大道9988号",
            "", // 空地址
            null, // null地址
            "A", // 短地址
            "这是一个非常非常长的地址用来测试长地址的ID生成情况包含很多字符"
        };
        
        System.out.println("1. 基本功能测试：");
        Map<String, String> addressIdMap = new HashMap<>();
        
        for (String address : testAddresses) {
            String addressId = generateAddressId(address);
            String displayAddress = address == null ? "null" : "\"" + address + "\"";
            System.out.println(displayAddress + " -> " + addressId);
            
            if (address != null && !address.isEmpty()) {
                addressIdMap.put(address, addressId);
            }
        }
        
        System.out.println("\n2. 一致性测试（多次生成相同地址的ID）：");
        String testAddress = "北京市朝阳区建国门外大街1号";
        Set<String> generatedIds = new HashSet<>();
        
        for (int i = 0; i < 5; i++) {
            String id = generateAddressId(testAddress);
            generatedIds.add(id);
            System.out.println("第" + (i + 1) + "次生成: " + id);
        }
        
        System.out.println("生成的唯一ID数量: " + generatedIds.size() + 
                          (generatedIds.size() == 1 ? " ✓ 一致性测试通过" : " ✗ 一致性测试失败"));
        
        System.out.println("\n3. 地址标准化测试：");
        String[] normalizeTests = {
            "北京市朝阳区建国门外大街1号",
            "北京市 朝阳区 建国门外大街 1号",
            "北京市　朝阳区　建国门外大街　1号",
            "北京市朝阳区建国门外大街1号（国贸）",
            "北京市朝阳区建国门外大街1号(国贸)",
            "北京市朝阳区建国门外大街1号，国贸",
            "北京市朝阳区建国门外大街1号,国贸"
        };
        
        for (String addr : normalizeTests) {
            String normalized = normalizeAddress(addr);
            String id = generateAddressId(addr);
            System.out.println("原始: " + addr);
            System.out.println("标准化: " + normalized);
            System.out.println("ID: " + id);
            System.out.println("---");
        }
        
        System.out.println("\n4. 性能测试：");
        long startTime = System.currentTimeMillis();
        int testCount = 10000;
        
        for (int i = 0; i < testCount; i++) {
            generateAddressId("测试地址" + (i % 100)); // 重复一些地址
        }
        
        long endTime = System.currentTimeMillis();
        System.out.println("生成 " + testCount + " 个地址ID耗时: " + (endTime - startTime) + "ms");
        System.out.println("平均每个ID生成耗时: " + ((endTime - startTime) * 1.0 / testCount) + "ms");
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("✓ 相同地址生成相同ID");
        System.out.println("✓ 地址标准化处理");
        System.out.println("✓ 异常情况处理");
        System.out.println("✓ 性能表现良好");
    }
}
