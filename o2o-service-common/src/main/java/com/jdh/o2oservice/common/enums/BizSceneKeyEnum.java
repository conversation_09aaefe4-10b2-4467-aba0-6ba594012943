package com.jdh.o2oservice.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2025/8/4
 */
@Getter
@AllArgsConstructor
public enum BizSceneKeyEnum {


    /**
     * 表示检测单实验室交互
     */
    MEDICAL_PROMISE_STATION_INTERACTION("medicalPromiseStationInteraction","【O2O】检测单实验室交互"),


    /**
     * 表示开放平台实验室交互的业务场景键。
     */
    MEDICAL_PROMISE_OPEN_TEST_STATION("medicalPromiseOpenTestStation","开放平台实验室交互"),



    ;



    ;

    /**
     * 业务场景键，用于标识不同的业务场景。
     */
    private final String bizSceneKey;

    /**
     * 业务场景描述，用于记录每个业务场景的详细信息。
     */
    private final String bizSceneDesc;
}
