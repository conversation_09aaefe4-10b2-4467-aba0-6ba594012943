package com.jdh.o2oservice.common.ext;

/**
 * @ClassName:ExtBusinessIdentifierCode
 * @Description:
 * @Author: ya<PERSON>qing<PERSON>
 * @Date: 2024/5/14 18:58
 * @Vserion: 1.0
 **/
public class ExtBusinessIdentifierCode {

    //#####################服务者履约扩展点身份编码配置 start ##############################//
    public static final String ANGEL_PROMISE_WORK_GENERATE_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_WorkGenerateExt_bizParamCheck";

    public static final String ANGEL_PROMISE_WORK_STATUS_HANDLE_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_WorkStatusHandleExt_checkTaskStatusAlready";

    public static final String ANGEL_TASK_STATUS_HANDLE_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_WorkStatusHandleExt_getTaskStatus";

    public static final String ANGEL_PROMISE_TASK_START_EXT_STATUS_HANDLE_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_AngelTaskExtStatusExt_getStartVerticalStatus";

    public static final String ANGEL_PROMISE_SHIP_CREATE_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_CreateShipExt_callTransfer";

    public static final String ANGEL_PROMISE_SHIP_RECREATE_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_CreateShipExt_reCallTransfer";

    public static final String ANGEL_PROMISE_SHIP_CANCEL_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_CreateShipExt_cancelTransfer";

    public static final String ANGEL_PROMISE_SHIP_CALLBACK_PARSE_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_CallbackExt_shipInfoConvert";

    public static final String ANGEL_PROMISE_SHIP_ORDER_DETAIL_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_CreateShipExt_getShipOrderDetail";

    public static final String ANGEL_PROMISE_SHIP_STATUS_BACK_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_DeliveryStatusBackExt_verifySignature";

    public static final String ANGEL_PROMISE_SHIP_TRANSFER_TRACK_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_DeliveryTrackExt_queryDeliveryTrack";

    public static final String ANGEL_PROMISE_SHIP_TRANSFER_REAL_TRACK_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_DeliveryTrackExt_queryDeliveryRealTrack";


    public static final String ANGEL_PROMISE_WORK_CREATE_PLAN_TIME_CALCULATE_EXT = "EXT_JDH_O2O_ANGEL_PROMISE_WorkGenerateExt_planTimeCalculate";

    //#####################服务者履约扩展点身份编码配置 end ##############################//



    //#####################创建第三方门店扩展点身份编码配置 start ##############################//
    public static final String ANGEL_STATION_CREATE_THIRD_STORE_EXT = "EXT_JDH_O2O_ANGEL_STATION_CreateThirdStoreExt_createThirdStore";
    public static final String ANGEL_STATION_UPDATE_THIRD_STORE_EXT = "EXT_JDH_O2O_ANGEL_STATION_UpdateThirdStoreExt_updateThirdStore";
    public static final String ANGEL_STATION_QUERY_THIRD_STORE_EXT = "EXT_JDH_O2O_ANGEL_STATION_QueryThirdStoreExt_queryThirdStore";
    //#####################创建第三方门店扩展点身份编码配置 end ##############################//
}
