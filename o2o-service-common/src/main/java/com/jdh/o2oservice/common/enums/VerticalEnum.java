package com.jdh.o2oservice.common.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 垂直业务身份枚举
 *
 * <AUTHOR>
 * @date 2024/05/21
 */
public enum VerticalEnum {

    /**
     *
     */

    NET_HOSPITAL_HOME_TEST("nhHomeTest", "互医检查检验", false),
    JZYY_HOME_TEST("jzyyHomeTest", "精准营养上门检测", true),
    OPEN_TEST("openTest", "实验室开放平台", true),
    EXTERNAL_B("external_b", "b2b", true),
    HOSPITAL_PAID_GUIDANCE_A("hospitalPaidGuidanceAHomeTest", "院内自费导诊解决方案A", false),
    BBC_HOME_TEST_NO_LABORATORY("bbcHomeTestNoLaboratory", "bbc上门检测（无实验室流程）",false),
    ;


    /**
     * 业务身份编码
     */
    private final String code;

    /**
     * 业务身份名称
     */
    private final String name;

    /**
     * 是否无订单模式
     */
    private final boolean nonOrder;
    VerticalEnum(String code, String name, boolean nonOrder) {
        this.code = code;
        this.name = name;
        this.nonOrder = nonOrder;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public boolean isNonOrder() {
        return nonOrder;
    }

    /**
     * STATUS_MAP
     */
    private static final Map<String, VerticalEnum> STATUS_MAP = Maps.newHashMap();


    static {
        for (VerticalEnum value : values()) {
            STATUS_MAP.put(value.getCode(), value);
        }
    }

    /**
     * 是否为无订单模式
     * @param code
     * @return
     */
    public static boolean isNonOrderVertical(String code) {
        if (STATUS_MAP.containsKey(code) && STATUS_MAP.get(code).nonOrder) {
            return true;
        }
        return false;
    }
}
