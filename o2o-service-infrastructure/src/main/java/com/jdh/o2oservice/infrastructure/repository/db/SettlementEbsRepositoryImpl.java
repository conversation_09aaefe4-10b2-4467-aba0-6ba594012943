package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.product.model.JdhSpecimenCodeBatch;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementEbs;
import com.jdh.o2oservice.core.domain.settlement.repository.db.SettlementEbsRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.SettlementEbsPoConvert;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSettlementEbsMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSettlementEbsPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 结算EBS仓库
 * @date 2024-06-03 10:07
 * <AUTHOR>
 */
@Repository
@Slf4j
public class SettlementEbsRepositoryImpl implements SettlementEbsRepository {

    /**
     * ebs mapper
     */
    @Resource
    JdhSettlementEbsMapper jdhSettlementEbsMapper;

    /**
     * 保存
     *
     * @param jdhSettlementEbs ebs
     * @return boolean
     */
    @Override
    public Boolean save(JdhSettlementEbs jdhSettlementEbs) {
        JdhSettlementEbsPo po = SettlementEbsPoConvert.INSTANCE.modelToPo(jdhSettlementEbs);
        if (jdhSettlementEbs.getSendStatus() == null) {
            po.setSendStatus(0);
        }
        Date now = new Date();
        po.setCreateUser(jdhSettlementEbs.getCreateUser());
        po.setCreateTime(now);
        po.setUpdateUser(jdhSettlementEbs.getUpdateUser());
        po.setUpdateTime(now);
        po.setYn(YnStatusEnum.YES.getCode());
        return jdhSettlementEbsMapper.insert(po) > 0;
    }

    /**
     * 更新
     *
     * @param jdhSettlementEbs ebs
     * @return boolean
     */
    @Override
    public Boolean update(JdhSettlementEbs jdhSettlementEbs) {
        LambdaUpdateWrapper<JdhSettlementEbsPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(jdhSettlementEbs.getSendStatus() != null, JdhSettlementEbsPo::getSendStatus, jdhSettlementEbs.getSendStatus())
                .setSql("`version` = `version` + 1")
                .eq(JdhSettlementEbsPo::getPreId, jdhSettlementEbs.getPreId());
        return jdhSettlementEbsMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 查询
     *
     * @param jdhSettlementEbs ebs
     * @return boolean
     */
    @Override
    public JdhSettlementEbs query(JdhSettlementEbs jdhSettlementEbs) {
        LambdaQueryWrapper<JdhSettlementEbsPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhSettlementEbsPo::getPreId, jdhSettlementEbs.getPreId())
                .eq(JdhSettlementEbsPo::getYn, YnStatusEnum.YES.getCode());
        return SettlementEbsPoConvert.INSTANCE.poToModel(jdhSettlementEbsMapper.selectOne(queryWrapper));
    }

    /**
     *
     * @param jdhSettlementEbs ebs
     * @return
     */
    @Override
    public List<JdhSettlementEbs> queryJdhSettlementEbsList(JdhSettlementEbs jdhSettlementEbs) {
        LambdaQueryWrapper<JdhSettlementEbsPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhSettlementEbsPo::getPreId, jdhSettlementEbs.getPreId())
                .eq(JdhSettlementEbsPo::getYn, YnStatusEnum.YES.getCode());
        return SettlementEbsPoConvert.INSTANCE.poToModel(jdhSettlementEbsMapper.selectList(queryWrapper));
    }

    /**
     * 查询
     *
     * @param jdhSettlementEbs ebs
     * @return boolean
     */
    @Override
    public Page<JdhSettlementEbs> queryPage(JdhSettlementEbs jdhSettlementEbs) {
        Page<JdhSettlementEbsPo> param = new Page<>(jdhSettlementEbs.getPageNum(), jdhSettlementEbs.getPageSize());
        LambdaQueryWrapper<JdhSettlementEbsPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(jdhSettlementEbs.getSendStatus() != null, JdhSettlementEbsPo::getSendStatus, jdhSettlementEbs.getSendStatus());
        queryWrapper.ge(Objects.nonNull(jdhSettlementEbs.getCreateTimeStart()), JdhSettlementEbsPo::getCreateTime, jdhSettlementEbs.getCreateTimeStart());
        queryWrapper.le(Objects.nonNull(jdhSettlementEbs.getCreateTimeEnd()), JdhSettlementEbsPo::getCreateTime, jdhSettlementEbs.getCreateTimeEnd());
        queryWrapper.eq(JdhSettlementEbsPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSettlementEbsPo::getUpdateTime);
        IPage<JdhSettlementEbsPo> page = jdhSettlementEbsMapper.selectPage(param, queryWrapper);
        List<JdhSettlementEbs> list= SettlementEbsPoConvert.INSTANCE.poToModel(page.getRecords());
        return JdhBasicPoConverter.initPage(page, list);
    }
}
