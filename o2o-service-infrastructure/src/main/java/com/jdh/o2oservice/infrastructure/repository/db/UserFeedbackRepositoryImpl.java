package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jd.laf.config.Configuration;
import com.jd.laf.config.Property;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.support.basic.dict.repository.DictRepository;
import com.jdh.o2oservice.base.exception.errorcode.BaseDomainErrorCode;
import com.jdh.o2oservice.core.domain.support.feedback.JdhFeedbackConfigDictAdaptor;
import com.jdh.o2oservice.core.domain.support.feedback.JdhUserFeedback;
import com.jdh.o2oservice.core.domain.support.feedback.JdhUserFeedbackIdentifier;
import com.jdh.o2oservice.core.domain.support.feedback.JdhUserFeedbackOperateHistory;
import com.jdh.o2oservice.core.domain.support.feedback.repository.db.UserFeedbackRepository;
import com.jdh.o2oservice.core.domain.support.feedback.repository.query.JdhFeedbackConfigDbQuery;
import com.jdh.o2oservice.core.domain.support.feedback.repository.query.JdhFeedbackDbQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhUserFeedbackInfrastructureConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhUserFeedbackOperateHistoryPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhUserFeedbackPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhUserFeedbackOperateHistoryPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhUserFeedbackPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName UserFeedbackRepositoryImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/1/14 11:07
 **/
@Service
@Slf4j
public class UserFeedbackRepositoryImpl implements UserFeedbackRepository {

    /**
     * environment
     */
    @Value("${spring.profiles.active}")
    private String environment;

    /**
     * 缓存
     */
    private static final Map<String, List<JdhFeedbackConfigDictAdaptor>> CACHE = Maps.newHashMap();

    /**
     * 用户反馈
     */
    @Resource
    private JdhUserFeedbackPoMapper jdhUserFeedbackPoMapper;

    /**
     * 用户反馈历史
     */
    @Resource
    private JdhUserFeedbackOperateHistoryPoMapper jdhUserFeedbackOperateHistoryPoMapper;

    /**
     * 字典表存储
     */
    @Resource
    private DictRepository dictRepository;

    /**
     * 当前配置
     */
    private static String ACTIVE;

    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        ACTIVE = value;
    }

    /**
     * 查询反馈数据
     * @param jdhUserFeedbackIdentifier
     * @return
     */
    @Override
    public JdhUserFeedback find(JdhUserFeedbackIdentifier jdhUserFeedbackIdentifier) {
        LambdaQueryWrapper<JdhUserFeedbackPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhUserFeedbackPo::getFeedbackId, jdhUserFeedbackIdentifier.getFeedbackId())
                .eq(JdhUserFeedbackPo::getYn, YnStatusEnum.YES.getCode());
        JdhUserFeedbackPo po = jdhUserFeedbackPoMapper.selectOne(queryWrapper);
        return JdhUserFeedbackInfrastructureConverter.INSTANCE.po2Entity(po);
    }

    /**
     *
     * @param entity
     * @return
     */
    @Override
    public int remove(JdhUserFeedback entity) {
        return 0;
    }

    /**
     *
     * @param userFeedback
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(JdhUserFeedback userFeedback) {
        JdhUserFeedbackPo userFeedbackPo = JdhUserFeedbackInfrastructureConverter.INSTANCE.entity2Po(userFeedback);
        //新增
        if (Objects.isNull(userFeedbackPo.getId())) {
            JdhBasicPoConverter.initInsertBasicPo(userFeedbackPo);
            return jdhUserFeedbackPoMapper.insert(userFeedbackPo);
        }
        //修改
        Integer oldVersion = userFeedback.getVersion();
        userFeedback.versionIncrease();
        userFeedbackPo.setBranch(environment);
        userFeedbackPo.setUpdateTime(new Date());
        userFeedbackPo.setVersion(userFeedback.getVersion());

        LambdaUpdateWrapper<JdhUserFeedbackPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(JdhUserFeedbackPo::getVersion,oldVersion)
                .eq(JdhUserFeedbackPo::getFeedbackId,userFeedbackPo.getFeedbackId());

        int count = jdhUserFeedbackPoMapper.update(userFeedbackPo, updateWrapper);
        if (count < 1) {
            throw new BusinessException(BaseDomainErrorCode.FEEDBACK_BUSY);
        }
        return count;
    }

    /**
     * 查询用户反馈题库列表
     * @param query
     * @return
     */
    @Override
    public List<JdhUserFeedback> queryUserFeedbackList(JdhFeedbackDbQuery query) {
        if (Objects.isNull(query) || StringUtils.isBlank(query.getBusinessScene()) || Objects.isNull(query.getPromiseId())) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<JdhUserFeedbackPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhUserFeedbackPo::getPromiseId, query.getPromiseId());
        queryWrapper.eq(JdhUserFeedbackPo::getBusinessScene, query.getBusinessScene());
        queryWrapper.eq(StringUtils.isNotBlank(query.getUserPin()), JdhUserFeedbackPo::getUserPin, query.getUserPin());
        queryWrapper.eq(JdhUserFeedbackPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhUserFeedbackPo> list = jdhUserFeedbackPoMapper.selectList(queryWrapper);
        return JdhUserFeedbackInfrastructureConverter.INSTANCE.po2Entity(list);
    }

    /**
     * 查询用户反馈题库配置
     * @param query
     * @return
     */
    @Override
    public List<JdhFeedbackConfigDictAdaptor> queryUserFeedbackBusinessModeConfigList(JdhFeedbackConfigDbQuery query) {
        if (Objects.isNull(query) || StringUtils.isBlank(query.getBusinessMode()) || StringUtils.isBlank(query.getVerticalCode())
                || StringUtils.isBlank(query.getFeedbackScene())) {
            return Collections.emptyList();
        }

        Map<String, List<JdhFeedbackConfigDictAdaptor>> stringListMap = queryCommonDictList(Sets.newHashSet(query.getFeedbackScene()));
        if (Objects.isNull(stringListMap) || stringListMap.isEmpty()) {
            return new ArrayList<>();
        }
        List<JdhFeedbackConfigDictAdaptor> list = stringListMap.get(query.getFeedbackScene());
        Map<String, JdhFeedbackConfigDictAdaptor> signMap = list.stream().collect(Collectors.toMap(adaptor -> adaptor.getBusinessMode() + adaptor.getVerticalCode(), adaptor -> adaptor, (t, t2) -> t2));
        //判断业务模式 + 业务身份是否有命中的题库配置
        String signKey = query.getBusinessMode() + query.getVerticalCode();
        if (signMap.containsKey(signKey)) {
            //如果有命中，返回对应的题库
            return Lists.newArrayList(signMap.get(signKey));
        }
        //如果没有命中，使用当前业务模式的默认题库返回
        String signDefaultKey = query.getBusinessMode();
        if (signMap.containsKey(signDefaultKey)) {
            return Lists.newArrayList(signMap.get(signDefaultKey));
        }
        return Lists.newArrayList();
    }

    /**
     * 批量保存
     * @param userFeedbackList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSave(List<JdhUserFeedback> userFeedbackList) {
        if(CollectionUtils.isEmpty(userFeedbackList)){
            return false;
        }
        userFeedbackList.forEach(this::save);
        return true;
    }

    /**
     * 批量保存用户反馈操作历史
     * @param historyList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSaveOperateHistory(List<JdhUserFeedbackOperateHistory> historyList) {
        log.info("UserFeedbackRepositoryImpl -> batchSaveOperateHistory start, historyList={}", JSON.toJSONString(historyList));
        if(CollectionUtils.isEmpty(historyList)){
            return false;
        }
        historyList.forEach(history -> {
            JdhUserFeedbackOperateHistoryPo historyPo = JdhUserFeedbackInfrastructureConverter.INSTANCE.historyEntity2Po(history);
            //新增
            if (Objects.isNull(historyPo.getId())) {
                Date cur = new Date();
                historyPo.setCreateTime(cur);
                historyPo.setUpdateTime(cur);
                historyPo.setYn(YnStatusEnum.YES.getCode());
                historyPo.setBranch(ACTIVE);
                historyPo.setVersion(NumConstant.NUM_1);
                jdhUserFeedbackOperateHistoryPoMapper.insert(historyPo);
            } else {
                //修改
                Integer oldVersion = history.getVersion();
                Integer newVersion = oldVersion + 1;
                historyPo.setBranch(environment);
                historyPo.setUpdateTime(new Date());
                historyPo.setVersion(newVersion);

                LambdaUpdateWrapper<JdhUserFeedbackOperateHistoryPo> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(JdhUserFeedbackOperateHistoryPo::getVersion,oldVersion)
                        .eq(JdhUserFeedbackOperateHistoryPo::getId,historyPo.getId());

                int count = jdhUserFeedbackOperateHistoryPoMapper.update(historyPo, updateWrapper);
                if (count < 1) {
                    throw new BusinessException(BaseDomainErrorCode.FEEDBACK_BUSY);
                }
            }
        });
        return true;
    }

    /**
     * 加载配置信息
     *
     * @param configuration config
     */
    @LafValue(name="feedback_config")
    public void loadProperties(Configuration configuration) {
        log.info("UserFeedbackRepositoryImpl -> loadProperties feedback_config 配置调整");
        Set<String> newPinConfig = new HashSet<>();
        for (Property property : configuration.getProperties()) {
            newPinConfig.add(property.getKey());
            try {
                //用户反馈的配置，反序列化为DictInfo的子类。目前根据key包含Feedback关键字判断
                List<JdhFeedbackConfigDictAdaptor> list = com.alibaba.fastjson.JSON.parseArray(property.getValue().toString(), JdhFeedbackConfigDictAdaptor.class);
                CACHE.put(property.getKey(), list);
            }catch (Exception e){
                log.error("UserFeedbackRepositoryImpl loadProperties error key={}, msg={}",property.getKey(), e.getMessage());
            }
        }

        //删除配置数据
        Set<String> removeKeys = removeConfig(newPinConfig, CACHE.keySet());
        log.info("UserFeedbackRepositoryImpl loadProperties 移除的配置key:{}",removeKeys);
        if(!removeKeys.isEmpty()){
            for (String removeKey : removeKeys) {
                CACHE.remove(removeKey);
            }
        }
    }

    /**
     * 删除配置
     *
     * @param newPinConfig newPinConfig
     * @param oldPinConfig oldPinConfig
     * @return {@link Set}<{@link String}>
     */
    private static Set<String> removeConfig(Set<String> newPinConfig,Set<String> oldPinConfig){
        Set<String> allConfig = new HashSet<>(oldPinConfig);
        allConfig.removeAll(newPinConfig);
        return allConfig;
    }

    /**
     *
     * @param keys
     * @return
     */
    private Map<String, List<JdhFeedbackConfigDictAdaptor>> queryCommonDictList(Set<String> keys) {
        if (CollUtil.isEmpty(keys)) {
            return Collections.emptyMap();
        }
        Map<String, List<JdhFeedbackConfigDictAdaptor>> retMap = new HashMap<>(1);
        for (String key : keys) {
            List<JdhFeedbackConfigDictAdaptor> ret = CACHE.get(key);
            // 空置返回, 多词典查询仅返回层级为1的数据
            if (CollUtil.isEmpty(ret)) {
                continue;
            }
            retMap.put(key, ret);
        }
        return retMap;
    }
}