package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.jd.health.xfyl.open.export.dto.TransCodeDto;
import com.jd.health.xfyl.open.export.dto.TransCodeListDto;
import com.jd.health.xfyl.open.export.param.check.*;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper

public interface QuickCheckRpcConverter {
    QuickCheckRpcConverter INSTANCE = Mappers.getMapper(QuickCheckRpcConverter.class);

    QuickCheckPushSampleDeliverBO convert(QuickCheckPushInfoBO quickCheckPushInfoBO);

    @Mapping(source = "status", target = "status")
    QuickCheckPushSampleDeliverParam convert(QuickCheckPushSampleDeliverBO quickCheckPushSampleDeliverBO);

    QuickCheckModifyParam convert(QuickCheckModifyBO quickCheckModifyParam);

    QuickCheckReportRetractParam convert(QuickCheckReportRetractBO quickCheckReportRetractBO);

    @Mapping(source = "medicalPromiseId",target = "sampleId")
    @Mapping(source = "stationId",target = "storeId")
    QuickCheckResultModifyParam convert(QuickCheckResultModifyBO quickCheckResultModifyBO);

    /**
     * 将 FlowCodeQueryBO 对象转换为 QuickCheckTransCodeQueryParam 对象。
     * @param flowCodeQueryBO FlowCodeQueryBO 对象，包含需要转换的属性。
     * @return 转换后的 QuickCheckTransCodeQueryParam 对象。
     */
    @Mapping(source = "medicalPromiseId",target = "sampleId")
    @Mapping(source = "specimenCode",target = "sampleBarcode")
    @Mapping(source = "stationId",target = "storeId")
    @Mapping(source = "specimenCodeList",target = "sampleBarcodeList")
    QuickCheckTransCodeQueryParam convert(FlowCodeQueryBO flowCodeQueryBO);

    FlowCodeBO convert(TransCodeDto transCodeDto);

    FlowCodeListBO convert(TransCodeListDto transCodeListDto);

    List<FlowCodeListBO> convert(List<TransCodeListDto> transCodeListDto);

    @Mapping(source = "medicalPromiseId",target = "sampleId")
    @Mapping(source = "flowCodeList",target = "transCodeList")
    @Mapping(source = "stationId",target = "storeId")
    QuickCheckTransCodeFreeParam convertParam(FreeFlowCodeBO freeFlowCodeBO);

    QuickCheckTransCodeParam convert(FlowCodeListBO flowCodeListBO);


    QuickCheckResultReviewParam convert(QuickCheckResultReviewBO bo);


}
