package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/13 17:25
 */
@Data
@TableName(value = "jdh_voice_covert_record",autoResultMap = true)
public class VoiceConvertRecordPo extends JdhBasicPo{
    /**
     * 主键
     */
    private Long id;

    private Long voiceId;

    /** 事件所属领域 */
    private String domainCode;
    /** 事件所属聚合 */
    private String aggregateCode;
    /** 事件主体 */
    private String aggregateId;
    private String fileIds;

    private Date refreshTime;
    /**
     * 状态 0:待解；1:解析中；2:解析成功；-1:解析失败
     */
    private Integer type;
    /**
     * 状态 0:待解；1:解析中；2:解析成功；-1:解析失败,-2:获取文件失败
     */
    private Integer status;

    private String tasks;



}
