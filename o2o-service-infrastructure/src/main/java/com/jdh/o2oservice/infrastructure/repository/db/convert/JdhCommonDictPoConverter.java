package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.core.domain.support.basic.dict.model.CommonDictionary;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhCommonDictionaryPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface JdhCommonDictPoConverter {

    /**
     * 实例
     */
    JdhCommonDictPoConverter INSTANCE = Mappers.getMapper(JdhCommonDictPoConverter.class);

    CommonDictionary convert2CommonDictionary(JdhCommonDictionaryPo jdhCommonDictionaryPo);

    JdhCommonDictionaryPo convert2JdhCommonDictionaryPo(CommonDictionary commonDictionary);

    List<CommonDictionary> convert2CommonDictionaryList(List<JdhCommonDictionaryPo> jdhCommonDictionaryPoList);

    List<JdhCommonDictionaryPo> convert2JdhCommonDictionaryPoList(List<CommonDictionary> commonDictionaryList);
}
