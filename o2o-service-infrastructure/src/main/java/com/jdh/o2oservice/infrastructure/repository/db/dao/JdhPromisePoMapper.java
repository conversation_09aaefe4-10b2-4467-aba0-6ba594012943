package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* @description JdhPromisePoMapper简介
* <AUTHOR>
* @date 2023-12-22 10:35:07
*/
@Mapper
public interface JdhPromisePoMapper extends BaseMapper<JdhPromisePo> {


    /**
     * 核销超时预约单
     * @return
     */
    List<JdhPromisePo> timeoutWrittenOffList(@Param("statusList") List<Integer> promiseStatus,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime,
                                             @Param("verticalCode") String verticalCode,
                                             @Param("limitStart") int limitStart,
                                             @Param("limitSize") int limitSize);

    /**
     * 临近过期履约单
     * @return
     */
    List<JdhPromisePo> closeOfExpirationList(@Param("statusList") List<Integer> promiseStatus,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime,
                                             @Param("verticalCode") String verticalCode,
                                             @Param("limitStart") int limitStart,
                                             @Param("limitSize") int limitSize);


    /**
     * 预约前一天预约单扫描
     * @return
     */
    List<JdhPromisePo> appointmentDayBefore(@Param("statusList") List<Integer> promiseStatus,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime,
                                             @Param("verticalCode") String verticalCode,
                                            @Param("limitStart") int limitStart,
                                            @Param("limitSize") int limitSize);


    /**
     * 数据库链接验证
     * @return
     */
    Integer ping();



}