package com.jdh.o2oservice.infrastructure.repository.db;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.util.TdeClientUtil;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipPageDBQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelShipPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelTaskPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelShipPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelShipPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:AngelShipRepositoryImpl
 * @Description: 运单仓储
 * @Author: yaoqinghai
 * @Date: 2024/4/21 14:48
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AngelShipRepositoryImpl implements AngelShipRepository {

    @Resource
    private JdhAngelShipPoMapper jdhAngelShipPoMapper;

    @Resource
    private TdeClientUtil tdeClientUtil;

    @Override
    public List<AngelShip> findList(AngelShipDBQuery angelShipDBQuery) {
        LambdaQueryWrapper<JdhAngelShipPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtils.isNotEmpty(angelShipDBQuery.getShipIds()), JdhAngelShipPo::getShipId, angelShipDBQuery.getShipIds())
                .eq(Objects.nonNull(angelShipDBQuery.getWorkId()), JdhAngelShipPo::getWorkId, angelShipDBQuery.getWorkId())
                .eq(StringUtils.isNotBlank(angelShipDBQuery.getOutShipId()), JdhAngelShipPo::getOutShipId, angelShipDBQuery.getOutShipId())
                .in(CollectionUtils.isNotEmpty(angelShipDBQuery.getWorkIds()), JdhAngelShipPo::getWorkId, angelShipDBQuery.getWorkIds())
                .in(CollectionUtils.isNotEmpty(angelShipDBQuery.getStatus()), JdhAngelShipPo::getShipStatus, angelShipDBQuery.getStatus())
                .eq(StringUtils.isNotBlank(angelShipDBQuery.getReceiverId()), JdhAngelShipPo::getReceiverId, angelShipDBQuery.getReceiverId())
                .in(CollectionUtils.isNotEmpty(angelShipDBQuery.getReceiverIds()), JdhAngelShipPo::getReceiverId, angelShipDBQuery.getReceiverIds())
                .eq(JdhAngelShipPo::getYn, YnStatusEnum.YES.getCode())
                .gt(Objects.nonNull(angelShipDBQuery.getQueryPlanCallTimeStart()),JdhAngelShipPo::getPlanCallTime,angelShipDBQuery.getQueryPlanCallTimeStart())
                .in(CollectionUtils.isNotEmpty(angelShipDBQuery.getTypes()),JdhAngelShipPo::getType,angelShipDBQuery.getTypes())
                .orderByDesc(JdhAngelShipPo::getCreateTime);

        List<JdhAngelShipPo> jdhAngelShipPos = jdhAngelShipPoMapper.selectList(queryWrapper);
        return JdhAngelShipPoConverter.INSTANCE.convertToEntityList(jdhAngelShipPos);
    }

    /**
     * 查询运单分页数据
     *
     * @param dbQuery
     */
    @Override
    public Page<AngelShip> finShipPage(AngelShipPageDBQuery dbQuery) {
        Page<JdhAngelShipPo> pageQuery = new Page<>(dbQuery.getPageNum(), dbQuery.getPageSize());
        LambdaQueryWrapper<JdhAngelShipPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(Objects.nonNull(dbQuery.getStartTime()), JdhAngelShipPo::getCreateTime, dbQuery.getStartTime());
        queryWrapper.lt(Objects.nonNull(dbQuery.getEndTime()), JdhAngelShipPo::getCreateTime, dbQuery.getEndTime());

        queryWrapper.orderByAsc(JdhAngelShipPo::getId);

        Page<JdhAngelShipPo> jdhAngelShipPoPage = jdhAngelShipPoMapper.selectPage(pageQuery, queryWrapper);
        if(Objects.isNull(jdhAngelShipPoPage) || CollectionUtils.isEmpty(jdhAngelShipPoPage.getRecords())) {
            log.info("[AngelShipRepositoryImpl -> finShipPage],分页查询结果为空!");
            return new Page<>();
        }

        List<JdhAngelShipPo> records = jdhAngelShipPoPage.getRecords();
        List<AngelShip> shipDataList = JdhAngelShipPoConverter.INSTANCE.convertToEntityList(records);
        return JdhBasicPoConverter.initPage(jdhAngelShipPoPage, shipDataList);
    }

    /**
     * 更新运单距离信息
     *
     * @param record
     */
    @Override
    public int saveDistance(AngelShip record) {
        JdhAngelShipPo angelShipPo = JdhAngelShipPoConverter.INSTANCE.convertToJdhAngelShipPo(record);

        LambdaUpdateWrapper<JdhAngelShipPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Objects.nonNull(angelShipPo.getType()), JdhAngelShipPo::getType, angelShipPo.getType())
                .set(Objects.nonNull(angelShipPo.getTotalDistance()), JdhAngelShipPo::getTotalDistance, angelShipPo.getTotalDistance())
                .set(Objects.nonNull(angelShipPo.getEstimateGrabTime()), JdhAngelShipPo::getEstimateGrabTime, angelShipPo.getEstimateGrabTime())
                .set(Objects.nonNull(angelShipPo.getEstimatePickUpTime()), JdhAngelShipPo::getEstimatePickUpTime, angelShipPo.getEstimatePickUpTime())
                .set(Objects.nonNull(angelShipPo.getEstimateReceiveTime()), JdhAngelShipPo::getEstimateReceiveTime, angelShipPo.getEstimateReceiveTime())
                .set(Objects.nonNull(angelShipPo.getSenderFullAddress()), JdhAngelShipPo::getSenderFullAddress, angelShipPo.getSenderFullAddress())
                .set(JdhAngelShipPo::getUpdateTime, new Date())
                .setSql("`version` = version+1")
                .eq(JdhAngelShipPo::getShipId, angelShipPo.getShipId());
        return jdhAngelShipPoMapper.update(null, updateWrapper);
    }

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param angelShipIdentifier
     */
    @Override
    public AngelShip find(AngelShipIdentifier angelShipIdentifier) {
        LambdaQueryWrapper<JdhAngelShipPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelShipPo::getShipId, angelShipIdentifier.getShipId());
        return JdhAngelTaskPoConverter.INSTANCE.convertToAngelShip(jdhAngelShipPoMapper.selectOne(queryWrapper));
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param aggregate
     */
    @Override
    public int remove(AngelShip aggregate) {
        log.info("AngelShipRepositoryImpl -> remove aggregate:{}", JSON.toJSONString(aggregate));
        LambdaUpdateWrapper<JdhAngelShipPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("`version` = `version` + 1").set(JdhAngelShipPo::getYn, YnStatusEnum.NO.getCode())
                .set(JdhAngelShipPo::getUpdateTime, new Date())
                .eq(JdhAngelShipPo::getShipId, aggregate.getShipId());
        return jdhAngelShipPoMapper.update(null, updateWrapper);
    }

    /**
     * 保存一个Aggregate
     * 保存后自动重置追踪条件
     *
     * @param angelShip
     */
    @Override
    public int save(AngelShip angelShip) {
        if(Objects.isNull(angelShip)){
            return 0;
        }
        JdhAngelShipPo angelShipPo = JdhAngelShipPoConverter.INSTANCE.convertToJdhAngelShipPo(angelShip);
        log.info("[AngelShipRepositoryImpl.save], angelShipPo={}", JSON.toJSONString(angelShipPo));
        if(Objects.isNull(angelShip.getId())){
            return jdhAngelShipPoMapper.insert(angelShipPo);
        }else {
            swapTransferPersionInfo(angelShipPo, angelShip);
            LambdaUpdateWrapper<JdhAngelShipPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(Objects.nonNull(angelShipPo.getType()), JdhAngelShipPo::getType, angelShipPo.getType())
                    .set(StringUtils.isNotBlank(angelShipPo.getTransferHeadImg()), JdhAngelShipPo::getTransferHeadImg, angelShipPo.getTransferHeadImg())
                    .set(Objects.nonNull(angelShipPo.getTransferStartLng()), JdhAngelShipPo::getTransferStartLng, angelShipPo.getTransferStartLng())
                    .set(Objects.nonNull(angelShipPo.getTransferStartLat()), JdhAngelShipPo::getTransferStartLat, angelShipPo.getTransferStartLat())
                    .set(StringUtils.isNotBlank(angelShipPo.getTransferHeadImg()), JdhAngelShipPo::getTransferHeadImg, angelShipPo.getTransferHeadImg())
                    .set(Objects.nonNull(angelShipPo.getTotalDistance()), JdhAngelShipPo::getTotalDistance, angelShipPo.getTotalDistance())
                    .set(Objects.nonNull(angelShipPo.getEstimateGrabTime()), JdhAngelShipPo::getEstimateGrabTime, angelShipPo.getEstimateGrabTime())
                    .set(Objects.nonNull(angelShipPo.getEstimatePickUpTime()), JdhAngelShipPo::getEstimatePickUpTime, angelShipPo.getEstimatePickUpTime())
                    .set(Objects.nonNull(angelShipPo.getEstimateReceiveTime()), JdhAngelShipPo::getEstimateReceiveTime, angelShipPo.getEstimateReceiveTime())
                    .set(JdhAngelShipPo::getTransferId, angelShipPo.getTransferId())
                    .set(JdhAngelShipPo::getTransferName, angelShipPo.getTransferName())
                    .set(JdhAngelShipPo::getTransferNameIndex, angelShipPo.getTransferNameIndex())
                    .set(JdhAngelShipPo::getTransferPhone, angelShipPo.getTransferPhone())
                    .set(JdhAngelShipPo::getTransferPhoneIndex, angelShipPo.getTransferPhoneIndex())
                    .set(Objects.nonNull(angelShipPo.getShipStatus()), JdhAngelShipPo::getShipStatus, angelShipPo.getShipStatus())
                    .set(Objects.nonNull(angelShipPo.getSenderFullAddress()), JdhAngelShipPo::getSenderFullAddress, angelShipPo.getSenderFullAddress())
                    .set(JdhAngelShipPo::getSenderName, angelShipPo.getSenderName())
                    .set(JdhAngelShipPo::getSenderNameIndex, angelShipPo.getSenderNameIndex())
                    .set(JdhAngelShipPo::getSenderPhone, angelShipPo.getSenderPhone())
                    .set(JdhAngelShipPo::getSenderPhoneIndex, angelShipPo.getSenderPhoneIndex())
                    .set(StringUtils.isNotBlank(angelShipPo.getUpdater()), JdhAngelShipPo::getUpdater, angelShipPo.getUpdater())
                    .set(StringUtils.isNotBlank(angelShipPo.getOutShipId()), JdhAngelShipPo::getOutShipId, angelShipPo.getOutShipId())
                    .set(Objects.nonNull(angelShipPo.getYn()), JdhAngelShipPo::getYn, angelShipPo.getYn())
                    .set(Objects.nonNull(angelShipPo.getStandCancelCode()), JdhAngelShipPo::getStandCancelCode, angelShipPo.getStandCancelCode())
                    .set(Objects.nonNull(angelShipPo.getRepeatType()), JdhAngelShipPo::getRepeatType, angelShipPo.getRepeatType())
                    .set(Objects.nonNull(angelShipPo.getLogisticsMessage()), JdhAngelShipPo::getLogisticsMessage, angelShipPo.getLogisticsMessage())
                    .set(StringUtils.isNotBlank(angelShipPo.getExtend()), JdhAngelShipPo::getExtend, angelShipPo.getExtend())
                    .setSql("`update_time` = now()")
                    .setSql("`version` = version+1")
                    .eq(JdhAngelShipPo::getShipId, angelShipPo.getShipId());

            if(StringUtils.isNotBlank(angelShipPo.getSendCode())) {
                updateWrapper.set(JdhAngelShipPo::getSendCode, tdeClientUtil.encrypt(angelShipPo.getSendCode()));
                updateWrapper.set(JdhAngelShipPo::getSendCodeIndex, tdeClientUtil.obtainKeyWordIndex(angelShipPo.getSendCode()));
            }
            if(StringUtils.isNotBlank(angelShipPo.getFinishCode())) {
                updateWrapper.set(JdhAngelShipPo::getFinishCode, tdeClientUtil.encrypt(angelShipPo.getFinishCode()));
                updateWrapper.set(JdhAngelShipPo::getFinishCodeIndex, tdeClientUtil.obtainKeyWordIndex(angelShipPo.getFinishCode()));
            }
            return jdhAngelShipPoMapper.update(null, updateWrapper);
        }
    }

    /**
     *
     * 更新
     *
     * @param angelShip
     */
    @Override
    public int updateByShipId(AngelShip angelShip) {
        if(Objects.isNull(angelShip)){
            return 0;
        }
        JdhAngelShipPo angelShipPo = JdhAngelShipPoConverter.INSTANCE.convertToJdhAngelShipPo(angelShip);
        log.info("[AngelShipRepositoryImpl.updateByShipId], angelShipPo={}", JSON.toJSONString(angelShipPo));

        swapTransferPersionInfo(angelShipPo, angelShip);
        LambdaUpdateWrapper<JdhAngelShipPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Objects.nonNull(angelShipPo.getType()), JdhAngelShipPo::getType, angelShipPo.getType())
                .set(StringUtils.isNotBlank(angelShipPo.getTransferHeadImg()), JdhAngelShipPo::getTransferHeadImg, angelShipPo.getTransferHeadImg())
                .set(Objects.nonNull(angelShipPo.getTransferStartLng()), JdhAngelShipPo::getTransferStartLng, angelShipPo.getTransferStartLng())
                .set(Objects.nonNull(angelShipPo.getTransferStartLat()), JdhAngelShipPo::getTransferStartLat, angelShipPo.getTransferStartLat())
                .set(StringUtils.isNotBlank(angelShipPo.getTransferHeadImg()), JdhAngelShipPo::getTransferHeadImg, angelShipPo.getTransferHeadImg())
                .set(Objects.nonNull(angelShipPo.getTotalDistance()), JdhAngelShipPo::getTotalDistance, angelShipPo.getTotalDistance())
                .set(Objects.nonNull(angelShipPo.getEstimateGrabTime()), JdhAngelShipPo::getEstimateGrabTime, angelShipPo.getEstimateGrabTime())
                .set(Objects.nonNull(angelShipPo.getEstimatePickUpTime()), JdhAngelShipPo::getEstimatePickUpTime, angelShipPo.getEstimatePickUpTime())
                .set(Objects.nonNull(angelShipPo.getEstimateReceiveTime()), JdhAngelShipPo::getEstimateReceiveTime, angelShipPo.getEstimateReceiveTime())
                .set(Objects.nonNull(angelShipPo.getPlanCallTime()), JdhAngelShipPo::getPlanCallTime, angelShipPo.getPlanCallTime())
                .set(JdhAngelShipPo::getTransferId, angelShipPo.getTransferId())
                .set(JdhAngelShipPo::getTransferName, angelShipPo.getTransferName())
                .set(JdhAngelShipPo::getTransferNameIndex, angelShipPo.getTransferNameIndex())
                .set(JdhAngelShipPo::getTransferPhone, angelShipPo.getTransferPhone())
                .set(JdhAngelShipPo::getTransferPhoneIndex, angelShipPo.getTransferPhoneIndex())
                .set(Objects.nonNull(angelShipPo.getShipStatus()), JdhAngelShipPo::getShipStatus, angelShipPo.getShipStatus())
                .set(Objects.nonNull(angelShipPo.getSenderFullAddress()), JdhAngelShipPo::getSenderFullAddress, angelShipPo.getSenderFullAddress())
                .set(JdhAngelShipPo::getSenderName, angelShipPo.getSenderName())
                .set(JdhAngelShipPo::getSenderNameIndex, angelShipPo.getSenderNameIndex())
                .set(JdhAngelShipPo::getSenderPhone, angelShipPo.getSenderPhone())
                .set(JdhAngelShipPo::getSenderPhoneIndex, angelShipPo.getSenderPhoneIndex())
                .set(StringUtils.isNotBlank(angelShipPo.getUpdater()), JdhAngelShipPo::getUpdater, angelShipPo.getUpdater())
                .set(StringUtils.isNotBlank(angelShipPo.getOutShipId()), JdhAngelShipPo::getOutShipId, angelShipPo.getOutShipId())
                .set(Objects.nonNull(angelShipPo.getYn()), JdhAngelShipPo::getYn, angelShipPo.getYn())
                .set(Objects.nonNull(angelShipPo.getStandCancelCode()), JdhAngelShipPo::getStandCancelCode, angelShipPo.getStandCancelCode())
                .set(StringUtils.isNotBlank(angelShipPo.getLogisticsMessage()), JdhAngelShipPo::getLogisticsMessage, angelShipPo.getLogisticsMessage())
                .set(Objects.nonNull(angelShipPo.getRepeatType()), JdhAngelShipPo::getRepeatType, angelShipPo.getRepeatType())
                .set(StringUtils.isNotEmpty(angelShipPo.getExtend()), JdhAngelShipPo::getExtend, angelShipPo.getExtend())
                .setSql("`update_time` = now()")
                .setSql("`version` = version+1")
                .eq(JdhAngelShipPo::getShipId, angelShipPo.getShipId());

        if(StringUtils.isNotBlank(angelShipPo.getSendCode())) {
            updateWrapper.set(JdhAngelShipPo::getSendCode, tdeClientUtil.encrypt(angelShipPo.getSendCode()));
            updateWrapper.set(JdhAngelShipPo::getSendCodeIndex, tdeClientUtil.obtainKeyWordIndex(angelShipPo.getSendCode()));
        }
        if(StringUtils.isNotBlank(angelShipPo.getFinishCode())) {
            updateWrapper.set(JdhAngelShipPo::getFinishCode, tdeClientUtil.encrypt(angelShipPo.getFinishCode()));
            updateWrapper.set(JdhAngelShipPo::getFinishCodeIndex, tdeClientUtil.obtainKeyWordIndex(angelShipPo.getFinishCodeIndex()));
        }
        return jdhAngelShipPoMapper.update(null, updateWrapper);

    }

    /**
     * 根据第三方运单号修改运单讯息
     * @param angelShip
     * @return
     */
    @Override
    public int updateByOutShipId(AngelShip angelShip) {
        if(Objects.isNull(angelShip)){
            return 0;
        }
        JdhAngelShipPo angelShipPo = JdhAngelShipPoConverter.INSTANCE.convertToJdhAngelShipPo(angelShip);
        log.info("[AngelShipRepositoryImpl.updateByShipId], angelShipPo={}", JSON.toJSONString(angelShipPo));

        swapTransferPersionInfo(angelShipPo, angelShip);
        LambdaUpdateWrapper<JdhAngelShipPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Objects.nonNull(angelShipPo.getType()), JdhAngelShipPo::getType, angelShipPo.getType())
                .set(StringUtils.isNotBlank(angelShipPo.getTransferHeadImg()), JdhAngelShipPo::getTransferHeadImg, angelShipPo.getTransferHeadImg())
                .set(Objects.nonNull(angelShipPo.getTransferStartLng()), JdhAngelShipPo::getTransferStartLng, angelShipPo.getTransferStartLng())
                .set(Objects.nonNull(angelShipPo.getTransferStartLat()), JdhAngelShipPo::getTransferStartLat, angelShipPo.getTransferStartLat())
                .set(StringUtils.isNotBlank(angelShipPo.getTransferHeadImg()), JdhAngelShipPo::getTransferHeadImg, angelShipPo.getTransferHeadImg())
                .set(Objects.nonNull(angelShipPo.getTotalDistance()), JdhAngelShipPo::getTotalDistance, angelShipPo.getTotalDistance())
                .set(Objects.nonNull(angelShipPo.getEstimateGrabTime()), JdhAngelShipPo::getEstimateGrabTime, angelShipPo.getEstimateGrabTime())
                .set(Objects.nonNull(angelShipPo.getEstimatePickUpTime()), JdhAngelShipPo::getEstimatePickUpTime, angelShipPo.getEstimatePickUpTime())
                .set(Objects.nonNull(angelShipPo.getEstimateReceiveTime()), JdhAngelShipPo::getEstimateReceiveTime, angelShipPo.getEstimateReceiveTime())
                .set(Objects.nonNull(angelShipPo.getPlanCallTime()), JdhAngelShipPo::getPlanCallTime, angelShipPo.getPlanCallTime())
                .set(JdhAngelShipPo::getTransferId, angelShipPo.getTransferId())
                .set(JdhAngelShipPo::getTransferName, angelShipPo.getTransferName())
                .set(JdhAngelShipPo::getTransferNameIndex, angelShipPo.getTransferNameIndex())
                .set(JdhAngelShipPo::getTransferPhone, angelShipPo.getTransferPhone())
                .set(JdhAngelShipPo::getTransferPhoneIndex, angelShipPo.getTransferPhoneIndex())
                .set(Objects.nonNull(angelShipPo.getShipStatus()), JdhAngelShipPo::getShipStatus, angelShipPo.getShipStatus())
                .set(Objects.nonNull(angelShipPo.getSenderFullAddress()), JdhAngelShipPo::getSenderFullAddress, angelShipPo.getSenderFullAddress())
                .set(JdhAngelShipPo::getSenderName, angelShipPo.getSenderName())
                .set(JdhAngelShipPo::getSenderNameIndex, angelShipPo.getSenderNameIndex())
                .set(JdhAngelShipPo::getSenderPhone, angelShipPo.getSenderPhone())
                .set(JdhAngelShipPo::getSenderPhoneIndex, angelShipPo.getSenderPhoneIndex())
                .set(StringUtils.isNotBlank(angelShipPo.getUpdater()), JdhAngelShipPo::getUpdater, angelShipPo.getUpdater())
                .set(StringUtils.isNotBlank(angelShipPo.getOutShipId()), JdhAngelShipPo::getOutShipId, angelShipPo.getOutShipId())
                .set(Objects.nonNull(angelShipPo.getYn()), JdhAngelShipPo::getYn, angelShipPo.getYn())
                .set(Objects.nonNull(angelShipPo.getStandCancelCode()), JdhAngelShipPo::getStandCancelCode, angelShipPo.getStandCancelCode())
                .set(Objects.nonNull(angelShipPo.getRepeatType()), JdhAngelShipPo::getRepeatType, angelShipPo.getRepeatType())
                .setSql("`update_time` = now()")
                .setSql("`version` = version+1")
                .eq(JdhAngelShipPo::getOutShipId, angelShipPo.getOutShipId());

        if(StringUtils.isNotBlank(angelShipPo.getSendCode())) {
            updateWrapper.set(JdhAngelShipPo::getSendCode, tdeClientUtil.encrypt(angelShipPo.getSendCode()));
            updateWrapper.set(JdhAngelShipPo::getSendCodeIndex, tdeClientUtil.obtainKeyWordIndex(angelShipPo.getSendCode()));
        }
        if(StringUtils.isNotBlank(angelShipPo.getFinishCode())) {
            updateWrapper.set(JdhAngelShipPo::getFinishCode, tdeClientUtil.encrypt(angelShipPo.getFinishCode()));
            updateWrapper.set(JdhAngelShipPo::getFinishCodeIndex, tdeClientUtil.obtainKeyWordIndex(angelShipPo.getFinishCodeIndex()));
        }
        return jdhAngelShipPoMapper.update(null, updateWrapper);
    }

    @Override
    public int updateNoNullByOutShipId(AngelShip angelShip) {
        JdhAngelShipPo angelShipPo = JdhAngelShipPoConverter.INSTANCE.convertToJdhAngelShipPo(angelShip);
        angelShipPo.setUpdateTime(new Date());

        LambdaUpdateWrapper<JdhAngelShipPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(JdhAngelShipPo::getOutShipId,angelShip.getOutShipId());
        updateWrapper.setSql("`update_time` = now()").setSql("`version` = version+1");

        return jdhAngelShipPoMapper.update(angelShipPo,updateWrapper);
    }

    /**
     * 处理加密字段
     *
     * @param angelShipPo
     * @param angelShip
     */
    private void swapTransferPersionInfo(JdhAngelShipPo angelShipPo, AngelShip angelShip) {
        if(StringUtils.isNotBlank(angelShip.getTransferName())){
            angelShipPo.setTransferName(tdeClientUtil.encrypt(angelShip.getTransferName()));
            angelShipPo.setTransferNameIndex(tdeClientUtil.obtainKeyWordIndex(angelShip.getTransferName()));
        }else {
            angelShipPo.setTransferName(null);
            angelShipPo.setTransferNameIndex(null);
        }
        if(StringUtils.isNotBlank(angelShip.getTransferPhone())){
            angelShipPo.setTransferPhone(tdeClientUtil.encrypt(angelShip.getTransferPhone()));
            angelShipPo.setTransferPhoneIndex(tdeClientUtil.obtainKeyWordIndex(angelShip.getTransferPhone()));
        }else {
            angelShipPo.setTransferPhone(null);
            angelShipPo.setTransferPhoneIndex(null);
        }
        if(StringUtils.isNotBlank(angelShip.getSenderName())){
            angelShipPo.setSenderName(tdeClientUtil.encrypt(angelShip.getSenderName()));
            angelShipPo.setSenderNameIndex(tdeClientUtil.obtainKeyWordIndex(angelShip.getSenderName()));
        }else {
            angelShipPo.setSenderName(null);
            angelShipPo.setSenderNameIndex(null);
        }
        if(StringUtils.isNotBlank(angelShip.getSenderPhone())){
            angelShipPo.setSenderPhone(tdeClientUtil.encrypt(angelShip.getSenderPhone()));
            angelShipPo.setSenderPhoneIndex(tdeClientUtil.obtainKeyWordIndex(angelShip.getSenderPhone()));
        }else {
            angelShipPo.setSenderPhone(null);
            angelShipPo.setSenderPhoneIndex(null);
        }
    }
}
