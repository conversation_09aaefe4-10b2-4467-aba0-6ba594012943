package com.jdh.o2oservice.infrastructure.repository.db;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.angel.enums.AngelErrorCode;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.db.ActivityRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.ActivityDetailGroupCountQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhActivityConfigRepQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelActivityRepQuery;
import com.jdh.o2oservice.domain.angel.core.ext.model.JdhAbstractActivityConfig;
import com.jdh.o2oservice.domain.angel.core.ext.model.JdhAbstractAngelActivity;
import com.jdh.o2oservice.domain.angel.core.ext.model.JdhActivityConfigIdentifier;
import com.jdh.o2oservice.domain.angel.core.ext.model.JdhAngelActivityIdentifier;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhActivityConfigPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelActivityInstancePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.ext.ActivityPoConvertDelegate;
import com.jdh.o2oservice.infrastructure.repository.db.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * @ClassName ActivityRepositoryImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/11/18 17:36
 **/
@Repository
@Slf4j
public class ActivityRepositoryImpl implements ActivityRepository {

    /**
     * activityPoConvertDelegate
     */
    @Resource
    private ActivityPoConvertDelegate activityPoConvertDelegate;

    /**
     *
     */
    @Resource
    private JdhActivityConfigPoMapper jdhActivityConfigPoMapper;

    /**
     *
     */
    @Resource
    private JdhAngelActivityInstancePoMapper jdhAngelActivityInstancePoMapper;

    /**
     *
     * @param jdhAngelActivityIdentifier
     * @return
     */
    @Override
    public JdhAbstractAngelActivity find(JdhAngelActivityIdentifier jdhAngelActivityIdentifier) {
        LambdaQueryWrapper<JdhAngelActivityInstancePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelActivityInstancePo::getAngelActivityId, jdhAngelActivityIdentifier.getAngelActivityId());
        //有效
        queryWrapper.eq(JdhAngelActivityInstancePo::getYn, YnStatusEnum.YES.getCode());

        JdhAngelActivityInstancePo jdhAngelActivityInstancePo = jdhAngelActivityInstancePoMapper.selectOne(queryWrapper);
        return activityPoConvertDelegate.convertPo2AngelActivity(jdhAngelActivityInstancePo);
    }

    /**
     *
     * @param entity
     * @return
     */
    @Override
    public int remove(JdhAbstractAngelActivity entity) {
        return 0;
    }

    /**
     *
     * @param angelActivity
     * @return
     */
    @Override
    public int save(JdhAbstractAngelActivity angelActivity) {
        JdhAngelActivityInstancePo angelActivityInstancePo = activityPoConvertDelegate.convertActivityConfig2Po(angelActivity);
        //新增活动配置
        if (Objects.isNull(angelActivityInstancePo.getId())) {
            JdhBasicPoConverter.initInsertBasicPo(angelActivityInstancePo);
            log.info("ActivityRepositoryImpl -> save angelActivityInstancePo:{}", JSON.toJSONString(angelActivityInstancePo));
            return jdhAngelActivityInstancePoMapper.insert(angelActivityInstancePo);
        } else {
            //修改活动信息
            LambdaUpdateWrapper<JdhAngelActivityInstancePo> updateAngelWrapper = new LambdaUpdateWrapper<>();
            //version++
            angelActivity.versionIncrease();

            updateAngelWrapper.eq(JdhAngelActivityInstancePo::getAngelActivityId, angelActivityInstancePo.getAngelActivityId())
                    .eq(JdhAngelActivityInstancePo::getVersion, angelActivityInstancePo.getVersion())
                    .set(JdhAngelActivityInstancePo::getActivityConfigId, angelActivityInstancePo.getActivityConfigId())
                    .set(JdhAngelActivityInstancePo::getAngelId, angelActivityInstancePo.getAngelId())
                    .set(JdhAngelActivityInstancePo::getAngelActivityStatus, angelActivityInstancePo.getAngelActivityStatus())
                    .set(JdhAngelActivityInstancePo::getActivityStartTime, angelActivityInstancePo.getActivityStartTime())
                    .set(JdhAngelActivityInstancePo::getActivityEndTime, angelActivityInstancePo.getActivityEndTime())
                    .set(JdhAngelActivityInstancePo::getActivityRuleProgress, angelActivityInstancePo.getActivityRuleProgress())
                    .set(JdhAngelActivityInstancePo::getActivityReward, angelActivityInstancePo.getActivityReward())
                    .set(JdhAngelActivityInstancePo::getRewardStatus, angelActivityInstancePo.getRewardStatus())
                    .set(JdhAngelActivityInstancePo::getUpdateTime, new Date())
                    .set(JdhAngelActivityInstancePo::getVersion, angelActivity.getVersion())
            ;
            log.info("ActivityRepositoryImpl -> update  updateAngelWrapper:{}", JSON.toJSONString(updateAngelWrapper));
            int updateSum = jdhAngelActivityInstancePoMapper.update(null, updateAngelWrapper);
            if (updateSum < 1) {
                throw new BusinessException(AngelErrorCode.ANGEL_UPDATE_DB_VERSION_ERROR);
            }
            return updateSum;
        }
    }

    /**
     * 保存活动配置
     * @param activityConfig
     * @return
     */
    @Override
    public int saveActivityConfig(JdhAbstractActivityConfig activityConfig) {
        JdhActivityConfigPo jdhActivityConfigPo = activityPoConvertDelegate.convertActivityConfig2Po(activityConfig);
        //新增活动配置
        if (Objects.isNull(jdhActivityConfigPo.getId())) {
            JdhBasicPoConverter.initInsertBasicPo(jdhActivityConfigPo);
            log.info("ActivityRepositoryImpl -> saveActivityConfig jdhActivityConfigPo:{}", JSON.toJSONString(jdhActivityConfigPo));
            return jdhActivityConfigPoMapper.insert(jdhActivityConfigPo);
        } else {
            //修改活动信息
            LambdaUpdateWrapper<JdhActivityConfigPo> updateAngelWrapper = new LambdaUpdateWrapper<>();
            //version++
            activityConfig.versionIncrease();

            updateAngelWrapper.eq(JdhActivityConfigPo::getActivityConfigId, jdhActivityConfigPo.getActivityConfigId())
                    .eq(JdhActivityConfigPo::getVersion, jdhActivityConfigPo.getVersion())
                    .set(JdhActivityConfigPo::getActivityConfigName, jdhActivityConfigPo.getActivityConfigName())
                    .set(JdhActivityConfigPo::getActivityConfigType, jdhActivityConfigPo.getActivityConfigType())
                    .set(JdhActivityConfigPo::getActivityConfigStatus, jdhActivityConfigPo.getActivityConfigStatus())
                    .set(JdhActivityConfigPo::getValidDateType, jdhActivityConfigPo.getValidDateType())
                    .set(JdhActivityConfigPo::getValidRelativeDay, jdhActivityConfigPo.getValidRelativeDay())
                    .set(JdhActivityConfigPo::getValidAbsoluteStartTime, jdhActivityConfigPo.getValidAbsoluteStartTime())
                    .set(JdhActivityConfigPo::getValidAbsoluteEndTime, jdhActivityConfigPo.getValidAbsoluteEndTime())
                    .set(JdhActivityConfigPo::getActivityConfigRule, jdhActivityConfigPo.getActivityConfigRule())
                    .set(JdhActivityConfigPo::getActivityConfigReward, jdhActivityConfigPo.getActivityConfigReward())
                    .set(JdhActivityConfigPo::getUpdateTime, new Date())
                    .set(JdhActivityConfigPo::getVersion, activityConfig.getVersion())
            ;
            log.info("ActivityRepositoryImpl -> update  updateAngelWrapper:{}", JSON.toJSONString(updateAngelWrapper));
            int updateSum = jdhActivityConfigPoMapper.update(null, updateAngelWrapper);
            if (updateSum < 1) {
                throw new BusinessException(AngelErrorCode.ANGEL_UPDATE_DB_VERSION_ERROR);
            }
            return updateSum;
        }
    }

    /**
     * 查询活动配置
     * @param activityConfigId
     * @return
     */
    @Override
    public JdhAbstractActivityConfig findActivityConfig(JdhActivityConfigIdentifier activityConfigId) {
        LambdaQueryWrapper<JdhActivityConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhActivityConfigPo::getActivityConfigId, activityConfigId.getActivityConfigId());
        //有效
        queryWrapper.eq(JdhActivityConfigPo::getYn, YnStatusEnum.YES.getCode());
        JdhActivityConfigPo jdhActivityConfigPo = jdhActivityConfigPoMapper.selectOne(queryWrapper);
        return activityPoConvertDelegate.convertPo2ActivityConfig(jdhActivityConfigPo);
    }

    /**
     * 查询活动配置列表
     * @param query
     * @return
     */
    @Override
    public List<JdhAbstractActivityConfig> findActivityConfigList(JdhActivityConfigRepQuery query) {
        LambdaQueryWrapper<JdhActivityConfigPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhActivityConfigPo::getActivityConfigType, query.getActivityConfigType());
        queryWrapper.eq(Objects.nonNull(query.getActivityConfigStatus()), JdhActivityConfigPo::getActivityConfigStatus, query.getActivityConfigStatus());
        //queryWrapper.ge(Objects.nonNull(query.getCurrentTime()), JdhActivityConfigPo::getWorkStartTime, query.getCurrentTime());
        //queryWrapper.le(Objects.nonNull(angelWorkDBQuery.getServiceStartTimeEnd()), JdhAngelWorkPo::getWorkStartTime, angelWorkDBQuery.getServiceStartTimeEnd())
        //有效
        queryWrapper.eq(JdhActivityConfigPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhActivityConfigPo> list = jdhActivityConfigPoMapper.selectList(queryWrapper);
        return activityPoConvertDelegate.convertPo2ActivityConfig(list);
    }

    /**
     * 查询护士活动列表
     * @param query
     * @return
     */
    @Override
    public List<JdhAbstractAngelActivity> findList(JdhAngelActivityRepQuery query) {
        LambdaQueryWrapper<JdhAngelActivityInstancePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getAngelActivityId()), JdhAngelActivityInstancePo::getAngelActivityId, query.getAngelActivityId());
        queryWrapper.eq(Objects.nonNull(query.getAngelId()), JdhAngelActivityInstancePo::getAngelId, query.getAngelId());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getAngelIdList()), JdhAngelActivityInstancePo::getAngelId, query.getAngelIdList());
        queryWrapper.eq(Objects.nonNull(query.getRewardStatus()), JdhAngelActivityInstancePo::getRewardStatus, query.getRewardStatus());
        if (CollectionUtils.isNotEmpty(query.getAcceptAngelIdList())) {
            queryWrapper.apply("JSON_EXTRACT(activity_rule_progress, '$.acceptAngelId') in (" + Joiner.on(",").join(query.getAcceptAngelIdList()) + ")");
        }
        //有效
        queryWrapper.eq(JdhAngelActivityInstancePo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.ge(Objects.nonNull(query.getCreateStartTime()), JdhAngelActivityInstancePo::getCreateTime, query.getCreateStartTime());
        queryWrapper.lt(Objects.nonNull(query.getCreateEndTime()), JdhAngelActivityInstancePo::getCreateTime, query.getCreateEndTime());
        List<JdhAngelActivityInstancePo> list = jdhAngelActivityInstancePoMapper.selectList(queryWrapper);
        return activityPoConvertDelegate.convertPo2AngelActivity(list);
    }

    /**
     * 查询护士活动列表(分页)
     * @param query
     * @return
     */
    @Override
    public Page<JdhAbstractAngelActivity> findPage(JdhAngelActivityRepQuery query) {
        Page<JdhAngelActivityInstancePo> param = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<JdhAngelActivityInstancePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getAngelActivityId()), JdhAngelActivityInstancePo::getAngelActivityId, query.getAngelActivityId());
        queryWrapper.eq(Objects.nonNull(query.getRewardStatus()), JdhAngelActivityInstancePo::getRewardStatus, query.getRewardStatus());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getAngelIdList()), JdhAngelActivityInstancePo::getAngelId, query.getAngelIdList());
        if (CollectionUtils.isNotEmpty(query.getAcceptAngelIdList())) {
            // 使用经过校验的getJdSafeSql返回值进行后续的操作
            String safeField = getJdSafeSql("acceptAngelId");
            queryWrapper.apply("JSON_EXTRACT(activity_rule_progress, '$." + safeField + "') in (" + Joiner.on(",").join(query.getAcceptAngelIdList()) + ")");
        }
        //有效
        queryWrapper.eq(JdhAngelActivityInstancePo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.ge(Objects.nonNull(query.getCreateStartTime()), JdhAngelActivityInstancePo::getCreateTime, query.getCreateStartTime());
        queryWrapper.lt(Objects.nonNull(query.getCreateEndTime()), JdhAngelActivityInstancePo::getCreateTime, query.getCreateEndTime());
        queryWrapper.orderByDesc(JdhAngelActivityInstancePo::getCreateTime);
        Page<JdhAngelActivityInstancePo> page = jdhAngelActivityInstancePoMapper.selectPage(param, queryWrapper);
        if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
            return null;
        }
        List<JdhAbstractAngelActivity> list = activityPoConvertDelegate.convertPo2AngelActivity(page.getRecords());
        return JdhBasicPoConverter.initPage(page, list);
    }

    public String getJdSafeSql(String userInput) {
        Map<String, String> fieldMap = new HashMap<>();
        fieldMap.put("acceptAngelId", "acceptAngelId");
        // 预定义默认字段
        String defaultField = "acceptAngelId";
        // 从预定义的字段字典中获取相对应的值，若未获取到相对应值则使用默认值
        return fieldMap.getOrDefault(userInput, defaultField);
    }

    /**
     * 查询护士活动列表总数
     * @param query
     * @return
     */
    @Override
    public Integer findCount(JdhAngelActivityRepQuery query) {
        LambdaQueryWrapper<JdhAngelActivityInstancePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getAngelActivityId()), JdhAngelActivityInstancePo::getAngelActivityId, query.getAngelActivityId());
        queryWrapper.eq(Objects.nonNull(query.getRewardStatus()), JdhAngelActivityInstancePo::getRewardStatus, query.getRewardStatus());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getAngelIdList()), JdhAngelActivityInstancePo::getAngelId, query.getAngelIdList());
        if (CollectionUtils.isNotEmpty(query.getAcceptAngelIdList())) {
            queryWrapper.apply("JSON_EXTRACT(activity_rule_progress, '$.acceptAngelId') in (" + Joiner.on(",").join(query.getAcceptAngelIdList()) + ")");
        }
        //有效
        queryWrapper.eq(JdhAngelActivityInstancePo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.ge(Objects.nonNull(query.getCreateStartTime()), JdhAngelActivityInstancePo::getCreateTime, query.getCreateStartTime());
        queryWrapper.lt(Objects.nonNull(query.getCreateEndTime()), JdhAngelActivityInstancePo::getCreateTime, query.getCreateEndTime());
        queryWrapper.orderByDesc(JdhAngelActivityInstancePo::getCreateTime);
        return jdhAngelActivityInstancePoMapper.selectCount(queryWrapper);
    }

    /**
     * 分组查询活动明细数量
     * @param query
     * @return
     */
    @Override
    public List<ActivityDetailGroupCount> findActivityDetailGroupCount(ActivityDetailGroupCountQuery query) {
        QueryWrapper<JdhAngelActivityInstancePo> wrapper = new QueryWrapper<>();
        wrapper.select(String.format("%s, count(*) as count", query.getColumn()))
                .groupBy(query.getColumn());

        LambdaQueryWrapper<JdhAngelActivityInstancePo> queryWrapper = wrapper.lambda();
        queryWrapper.eq(Objects.nonNull(query.getActivityConfigId()),JdhAngelActivityInstancePo::getActivityConfigId, query.getActivityConfigId())
                .eq(Objects.nonNull(query.getActivityConfigType()),JdhAngelActivityInstancePo::getActivityConfigType,query.getActivityConfigType())
                .eq(Objects.nonNull(query.getAngelId()),JdhAngelActivityInstancePo::getAngelId,query.getAngelId())
                .eq(Objects.nonNull(query.getRewardStatus()),JdhAngelActivityInstancePo::getRewardStatus,query.getRewardStatus())
                .ge(Objects.nonNull(query.getCreateStartTime()), JdhAngelActivityInstancePo::getCreateTime, query.getCreateStartTime())
                .lt(Objects.nonNull(query.getCreateEndTime()), JdhAngelActivityInstancePo::getCreateTime, query.getCreateEndTime())
                .eq(JdhAngelActivityInstancePo::getYn, YnStatusEnum.YES.getCode());

        List<Map<String, Object>> maps = jdhAngelActivityInstancePoMapper.selectMaps(queryWrapper);
        List<ActivityDetailGroupCount> result = new ArrayList<>();
        for (Map<String, Object> objectMap : maps) {
            Object o = objectMap.get(query.getColumn());
            if (Objects.nonNull(o)) {
                result.add(ActivityDetailGroupCount.builder().groupKeyValue(o).count((Long)objectMap.getOrDefault("count", 0L)).build());
            }
        }
        return result;
    }
}