package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

/**
 * 虚拟号发送短信记录表
 */
@Data
@TableName(value = "jdh_virtual_number_sms_record", autoResultMap = true)
public class JdhVirtualNumberSmsRecordPo extends JdhBasicTimePo {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户pin
     */
    @TableField(value = "user_pin")
    private String userPin;

    /**
     * 服务者pin
     */
    @TableField(value = "angel_pin")
    private String angelPin;

    /**
     * 履约单ID
     */
    @TableField(value = "promise_id")
    private Long promiseId;

    /**
     * 服务兑换的来源id，兑换id/orderId/outerOrderId
     */
    @TableField(value = "source_voucher_id")
    private String sourceVoucherId;

    /**
     * 短信唯一标识
     */
    @TableField(value = "sms_id")
    private String smsId;

    /**
     * 绑定关系ID
     */
    @TableField(value = "bind_id")
    private String bindId;

    /**
     * 真实发送方号码
     */
    @TableField(value = "call_no")
    private String callNo;

    /**
     * 真实接收方号码
     */
    @TableField(value = "peer_no")
    private String peerNo;

    /**
     * 分机号
     */
    @TableField(value = "extension_no")
    private String extensionNo;

    /**
     * 发送方显示号码
     */
    @TableField(value = "sender_show")
    private String senderShow;

    /**
     * 接收方显示号码
     */
    @TableField(value = "receiver_show")
    private String receiverShow;

    /**
     * 短信发送时间
     */
    @TableField(value = "sms_time")
    private Date smsTime;

    /**
     * 实际转发短信条数：140字节或70汉字为1条短信，长短信会拆分为多条下发
     */
    @TableField(value = "sms_number")
    private String smsNumber;

    /**
     * 发送结果：0：成功，1：失败
     */
    @TableField(value = "sms_result")
    private Integer smsResult;

    /**
     * 用户发送的短信内容，仅在短信托收模式下提供
     */
    @TableField(value = "sms_content")
    private String smsContent;

    /**
     * 用户附属信息
     */
    @TableField(value = "user_data")
    private String userData;

    /**
     * 扩展数据
     */
    @TableField(value = "ext_json")
    private String extJson;

    /**
     * 是否有效 1有效 0无效
     */
    @TableField(value = "yn")
    private Integer yn;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

}