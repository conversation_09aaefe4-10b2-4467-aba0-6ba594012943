package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.constatnt.CacheConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.model.Pagination;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherExtend;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.VoucherRepPageQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.VoucherRepQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhVoucherInfrastructureConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhVoucherItemPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhVoucherPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelProfessionRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhVoucherItemPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhVoucherPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VoucherRepositoryImpl implements VoucherRepository {

    /**
     * jdhVoucherPoMapper
     */
    @Resource
    private JdhVoucherPoMapper jdhVoucherPoMapper;

    /**
     * jdhPromisePoMapper
     */
    @Resource
    private JdhPromisePoMapper jdhPromisePoMapper;

    /**
     * jdhVoucherItemPoMapper
     */
    @Resource
    private JdhVoucherItemPoMapper jdhVoucherItemPoMapper;

    /**
     * jdhBasicPoConverter
     */
    @Resource
    private JdhBasicPoConverter jdhBasicPoConverter;

    /**
     * 当前配置
     */
    private static String ACTIVE;

    /**
     * generateIdFactory
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        ACTIVE = value;
    }
    /**
     * 删除
     *
     * @param aggregate
     */
    @Override
    public int remove(JdhVoucher aggregate) {
        log.info("VoucherRepositoryImpl -> remove aggregate:{}", JSON.toJSONString(aggregate));
        LambdaUpdateWrapper<JdhVoucherPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("`version` = `version` + 1").set(JdhVoucherPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhVoucherPo::getVoucherId, aggregate.getVoucherId());
        return jdhVoucherPoMapper.update(null, updateWrapper);
    }

    /**
     * 保存
     *
     * @param jdhVoucher
     */
    @Override
    @CacheEvict(cacheNames = CacheConstant.PROMISE_CACHE, key = "#jdhVoucher.getIdentifier().serialize()",condition = "#jdhVoucher.getId() != null")
    public int save(JdhVoucher jdhVoucher) {
        JdhVoucherPo jdhVoucherPo = JdhVoucherInfrastructureConverter.ins.entity2Po(jdhVoucher);
        log.info("VoucherRepositoryImpl -> save jdhVoucherPo:{}", JSON.toJSONString(jdhVoucherPo));
        //insert
        if(Objects.isNull(jdhVoucherPo.getId())){
            log.info("VoucherRepositoryImpl -> save 执行插入逻辑");
            JdhBasicPoConverter.initInsertBasicPo(jdhVoucherPo);
            log.info("VoucherRepositoryImpl -> save 赋值后 jdhVoucherPo:{}", JSON.toJSONString(jdhVoucherPo));
            int insert = jdhVoucherPoMapper.insert(jdhVoucherPo);
            List<JdhVoucherItemPo> jdhVoucherItemPoList = JdhVoucherInfrastructureConverter.ins.entity2PoList(jdhVoucher.getVoucherItemList());
            for (JdhVoucherItemPo jdhVoucherItemPo : jdhVoucherItemPoList) {
                JdhBasicPoConverter.initInsertBasicPo(jdhVoucherItemPo);
                log.info("VoucherRepositoryImpl -> save 赋值后 jdhVoucherItemPo:{}", JSON.toJSONString(jdhVoucherItemPo));
                jdhVoucherItemPoMapper.insert(jdhVoucherItemPo);
            }
            return insert;
        //update
        }else{
            log.info("VoucherRepositoryImpl -> save 执行更新逻辑");
            jdhVoucher.versionIncrease();
            LambdaUpdateWrapper<JdhVoucherPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(JdhVoucherPo::getVersion, jdhVoucher.getVersion())
                    .set(JdhVoucherPo::getStatus,jdhVoucherPo.getStatus())
                    .set(JdhVoucherPo::getFreeze,jdhVoucherPo.getFreeze())
                    .set(JdhVoucherPo::getExpireDate,jdhVoucherPo.getExpireDate())
                    .set(JdhVoucherPo::getUpdateTime, new Date())
                    .set(jdhVoucher.getPromiseNum() != null, JdhVoucherPo::getPromiseNum, jdhVoucher.getPromiseNum())
                    .eq(JdhVoucherPo::getVoucherId,jdhVoucherPo.getVoucherId())
                    .eq(JdhVoucherPo::getVersion,jdhVoucherPo.getVersion());

            return jdhVoucherPoMapper.update(null,updateWrapper);
        }
    }

    /**
     * 通过Identify 查询
     *
     * @param identifier
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.PROMISE_CACHE,key = "#identifier.serialize()")
    public JdhVoucher find(JdhVoucherIdentifier identifier) {
        LambdaQueryWrapper<JdhVoucherPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhVoucherPo::getVoucherId,identifier.getVoucherId());
        queryWrapper.eq(JdhVoucherPo::getYn,YnStatusEnum.YES.getCode());
        JdhVoucherPo jdhVoucherPo = jdhVoucherPoMapper.selectOne(queryWrapper);
        if(Objects.isNull(jdhVoucherPo)){
            return null;
        }
        log.info("jdhVoucherPo={}", JSON.toJSONString(jdhVoucherPo));
        if (jdhVoucherPo.getExtend() != null) {
            log.info("jdhVoucherPo.getExtend()={}", jdhVoucherPo.getExtend());
            try {
                log.info("extend={}", JSON.parseObject(jdhVoucherPo.getExtend(), JdhVoucherExtend.class));
            } catch (Exception e) {
                log.error(e.getMessage());
                log.error("error", e);
            }
        }
        JdhVoucher jdhVoucher = JdhVoucherInfrastructureConverter.ins.po2Entity(jdhVoucherPo);
        fetchVoucherItem(jdhVoucher);

        return jdhVoucher;
    }

    /**
     * fetchVoucherItem
     *
     * @param voucher voucher
     */
    private void fetchVoucherItem(JdhVoucher voucher){
        if (!Boolean.FALSE.equals(voucher.getVoucherItem())) {
            LambdaQueryWrapper<JdhVoucherItemPo> itemQueryWrapper = Wrappers.lambdaQuery();
            itemQueryWrapper.eq(JdhVoucherItemPo::getVoucherId,voucher.getVoucherId());
            itemQueryWrapper.eq(JdhVoucherItemPo::getYn,YnStatusEnum.YES.getCode());
            List<JdhVoucherItemPo> jdhVoucherItemPoList = jdhVoucherItemPoMapper.selectList(itemQueryWrapper);
            voucher.setVoucherItemList(JdhVoucherInfrastructureConverter.ins.itemPo2EntityList(jdhVoucherItemPoList));
        }
    }


    /**
     * queryJdhVoucher
     *
     * @param jdhVoucherQuery jdhVoucherQuery
     * @return {@link List}<{@link JdhVoucher}>
     */
    @Override
    public List<JdhVoucher> listByQuery(VoucherRepQuery jdhVoucherQuery) {
        LambdaQueryWrapper<JdhVoucherPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StrUtil.isNotEmpty(jdhVoucherQuery.getSourceVoucherId()),JdhVoucherPo::getSourceVoucherId,jdhVoucherQuery.getSourceVoucherId())
                .in(CollUtil.isNotEmpty(jdhVoucherQuery.getSourceVoucherIds()),JdhVoucherPo::getSourceVoucherId,jdhVoucherQuery.getSourceVoucherIds())
                .in(CollUtil.isNotEmpty(jdhVoucherQuery.getVoucherIds()),JdhVoucherPo::getVoucherId,jdhVoucherQuery.getVoucherIds())
                .eq(Objects.nonNull(jdhVoucherQuery.getSourceType()),JdhVoucherPo::getSourceType,jdhVoucherQuery.getSourceType())
                .eq(StrUtil.isNotEmpty(jdhVoucherQuery.getVerticalCode()),JdhVoucherPo::getVerticalCode,jdhVoucherQuery.getVerticalCode())
                .eq(StrUtil.isNotEmpty(jdhVoucherQuery.getServiceType()),JdhVoucherPo::getServiceType,jdhVoucherQuery.getServiceType())
                .notIn(CollUtil.isNotEmpty(jdhVoucherQuery.getStatusNotInList()),JdhVoucherPo::getStatus,jdhVoucherQuery.getStatusNotInList())
                .gt(Objects.nonNull(jdhVoucherQuery.getExpireDateGt()),JdhVoucherPo::getExpireDate,jdhVoucherQuery.getExpireDateGt())
                .eq(Objects.nonNull(jdhVoucherQuery.getExpireDate()),JdhVoucherPo::getExpireDate,jdhVoucherQuery.getExpireDate())
                .eq(JdhVoucherPo::getYn,YnStatusEnum.YES.getCode());

        List<JdhVoucherPo> jdhVoucherPos = jdhVoucherPoMapper.selectList(queryWrapper);

        //查到voucher信息，并且要过滤履约单
        if(CollUtil.isNotEmpty(jdhVoucherPos) && Objects.nonNull(jdhVoucherQuery.getFilterPromise()) && jdhVoucherQuery.getFilterPromise()){
            List<Long> voucherIdList = jdhVoucherPos.stream().map(JdhVoucherPo::getVoucherId).collect(Collectors.toList());
            //过滤promise
            LambdaQueryWrapper<JdhPromisePo> promiseQueryWrapper = Wrappers.lambdaQuery();
            promiseQueryWrapper.in(JdhPromisePo::getVoucherId,voucherIdList)
                    .eq(StrUtil.isNotEmpty(jdhVoucherQuery.getCode()),JdhPromisePo::getCode,jdhVoucherQuery.getCode())
                    .eq(StrUtil.isNotEmpty(jdhVoucherQuery.getCodeId()),JdhPromisePo::getCodeId,jdhVoucherQuery.getCodeId())
                    .eq(StrUtil.isNotEmpty(jdhVoucherQuery.getCodePwd()),JdhPromisePo::getCodePwd,jdhVoucherQuery.getCodePwd())
                    .eq(JdhPromisePo::getYn,YnStatusEnum.YES.getCode());

            List<JdhPromisePo> jdhPromisePos = jdhPromisePoMapper.selectList(promiseQueryWrapper);
            List<Long> promiseVoucherIdList = jdhPromisePos.stream().map(JdhPromisePo::getVoucherId).collect(Collectors.toList());

            //根据查询到的履约单对应的服务单，过滤前面条件查出来的服务单
            jdhVoucherPos.removeIf(next -> !promiseVoucherIdList.contains(next.getVoucherId()));
        }

        List<JdhVoucher> vouchers = JdhVoucherInfrastructureConverter.ins.po2EntityList(jdhVoucherPos);
        if(CollUtil.isNotEmpty(vouchers)){
            for (JdhVoucher voucher : vouchers) {
                fetchVoucherItem(voucher);
            }
            return vouchers;
        }
        return null;
    }

    /**
     * listByVoucherIds
     *
     * @param voucherIds voucherIds
     * @return {@link List}<{@link JdhVoucher}>
     */
    @Override
    public List<JdhVoucher> listByVoucherIds(List<Long> voucherIds) {
        return this.listByQuery(VoucherRepQuery.builder().voucherIds(voucherIds).build());
    }

    /**
     * listBySourceVoucherId
     *
     * @param sourceVoucherId sourceVoucherId
     * @param verticalCode    verticalCode
     * @return {@link List}<{@link JdhVoucher}>
     */
    @Override
    public List<JdhVoucher> listBySourceVoucherId(List<String> sourceVoucherId, String verticalCode) {
        VoucherRepQuery voucherRepQuery = VoucherRepQuery.builder()
                .sourceVoucherIds(sourceVoucherId)
                .build();
        voucherRepQuery.setVerticalCode(verticalCode);

        return this.listByQuery(voucherRepQuery);
    }

    /**
     * findPageList
     *
     * @param query 查询
     * @return {@link Pagination}<{@link JdhVoucher}>
     */
    @Override
    public Page<JdhVoucher> findPageList(VoucherRepPageQuery query) {
        QueryWrapper<JdhVoucherPo> queryWrapper=new QueryWrapper<>();
        if(CollectionUtils.isNotEmpty(query.getGroupFields())){
            query.getGroupFields().forEach(queryWrapper::groupBy);
        }
        LambdaQueryWrapper<JdhVoucherPo> lambdaQueryWrapper = queryWrapper.lambda();

        lambdaQueryWrapper.eq(StrUtil.isNotEmpty(query.getSourceVoucherId()),JdhVoucherPo::getSourceVoucherId,query.getSourceVoucherId())
                .notIn(CollUtil.isNotEmpty(query.getStatusNotInList()),JdhVoucherPo::getStatus,query.getStatusNotInList())
                .gt(Objects.nonNull(query.getExpireDateGt()),JdhVoucherPo::getExpireDate,query.getExpireDateGt())
                .ge(Objects.nonNull(query.getExpireDateGe()),JdhVoucherPo::getExpireDate,query.getExpireDateGe())
                .lt(Objects.nonNull(query.getExpireDateLt()),JdhVoucherPo::getExpireDate,query.getExpireDateLt())
                .eq(Objects.nonNull(query.getVoucherId()),JdhVoucherPo::getVoucherId,query.getVoucherId())
                .eq(Objects.nonNull(query.getVerticalCode()),JdhVoucherPo::getVerticalCode,query.getVerticalCode())
                .eq(Objects.nonNull(query.getExpireDate()),JdhVoucherPo::getExpireDate,query.getExpireDate());


        Page<JdhVoucherPo> param = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<JdhVoucherPo> jdhVoucherPos = jdhVoucherPoMapper.selectPage(param, queryWrapper);
        List<JdhVoucher> jdhVoucherList = JdhVoucherInfrastructureConverter.ins.po2EntityList(jdhVoucherPos.getRecords());
        if(CollUtil.isNotEmpty(jdhVoucherList)){
            for (JdhVoucher jdhVoucher : jdhVoucherList) {
                fetchVoucherItem(jdhVoucher);
            }
        }
        return JdhBasicPoConverter.initPage(jdhVoucherPos, jdhVoucherList);
    }

    /**
     * 刷数据需要,根据订单号删除数据
     * @param orderIds
     * @return
     */
    @Override
    public Integer removeByOrderId(List<Long> orderIds) {

        List<String> param = Lists.newArrayList();
        for (Long orderId : orderIds) {
            param.add(String.valueOf(orderId));
        }
        LambdaQueryWrapper<JdhVoucherPo> deleteWrapper = new LambdaQueryWrapper<JdhVoucherPo>();
        deleteWrapper.in(JdhVoucherPo::getUpdateUser, param);
        int count = jdhVoucherPoMapper.delete(deleteWrapper);

        LambdaQueryWrapper<JdhVoucherItemPo> deleteItemWrapper = new LambdaQueryWrapper<JdhVoucherItemPo>();
        deleteItemWrapper.in(JdhVoucherItemPo::getUpdateUser, param);
        jdhVoucherItemPoMapper.delete(deleteItemWrapper);
        return count;
    }

    /**
     * 根据原单号来新增或者更新voucher信息，返回voucherId
     * voucherItem只做新增不做更新
     * @param jdhVoucher
     * @return
     */
    @Override
    public Long insertOrUpdateBySourceVoucherId(JdhVoucher jdhVoucher) {
        JdhVoucherPo jdhVoucherPo = JdhVoucherInfrastructureConverter.ins.entity2Po(jdhVoucher);
        log.info("VoucherRepositoryImpl -> insertOrUpdateBySourceVoucherId jdhVoucherPo:{}", JSON.toJSONString(jdhVoucherPo));
        LambdaQueryWrapper<JdhVoucherPo> oneWrapper = new LambdaQueryWrapper<JdhVoucherPo>();
        oneWrapper.in(JdhVoucherPo::getSourceVoucherId, jdhVoucherPo.getSourceVoucherId());
        JdhVoucherPo jdhVoucherPoFromDb = jdhVoucherPoMapper.selectOne(oneWrapper);
        //insert
        if(Objects.isNull(jdhVoucherPoFromDb)){
            log.info("VoucherRepositoryImpl -> insertOrUpdateBySourceVoucherId 执行插入逻辑");
            jdhVoucherPo.setVoucherId(generateIdFactory.getId());
            log.info("VoucherRepositoryImpl -> insertOrUpdateBySourceVoucherId 赋值后 jdhVoucherPo:{}", JSON.toJSONString(jdhVoucherPo));
            jdhVoucherPoMapper.insert(jdhVoucherPo);
            List<JdhVoucherItemPo> jdhVoucherItemPoList = JdhVoucherInfrastructureConverter.ins.entity2PoList(jdhVoucher.getVoucherItemList());
            for (JdhVoucherItemPo jdhVoucherItemPo : jdhVoucherItemPoList) {
                jdhVoucherItemPo.setVoucherId(jdhVoucherPo.getVoucherId());
                log.info("VoucherRepositoryImpl -> insertOrUpdateBySourceVoucherId 赋值后 jdhVoucherItemPo:{}", JSON.toJSONString(jdhVoucherItemPo));
                jdhVoucherItemPoMapper.insert(jdhVoucherItemPo);
            }
            return jdhVoucherPo.getVoucherId();
        //update
        }else{
            log.info("VoucherRepositoryImpl -> insertOrUpdateBySourceVoucherId 执行更新逻辑");
            LambdaUpdateWrapper<JdhVoucherPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.setSql("`version` = `version` + 1").eq(JdhVoucherPo::getSourceVoucherId, jdhVoucherPo.getSourceVoucherId());
            jdhVoucherPo.setVoucherId(jdhVoucherPoFromDb.getVoucherId());
            jdhVoucherPo.setUpdateTime(new Date());
            jdhVoucherPoMapper.update(jdhVoucherPo, updateWrapper);
            return jdhVoucherPoFromDb.getVoucherId();
        }
    }

}
