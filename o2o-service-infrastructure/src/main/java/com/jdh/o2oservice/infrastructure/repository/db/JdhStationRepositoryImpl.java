package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.model.JdhStationIdentifier;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelStationPageQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.StationDbQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhMapPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhStationPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelStationPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelStationPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:JdhStationRepositoryImpl
 * @Description: 服务站仓储实现
 * @Author: yaoqinghai
 * @Date: 2024/4/22 23:52
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class JdhStationRepositoryImpl implements JdhStationRepository {

    @Resource
    private JdhAngelStationPoMapper jdhStationPoMapper;

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param jdhStationIdentifier
     */
    @Override
    public JdhStation find(JdhStationIdentifier jdhStationIdentifier) {
        LambdaQueryWrapper<JdhAngelStationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelStationPo::getAngelStationId, jdhStationIdentifier.getAngelStationId())
                .eq(JdhAngelStationPo::getYn, YnStatusEnum.YES.getCode());
        return JdhMapPoConverter.ins.convertToJdhStation(jdhStationPoMapper.selectOne(queryWrapper));
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param aggregate
     */
    @Override
    public int remove(JdhStation aggregate) {
        LambdaUpdateWrapper<JdhAngelStationPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.setSql("`version` = `version` + 1")
                .eq(JdhAngelStationPo::getAngelStationId, aggregate.getAngelStationId())
                .eq(JdhAngelStationPo::getYn, YnStatusEnum.YES.getCode())
                .set(JdhAngelStationPo::getYn, YnStatusEnum.NO.getCode());
        return jdhStationPoMapper.update(null, updateWrapper);
    }

    /**
     * 保存一个Aggregate
     * 保存后自动重置追踪条件
     *
     * @param jdhStation
     */
    @Override
    @Transactional
    public int save(JdhStation jdhStation) {
        if(Objects.isNull(jdhStation.getId())){
            JdhAngelStationPo jdhAngelStationPo = JdhStationPoConverter.ins.convertToJdhAngelStationPo(jdhStation);
            return jdhStationPoMapper.insert(jdhAngelStationPo);
        }else {
            LambdaUpdateWrapper<JdhAngelStationPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(StringUtils.isNotBlank(jdhStation.getAngelStationName()), JdhAngelStationPo::getAngelStationName, jdhStation.getAngelStationName())
                    .set(Objects.nonNull(jdhStation.getMapId()), JdhAngelStationPo::getMapId, jdhStation.getMapId())
                    .set(Objects.nonNull(jdhStation.getLayerId()), JdhAngelStationPo::getLayerId, jdhStation.getLayerId())
                    .set(StringUtils.isNotBlank(jdhStation.getFenceId()), JdhAngelStationPo::getFenceId, jdhStation.getFenceId())
                    .set(Objects.nonNull(jdhStation.getFenceRangeType()), JdhAngelStationPo::getFenceRangeType, jdhStation.getFenceRangeType())
                    .set(JdhAngelStationPo::getFenceRangeRadius, jdhStation.getFenceRangeRadius())
                    .set(StringUtils.isNotBlank(jdhStation.getFenceRangeCenterLat()), JdhAngelStationPo::getFenceRangeCenterLat, jdhStation.getFenceRangeCenterLat())
                    .set(StringUtils.isNotBlank(jdhStation.getFenceRangeCenterLng()), JdhAngelStationPo::getFenceRangeCenterLng, jdhStation.getFenceRangeCenterLng())
                    .set(StringUtils.isNotBlank(jdhStation.getFenceBoundaryList()), JdhAngelStationPo::getFenceBoundaryList, jdhStation.getFenceBoundaryList())
                    .set(StringUtils.isNotBlank(jdhStation.getStationMaster()), JdhAngelStationPo::getStationMaster, jdhStation.getStationMaster())
                    // todo 注意：调用服务站更新方法时，多级地址传入时不进行空值判断
                    //  空值会写入数据库中，保证每次更新的数据为最新
                    .set(JdhAngelStationPo::getProvinceCode, jdhStation.getProvinceCode())
                    .set(JdhAngelStationPo::getProvinceName, jdhStation.getProvinceName())
                    .set(JdhAngelStationPo::getCityCode, jdhStation.getCityCode())
                    .set(JdhAngelStationPo::getCityName, jdhStation.getCityName())
                    .set(JdhAngelStationPo::getDistrictCode, jdhStation.getDistrictCode())
                    .set(JdhAngelStationPo::getDistrictName, jdhStation.getDistrictName())
                    .set(JdhAngelStationPo::getCountyCode, jdhStation.getCountyCode())
                    .set(JdhAngelStationPo::getCountyName, jdhStation.getCountyName())
                    .set(JdhAngelStationPo::getAddressDetail, jdhStation.getAddressDetail())

                    .set(StringUtils.isNotBlank(jdhStation.getFullAddress()), JdhAngelStationPo::getFullAddress, jdhStation.getFullAddress())
                    .set(Objects.nonNull(jdhStation.getStationStatus()), JdhAngelStationPo::getStationStatus, jdhStation.getStationStatus())
                    .set(JdhAngelStationPo::getOpenHour, jdhStation.getOpenHour())
                    .set(JdhAngelStationPo::getCloseHour, jdhStation.getCloseHour())
                    .set(Objects.nonNull(jdhStation.getStationStatus()), JdhAngelStationPo::getStationStatus, jdhStation.getStationStatus())
                    .set(StringUtils.isNotBlank(jdhStation.getUpdateUser()), JdhAngelStationPo::getUpdateUser, jdhStation.getUpdateUser())
                    .set(Objects.nonNull(jdhStation.getAngelType()), JdhAngelStationPo::getAngelType, jdhStation.getAngelType())
                    .set(JdhAngelStationPo::getDeliverySupplier, jdhStation.getDeliverySupplier())
                    .set(JdhAngelStationPo::getStationId, jdhStation.getStationId())
                    .set(JdhAngelStationPo::getStationName, jdhStation.getStationName())
                    .set(JdhAngelStationPo::getAngelNum, jdhStation.getAngelNum())
                    .set(JdhAngelStationPo::getNurseNum, jdhStation.getNurseNum())
                    .set(jdhStation.getFenceShapeType()!=null, JdhAngelStationPo::getFenceShapeType, jdhStation.getFenceShapeType())
                    .set(jdhStation.getJdTransferStationId()!=null, JdhAngelStationPo::getJdTransferStationId, jdhStation.getJdTransferStationId())
                    .setSql("`update_time` = now()")
                    .setSql("`version` = version+1")
                    .eq(JdhAngelStationPo::getAngelStationId, jdhStation.getAngelStationId())
                    .eq(JdhAngelStationPo::getVersion, jdhStation.getVersion());

            return jdhStationPoMapper.update(null, updateWrapper);
        }
    }

    /**
     * 查询服务站列表
     *
     * @param stationDbQuery
     * @return
     */
    @Override
    public List<JdhStation> findList(StationDbQuery stationDbQuery) {
        LambdaQueryWrapper<JdhAngelStationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtils.isNotEmpty(stationDbQuery.getStationIds()), JdhAngelStationPo::getAngelStationId, stationDbQuery.getStationIds())
                .in(CollectionUtils.isNotEmpty(stationDbQuery.getFenceIds()), JdhAngelStationPo::getFenceId, stationDbQuery.getFenceIds())
                .in(CollectionUtils.isNotEmpty(stationDbQuery.getStationIdList()), JdhAngelStationPo::getStationId, stationDbQuery.getStationIdList())
                .like(StringUtils.isNotBlank(stationDbQuery.getStationName()), JdhAngelStationPo::getAngelStationName, stationDbQuery.getStationName())
                .in(CollectionUtils.isNotEmpty(stationDbQuery.getStationMasters()), JdhAngelStationPo::getStationMaster, stationDbQuery.getStationMasters())
                .in(CollectionUtils.isNotEmpty(stationDbQuery.getLayerIds()), JdhAngelStationPo::getLayerId, stationDbQuery.getLayerIds())
                //以下or是为了兼容线上旧数据(angelType为空)
                .and(CollectionUtils.isNotEmpty(stationDbQuery.getAngelTypes()),t->t.isNull(JdhAngelStationPo::getAngelType).or().in(JdhAngelStationPo::getAngelType, stationDbQuery.getAngelTypes()))
                .eq(Objects.nonNull(stationDbQuery.getStationModeType()), JdhAngelStationPo::getStationModeType, stationDbQuery.getStationModeType())
                .eq(Objects.nonNull(stationDbQuery.getProvinceCode()), JdhAngelStationPo::getProvinceCode, stationDbQuery.getProvinceCode())
                .eq(Objects.nonNull(stationDbQuery.getCityCode()), JdhAngelStationPo::getCityCode, stationDbQuery.getCityCode())
                .eq(Objects.nonNull(stationDbQuery.getJdTransferStationId()), JdhAngelStationPo::getJdTransferStationId, stationDbQuery.getJdTransferStationId())
                .in(CollectionUtils.isNotEmpty(stationDbQuery.getJdTransferStationIds()), JdhAngelStationPo::getJdTransferStationId, stationDbQuery.getJdTransferStationIds())
                .eq(Objects.nonNull(stationDbQuery.getAngelStationStatus()), JdhAngelStationPo::getStationStatus, stationDbQuery.getAngelStationStatus())
                .in(CollectionUtils.isNotEmpty(stationDbQuery.getProvinceCodeList()), JdhAngelStationPo::getProvinceCode, stationDbQuery.getProvinceCodeList())
                .notIn(CollectionUtils.isNotEmpty(stationDbQuery.getNotProvinceCodeList()), JdhAngelStationPo::getProvinceCode, stationDbQuery.getNotProvinceCodeList())
                .eq(JdhAngelStationPo::getYn, YnStatusEnum.YES.getCode());
        return JdhStationPoConverter.ins.convertToJdhStationList(jdhStationPoMapper.selectList(queryWrapper));
    }

    /**
     * 分页查询服务站列表
     *
     * @param angelStationPageQuery
     */
    @Override
    @LogAndAlarm
    public Page<JdhStation> findPageList(AngelStationPageQuery angelStationPageQuery) {
        IPage<JdhAngelStationPo> iPage = new Page<>(angelStationPageQuery.getPageNum(), angelStationPageQuery.getPageSize());
        LambdaQueryWrapper<JdhAngelStationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(angelStationPageQuery.getAngelStationId()), JdhAngelStationPo::getAngelStationId, angelStationPageQuery.getAngelStationId())
                .in(CollectionUtils.isNotEmpty(angelStationPageQuery.getAngelStationIdList()), JdhAngelStationPo::getAngelStationId, angelStationPageQuery.getAngelStationIdList())
                .like(StringUtils.isNotBlank(angelStationPageQuery.getAngelStationName()), JdhAngelStationPo::getAngelStationName, angelStationPageQuery.getAngelStationName())
                .eq(StringUtils.isNotBlank(angelStationPageQuery.getFenceId()), JdhAngelStationPo::getFenceId, angelStationPageQuery.getFenceId())
                .eq(StringUtils.isNotBlank(angelStationPageQuery.getProvinceCode()), JdhAngelStationPo::getProvinceCode, angelStationPageQuery.getProvinceCode())
                .eq(StringUtils.isNotBlank(angelStationPageQuery.getCityCode()), JdhAngelStationPo::getCityCode, angelStationPageQuery.getCityCode())
                .eq(StringUtils.isNotBlank(angelStationPageQuery.getDistrictCode()), JdhAngelStationPo::getDistrictCode, angelStationPageQuery.getDistrictCode())
                .eq(Objects.nonNull(angelStationPageQuery.getAngelStationStatus()), JdhAngelStationPo::getStationStatus, angelStationPageQuery.getAngelStationStatus())
                .like(StringUtils.isNotBlank(angelStationPageQuery.getAngelStationMaster()), JdhAngelStationPo::getStationMaster, angelStationPageQuery.getAngelStationMaster())
                .eq(Objects.nonNull(angelStationPageQuery.getStationModeType()), JdhAngelStationPo::getStationModeType, angelStationPageQuery.getStationModeType())
                .eq(JdhAngelStationPo::getYn, YnStatusEnum.YES.getCode())
                .eq(StringUtils.isNotBlank(angelStationPageQuery.getStationId()),JdhAngelStationPo::getStationId, angelStationPageQuery.getStationId())
                .ge(Objects.nonNull(angelStationPageQuery.getQueryUpdateTimeStart()),JdhAngelStationPo::getUpdateTime, angelStationPageQuery.getQueryUpdateTimeStart())
                .lt(Objects.nonNull(angelStationPageQuery.getQueryUpdateTimeEnd()),JdhAngelStationPo::getUpdateTime, angelStationPageQuery.getQueryUpdateTimeEnd())
                .eq(Objects.nonNull(angelStationPageQuery.getStationStatus()),JdhAngelStationPo::getStationStatus, angelStationPageQuery.getStationStatus())
                .in(CollectionUtils.isNotEmpty(angelStationPageQuery.getAngelTypeSet()),JdhAngelStationPo::getAngelType, angelStationPageQuery.getAngelTypeSet())
                .orderByDesc(JdhAngelStationPo::getCreateTime);

        IPage<JdhAngelStationPo> jdhAngelStationPoIPage = jdhStationPoMapper.selectPage(iPage, queryWrapper);
        if (Objects.isNull(jdhAngelStationPoIPage) || CollectionUtils.isEmpty(jdhAngelStationPoIPage.getRecords())) {
            return null;
        }
        List<JdhStation> stationList = JdhStationPoConverter.ins.convertToJdhStationList(jdhAngelStationPoIPage.getRecords());
        return JdhBasicPoConverter.initPage(jdhAngelStationPoIPage, stationList);
    }

    /**
     * 查询没有绑定站长的服务站信息
     *
     * @return
     */
    @Override
    public List<JdhStation> findNoMasterList() {
        LambdaQueryWrapper<JdhAngelStationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.isNull(JdhAngelStationPo::getStationMaster)
                .eq(JdhAngelStationPo::getYn, YnStatusEnum.YES.getCode());
        return JdhStationPoConverter.ins.convertToJdhStationList(jdhStationPoMapper.selectList(queryWrapper));
    }

    /**
     * 根据名称精确查询
     *
     * @param stationDbQuery
     * @return
     */
    @Override
    public List<JdhStation> findAccurateList(StationDbQuery stationDbQuery) {
        LambdaQueryWrapper<JdhAngelStationPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelStationPo::getAngelStationName, stationDbQuery.getStationName())
                .notIn(CollectionUtils.isNotEmpty(stationDbQuery.getNotInStationIds()), JdhAngelStationPo::getAngelStationId, stationDbQuery.getNotInStationIds())
                .eq(JdhAngelStationPo::getYn, YnStatusEnum.YES.getCode());
        return JdhStationPoConverter.ins.convertToJdhStationList(jdhStationPoMapper.selectList(queryWrapper));
    }

    /**
     * 修改服务站站长
     *
     * @param jdhStation
     */
    @Override
    public int updateStation(JdhStation jdhStation) {
        LambdaUpdateWrapper<JdhAngelStationPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StringUtils.isNotBlank(jdhStation.getStationMaster()), JdhAngelStationPo::getStationMaster, jdhStation.getStationMaster())
                .set(Objects.nonNull(jdhStation.getStationStatus()), JdhAngelStationPo::getStationStatus, jdhStation.getStationStatus())
                .set(Objects.nonNull(jdhStation.getOutShopId()), JdhAngelStationPo::getOutShopId, jdhStation.getOutShopId())
                .set(Objects.nonNull(jdhStation.getJdTransferStationId()), JdhAngelStationPo::getJdTransferStationId, jdhStation.getJdTransferStationId())
                .setSql("`update_time` = now()")
                .eq(JdhAngelStationPo::getAngelStationId, jdhStation.getAngelStationId());

        return jdhStationPoMapper.update(null, updateWrapper);
    }
}
