package com.jdh.o2oservice.infrastructure.rpc.adapter;

import com.jd.trade2.base.export.model.adapter.AdapterLoader;
import com.jd.trade2.base.export.model.adapter.AdapterManagerImpl;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * AdapterManager 初始化执行中台的解析起
 * <AUTHOR>
 * @version 2024/3/14 17:14
 **/
@Component
public class AdapterManager implements InitializingBean {

    /**
     * AdapterManager
     */
    public static AdapterManagerImpl ADAPTER_MANAGER = null;

    /**
     * 初始化
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        AdapterLoader adapterLoader = new AdapterLoader();
        // 应用启动加载一次即可
        adapterLoader.load();
    }
}
