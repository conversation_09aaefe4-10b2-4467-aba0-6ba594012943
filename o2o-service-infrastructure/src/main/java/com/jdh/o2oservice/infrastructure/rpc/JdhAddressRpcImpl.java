package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.b2c.export.api.address.AddressInfoExport;
import com.jdh.b2c.export.base.ApiResult;
import com.jdh.b2c.export.base.ClientInfo;
import com.jdh.b2c.export.dto.address.AddressInfoDTO;
import com.jdh.b2c.export.param.address.AddressQueryParam;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.TdeClientUtil;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: yangxiyu
 * @date: 2023/11/27 3:34 下午
 * @version: 1.0
 */
@Service
@Slf4j
public class JdhAddressRpcImpl implements JdhAddressRpc {


    private static final String APP_ID = "21790";
    private static final String APP_NAME = "physicalexamination";
    private static final String DEFAULT_IP = "127.0.0.1";
    private static final String BUSINESS_IDENTITY = "xfyl";
    @Resource
    private AddressInfoExport addressInfoExport;
    @Resource
    private TdeClientUtil tdeClientUtil;

    /**
     * queryAddressList
     *
     * @param userPin
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.JdhAddressRpcImpl.queryAddressList")
    public List<AddressDetailBO> queryAddressList(String userPin) {
        AddressQueryParam param = new AddressQueryParam();
        param.setUserPin(userPin);
        ApiResult<List<AddressInfoDTO>> result = addressInfoExport.queryAddressList(param, getClient());
        log.info("JdhAddressRpcImpl->listAddress res param={},result={}", JSON.toJSONString(param), JSON.toJSONString(result));
        if (Objects.isNull(result)) {
            log.warn("JdhAddressRpcImpl->listAddress result is null");
            return Collections.emptyList();
        }

        if (CollectionUtil.isEmpty(result.getData())) {
            log.warn("JdhAddressRpcImpl->listAddress result is empty");
            return Collections.emptyList();
        }
        List<AddressDetailBO> list = result.getData().stream().map(this::convertToBo).collect(Collectors.toList());
        log.info("JdhAddressRpcImpl-> queryAddressList pin:{},result:{}",userPin, JSON.toJSONString(list));
        return list;
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.JdhAddressRpcImpl.getAddressDetail")
    public AddressDetailBO getAddressDetail(String userPin, Long addressId) {
        AddressQueryParam param = new AddressQueryParam();
        param.setUserPin(userPin);
        ApiResult<List<AddressInfoDTO>> result = addressInfoExport.queryAddressList(param, getClient());

        if (Objects.isNull(result)) {
            log.warn("JdhAddressRpcImpl->listAddress result is null");
            return null;
        }

        if (CollectionUtil.isEmpty(result.getData())) {
            log.warn("JdhAddressRpcImpl->listAddress result is empty");
            return null;
        }
        log.info("JdhAddressRpcImpl-> queryAddressList pin:{},result:{}",userPin, JSON.toJSONString(result.getData()));
        return result.getData().stream().filter(e -> Objects.equals(addressId, e.getId())).map(this::convertToBo).findFirst().orElse(null);
    }

    /**
     * 查询默认收货地址
     *
     * @param userPin
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.JdhAddressRpcImpl.getAddressDefault")
    public AddressDetailBO getAddressDefault(String userPin) {
        List<AddressDetailBO> addressInfoList = queryAddressList(userPin);

        //如果没有默认地址，默认取第一个
        return CollectionUtil.isEmpty(addressInfoList) ? null : addressInfoList.get(0);
    }

    /**
     * 新增收货地址
     *
     * @param addressDetailBO bo
     * @return bo
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.JdhAddressRpcImpl.insertAddress")
    public AddressDetailBO insertAddress(String userPin, AddressDetailBO addressDetailBO) {
        AddressInfoDTO addressInfoDTO = new AddressInfoDTO();
        addressInfoDTO.setName(addressDetailBO.getName());
        addressInfoDTO.setMobile(addressDetailBO.getMobile());
        addressInfoDTO.setAddressDetail(addressDetailBO.getAddressDetail());
        addressInfoDTO.setProvinceId(addressDetailBO.getProvinceId() == null ? 0 : addressDetailBO.getProvinceId());
        addressInfoDTO.setCityId(addressDetailBO.getCityId() == null ? 0 : addressDetailBO.getCityId());
        addressInfoDTO.setCountyId(addressDetailBO.getCountyId() == null ? 0 : addressDetailBO.getCountyId());
        addressInfoDTO.setTownId(addressDetailBO.getTownId() == null ? 0 : addressDetailBO.getTownId());
        log.info("JdhAddressRpcImpl.insertAddress,req={}",JSON.toJSONString(addressInfoDTO));
        ApiResult<List<AddressInfoDTO>> result = addressInfoExport.insertAddress(userPin, addressInfoDTO, getClient());
        log.info("JdhAddressRpcImpl.insertAddress,result={}",JSON.toJSONString(result));

        if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
            if (StringUtils.isNotBlank(result.getMsg())){
                throw new BusinessException(new DynamicErrorCode(SystemErrorCode.PRC_UNKNOWN_EXCEPTION.getCode(), result.getMsg()));
            }
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
        return result.getData().stream().map(this::convertToBo).collect(Collectors.toList()).get(0);
    }

    /**
     * 收货地址
     *
     * @param addressInfoDTO dto
     * @return bo
     */
    private AddressDetailBO convertToBo(AddressInfoDTO addressInfoDTO) {
        if (addressInfoDTO == null) {
            return null;
        }
        AddressDetailBO addressDetailBO = new AddressDetailBO();
        addressDetailBO.setFullAddress(addressInfoDTO.getFullAddress());
        addressDetailBO.setName(addressInfoDTO.getName());
        addressDetailBO.setMobile(addressInfoDTO.getMobile());
        addressDetailBO.setAddressDetail(addressInfoDTO.getAddressDetail());
        addressDetailBO.setProvinceId(addressInfoDTO.getProvinceId());
        addressDetailBO.setProvinceName(addressInfoDTO.getProvinceName());
        addressDetailBO.setCityId(addressInfoDTO.getCityId());
        addressDetailBO.setCityName(addressInfoDTO.getCityName());
        addressDetailBO.setCountyId(addressInfoDTO.getCountyId());
        addressDetailBO.setCountyName(addressInfoDTO.getCountyName());
        addressDetailBO.setTownId(addressInfoDTO.getTownId());
        addressDetailBO.setTownName(addressInfoDTO.getTownName());
        addressDetailBO.setAddressDefault(addressInfoDTO.isAddressDefault());
        addressDetailBO.setAddressId(addressInfoDTO.getId());
        addressDetailBO.setLatitude(addressInfoDTO.getLatitude());
        addressDetailBO.setLongitude(addressInfoDTO.getLongitude());
        addressDetailBO.setCoordType(addressInfoDTO.getCoordType());
        addressDetailBO.setEncryptMobile(addressInfoDTO.getEncryptMobile());
        return addressDetailBO;
    }

    /**
     * 获取客户端配置
     *
     * @return
     */
    private ClientInfo getClient() {
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setAppId(APP_ID);
        clientInfo.setAppName(APP_NAME);
        clientInfo.setBusinessIdentity(BUSINESS_IDENTITY);
        String ip;
        try {
            ip = InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            log.warn("JdhAddressRpcImpl->listAddress getHostAddress error msg={}", e.getMessage());
            ip = DEFAULT_IP;
        }
        clientInfo.setIpAddress(ip);
        return clientInfo;
    }
}
