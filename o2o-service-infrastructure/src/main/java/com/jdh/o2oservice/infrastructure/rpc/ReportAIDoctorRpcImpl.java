package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.jd.medicine.b2c.base.export.domain.Result;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.core.domain.report.bo.ReportAIDoctorBo;
import com.jdh.o2oservice.core.domain.report.rpc.ReportAIDoctorRpc;
import com.jdh.user.front.jsf.export.CacheDataTempSaveExport;
import com.jdh.user.front.jsf.export.dto.PreDiagGuideSaveProcessDataParamDTO;
import com.jdh.user.front.jsf.export.dto.PreDiagGuideSaveProcessDataResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName ReportAIDoctorRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/14 14:59
 **/
@Component
@Slf4j
public class ReportAIDoctorRpcImpl implements ReportAIDoctorRpc {

    /**
     *
     */
    @Resource
    private CacheDataTempSaveExport cacheDataTempSaveExport;

    /**
     * 获取大为医生跳转链接
     * @param reportAIDoctorBo
     * @return
     */
    @Override
    public String preDiagGuiceSaveCacheReturnUrl(ReportAIDoctorBo reportAIDoctorBo) {
        try {
            log.info("ReportAIDoctorRpcImpl -> preDiagGuiceSaveCacheReturnUrl, reportAIDoctorBo={}", JSON.toJSONString(reportAIDoctorBo));
            PreDiagGuideSaveProcessDataParamDTO param = new PreDiagGuideSaveProcessDataParamDTO();
            param.setUserPin(reportAIDoctorBo.getUserPin());
            //pin置空
            reportAIDoctorBo.setUserPin(null);
            Map saveCacheData = BeanUtil.beanToMap(reportAIDoctorBo);
            param.setSaveCacheData(saveCacheData);
            log.info("ReportAIDoctorRpcImpl -> preDiagGuiceSaveCacheReturnUrl, param={}", JSON.toJSONString(param));
            Result<PreDiagGuideSaveProcessDataResultDTO> result = cacheDataTempSaveExport.preDiagGuiceSaveCacheReturnUrl(param);
            log.info("ReportAIDoctorRpcImpl -> preDiagGuiceSaveCacheReturnUrl, result={}", JSON.toJSONString(result));
            if (result != null && Objects.equals(BusinessErrorCode.SUCCESS.getCode(), result.getCode()) && Objects.nonNull(result.getData())) {
                return result.getData().getUrl();
            }
            throw new BusinessException(new DynamicErrorCode(result.getCode(), result.getMsg()));
        } catch (BusinessException e){
            throw e;
        } catch (Throwable e) {
            log.error("ReportAIDoctorRpcImpl -> preDiagGuiceSaveCacheReturnUrl error,", e);
            throw new SystemException(SystemErrorCode.PRC_UNKNOWN_EXCEPTION);
        }
    }
}