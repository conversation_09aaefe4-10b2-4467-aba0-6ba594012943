package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ReFundAmountTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.*;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderExtRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderMoneyRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.repository.query.JdOrderMoneyQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.*;
import com.jdh.o2oservice.infrastructure.repository.db.dao.*;
import com.jdh.o2oservice.infrastructure.repository.db.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:JdOrderRepositoryImpl
 * @Description: 京东订单仓储
 * @Author: yaoqinghai
 * @Date: 2024/1/9 15:14
 * @Vserion: 1.0
 **/
@Slf4j
@Repository
public class JdOrderRepositoryImpl implements JdOrderRepository {

    @Resource
    private JdOrderPoMapper jdOrderPoMapper;

    @Resource
    private JdOrderItemPoMapper jdOrderItemPoMapper;

    @Resource
    private JdOrderMoneyPoMapper jdOrderMoneyPoMapper;

    @Resource
    private JdOrderStatusPoMapper jdOrderStatusPoMapper;

    @Resource
    private JdOrderExtPoMapper jdOrderExtPoMapper;
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;
    @Resource
    private JdOrderExtRepository jdOrderExtRepository;

    @Resource
    private JdOrderMoneyRepository jdOrderMoneyRepository;




    @Override
    public JdOrder findFull(Long orderId, String userPin) {
        LambdaQueryWrapper<JdOrderPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdOrderPo::getOrderId, orderId)
                .eq(JdOrderPo::getUserPin,userPin)
                .eq(JdOrderPo::getYn, YnStatusEnum.YES.getCode());

        JdOrderPo po = jdOrderPoMapper.selectOne(queryWrapper);
        JdOrder jdOrder = JdOrderPoConverter.INSTANCE.convertToJdOrder(po);
        if (Objects.isNull(jdOrder)) {
            return null;
        }

        List<JdOrderItem> items = jdOrderItemRepository.listByOrderId(orderId);
        jdOrder.setJdOrderItemList(items);

        List<JdOrderExt> exts = jdOrderExtRepository.findJdOrderExtList(orderId);
        jdOrder.setJdOrderExtList(exts);
        return jdOrder;
    }

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param jdOrderIdentifier
     */
    @Override
    public JdOrder find(JdOrderIdentifier jdOrderIdentifier) {
        LambdaQueryWrapper<JdOrderPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdOrderPo::getOrderId, jdOrderIdentifier.getOrderId()).eq(JdOrderPo::getYn, YnStatusEnum.YES.getCode());

        JdOrderPo po = jdOrderPoMapper.selectOne(queryWrapper);
        return JdOrderPoConverter.INSTANCE.convertToJdOrder(po);
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param aggregate
     */
    @Override
    public int remove(JdOrder aggregate) {

        LambdaUpdateWrapper<JdOrderPo> wrapper = new LambdaUpdateWrapper<JdOrderPo>();
        wrapper.eq(JdOrderPo::getOrderId, aggregate.getOrderId());
        return jdOrderPoMapper.delete(wrapper);
    }

    /**
     * 写入订单信息
     *
     * @param jdOrder
     * @return
     */
    @Override
    public int save(JdOrder jdOrder) {
        JdOrderPo jdOrderPo = JdOrderPoConverter.INSTANCE.convertToJdOrderPo(jdOrder);
        int jdOrderInsert = jdOrderPoMapper.insert(jdOrderPo);

        List<JdOrderItem> jdOrderItemList = jdOrder.getJdOrderItemList();
        if (jdOrderItemList != null) {
            List<JdOrderItemPo> jdOrderItemPoList = JdOrderItemPoConverter.INSTANCE.batchConvertToJdOrderItemPo(jdOrderItemList);
            for (JdOrderItemPo jdOrderItemPo : jdOrderItemPoList) {
                jdOrderItemPoMapper.insert(jdOrderItemPo);
            }
        }

        List<JdOrderMoney> jdOrderMoneyList = jdOrder.getJdOrderMoneyList();
        if (jdOrderMoneyList != null) {
            List<JdOrderMoneyPo> jdOrderMoneyPoList = JdOrderMoneyPoConverter.INSTANCE.batchConvertToJdOrderMoneyPo(jdOrderMoneyList);
            for (JdOrderMoneyPo jdOrderMoneyPo : jdOrderMoneyPoList) {
                jdOrderMoneyPoMapper.insert(jdOrderMoneyPo);
            }
        }

        List<JdOrderStatus> jdOrderStatusList = jdOrder.getJdOrderStatusList();
        if (jdOrderStatusList != null) {
            List<JdOrderStatusPo> jdOrderStatusPoList = JdOrderStatusPoConverter.INSTANCE.batchConvertToJdOrderStatusPo(jdOrderStatusList);
            for (JdOrderStatusPo jdOrderStatusPo : jdOrderStatusPoList) {
                jdOrderStatusPoMapper.insert(jdOrderStatusPo);
            }
        }

        List<JdOrderExtPo> jdOrderExtPos = JdOrderExtConverter.INSTANCE.convertToJdOrderExtPo(jdOrder.getJdOrderExtList());
        if (jdOrderExtPos != null) {
            for (JdOrderExtPo jdOrderExtPo : jdOrderExtPos) {
                jdOrderExtPoMapper.insert(jdOrderExtPo);
            }
        }
        return jdOrderInsert;
    }

    /**
     * 更新不拆单订单信息
     *
     * @param jdOrder
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateNoSplitOrder(JdOrder jdOrder) {
        Long orderId = jdOrder.getOrderId();
        log.info("[JdOrderRepositoryImpl->updateNoSplitOrder], orderId={}", orderId);
        JdOrderPo jdOrderPo = JdOrderPoConverter.INSTANCE.convertToJdOrderPo(jdOrder);
        jdOrderPo.setOrderStatus(OrderStatusEnum.ORDER_PAID.getStatus());
        LambdaUpdateWrapper<JdOrderPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("`version` = `version` + 1")
                .eq(JdOrderPo::getOrderId, jdOrderPo.getOrderId());
        int jdOrderInsert = jdOrderPoMapper.update(jdOrderPo, updateWrapper);

        List<JdOrderStatus> jdOrderStatusList = jdOrder.getJdOrderStatusList();
        List<JdOrderStatusPo> jdOrderStatusPoList = JdOrderStatusPoConverter.INSTANCE.batchConvertToJdOrderStatusPo(jdOrderStatusList);
        for (JdOrderStatusPo jdOrderStatusPo : jdOrderStatusPoList) {
            jdOrderStatusPo.setStatus(OrderStatusEnum.ORDER_PAID.getStatus());
            jdOrderStatusPoMapper.insert(jdOrderStatusPo);
        }

        List<JdOrderItem> jdOrderItemList = jdOrder.getJdOrderItemList();
        List<JdOrderItemPo> jdOrderItemPoList = JdOrderItemPoConverter.INSTANCE.batchConvertToJdOrderItemPo(jdOrderItemList);
        for (JdOrderItemPo jdOrderItemPo : jdOrderItemPoList) {
            LambdaUpdateWrapper<JdOrderItemPo> updateItemWrapper = Wrappers.lambdaUpdate();
            updateItemWrapper.set(JdOrderItemPo::getVerticalCode, jdOrder.getVerticalCode())
                    .set(JdOrderItemPo::getServiceType, jdOrder.getServiceType())
                    .setSql("`version` = `version` + 1")
                    .eq(JdOrderItemPo::getSkuId, jdOrderItemPo.getSkuId())
                    .eq(JdOrderItemPo::getOrderId, jdOrderPo.getOrderId());
            jdOrderItemPoMapper.update(null, updateItemWrapper);
        }

        if (CollUtil.isNotEmpty(jdOrder.getJdOrderMoneyList())) {
            List<JdOrderMoney> jdOrderMoneyList = jdOrder.getJdOrderMoneyList();
            List<JdOrderMoneyPo> jdOrderMoneyPoList = JdOrderMoneyPoConverter.INSTANCE.batchConvertToJdOrderMoneyPo(jdOrderMoneyList);
            for (JdOrderMoneyPo jdOrderMoneyPo : jdOrderMoneyPoList) {
                jdOrderMoneyPoMapper.insert(jdOrderMoneyPo);
            }
        }

        if (CollUtil.isNotEmpty(jdOrder.getJdOrderExtList())) {
            List<JdOrderExt> jdOrderExtList = jdOrder.getJdOrderExtList();
            List<JdOrderExtPo> jdOrderExtPos = JdOrderExtConverter.INSTANCE.convertToJdOrderExtPo(jdOrderExtList);
            for (JdOrderExtPo jdOrderExtPo : jdOrderExtPos) {
                jdOrderExtPoMapper.insert(jdOrderExtPo);
            }
        }
        return jdOrderInsert;
    }

    /**
     * 保存拆单后子单信息
     *
     * @param jdOrder
     * @param parentIdOrder
     * @param jdOrderItem
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveSplitOrder(JdOrder jdOrder, JdOrder parentIdOrder, JdOrderItem jdOrderItem) {
        Long orderId = jdOrder.getOrderId();
        log.info("[JdOrderRepositoryImpl->updateSplitOrder], orderId={}", orderId);

        // 拆后子订单数据保存
        this.saveChildOrder(jdOrder, jdOrderItem);

        // 父订单数据更新
        // 1.jd_order
        parentIdOrder.setOrderStatus(OrderStatusEnum.ORDER_SPLIT.getStatus());
        LambdaUpdateWrapper<JdOrderPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Objects.nonNull(parentIdOrder.getOrderStatus()), JdOrderPo::getOrderStatus, parentIdOrder.getOrderStatus())
                .set(StringUtil.isNotBlank(parentIdOrder.getVerticalCode()), JdOrderPo::getVerticalCode, parentIdOrder.getVerticalCode())
                .set(StringUtil.isNotBlank(parentIdOrder.getServiceType()), JdOrderPo::getServiceType, parentIdOrder.getServiceType())
                .setSql("`version` = `version` + 1")
                .eq(JdOrderPo::getOrderId, parentIdOrder.getOrderId());
        int jdOrderInsert = jdOrderPoMapper.update(null, updateWrapper);

        // 3.jd_order_item
        List<JdOrderItem> jdOrderItemList = jdOrder.getJdOrderItemList();
        List<JdOrderItemPo> jdOrderItemPoList = JdOrderItemPoConverter.INSTANCE.batchConvertToJdOrderItemPo(jdOrderItemList);
        for (JdOrderItemPo jdOrderItemPo : jdOrderItemPoList) {
            LambdaUpdateWrapper<JdOrderItemPo> updateItemWrapper = Wrappers.lambdaUpdate();
            updateItemWrapper.set(JdOrderItemPo::getVerticalCode, jdOrder.getVerticalCode())
                    .set(JdOrderItemPo::getServiceType, jdOrder.getServiceType())
                    .set(JdOrderItemPo::getSkuExpireDate, jdOrderItemPo.getSkuExpireDate())
                    .set(JdOrderItemPo::getSkuFeatures, jdOrderItemPo.getSkuFeatures())
                    .set(JdOrderItemPo::getVenderId, jdOrderItemPo.getVenderId())
                    .setSql("`version` = `version` + 1")
                    .eq(JdOrderItemPo::getSkuId, jdOrderItemPo.getSkuId())
                    .eq(JdOrderItemPo::getOrderId, parentIdOrder.getOrderId());
            jdOrderItemPoMapper.update(null, updateItemWrapper);
        }
        return jdOrderInsert;
    }

    /**
     * 写入订单信息
     *
     * @param jdOrder
     * @param jdOrderItem
     * @return
     */
    private int saveChildOrder(JdOrder jdOrder, JdOrderItem jdOrderItem) {
        log.info("JdOrderRepositoryImpl.saveChildOrder.jdOrder={},jdOrderItem={}", JSON.toJSONString(jdOrder),JSON.toJSONString(jdOrderItem));
        JdOrderPo jdOrderPo = JdOrderPoConverter.INSTANCE.convertToJdOrderPo(jdOrder);
        //加项逻辑
        jdOrderPo.setHasAdded(jdOrderItem.getIsAdded());
        log.info("JdOrderRepositoryImpl.saveChildOrder.jdOrderPo={}", JSON.toJSONString(jdOrderPo));
        int jdOrderInsert = jdOrderPoMapper.insert(jdOrderPo);

        List<JdOrderItem> jdOrderItemList = jdOrder.getJdOrderItemList();
        List<JdOrderItemPo> jdOrderItemPoList = JdOrderItemPoConverter.INSTANCE.batchConvertToJdOrderItemPo(jdOrderItemList);
        for (JdOrderItemPo jdOrderItemPo : jdOrderItemPoList) {
            jdOrderItemPo.setVerticalCode(jdOrder.getVerticalCode());
            jdOrderItemPo.setServiceType(jdOrder.getServiceType());
            jdOrderItemPo.setItemAmount(jdOrderItem.getItemAmount());
            jdOrderItemPo.setItemDiscount(jdOrderItem.getItemDiscount());
            jdOrderItemPo.setItemTotalAmount(jdOrderItem.getItemTotalAmount());
            //是否为加项商品
            jdOrderItemPo.setIsAdded(jdOrderItem.getIsAdded());
            jdOrderItemPoMapper.insert(jdOrderItemPo);
        }

        List<JdOrderMoney> jdOrderMoneyList = jdOrder.getJdOrderMoneyList();
        if (jdOrderMoneyList != null) {
            List<JdOrderMoneyPo> jdOrderMoneyPoList = JdOrderMoneyPoConverter.INSTANCE.batchConvertToJdOrderMoneyPo(jdOrderMoneyList);
            for (JdOrderMoneyPo jdOrderMoneyPo : jdOrderMoneyPoList) {
                jdOrderMoneyPoMapper.insert(jdOrderMoneyPo);
            }
        }

        List<JdOrderStatus> jdOrderStatusList = jdOrder.getJdOrderStatusList();
        if (jdOrderStatusList != null) {
            List<JdOrderStatusPo> jdOrderStatusPoList = JdOrderStatusPoConverter.INSTANCE.batchConvertToJdOrderStatusPo(jdOrderStatusList);
            for (JdOrderStatusPo jdOrderStatusPo : jdOrderStatusPoList) {
                jdOrderStatusPoMapper.insert(jdOrderStatusPo);
            }
        }

        List<JdOrderExtPo> jdOrderExtPos = JdOrderExtConverter.INSTANCE.convertToJdOrderExtPo(jdOrder.getJdOrderExtList());
        if (jdOrderExtPos != null) {
            for (JdOrderExtPo jdOrderExtPo : jdOrderExtPos) {
                jdOrderExtPoMapper.insert(jdOrderExtPo);
            }
        }
        return jdOrderInsert;
    }

    /**
     * 取消订单支付
     *
     * @param jdOrder
     * @return
     */
    @Override
    public int cancalOrderByOrderId(JdOrder jdOrder) {
        LambdaUpdateWrapper<JdOrderPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Objects.nonNull(jdOrder.getOrderStatus()), JdOrderPo::getOrderStatus, jdOrder.getOrderStatus())
                .setSql("`version` = `version` + 1")
                .eq(JdOrderPo::getOrderId, jdOrder.getOrderId())
                .eq(JdOrderPo::getUserPin, jdOrder.getUserPin());
        return jdOrderPoMapper.update(null, updateWrapper);
    }

    /**
     * 查询订单详情
     *
     * @param jdOrder
     * @return
     */
    @Override
    public JdOrder findOrderDetail(JdOrder jdOrder) {
        try {
            LambdaQueryWrapper<JdOrderPo> jdOrderPoLambdaQueryWrapper = Wrappers.lambdaQuery();
            jdOrderPoLambdaQueryWrapper.eq(JdOrderPo::getOrderId, jdOrder.getOrderId());
            jdOrderPoLambdaQueryWrapper.eq(JdOrderPo::getYn, YnStatusEnum.YES.getCode());
            jdOrderPoLambdaQueryWrapper.eq(StringUtils.isNotBlank(jdOrder.getUserPin()), JdOrderPo::getUserPin, jdOrder.getUserPin());
            JdOrderPo jdOrderPo = jdOrderPoMapper.selectOne(jdOrderPoLambdaQueryWrapper);
            if (Objects.isNull(jdOrderPo)) {
                return null;
            }

            List<Integer> moneyTypeList = ReFundAmountTypeEnum.getRefundTypeList();
            JdOrderMoneyQuery jdOrderMoneyQuery = new JdOrderMoneyQuery();
            jdOrderMoneyQuery.setOrderId(jdOrder.getOrderId());
            jdOrderMoneyQuery.setMoneyTypeList(moneyTypeList);
            List<JdOrderMoney> jdOrderMoneyList = jdOrderMoneyRepository.findOrderMoneyList(jdOrderMoneyQuery);
            if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
                BigDecimal orderAmount = BigDecimal.ZERO;
                for(JdOrderMoney jdOrderMoney : jdOrderMoneyList){
                    if(moneyTypeList.contains(jdOrderMoney.getMoneyType())){
                        orderAmount = orderAmount.add(jdOrderMoney.getAmount());
                    }
                }
                jdOrderPo.setOrderAmount(orderAmount);
            }
            return JdOrderPoConverter.INSTANCE.convertToJdOrder(jdOrderPo);
        } catch (Exception e) {
            log.error("JdOrderRepositoryImpl->findJdOrder query={}", JSON.toJSONString(jdOrder), e);
            return null;
        }
    }

    /**
     * 查询订单详情
     *
     * @param jdOrder
     * @return
     */
    @Override
    public List<JdOrder> findOrderListByParentId(JdOrder jdOrder) {
        if(Objects.isNull(jdOrder.getParentId()) || jdOrder.getParentId().intValue() == 0){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<JdOrderPo> jdOrderPoLambdaQueryWrapper = Wrappers.lambdaQuery();
        jdOrderPoLambdaQueryWrapper.eq(Objects.nonNull(jdOrder.getParentId()), JdOrderPo::getParentId, jdOrder.getParentId());
        jdOrderPoLambdaQueryWrapper.eq(JdOrderPo::getYn, YnStatusEnum.YES.getCode());
        jdOrderPoLambdaQueryWrapper.eq(StringUtils.isNotBlank(jdOrder.getUserPin()), JdOrderPo::getUserPin, jdOrder.getUserPin());
        List<JdOrderPo> jdOrderPoList = jdOrderPoMapper.selectList(jdOrderPoLambdaQueryWrapper);
        return JdOrderPoConverter.INSTANCE.convertToJdOrderList(jdOrderPoList);
    }

    @Override
    public List<JdOrder> findOrderListByOrderId(JdOrderIdentifier id) {
        LambdaQueryWrapper<JdOrderPo> jdOrderPoLambdaQueryWrapper = Wrappers.lambdaQuery();
        jdOrderPoLambdaQueryWrapper.eq(JdOrderPo::getYn, YnStatusEnum.YES.getCode());
        jdOrderPoLambdaQueryWrapper.eq(JdOrderPo::getOrderId, id.getOrderId()).or().eq(JdOrderPo::getParentId, id.getOrderId());
        List<JdOrderPo> jdOrderPoList = jdOrderPoMapper.selectList(jdOrderPoLambdaQueryWrapper);
        return JdOrderPoConverter.INSTANCE.convertToJdOrderList(jdOrderPoList);
    }

    /**
     * 更新订单的身份信息
     *
     * @param jdOrder
     * @return
     */
    @Override
    public int updateOrderByOrderId(JdOrder jdOrder) {
        JdOrderPo jdOrderPo = JdOrderPoConverter.INSTANCE.convertToJdOrderPo(jdOrder);
        jdOrderPo.setUpdateTime(new Date());

        LambdaUpdateWrapper<JdOrderPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("`version` = `version` + 1")
                .eq(JdOrderPo::getOrderId, jdOrder.getOrderId());
        int update = jdOrderPoMapper.update(jdOrderPo, updateWrapper);

        List<JdOrderItem> jdOrderItemList = jdOrder.getJdOrderItemList();
        if (CollectionUtils.isEmpty(jdOrderItemList)) {
            return update;
        }

        for (JdOrderItem jdOrderItem : jdOrderItemList) {
            JdOrderItemPo jdOrderItemPo = JdOrderItemPoConverter.INSTANCE.convertToJdOrderItemPo(jdOrderItem);
            jdOrderItemPo.setUpdateTime(new Date());
            if (Objects.isNull(jdOrderItem.getOrderItemId())) {
                continue;
            }
            LambdaUpdateWrapper<JdOrderItemPo> itemUpdateWrapper = Wrappers.lambdaUpdate();
            itemUpdateWrapper.setSql("`version` = `version` + 1")
                    .eq(JdOrderItemPo::getOrderItemId, jdOrderItem.getOrderItemId());
            jdOrderItemPoMapper.update(jdOrderItemPo, itemUpdateWrapper);
        }
        return update;
    }

    @Override
    public Long queryParentOrderIdByPartnerSourceOrderId(String partnerSourceOrderId, String UserPin) {
        LambdaQueryWrapper<JdOrderPo> jdOrderPoLambdaQueryWrapper = Wrappers.lambdaQuery();
        jdOrderPoLambdaQueryWrapper.eq(JdOrderPo::getPartnerSourceOrderId, partnerSourceOrderId);
        jdOrderPoLambdaQueryWrapper.eq(JdOrderPo::getYn, YnStatusEnum.YES.getCode());
        jdOrderPoLambdaQueryWrapper.eq(StringUtils.isNotBlank(UserPin), JdOrderPo::getUserPin, UserPin);
        jdOrderPoLambdaQueryWrapper.orderByDesc(JdOrderPo::getUpdateTime);
        jdOrderPoLambdaQueryWrapper.last("limit 1");
        JdOrderPo jdOrderPo = jdOrderPoMapper.selectOne(jdOrderPoLambdaQueryWrapper);
        if (Objects.isNull(jdOrderPo)) {
            return null;
        }
        if (jdOrderPo.getParentId() != null && jdOrderPo.getParentId() != 0) {
            return jdOrderPo.getParentId();
        }
        return jdOrderPo.getOrderId();
    }

    /**
     * 更新订单状态信息
     *
     * @param jdOrder
     * @return
     */
    @Override
    public int updateOrderStatusByOrderId(JdOrder jdOrder) {
        LambdaUpdateWrapper<JdOrderPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdOrderPo::getOrderStatus, jdOrder.getOrderStatus())
                .setSql("`version` = `version` + 1")
                .eq(JdOrderPo::getOrderId, jdOrder.getOrderId());
        int update = jdOrderPoMapper.update(null, updateWrapper);
        return update;
    }

    /**
     * 更新订单扩展信息
     *
     * @param jdOrderExtList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateOrderExt(List<JdOrderExt> jdOrderExtList) {
        int count = 0;
        if (CollUtil.isNotEmpty(jdOrderExtList)) {
            List<JdOrderExtPo> jdOrderExtPos = JdOrderExtConverter.INSTANCE.convertToJdOrderExtPo(jdOrderExtList);
            for (JdOrderExtPo jdOrderExtPo : jdOrderExtPos) {
                jdOrderExtPo.setVersion(jdOrderExtPo.getVersion() + 1);
                jdOrderExtPoMapper.updateById(jdOrderExtPo);
                count++;
            }
        }
        return count;
    }

    /**
     * 根据订单号查询父子单清单
     *
     * @param orderIds@return
     */
    @Override
    public List<JdOrder> findOrdersByList(List<Long> orderIds) {
        LambdaQueryWrapper<JdOrderPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdOrderPo::getYn, CommonConstant.ONE)
                .in(JdOrderPo::getOrderId,orderIds);
        List<JdOrderPo> jdOrderPos = jdOrderPoMapper.selectList(queryWrapper);
        return JdOrderPoConverter.INSTANCE.convertToJdOrderList(jdOrderPos);
    }

    @Override
    public List<JdOrder> findOrderListByPartnerSource(JdOrder jdOrder) {
        LambdaQueryWrapper<JdOrderPo> jdOrderPoLambdaQueryWrapper = Wrappers.lambdaQuery();
        jdOrderPoLambdaQueryWrapper.eq(JdOrderPo::getPartnerSourceOrderId, jdOrder.getPartnerSourceOrderId());
        jdOrderPoLambdaQueryWrapper.eq(JdOrderPo::getPartnerSource, jdOrder.getPartnerSource());
        jdOrderPoLambdaQueryWrapper.eq(Objects.nonNull(jdOrder.getOrderId()), JdOrderPo::getOrderId, jdOrder.getOrderId());
        jdOrderPoLambdaQueryWrapper.eq(JdOrderPo::getYn, YnStatusEnum.YES.getCode());
        List<JdOrderPo> jdOrderPo = jdOrderPoMapper.selectList(jdOrderPoLambdaQueryWrapper);
        return JdOrderPoConverter.INSTANCE.convertToJdOrderList(jdOrderPo);
    }
}
