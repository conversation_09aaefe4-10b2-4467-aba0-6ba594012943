package com.jdh.o2oservice.infrastructure.rpc;

import com.google.common.collect.Lists;
import com.jd.jdh.net.domain.client.dto.common.ClientDTO;
import com.jd.jdh.net.domain.client.dto.order.NetOrderDTO;
import com.jd.jdh.net.domain.client.dto.order.NetOrderExpenditureDTO;
import com.jd.jdh.net.domain.client.dto.order.SyncOrderResp;
import com.jd.jdh.net.domain.client.enums.dictionary.OrderExpenditureItem;
import com.jd.jdh.net.domain.client.service.order.SyncOrderClient;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.yao.common.client.base.vo.Res;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BaseDomainErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.TianSuanSettlementRpc;
import com.jdh.o2oservice.core.domain.support.reach.model.WriteLedgerOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 天算RPCImpl
 * @Author: wangpengfei144
 * @Date: 2024/5/8
 */
@Service
@Slf4j
public class TianSuanSettlementRpcImpl implements TianSuanSettlementRpc {

    /**
     * 接口文档：<a href="https://joyspace.jd.com/pages/Xrh43fMCApnqzbmhq0a7">...</a>
     */
    @Resource
    private SyncOrderClient syncOrderClient;

    /**
     * 缓存
     */
    @Resource
    private Cluster jimClient;

    /**
     * 资金写入
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.TianSuanSettlementRpcImpl.syncOrder")
    public String syncOrder(WriteLedgerOrder writeLedgerOrder) {
        ClientDTO client = new ClientDTO();
        //TODO
        client.setClient("jdh-o2o-service");
        client.setCurrentTime(System.currentTimeMillis());
        client.setClientIp("*******");

        NetOrderDTO order = new NetOrderDTO();
        order.setChannelRuleCode(writeLedgerOrder.getChannelRuleCode());
        order.setOwnOrderId(String.valueOf(writeLedgerOrder.getAppointmentId()));
        order.setDirection(writeLedgerOrder.getDirection());
        order.setOptType(writeLedgerOrder.getOptType());
        order.setCreateTime(writeLedgerOrder.getOrderStartTime());
        order.setSettlementTimePoint(writeLedgerOrder.getSettlementTimePoint());
        order.setSettlementTime(writeLedgerOrder.getVerifyTime());
        order.setOrderAmount(writeLedgerOrder.getActualPrice());
        order.setUserPin(writeLedgerOrder.getUserPin());
        // 费项
        order.setExpenditureList(buildExpenditure(writeLedgerOrder));
        order.setOrderStatus(writeLedgerOrder.getOrderStatus());
        order.setTraceNo(writeLedgerOrder.getLedgerId());
        order.setOperationType(writeLedgerOrder.getOperationType());
        order.setVenderId(writeLedgerOrder.getVenderId());
        log.info("TianSuanSettlementServiceRpcImpl->syncOrder order={}", JsonUtil.toJSONString(order));
        Res<SyncOrderResp> res = syncOrderClient.syncOrder(client, order);
        log.info("TianSuanSettlementServiceRpcImpl->syncOrder res={}", JsonUtil.toJSONString(res));

        if (Objects.isNull(res)){
            log.error("TianSuanSettlementRpcImpl->syncOrder res is null");
            throw new BusinessException(BaseDomainErrorCode.TIAN_SUAN_PUSH_ERROR);
        }
        log.info("TianSuanSettlementRpcImpl->syncOrder res={}", JsonUtil.toJSONString(res));
        if(!res.isSuccess()){
            log.error("TianSuanSettlementRpcImpl-> res is fail");
            throw new BusinessException(BaseDomainErrorCode.TIAN_SUAN_PUSH_ERROR);
        }
        //加缓存
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.TIAN_SUAN_KEY,writeLedgerOrder.getAppointmentId());
        jimClient.setEx(redisKey, res.getData().getSerialNumber(),CommonConstant.FIFTEEN, TimeUnit.DAYS);
        //返回流水号
        return res.getData().getSerialNumber();
    }

    /**
     * 京东收入=用户支付-平台佣金-达人佣金-服务商佣金+平台补贴
     * 商品总金额=用户支付
     * 平台抽佣=平台佣金
     * @param ledgerOrderRpcBO
     * @return
     */
    private List<NetOrderExpenditureDTO> buildExpenditure(WriteLedgerOrder ledgerOrderRpcBO){
        List<NetOrderExpenditureDTO> res = Lists.newArrayListWithExpectedSize(1);
        // 用户支付(对应商品金额)
        NetOrderExpenditureDTO pay = new NetOrderExpenditureDTO();
        pay.setExpenditureAmount(ledgerOrderRpcBO.getPay());
        pay.setExpenditureItem("ORDER_RECEIVABLE_ITEM");
        pay.setExpenditureSubItem(OrderExpenditureItem.ORDER_RECEIVABLE_ITEM_29.getCode());
        res.add(pay);
        return res;
    }
}
