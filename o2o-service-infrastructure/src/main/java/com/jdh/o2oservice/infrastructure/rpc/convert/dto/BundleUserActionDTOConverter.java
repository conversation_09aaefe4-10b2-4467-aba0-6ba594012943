package com.jdh.o2oservice.infrastructure.rpc.convert.dto;

import com.alibaba.fastjson.JSON;
import com.jd.trade2.base.export.relation.Relation;
import com.jd.trade2.core.domain.promise.export.dto.AbstractPromiseItemDTO;
import com.jd.trade2.core.domain.promise.export.dto.PromiseCollectionDTO;
import com.jd.trade2.core.domain.shipment.export.dto.AbstractShipmentItemDTO;
import com.jd.trade2.core.domain.shipment.export.dto.ShipmentCollectionDTO;
import com.jd.trade2.core.domain.shoppinglist.export.dto.model.AbstractShoppingItemDTO;
import com.jd.trade2.core.domain.shoppinglist.export.dto.model.ShoppingListCollectionDTO;
import com.jd.trade2.core.domain.store.export.dto.model.AbstractStoreItemDTO;
import com.jd.trade2.core.domain.store.export.dto.model.StoreCollectionDTO;
import com.jd.trade2.core.domain.vender.export.dto.model.AbstractVenderItemDTO;
import com.jd.trade2.core.domain.vender.export.dto.model.VenderCollectionDTO;
import com.jd.trade2.vertical.tool.ParseTool;
import com.jdh.o2oservice.base.enums.IdentityUserActionEnum;
import com.jdh.o2oservice.core.domain.trade.vo.*;
import com.jdh.o2oservice.infrastructure.rpc.convert.TradeParamConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * BundleUserActionDTOConverter 用户行为-包裹
 *
 * <AUTHOR>
 * @version 2024/3/15 14:32
 **/
@Slf4j
@Component
public class BundleUserActionDTOConverter extends AbstractUserActionDTOConverter<OrderTradeValueObject> {

    @Resource
    private TradeParamConverter tradeParamInfrastructureAssembler;

    @Resource
    private ShipmentUserActionDTOConverter shipmentUserActionDTOAssembler;

    @Resource
    private ProductUserActionDTOConverter productUserActionDTOConverter;

    @Override
    public boolean identityContainsUserAction(List<String> identityUserActionList) {
        return identityUserActionList.contains(IdentityUserActionEnum.BUNDLE_USER_ACTION.getUserActionType());
    }

    @Override
    public void handlerIdentityTradeOrder(ParseTool parseTool, OrderTradeValueObject orderTradeResult, Relation relation, String identity) {
        List<BundleInfoValueObject> bundleInfos = buildBundleInfo(parseTool, relation);
        if (CollectionUtils.isEmpty(bundleInfos)) {
            return;
        }
        orderTradeResult.setBundleInfoList(bundleInfos);
    }

    @Override
    public void handlerIdentityUserAction(ParseTool parseTool, OrderUserActionValueObject orderUserActionResult, Relation relation, String identity) {
        List<BundleInfoValueObject> bundleInfos = buildBundleInfo(parseTool, relation);
        if (CollectionUtils.isEmpty(bundleInfos)) {
            return;
        }
        orderUserActionResult.setBundleInfoList(bundleInfos);
    }


    /**
     * 组装出参中包裹信息
     *
     * @param parseTool parseTool
     * @param relation  relation
     * @return BundleInfo
     */
    private List<BundleInfoValueObject> buildBundleInfo(ParseTool parseTool, Relation relation) {
        List<BundleInfoValueObject> bundleInfoList = new ArrayList<>();
        VenderCollectionDTO venderCollectionDTO = parseTool.getCollectionDTO(VenderCollectionDTO.class);
        StoreCollectionDTO storeCollectionDTO = parseTool.getCollectionDTO(StoreCollectionDTO.class);
        ShoppingListCollectionDTO shoppingListCollectionDTO = parseTool.getCollectionDTO(ShoppingListCollectionDTO.class);
        ShipmentCollectionDTO shipmentCollectionDTO = parseTool.getCollectionDTO(ShipmentCollectionDTO.class);
        PromiseCollectionDTO promiseCollectionDTO = parseTool.getCollectionDTO(PromiseCollectionDTO.class);

        String orderObtainUUID = relation.obtainOrder();
        // 获取商家下包裹列表
        List<String> bundleIds = (List<String>) relation.parse(relation.obtainOrder(), relation.obtainAllBundle());
        for (String bundleId : bundleIds) {
            BundleInfoValueObject bundleInfo = new BundleInfoValueObject();
            // 获取包裹下所有的清单
            List<AbstractShoppingItemDTO> shoppingItemDTOList = shoppingListCollectionDTO.getShoppingItemListByBundle(bundleId, relation);
            log.info("BundleUserActionDTOConverter buildBundleInfo shoppingItemDTOList={}", JSON.toJSONString(shoppingItemDTOList));
            bundleInfo.setSkuItemVoList(productUserActionDTOConverter.getSkuItemVoList(relation, parseTool));
            Set<String> skuObtainUUIDSet = new HashSet<>();
            boolean isXnzt = true;
            boolean isGift = true;
            for (AbstractShoppingItemDTO abstractShoppingItemDTO : shoppingItemDTOList) {
                String complexMainItemUUID = abstractShoppingItemDTO.getComplexMainItemUUID();
                if (!abstractShoppingItemDTO.isComplexItem()) {
                    isXnzt = false;
                }
                if (!abstractShoppingItemDTO.isGift()) {
                    isGift = false;
                }
                if (StringUtils.isNotEmpty(complexMainItemUUID)) {
                    skuObtainUUIDSet.add(complexMainItemUUID);
                } else {
                    skuObtainUUIDSet.add(abstractShoppingItemDTO.obtainUUID());
                }
            }
            if (isXnzt || isGift) {
                continue;
            }
            // 配送方式
            AbstractShipmentItemDTO abstractShipmentItemDTO = shipmentCollectionDTO.getSelectedShipmentByBundle(bundleId, relation);
            ShipmentInfoValueObject shipmentInfo = tradeParamInfrastructureAssembler.convertShipmentInfo(abstractShipmentItemDTO);
            if (shipmentInfo == null) {
                continue;
            }
            shipmentInfo.setOrderObtainUUID(orderObtainUUID);

            List<String> basketIds = (List<String>) relation.parse(bundleId, relation.obtainAllBasket());
            if (CollectionUtils.isNotEmpty(basketIds)) {
                shipmentInfo.setBasketId(basketIds.get(0));
            }

            List<AbstractVenderItemDTO> venderItemDTOList = venderCollectionDTO.getVenderItemListByBundle(bundleId, relation);
            if (CollectionUtils.isNotEmpty(venderItemDTOList)) {
                List<Long> venderIdList = venderItemDTOList.stream().map(s -> Long.parseLong(s.getVenderId())).collect(Collectors.toList());
                bundleInfo.setVenderIdList(venderIdList);
            }

            AbstractStoreItemDTO storeItemDTO = storeCollectionDTO.getStoreItemListByBundle(bundleId, relation);
            if (storeItemDTO != null && StringUtils.isNumeric(storeItemDTO.getStoreId())) {
                bundleInfo.setStoreId(Long.parseLong(storeItemDTO.getStoreId()));
            }

            shipmentInfo.setBundleId(bundleId);
            shipmentInfo.setShipmentObtainUUID(abstractShipmentItemDTO.obtainUUID());
            bundleInfo.setShipmentInfo(shipmentInfo);

            // 时效
            AbstractPromiseItemDTO abstractPromiseItemDTO = promiseCollectionDTO.getSelectedPromiseByBundle(bundleId, relation);
            if (abstractPromiseItemDTO != null) {
                PromiseInfoValueObject promiseInfo = shipmentUserActionDTOAssembler.buildPromiseInfo(abstractPromiseItemDTO);
                bundleInfo.setPromiseInfo(promiseInfo);
            }

            if (CollectionUtils.isNotEmpty(basketIds)) {
                bundleInfo.setBasketId(basketIds.get(0));
            }
            bundleInfo.setOrderObtainUUID(orderObtainUUID);
            bundleInfo.setBundleId(bundleId);
            bundleInfo.setSkuObtainUUIDList(new ArrayList<>(skuObtainUUIDSet));
            bundleInfoList.add(bundleInfo);
        }
        return bundleInfoList;
    }
}
