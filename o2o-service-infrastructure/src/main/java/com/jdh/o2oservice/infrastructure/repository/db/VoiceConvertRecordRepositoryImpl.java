package com.jdh.o2oservice.infrastructure.repository.db;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.support.file.model.VoiceConvertRecord;
import com.jdh.o2oservice.core.domain.support.file.model.VoiceIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.VoiceConvertRecordRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.VoiceConvertRecordConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.VoiceConvertRecordPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.VoiceConvertRecordPo;
import com.jdh.o2oservice.infrastructure.repository.es.EsClientFactoryHealthcare;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.get.GetResponse;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/13 17:24
 */
@Component
@Slf4j
public class VoiceConvertRecordRepositoryImpl implements VoiceConvertRecordRepository {
    /**
     * indexName
     */
    private static String indexName = "o2o_voice_convert_record_index";
    /**
     * typeName
     */
    private final static String typeName = "record";
    @Resource
    private EsClientFactoryHealthcare esClientFactoryHealthcare;
    @Resource
    private VoiceConvertRecordPoMapper voiceConvertRecordPoMapper;
    @Override
    public VoiceConvertRecord find(VoiceIdentifier voiceIdentifier) {
        LambdaQueryWrapper<VoiceConvertRecordPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VoiceConvertRecordPo::getVoiceId,voiceIdentifier.getVoiceId());
        VoiceConvertRecordPo po = voiceConvertRecordPoMapper.selectOne(queryWrapper);
        return VoiceConvertRecordConverter.INSTANCE.po2Entity(po);
    }

    @Override
    public int remove(VoiceConvertRecord entity) {
        return 0;
    }

    @Override
    public int save(VoiceConvertRecord entity) {
        VoiceConvertRecordPo recordPo = VoiceConvertRecordConverter.INSTANCE.entity2Po(entity);

        if (Objects.isNull(recordPo.getId())) {
            JdhBasicPoConverter.initInsertBasicPo(recordPo);

            return voiceConvertRecordPoMapper.insert(recordPo);
        }else {

            LambdaUpdateWrapper<VoiceConvertRecordPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(VoiceConvertRecordPo::getVoiceId,entity.getVoiceId());
            return voiceConvertRecordPoMapper.update(recordPo, updateWrapper);
        }
    }

    @Override
    public void saveText(VoiceConvertRecord record) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("voiceId", record.getIdentifier().getVoiceId());
            map.put("text", record.getText());
            // 保存ES
            boolean res = esClientFactoryHealthcare.index(indexName, typeName, record.getIdentifier().serialize(), map);
            log.info("[VoiceConvertRecordRepositoryImpl -> saveText],res={}", res);
        }catch (Exception e){
            log.error("VoiceConvertRecordRepositoryImpl -> saveText error",e);
            throw new BusinessException(BusinessErrorCode.ES_PREPARE_ACTION_ERROR);
        }
    }

    @Override
    public VoiceConvertRecord findText(Long voiceId) {
        try{

            GetResponse response = esClientFactoryHealthcare.getDocument(indexName, typeName, String.valueOf(voiceId));
            String json = response.getSourceAsString();
            return JSON.parseObject(json, VoiceConvertRecord.class);
        }catch (Exception e){
            log.error("VoiceConvertRecordRepositoryImpl.getById error",e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<VoiceConvertRecord> list(Integer status, Date start, Date end) {
        LambdaQueryWrapper<VoiceConvertRecordPo> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(VoiceConvertRecordPo::getStatus, status)
                .gt(VoiceConvertRecordPo::getRefreshTime, start)
                .lt(VoiceConvertRecordPo::getRefreshTime, end);

        List<VoiceConvertRecordPo> pos = voiceConvertRecordPoMapper.selectList(queryWrapper);
        return VoiceConvertRecordConverter.INSTANCE.po2Entitys(pos);
    }
}
