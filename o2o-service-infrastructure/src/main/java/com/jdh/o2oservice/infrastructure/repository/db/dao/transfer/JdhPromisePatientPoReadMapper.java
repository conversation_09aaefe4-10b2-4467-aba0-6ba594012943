package com.jdh.o2oservice.infrastructure.repository.db.dao.transfer;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePatientPo;

/**
 * @ClassName JdhPromisePatientPoReadMapper
 * @Description
 * <AUTHOR>
 * @Date 2025/6/9 12:36
 **/
@DS("slave")
public interface JdhPromisePatientPoReadMapper extends BaseMapper<JdhPromisePatientPo> {
}