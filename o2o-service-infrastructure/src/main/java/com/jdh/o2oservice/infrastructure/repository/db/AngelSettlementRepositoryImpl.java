package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementUpdateContext;
import com.jdh.o2oservice.core.domain.settlement.model.*;
import com.jdh.o2oservice.core.domain.settlement.repository.AngelSettlementRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.AngelSettlementConvert;
import com.jdh.o2oservice.infrastructure.repository.db.dao.AngelSettlementDetailMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.AngelSettlementMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/13 5:57 下午
 * @Description:
 */
@Repository
@Slf4j
public class AngelSettlementRepositoryImpl implements AngelSettlementRepository{

    @Autowired
    private AngelSettlementMapper angelSettlementMapper;
    @Autowired
    private AngelSettlementDetailMapper angelSettlementDetailMapper;

    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;


    @Override
    public AngelSettlement find(AngelSettlementIdentifier angelSettlementIdentifier) {
        LambdaQueryWrapper<AngelSettlementPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AngelSettlementPo::getSettleId, angelSettlementIdentifier.getSettleId());
        AngelSettlementPo po = angelSettlementMapper.selectOne(queryWrapper);
        return AngelSettlementConvert.ins.convert2AngelSettlement(po);

    }


    @Override
    public Long save(AngelSettlement angelSettlement) {
        AngelSettlementPo angelSettlementPo = AngelSettlementConvert.ins.dao2AngelSettlementPo(angelSettlement);
        Integer offset;
        if (Objects.isNull(angelSettlementPo.getSettleId())) {
            angelSettlementPo.setSettleId(generateIdFactory.getId());
            offset = angelSettlementMapper.insert(angelSettlementPo);
        } else {
            LambdaQueryWrapper<AngelSettlementPo> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(AngelSettlementPo::getSettleId, angelSettlement.getSettleId());
            queryWrapper.eq(AngelSettlementPo::getYn, YnStatusEnum.YES.getCode());
            AngelSettlementPo exist = angelSettlementMapper.selectOne(queryWrapper);
            if (Objects.isNull(exist)) {
                throw new ArgumentsException(new DynamicErrorCode("100001", "数据不存在!"));
            }
            LambdaUpdateWrapper<AngelSettlementPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(AngelSettlementPo::getSettleId, angelSettlement.getSettleId())
                    .eq(AngelSettlementPo::getYn, YnStatusEnum.YES.getCode());
            angelSettlementPo.setVersion(exist.getVersion() + 1);
            angelSettlementPo.setCreateUser(null);
            angelSettlementPo.setCreateTime(null);
            offset = angelSettlementMapper.update(angelSettlementPo,updateWrapper);
        }
        return offset < 1 ? null : angelSettlementPo.getSettleId();
    }

    /**
     * 批量保存护士结算+明细
     *
     * @param angelSettlementList
     * @param angelSettlementDetailList
     * @return
     */
    @Override
    public Long batchSaveAngelSettlementAndDetail(List<AngelSettlement> angelSettlementList, List<AngelSettlementDetail> angelSettlementDetailList) {
        if(CollectionUtil.isNotEmpty(angelSettlementList)){
            List<AngelSettlementPo> angelSettlementPoList = AngelSettlementConvert.ins.angelSettlementsToPo(angelSettlementList);
            angelSettlementMapper.batchInsert(angelSettlementPoList);
        }
        if(CollectionUtil.isNotEmpty(angelSettlementDetailList)){
            List<AngelSettlementDetailPo> angelSettlementDetailPoList = AngelSettlementConvert.ins.angelSettlementDetailsToPo(angelSettlementDetailList);
            angelSettlementDetailMapper.batchInsert(angelSettlementDetailPoList);
        }
        return 1L;
    }

    @Override
    public List<AngelSettlement> querySettlementList(AngelSettlementQueryContext queryContext) {
        LambdaQueryWrapper<AngelSettlementPo> queryWrapper = AngelSettlementConvert.ins.getQueryWrapper(queryContext);
        List<AngelSettlementPo> res = angelSettlementMapper.selectList(queryWrapper);

        return AngelSettlementConvert.ins.dao2AngelSettlements(res);
    }


    @Override
    public BigDecimal querySettlementAmountTot(AngelSettlementQueryContext queryContext) {
        LambdaQueryWrapper<AngelSettlementPo> queryWrapper = AngelSettlementConvert.ins.getQueryWrapper(queryContext);
        BigDecimal tot = angelSettlementMapper.selectList(queryWrapper).stream()
                .map(AngelSettlementPo::getSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        return tot;
    }

    /**
     * 根据字段聚合金额查询
     * @param queryContext
     * @return
     */
    @Override
    public List<SettlementDetailGroupMoneySum> querySettlementDetailGroupMoneySum(AngelSettlementQueryContext queryContext) {
        QueryWrapper<AngelSettlementPo> wrapper = new QueryWrapper<>();
        wrapper.select(String.format("%s, sum(settleAmount) as sum", queryContext.getColumn()))
                .groupBy(queryContext.getColumn());
        LambdaQueryWrapper<AngelSettlementPo> queryWrapper = AngelSettlementConvert.ins.getQueryWrapper(wrapper.lambda(), queryContext);

        List<Map<String, Object>> maps = angelSettlementMapper.selectMaps(queryWrapper);
        List<SettlementDetailGroupMoneySum> result = new ArrayList<>();
        for (Map<String, Object> objectMap : maps) {
            Object o = objectMap.get(queryContext.getColumn());
            if (Objects.nonNull(o)) {
                Object sum = objectMap.getOrDefault("sum", "0");
                BigDecimal money = NumberUtils.createBigDecimal(sum.toString());
                result.add(SettlementDetailGroupMoneySum.builder().groupKeyValue(o).sum(money).build());
            }
        }
        return result;
    }

    /**
     * 根据条件聚合月份数据列表
     * @param queryContext
     * @return
     */
    @Override
    public List<SettlementMonthGroupDetailSum> querySettlementMonthGroupDetailSum(AngelSettlementQueryContext queryContext) {
        QueryWrapper<AngelSettlementPo> wrapper = new QueryWrapper<>();
        wrapper.select("DATE_FORMAT(create_time, '%Y-%m') AS month, count(1) as total_num")
                .groupBy("month");
        LambdaQueryWrapper<AngelSettlementPo> queryWrapper = AngelSettlementConvert.ins.getQueryWrapper(wrapper.lambda(), queryContext);
        queryWrapper.orderByDesc(AngelSettlementPo::getCreateTime);

        List<Map<String, Object>> maps = angelSettlementMapper.selectMaps(queryWrapper);
        List<SettlementMonthGroupDetailSum> result = new ArrayList<>();
        for (Map<String, Object> objectMap : maps) {
            Object o = objectMap.get("month");
            if (Objects.nonNull(o)) {
                Object total = objectMap.getOrDefault("total_num", "0");
                Integer totalNum = NumberUtils.createInteger(total.toString());
                result.add(SettlementMonthGroupDetailSum.builder().yearMonth(String.valueOf(o)).totalNum(totalNum).build());
            }
        }
        return result;
    }

    @Override
    public Page<AngelSettlement> querySettlementPage(AngelSettlementQueryContext queryContext) {
        log.info("AngelSettlementRepositoryImpl querySettlementPage queryContext={}", JSON.toJSONString(queryContext));
        Page<AngelSettlementPo> param = new Page<>(queryContext.getPageNum(), queryContext.getPageSize());

        LambdaQueryWrapper<AngelSettlementPo> queryWrapper = AngelSettlementConvert.ins.getQueryWrapper(queryContext);
        IPage<AngelSettlementPo> page = angelSettlementMapper.selectPage(param, queryWrapper);
        return AngelSettlementConvert.ins.dao2AngelSettlementPage(page);
    }

    /**
     *
     * @param queryContext
     * @return
     */
    @Override
    public Integer querySettlementCount(AngelSettlementQueryContext queryContext) {
        LambdaQueryWrapper<AngelSettlementPo> queryWrapper = AngelSettlementConvert.ins.getQueryWrapper(queryContext);
        return angelSettlementMapper.selectCount(queryWrapper);
    }


    @Override
    public List<AngelSettlementDetail> querySettlementDetailList(AngelSettlementQueryContext queryContext) {
        LambdaQueryWrapper<AngelSettlementDetailPo> queryWrapper = AngelSettlementConvert.ins.getDetailQueryWrapper(queryContext);
        List<AngelSettlementDetailPo> res = angelSettlementDetailMapper.selectList(queryWrapper);
        return AngelSettlementConvert.ins.detailDao2AngelSettlements(res);
    }

    /**
     *
     * @param angelSettlementUpdateContext
     */
    @Override
    public Integer updateSettleStatusBySettleIdList(AngelSettlementUpdateContext angelSettlementUpdateContext){
        LambdaUpdateWrapper<AngelSettlementPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(AngelSettlementPo::getSettleStatus, angelSettlementUpdateContext.getSettleTargetStatus())
                .in(CollUtil.isNotEmpty(angelSettlementUpdateContext.getSettleIdList()), AngelSettlementPo::getSettleId, angelSettlementUpdateContext.getSettleIdList())
                .eq(AngelSettlementPo::getSettleStatus, angelSettlementUpdateContext.getSettleStatus());
        return angelSettlementMapper.update(null, updateWrapper);
    }

    @Override
    public Integer updateSyncStatusBySettleId(AngelSettlementUpdateContext angelSettlementUpdateContext) {
        LambdaUpdateWrapper<AngelSettlementPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(AngelSettlementPo::getSyncStatus, angelSettlementUpdateContext.getSyncStatus())
                .in(CollUtil.isNotEmpty(angelSettlementUpdateContext.getSettleIdList()), AngelSettlementPo::getSettleId, angelSettlementUpdateContext.getSettleIdList());
        return angelSettlementMapper.update(null, updateWrapper);
    }

}
