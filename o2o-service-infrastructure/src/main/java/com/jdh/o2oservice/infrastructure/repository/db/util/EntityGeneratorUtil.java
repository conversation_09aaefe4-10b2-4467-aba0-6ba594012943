package com.jdh.o2oservice.infrastructure.repository.db.util;

import lombok.Data;
import java.io.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MySQL表结构映射实体类生成工具
 * 支持将生成的实体类直接写入文件
 */
public class EntityGeneratorUtil {

    // MySQL到Java类型映射
    private static final Map<String, String> TYPE_MAPPING = new HashMap<>();

    static {
        TYPE_MAPPING.put("tinyint", "Integer");
        TYPE_MAPPING.put("smallint", "Integer");
        TYPE_MAPPING.put("mediumint", "Integer");
        TYPE_MAPPING.put("int", "Integer");
        TYPE_MAPPING.put("integer", "Integer");
        TYPE_MAPPING.put("bigint", "Long");
        TYPE_MAPPING.put("float", "Float");
        TYPE_MAPPING.put("double", "Double");
        TYPE_MAPPING.put("decimal", "BigDecimal");
        TYPE_MAPPING.put("numeric", "BigDecimal");
        TYPE_MAPPING.put("char", "String");
        TYPE_MAPPING.put("varchar", "String");
        TYPE_MAPPING.put("text", "String");
        TYPE_MAPPING.put("longtext", "String");
        TYPE_MAPPING.put("date", "Date");
        TYPE_MAPPING.put("datetime", "Date");
        TYPE_MAPPING.put("timestamp", "Date");
        TYPE_MAPPING.put("time", "Date");
        TYPE_MAPPING.put("blob", "byte[]");
        TYPE_MAPPING.put("binary", "byte[]");
    }

    /**
     * 将下划线命名转换为驼峰命名（首字母大写）
     */
    public static String toCamelCase(String name, boolean firstLetterUpper) {
        if (name == null || name.isEmpty()) {
            return name;
        }

        StringBuilder result = new StringBuilder();
        String[] parts = name.split("_");

        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            if (part.isEmpty()) continue;

            if (i == 0 && !firstLetterUpper) {
                result.append(part);
            } else {
                result.append(Character.toUpperCase(part.charAt(0)))
                        .append(part.substring(1).toLowerCase());
            }
        }

        return result.toString();
    }

    /**
     * 解析MySQL字段类型，返回对应的Java类型
     */
    public static String parseFieldType(String mysqlType) {
        if (mysqlType == null || mysqlType.isEmpty()) {
            return "Object";
        }

        // 去除括号内的内容，如varchar(255) -> varchar
        String type = mysqlType.split("\\(")[0].toLowerCase();

        // 处理空格
        type = type.trim().split(" ")[0];

        // 检查是否为无符号类型
        boolean unsigned = mysqlType.toLowerCase().contains("unsigned");

        // 处理无符号bigint特殊情况
        if ("bigint".equals(type) && unsigned) {
            return "BigInteger";
        }

        return TYPE_MAPPING.getOrDefault(type, "Object");
    }

    /**
     * 解析CREATE TABLE语句，提取表名和字段信息
     */
    public static TableSchema parseCreateTable(String sql) {
        TableSchema schema = new TableSchema();

        // 提取表名
        Pattern tablePattern = Pattern.compile("CREATE TABLE `(.*?)`", Pattern.CASE_INSENSITIVE);
        Matcher tableMatcher = tablePattern.matcher(sql);
        if (tableMatcher.find()) {
            schema.setTableName(tableMatcher.group(1));
        }

        // 提取表注释
        Pattern commentPattern = Pattern.compile("COMMENT='(.*?)'", Pattern.CASE_INSENSITIVE);
        Matcher commentMatcher = commentPattern.matcher(sql);
        if (commentMatcher.find()) {
            schema.setTableComment(commentMatcher.group(1));
        }

        // 提取字段定义部分
        Pattern fieldSectionPattern = Pattern.compile("\\((.*?)\\)\\s*[^)]*$", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher fieldSectionMatcher = fieldSectionPattern.matcher(sql);
        if (fieldSectionMatcher.find()) {
            String fieldSection = fieldSectionMatcher.group(1);

            // 分割字段定义
            String[] fieldLines = fieldSection.split(",\\s*");

            // 提取主键字段
            Set<String> primaryKeys = new HashSet<>();
            for (String line : fieldLines) {
                if (line.trim().toUpperCase().startsWith("PRIMARY KEY")) {
                    Pattern pkPattern = Pattern.compile("PRIMARY KEY\\s*\\(`(.*?)`\\)", Pattern.CASE_INSENSITIVE);
                    Matcher pkMatcher = pkPattern.matcher(line);
                    if (pkMatcher.find()) {
                        primaryKeys.add(pkMatcher.group(1));
                    }
                }
            }

            // 解析每个字段
            for (String line : fieldLines) {
                if (line.trim().toUpperCase().startsWith("PRIMARY KEY") ||
                        line.trim().toUpperCase().startsWith("UNIQUE KEY") ||
                        line.trim().toUpperCase().startsWith("KEY")) {
                    continue; // 跳过索引定义
                }

                // 解析字段定义
                Pattern fieldPattern = Pattern.compile(
                        "`(.*?)`\\s+(.*?)(?:\\s+COLLATE.*?)?(?:\\s+DEFAULT\\s+.*?)?(?:\\s+COMMENT\\s+'(.*?)')?,?\\s*$",
                        Pattern.CASE_INSENSITIVE
                );
                Matcher fieldMatcher = fieldPattern.matcher(line);

                if (fieldMatcher.find()) {
                    String fieldName = fieldMatcher.group(1);
                    String fieldType = fieldMatcher.group(2).trim();
                    String fieldComment = fieldMatcher.group(3);

                    boolean isPrimaryKey = primaryKeys.contains(fieldName);
                    boolean isAutoIncrement = line.toUpperCase().contains("AUTO_INCREMENT");

                    TableField field = new TableField(fieldName, fieldType, isPrimaryKey, isAutoIncrement, fieldComment);
                    schema.addField(field);
                }
            }
        }

        return schema;
    }

    /**
     * 生成实体类代码
     */
    public static String generateEntityClass(TableSchema schema, String packageName) {
        String className = toCamelCase(schema.getTableName(), true) + "Po";

        StringBuilder sb = new StringBuilder();

        // 添加包声明
        if (packageName != null && !packageName.isEmpty()) {
            sb.append("package ").append(packageName).append(";\n\n");
        }

        // 添加导入语句
        Set<String> imports = new LinkedHashSet<>();
        imports.add("com.baomidou.mybatisplus.annotation.*");
        imports.add("lombok.Data");

        // 检查需要导入的类型
        for (TableField field : schema.getFields()) {
            String javaType = parseFieldType(field.getType());
            switch (javaType) {
                case "BigDecimal":
                    imports.add("java.math.BigDecimal");
                    break;
                case "BigInteger":
                    imports.add("java.math.BigInteger");
                    break;
                case "Date":
                    imports.add("java.util.Date");
                    break;
            }
        }

        // 添加导入语句
        for (String importItem : imports) {
            sb.append("import ").append(importItem).append(";\n");
        }
        sb.append("\n");

        // 添加类注释
        if (schema.getTableComment() != null && !schema.getTableComment().isEmpty()) {
            sb.append("/**\n");
            sb.append(" * ").append(schema.getTableComment()).append("\n");
            sb.append(" */\n");
        }

        // 添加类注解
        sb.append("@Data\n");
        sb.append("@TableName(value = \"").append(schema.getTableName()).append("\", autoResultMap = true)\n");

        // 类定义
        sb.append("public class ").append(className).append(" {\n\n");

        // 添加字段
        for (TableField field : schema.getFields()) {
            // 字段注释
            if (field.getComment() != null && !field.getComment().isEmpty()) {
                sb.append("    /**\n");
                sb.append("     * ").append(field.getComment()).append("\n");
                sb.append("     */\n");
            }

            // 字段注解
            if (field.isPrimaryKey()) {
                if (field.isAutoIncrement()) {
                    sb.append("    @TableId(type = IdType.AUTO)\n");
                } else {
                    sb.append("    @TableId\n");
                }
            } else {
                sb.append("    @TableField(value = \"").append(field.getName()).append("\")\n");
            }

            // 字段定义
            String fieldName = toCamelCase(field.getName(), false);
            String javaType = parseFieldType(field.getType());
            sb.append("    private ").append(javaType).append(" ").append(fieldName).append(";\n\n");
        }

        sb.append("}");

        return sb.toString();
    }

    /**
     * 将实体类写入文件
     */
    public static boolean writeEntityToFile(String content, String className, String outputDir) {
        try {
            // 创建包目录结构
            String packagePath = "";

            // 创建输出目录
            File dir = new File(outputDir + File.separator + packagePath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 创建文件
            File file = new File(dir, className + ".java");
            try (FileWriter writer = new FileWriter(file)) {
                writer.write(content);
            }

            System.out.println("实体类已生成: " + file.getAbsolutePath());
            return true;
        } catch (IOException e) {
            System.err.println("写入文件时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 表结构信息类
     */
    @Data
    public static class TableSchema {
        private String tableName;
        private String tableComment;
        private List<TableField> fields = new ArrayList<>();

        public void addField(TableField field) {
            this.fields.add(field);
        }
    }

    /**
     * 表字段信息类
     */
    @Data
    public static class TableField {
        private String name;
        private String type;
        private boolean isPrimaryKey;
        private boolean isAutoIncrement;
        private String comment;

        public TableField(String name, String type, boolean isPrimaryKey, boolean isAutoIncrement, String comment) {
            this.name = name;
            this.type = type;
            this.isPrimaryKey = isPrimaryKey;
            this.isAutoIncrement = isAutoIncrement;
            this.comment = comment;
        }

    }

    // 使用示例
    public static void main(String[] args) {
        String sql = "CREATE TABLE `jdh_virtual_number_sms_record` (\n" +
                "  `id` bigint(19) NOT NULL AUTO_INCREMENT COMMENT '主键',\n" +
                "  `user_pin` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户pin',\n" +
                "  `angel_pin` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '服务者pin',\n" +
                "  `promise_id` bigint(19) DEFAULT NULL COMMENT '履约单ID',\n" +
                "  `source_voucher_id` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '服务兑换的来源id，兑换id/orderId/outerOrderId',\n" +
                "  `sms_id` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '短信唯一标识',\n" +
                "  `bind_id` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '绑定关系ID',\n" +
                "  `call_no` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '真实发送方号码',\n" +
                "  `peer_no` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '真实接收方号码',\n" +
                "  `extension_no` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分机号',\n" +
                "  `sender_show` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发送方显示号码',\n" +
                "  `receiver_show` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '接收方显示号码',\n" +
                "  `sms_time` datetime DEFAULT NULL COMMENT '短信发送时间',\n" +
                "  `sms_number` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '实际转发短信条数：140字节或70汉字为1条短信，长短信会拆分为多条下发',\n" +
                "  `sms_result` tinyint(4) DEFAULT NULL COMMENT '发送结果：0：成功，1：失败',\n" +
                "  `sms_content` varchar(2048) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户发送的短信内容，仅在短信托收模式下提供',\n" +
                "  `user_data` varchar(2048) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户附属信息',\n" +
                "  `ext_json` varchar(1024) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展数据',\n" +
                "  `yn` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否有效 1有效 0无效',\n" +
                "  `create_time` datetime NOT NULL COMMENT '创建时间',\n" +
                "  `update_time` datetime NOT NULL COMMENT '修改时间',\n" +
                "  PRIMARY KEY (`id`) USING BTREE,\n" +
                "  UNIQUE KEY `uniq_sms_id` (`sms_id`) USING BTREE,\n" +
                "  KEY `idx_promise_id` (`promise_id`) USING BTREE\n" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='虚拟号发送短信记录表'";

        // 解析SQL
        TableSchema schema = parseCreateTable(sql);

        // 生成实体类
        String packageName = "com.jdh.o2oservice.infrastructure.repository.db.po";
        String entityCode = generateEntityClass(schema, packageName);

        // 将实体类写入文件
        String className = toCamelCase(schema.getTableName(), true) + "Po";
        // 替换为您的输出目录
        String outputDir = "o2o-service-infrastructure/src/main/java/com/jdh/o2oservice/infrastructure/repository/db/po";
        boolean success = writeEntityToFile(entityCode, className, outputDir);

        if (success) {
            System.out.println("实体类生成成功！");
        } else {
            System.out.println("实体类生成失败！");
        }
    }
}