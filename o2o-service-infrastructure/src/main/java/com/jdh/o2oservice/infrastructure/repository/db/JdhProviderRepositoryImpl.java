package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.core.domain.provider.model.JdhProvider;
import com.jdh.o2oservice.core.domain.provider.repository.db.JdhProviderRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhProviderConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhProviderPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhProviderPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 供应商（机构）仓储--新表
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
@Repository
@Slf4j
public class JdhProviderRepositoryImpl implements JdhProviderRepository {

    /**
     * 商家（机构）
     */
    @Resource
    private JdhProviderPoMapper jdhProviderPoMapper;

    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * 分页查询
     *
     * @param jdhProvider jdhProvider
     * @return page
     */
    @Override
    public Page<JdhProvider> queryPage(JdhProvider jdhProvider) {
        LambdaQueryWrapper<JdhProviderPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(jdhProvider.getProviderId() != null, JdhProviderPo::getProviderId, jdhProvider.getProviderId())
                .like(StringUtils.isNotBlank(jdhProvider.getProviderName()), JdhProviderPo::getProviderName, jdhProvider.getProviderName())
                .eq(jdhProvider.getProvinceId() != null, JdhProviderPo::getProvinceId, jdhProvider.getProvinceId())
                .eq(jdhProvider.getCityId() != null, JdhProviderPo::getCityId, jdhProvider.getCityId())
                .eq(jdhProvider.getCountyId() != null, JdhProviderPo::getCountyId, jdhProvider.getCountyId())
                .eq(jdhProvider.getProviderStatus() != null, JdhProviderPo::getProviderStatus, jdhProvider.getProviderStatus())
                .eq(JdhProviderPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhProviderPo::getCreateTime);
        Page<JdhProviderPo> param = new Page<>(jdhProvider.getPageNum(), jdhProvider.getPageSize());
        IPage<JdhProviderPo> jdhProviderPoPage = jdhProviderPoMapper.selectPage(param, queryWrapper);
        List<JdhProvider> jdhProviders = JdhProviderConverter.INSTANCE.poToModel(jdhProviderPoPage.getRecords());
        return JdhBasicPoConverter.initPage(jdhProviderPoPage, jdhProviders);
    }

    /**
     * 查询详情
     *
     * @param jdhProvider jdhProvider
     * @return page
     */
    @Override
    public JdhProvider query(JdhProvider jdhProvider) {
        LambdaQueryWrapper<JdhProviderPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(jdhProvider.getProviderId() != null, JdhProviderPo::getProviderId, jdhProvider.getProviderId())
                .eq(StringUtils.isNotBlank(jdhProvider.getProviderName()), JdhProviderPo::getProviderName, jdhProvider.getProviderName())
                .eq(JdhProviderPo::getYn, YnStatusEnum.YES.getCode());
        return JdhProviderConverter.INSTANCE.poToModel(jdhProviderPoMapper.selectOne(queryWrapper));
    }

    /**
     * 查询列表
     *
     * @param jdhProvider jdhProvider
     * @return page
     */
    @Override
    public List<JdhProvider> queryList(JdhProvider jdhProvider) {
        LambdaQueryWrapper<JdhProviderPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(jdhProvider.getProviderId() != null, JdhProviderPo::getProviderId, jdhProvider.getProviderId())
                .like(StringUtils.isNotBlank(jdhProvider.getProviderName()), JdhProviderPo::getProviderName, jdhProvider.getProviderName())
                .eq(jdhProvider.getProvinceId() != null, JdhProviderPo::getProvinceId, jdhProvider.getProvinceId())
                .eq(jdhProvider.getCityId() != null, JdhProviderPo::getCityId, jdhProvider.getCityId())
                .eq(jdhProvider.getCountyId() != null, JdhProviderPo::getCountyId, jdhProvider.getCountyId())
                .eq(jdhProvider.getProviderStatus() != null, JdhProviderPo::getProviderStatus, jdhProvider.getProviderStatus())
                .in(CollUtil.isNotEmpty(jdhProvider.getProviderIds()), JdhProviderPo::getProviderId, jdhProvider.getProviderIds())
                .eq(JdhProviderPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhProviderPo::getCreateTime);
        return JdhProviderConverter.INSTANCE.poToModel(jdhProviderPoMapper.selectList(queryWrapper));
    }

    /**
     * 保存
     *
     * @param jdhProvider jdhProvider
     * @return page
     */
    @Override
    public Boolean save(JdhProvider jdhProvider) {
        JdhProviderPo po = JdhProviderConverter.INSTANCE.modelToPo(jdhProvider);
        Date now = new Date();
        if (po.getProviderStatus() == null) {
            po.setProviderStatus(1);
        }
        po.setProviderId(generateIdFactory.getId());
        po.setCreateUser(jdhProvider.getCreateUser());
        po.setCreateTime(now);
        po.setUpdateUser(jdhProvider.getUpdateUser());
        po.setUpdateTime(now);
        po.setYn(YnStatusEnum.YES.getCode());
        return jdhProviderPoMapper.insert(po) > 0;
    }

    /**
     * 更新
     *
     * @param jdhProvider jdhProvider
     * @return page
     */
    @Override
    public Boolean update(JdhProvider jdhProvider) {
        LambdaUpdateWrapper<JdhProviderPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StringUtils.isNotBlank(jdhProvider.getProviderName()), JdhProviderPo::getProviderName, jdhProvider.getProviderName())
                .set(StringUtils.isNotBlank(jdhProvider.getContactName()), JdhProviderPo::getContactName, jdhProvider.getContactName())
                .set(StringUtils.isNotBlank(jdhProvider.getContactPhone()), JdhProviderPo::getContactPhone, jdhProvider.getContactPhone())
                .set(StringUtils.isNotBlank(jdhProvider.getAddress()), JdhProviderPo::getAddress, jdhProvider.getAddress())
                .set(jdhProvider.getProvinceId() != null, JdhProviderPo::getProvinceId, jdhProvider.getProvinceId())
                .set(StringUtils.isNotBlank(jdhProvider.getProvinceName()), JdhProviderPo::getProvinceName, jdhProvider.getProvinceName())
                .set(jdhProvider.getCityId() != null, JdhProviderPo::getCityId, jdhProvider.getCityId())
                .set(StringUtils.isNotBlank(jdhProvider.getCityName()), JdhProviderPo::getCityName, jdhProvider.getCityName())
                .set(jdhProvider.getCountyId() != null, JdhProviderPo::getCountyId, jdhProvider.getCountyId())
                .set(StringUtils.isNotBlank(jdhProvider.getCountyName()), JdhProviderPo::getCountyName, jdhProvider.getCountyName())
                .set(jdhProvider.getLat() != null, JdhProviderPo::getLat, jdhProvider.getLat())
                .set(jdhProvider.getLng() != null, JdhProviderPo::getLng, jdhProvider.getLng())
                .set(StringUtils.isNotBlank(jdhProvider.getNetChannelNo()), JdhProviderPo::getNetChannelNo, jdhProvider.getNetChannelNo())
                .set(jdhProvider.getProviderStatus() != null, JdhProviderPo::getProviderStatus, jdhProvider.getProviderStatus())
                .setSql("`version` = `version` + 1")
                .set(JdhProviderPo::getUpdateUser, jdhProvider.getUpdateUser())
                .eq(JdhProviderPo::getProviderId, jdhProvider.getProviderId());
        return jdhProviderPoMapper.update(null, updateWrapper) > 0;
    }
}
