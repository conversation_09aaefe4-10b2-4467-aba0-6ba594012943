package com.jdh.o2oservice.infrastructure.rpc.quickcheckmedicine;

import com.jd.medicine.b2c.app.domain.RxClientDTO;

import com.jd.medicine.b2c.app.domain.quickcheck.QuickCheckDrugParamDTO;
import com.jd.medicine.b2c.app.domain.quickcheck.QuickCheckParamDTO;
import com.jd.medicine.b2c.app.domain.quickcheck.QuickCheckResultDTO;
import com.jd.medicine.b2c.app.outexport.QuickCheckExport;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jd.medicine.b2c.trade.center.export.domain.rxnewprescriptive.addcart.AddCartResultDTO;
import com.jd.medicine.b2c.trade.center.export.domain.rxnewprescriptive.addcart.RxNewPrescriptAddCartParam;
import com.jd.medicine.b2c.trade.center.export.rxnewprescriptive.RxNewPrescriptiveExport;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo.*;
import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.rpc.QuickCheckMedicineRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/18
 */
@Component
@Slf4j
public class QuickCheckMedicineRpcImpl implements QuickCheckMedicineRpc {


    /**
     * 自动注入的快速检查导出服务对象，用于处理一键购药跳转链接等操作。
     */
    @Autowired
    private QuickCheckExport quickCheckExport;

    /**
     * 自动注入的新处方导出服务对象，用于处理添加处方药到购物车等操作。
     */
    @Autowired
    private RxNewPrescriptiveExport rxNewPrescriptiveExport;
    
    /**
     * 存储客户端IP地址的静态变量，用于记录请求来源。
     */
    public static String clientIp = "127.0.0.1";

    /**
     * 存储客户端名称的静态变量，用于标识请求来源。
     */
    public static String clientName = "jdh-o2o-service";

    /**
     * 一键购药跳转链接方法
     *
     * @param quickCheckMedicineBo 快速检查药品信息对象
     * @return 快速检查结果对象
     */
    @Override
    @LogAndAlarm
    public QuickCheckResultBo quickCheckJumpLink(QuickCheckMedicineBo quickCheckMedicineBo) {
        QuickCheckParamDTO quickCheckParamDTO = convert(quickCheckMedicineBo);
        log.info("QuickCheckMedicineRpcImpl->quickCheckJumpLink,quickCheckMedicineBo={}", JsonUtil.toJSONString(quickCheckMedicineBo));
        JsfResult<QuickCheckResultDTO> result = quickCheckExport.quickCheckJumpLink(quickCheckParamDTO,packRxClientDTO());
        log.info("QuickCheckMedicineRpcImpl->quickCheckJumpLink,result={}", JsonUtil.toJSONString(result));
        if (Objects.nonNull(result) && result.isSuccess()){
            return convert(result.getData());
        }
        return null;
    }

    /**
     * 添加处方药到购物车
     *
     * @param rxNewPrescriptAddCartBo 处方药添加到购物车的请求参数对象
     * @return 添加结果
     */
    @Override
    @LogAndAlarm
    public AddCartResultBo addCartForPrescriptV2(RxNewPrescriptAddCartBo rxNewPrescriptAddCartBo) {
        RxNewPrescriptAddCartParam param = convert(rxNewPrescriptAddCartBo);
        JsfResult<AddCartResultDTO> result = rxNewPrescriptiveExport.addCartForPrescriptV2(param,packDomainRxClientDTO());
        log.info("QuickCheckMedicineRpcImpl->addCartForPrescriptV2,result={}", JsonUtil.toJSONString(result));
        if (Objects.nonNull(result) && result.isSuccess()){
             return convert(result.getData());
        }
        return null;
    }




    /**
     * 将 QuickCheckMedicineBo 对象转换为 QuickCheckParamDTO 对象。
     * @param quickCheckMedicineBo 一个包含待转换信息的 QuickCheckMedicineBo 对象。
     * @return 转换后的 QuickCheckParamDTO 对象。
     */
    private QuickCheckParamDTO convert(QuickCheckMedicineBo quickCheckMedicineBo){
        QuickCheckParamDTO quickCheckParamDTO = new QuickCheckParamDTO();
        quickCheckParamDTO.setDetectionImgList(quickCheckMedicineBo.getDetectionImgList());
        quickCheckParamDTO.setPid(String.valueOf(quickCheckMedicineBo.getPatientId()));
        quickCheckParamDTO.setPerformanceId(quickCheckMedicineBo.getPerformanceId());
        quickCheckParamDTO.setUniqueSign(quickCheckMedicineBo.getUniqueSign());
        quickCheckParamDTO.setUserPin(quickCheckMedicineBo.getUserPin());
        quickCheckParamDTO.setDetectionResultList(quickCheckMedicineBo.getDetectionResultList());
        quickCheckParamDTO.setDrugParamDTOList(convert(quickCheckMedicineBo.getQuickCheckDrugBos()));
        return quickCheckParamDTO;
    }

    /**
     * 将 QuickCheckDrugBo 对象转换为 QuickCheckDrugParamDTO 对象。
     * @param quickCheckDrugBo 要转换的 QuickCheckDrugBo 对象。
     * @return 转换后的 QuickCheckDrugParamDTO 对象。
     */
    private QuickCheckDrugParamDTO convert(QuickCheckDrugBo quickCheckDrugBo){
        QuickCheckDrugParamDTO quickCheckDrugParamDTO = new QuickCheckDrugParamDTO();
        quickCheckDrugParamDTO.setGenericName(quickCheckDrugBo.getGenericName());
        quickCheckDrugParamDTO.setNum(quickCheckDrugBo.getNum());
        quickCheckDrugParamDTO.setSpecification(quickCheckDrugBo.getSpecification());
        return quickCheckDrugParamDTO;
    }

    /**
     * 将 QuickCheckDrugBo 列表转换为 QuickCheckDrugParamDTO 列表。
     * @param quickCheckDrugBos 需要转换的 QuickCheckDrugBo 对象列表。
     * @return 转换后的 QuickCheckDrugParamDTO 对象列表。
     */
    private List<QuickCheckDrugParamDTO> convert(List<QuickCheckDrugBo> quickCheckDrugBos){
        if (CollectionUtils.isEmpty(quickCheckDrugBos)){
            return null;
        }
        List<QuickCheckDrugParamDTO> res = Lists.newArrayList();
        for (QuickCheckDrugBo quickCheckDrugBo : quickCheckDrugBos){
            res.add(convert(quickCheckDrugBo));
        }
        return res;
    }

    /**
     * 将 QuickCheckResultDTO 转换为 QuickCheckResultBo。
     * @param quickCheckResultDTO 要转换的 QuickCheckResultDTO 对象。
     * @return 转换后的 QuickCheckResultBo 对象。
     */
    private QuickCheckResultBo convert(QuickCheckResultDTO quickCheckResultDTO){
        QuickCheckResultBo quickCheckResultBo = new QuickCheckResultBo();
        quickCheckResultBo.setJumpLink(quickCheckResultDTO.getJumpLink());
        return quickCheckResultBo;
    }

    /**
     * 打包 RxClientDTO 对象。
     * @return 打包后的 RxClientDTO 对象。
     */
    private RxClientDTO packRxClientDTO(){
        RxClientDTO rxClientDTO = new RxClientDTO();
        rxClientDTO.setClient(clientName);
        rxClientDTO.setReqUuid(UUID.randomUUID().toString());
        rxClientDTO.setServerIp(clientIp);
        return rxClientDTO;
    }

    /**
     * 将 RxNewPrescriptAddCartBo 对象转换为 RxNewPrescriptAddCartParam 对象。
     * @param rxNewPrescriptAddCartBo 待转换的 RxNewPrescriptAddCartBo 对象。
     * @return 转换后的 RxNewPrescriptAddCartParam 对象。
     */
    private  RxNewPrescriptAddCartParam convert(RxNewPrescriptAddCartBo rxNewPrescriptAddCartBo) {
        RxNewPrescriptAddCartParam rxNewPrescriptAddCartParam = new RxNewPrescriptAddCartParam();
        rxNewPrescriptAddCartParam.setAddCartChannel(rxNewPrescriptAddCartBo.getAddCartChannel());
        rxNewPrescriptAddCartParam.setPrescriptId(rxNewPrescriptAddCartBo.getPrescriptId());
        rxNewPrescriptAddCartParam.setFlags(rxNewPrescriptAddCartBo.getFlags());
        rxNewPrescriptAddCartParam.setPin(rxNewPrescriptAddCartBo.getPin());
        rxNewPrescriptAddCartParam.setUserAgent(rxNewPrescriptAddCartBo.getAgent());
        return rxNewPrescriptAddCartParam;
    }

    /**
     * 将域对象 RxClientDTO 打包成 DTO 对象。
     * @return 打包后的 RxClientDTO 对象。
     */
    private com.jd.medicine.b2c.trade.center.export.domain.rxnewprescriptive.RxClientDTO packDomainRxClientDTO(){
        com.jd.medicine.b2c.trade.center.export.domain.rxnewprescriptive.RxClientDTO rxClientDTO
                = new com.jd.medicine.b2c.trade.center.export.domain.rxnewprescriptive.RxClientDTO();
        rxClientDTO.setClient(clientName);
        rxClientDTO.setReqUuid(UUID.randomUUID().toString());
        rxClientDTO.setServerIp(clientIp);
        return rxClientDTO;
    }

    /**
     * 将 AddCartResultDTO 转换为 AddCartResultBo。
     * @param addCartResultDTO 转换的源对象。
     * @return 转换后的目标对象。
     */
    private AddCartResultBo convert(AddCartResultDTO addCartResultDTO){
        AddCartResultBo addCartResultBo = new AddCartResultBo();
        addCartResultBo.setSuccess(addCartResultDTO.getSuccess());
        addCartResultBo.setBusinessMap(addCartResultDTO.getBusinessMap());
        return addCartResultBo;
    }

}
