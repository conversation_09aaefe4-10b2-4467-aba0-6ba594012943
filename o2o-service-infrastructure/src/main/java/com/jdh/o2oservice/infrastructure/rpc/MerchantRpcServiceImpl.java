package com.jdh.o2oservice.infrastructure.rpc;

import com.jd.health.xfyl.merchant.export.dto.AppointResultDTO;
import com.jd.health.xfyl.merchant.export.param.AppointmentParam;
import com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService;
import com.jd.health.xfyl.open.export.dto.QuickPushInfoResultDTO;
import com.jd.health.xfyl.open.export.service.outer.QuickCheckThirdExportService;
import com.jd.medicine.b2c.base.export.domain.JsfResult;
import com.jdh.o2oservice.base.annotation.AutoTestSupport;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.medpromise.rpc.MerchantRpcService;
import com.jdh.o2oservice.base.exception.errorcode.BaseDomainErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.jd.health.xfyl.open.export.param.check.QuickCheckPushInfoParam;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 商家端RPCServiceImpl
 * @Author: wangpengfei144
 * @Date: 2024/4/28
 */
@Slf4j
@Service
public class MerchantRpcServiceImpl implements MerchantRpcService {

    /**
     * 商家端预约RPC
     */
    @Resource
    private XfylMerchantAppointApiExportService xfylMerchantAppointApiExportService;

    /**
     * quickCheckThirdExportService
     */
    @Resource
    private QuickCheckThirdExportService quickCheckThirdExportService;

    /**
     * 预约
     *
     * @param appointmentParam appointmentParam
     * @return result
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.MerchantRpcServiceImpl.appointment")
    @AutoTestSupport(whiteKey = "jdAppointmentId")
    public AppointResultDTO appointment(AppointmentParam appointmentParam) {
        appointmentParam.setCallSystem("o2o");
        JsfResult<AppointResultDTO> appointment = xfylMerchantAppointApiExportService.appointment(appointmentParam);
        if (Objects.nonNull(appointment) && appointment.isSuccess()){
            return appointment.getData();
        }
        return null;
    }

    /**
     * @param quickPushInfoParam
     * @return
     */
    @Override
    @LogAndAlarm
    public QuickPushInfoResultDTO quickCheckAppoint(QuickCheckPushInfoParam quickPushInfoParam) {
        JsfResult<QuickPushInfoResultDTO> res = quickCheckThirdExportService.quickCheckAppoint(quickPushInfoParam);
        if (Objects.nonNull(res)){
            if(res.isSuccess()){
                return res.getData();
            }else {
                throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR,res.getMsg());
            }
        }
        throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
    }

    /**
     * 快检推送信息(绑码之外)
     * 2.export —> service  —> rpc 调用过程
     *
     * @param appointmentParam
     * @returnXfylMerchantBusinessRpcImpl
     */
    @Override
    @LogAndAlarm
    public QuickPushInfoResultDTO quickCheckPushInfo(QuickCheckPushInfoParam appointmentParam) {
        JsfResult<QuickPushInfoResultDTO> res = quickCheckThirdExportService.quickCheckPushInfo(appointmentParam);
        if (Objects.nonNull(res)){
            if(res.isSuccess()){
                return res.getData();
            }else {
                throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR,res.getMsg());
            }
        }
        throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
    }

}
