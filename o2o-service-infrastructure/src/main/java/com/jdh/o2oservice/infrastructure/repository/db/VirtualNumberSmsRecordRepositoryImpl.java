package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import com.jdh.o2oservice.core.domain.support.securitynumber.model.SmsRecord;
import com.jdh.o2oservice.core.domain.support.securitynumber.repository.SmsRecordRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhVirtualNumberSmsRecordPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhVirtualNumberSmsRecordPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhVirtualNumberSmsRecordPo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: maoxianglin1
 * @Date: 2025/9/3 11:18
 * @Description:
 **/
@Component
@Slf4j
public class VirtualNumberSmsRecordRepositoryImpl implements SmsRecordRepository {

    @Resource
    private JdhVirtualNumberSmsRecordPoMapper jdhVirtualNumberSmsRecordPoMapper;

    /**
     * 保存
     * @param entity
     * @return
     */
    @Override
    public int save(SmsRecord entity) {
        JdhVirtualNumberSmsRecordPo jdhVirtualNumberSmsRecordPo = JdhVirtualNumberSmsRecordPoConverter.INSTANCE.convertToSmsRecordPo(entity);
        JdhBasicPoConverter.initInsertBasicTimePo(jdhVirtualNumberSmsRecordPo);
        return jdhVirtualNumberSmsRecordPoMapper.insert(jdhVirtualNumberSmsRecordPo);
    }

    /**
     * 列表查询
     * @param query
     * @return
     */
    @Override
    public List<SmsRecord> queryList(SmsRecord query) {
        LambdaQueryWrapper<JdhVirtualNumberSmsRecordPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getPromiseId()), JdhVirtualNumberSmsRecordPo::getPromiseId, query.getPromiseId());
        queryWrapper.eq(Objects.nonNull(query.getSourceVoucherId()), JdhVirtualNumberSmsRecordPo::getSourceVoucherId, query.getSourceVoucherId());
        queryWrapper.eq(Objects.nonNull(query.getSmsId()), JdhVirtualNumberSmsRecordPo::getSmsId, query.getSmsId());
        queryWrapper.eq(StringUtils.isNotBlank(query.getAngelPin()), JdhVirtualNumberSmsRecordPo::getAngelPin, query.getAngelPin());
        queryWrapper.eq(Objects.nonNull(query.getSmsResult()), JdhVirtualNumberSmsRecordPo::getSmsResult, query.getSmsResult());
        queryWrapper.eq(JdhVirtualNumberSmsRecordPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhVirtualNumberSmsRecordPo> jdhVirtualNumberSmsRecordPos = jdhVirtualNumberSmsRecordPoMapper.selectList(queryWrapper);
        return JdhVirtualNumberSmsRecordPoConverter.INSTANCE.convertPoToSmsRecordList(jdhVirtualNumberSmsRecordPos);
    }

    /**
     * 更新
     * @param entity
     * @return
     */
    @Override
    public int updateById(SmsRecord entity) {
        JdhVirtualNumberSmsRecordPo jdhVirtualNumberSmsRecordPo = JdhVirtualNumberSmsRecordPoConverter.INSTANCE.convertToSmsRecordPo(entity);
        return jdhVirtualNumberSmsRecordPoMapper.updateById(jdhVirtualNumberSmsRecordPo);
    }

    /**
     * 根据短信id查询
     * @param smsId 短信唯一id
     * @return smsRecord
     */
    @Override
    public SmsRecord querySmsRecordBySmsId(String smsId) {
        LambdaQueryWrapper<JdhVirtualNumberSmsRecordPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhVirtualNumberSmsRecordPo::getSmsId, smsId);
        queryWrapper.eq(JdhVirtualNumberSmsRecordPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhVirtualNumberSmsRecordPo> jdhVirtualNumberSmsRecordPos = jdhVirtualNumberSmsRecordPoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(jdhVirtualNumberSmsRecordPos)){
            return null;
        }
        List<SmsRecord> smsRecords = JdhVirtualNumberSmsRecordPoConverter.INSTANCE.convertPoToSmsRecordList(jdhVirtualNumberSmsRecordPos);
        return smsRecords.get(0);
    }
}
