<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdh.o2oservice.infrastructure.repository.db.dao.JdhMedicalPromisePoMapper">


    <resultMap id="BaseResultMap"
               type="com.jdh.o2oservice.infrastructure.repository.db.po.JdhMedicalPromisePo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="medical_promise_id" jdbcType="BIGINT" property="medicalPromiseId"/>
        <result column="vertical_code" jdbcType="VARCHAR" property="verticalCode"/>
        <result column="service_type" jdbcType="VARCHAR" property="serviceType"/>
        <result column="service_id" jdbcType="BIGINT" property="serviceId"/>
        <result column="user_pin" jdbcType="VARCHAR" property="userPin"/>
        <result column="promise_id" jdbcType="BIGINT" property="promiseId"/>
        <result column="voucher_id" jdbcType="BIGINT" property="voucherId"/>
        <result column="outer_id" jdbcType="VARCHAR" property="outerId"/>
        <result column="promise_patient_id" jdbcType="BIGINT" property="promisePatientId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="specimen_code" jdbcType="VARCHAR" property="specimenCode"/>
        <result column="service_item_id" jdbcType="VARCHAR" property="serviceItemId"/>
        <result column="service_item_name" jdbcType="VARCHAR" property="serviceItemName"/>
        <result column="provider_id" jdbcType="BIGINT" property="providerId"/>
        <result column="station_id" jdbcType="VARCHAR" property="stationId"/>
        <result column="station_address" jdbcType="VARCHAR" property="stationAddress"/>
        <result column="station_name" jdbcType="VARCHAR" property="stationName"/>
        <result column="station_phone" jdbcType="VARCHAR" property="stationPhone"/>
        <result column="flag" jdbcType="VARCHAR" property="flag"/>
        <result column="report_status" jdbcType="INTEGER" property="reportStatus"/>
        <result column="freeze" jdbcType="INTEGER" property="freeze"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="branch" jdbcType="VARCHAR" property="branch"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="merge_medical_id" jdbcType="BIGINT" property="mergeMedicalId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, medical_promise_id, vertical_code, service_type, service_id, user_pin,
        promise_id, voucher_id, outer_id,promise_patient_id, status,
        specimen_code, service_item_id, service_item_name, provider_id,
        station_id, station_address, station_name, station_phone, report_status,
        freeze, version, branch, check_time,
        yn, create_user, update_user, create_time, update_time,merge_medical_id,flag
    </sql>

    <update id="updateBatch" >
        <foreach collection="jdhMedicalPromisePos" item="item" index="index" separator=";">
            update jdh_medical_promise
            <set>
                <if test="item.freeze != null">
                    freeze = #{item.freeze},
                </if>
                <if test="item.outerId != null">
                    outer_id = #{item.outerId},
                </if>
                <if test="item.stationId != null">
                    station_id = #{item.stationId},
                </if>
                <if test="item.stationName != null">
                    station_name = #{item.stationName},
                </if>
                <if test="item.providerId != null">
                    provider_id = #{item.providerId},
                </if>
                <if test="item.stationAddress != null">
                    station_address = #{item.stationAddress},
                </if>
                <if test="item.stationPhone != null">
                    station_phone = #{item.stationPhone},
                </if>
                version = version+1
            </set>

            <where>
                medical_promise_id = #{item.medicalPromiseId}
            </where>
        </foreach>
    </update>

    <update id="updateBatchFreeze">
        update jdh_medical_promise
        <set>
            <if test="medicalPromiseFreezeBO.freeze != null">
                freeze = #{medicalPromiseFreezeBO.freeze},
            </if>
            version = version+1
        </set>
        <where>
            medical_promise_id in
            <foreach collection="medicalPromiseFreezeBO.medicalPromiseIds" item="medicalPromiseId" index="index"
                     open="(" close=")" separator=",">
                #{medicalPromiseId}
            </foreach>
        </where>

    </update>

    <select id="listByPatients" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_medical_promise
        where  promise_id = #{param.promiseId,jdbcType=BIGINT}
        and (promise_patient_id, service_id) in
        <foreach collection="param.medicalPromiseNestedQueries" index="index" item="item" open="(" close=")" separator=",">
            (#{item.promisePatientId,jdbcType=BIGINT}, #{item.serviceId,jdbcType=BIGINT})
        </foreach>
    </select>

    <select id="queryTimeSummary" resultType="com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseSummary">
        SELECT
            mp.medical_promise_id,
            mp.station_name,
            mp.station_id,
            mp.service_item_name,
            mp.service_item_id,
            mp.delivery_finish_time,
            mp.check_time,
            mp.test_time,
            mp.test_finish_time,
            mp.report_time,
            sir.test_duration as test_duration,
            (
                UNIX_TIMESTAMP(mp.test_finish_time) - UNIX_TIMESTAMP(mp.)
            ) as testCostSecond,
            case
                (
                    UNIX_TIMESTAMP(mp.test_finish_time) - UNIX_TIMESTAMP(mp.delivery_finish_time)
                ) &gt; sir.test_duration * 60
            when 1 then 1
            else 0
            end as testTimeOut,

            case
                (
                    UNIX_TIMESTAMP(mp.report_time) - UNIX_TIMESTAMP(mp.delivery_finish_time)
                ) &gt; sir.test_duration * 60
            when 0 then 1
            else 0
            end as testNoTimeOut,

            (
                UNIX_TIMESTAMP(mp.test_time) - UNIX_TIMESTAMP(mp.check_time)
            ) as upCostSecond,
            case
                (
                    UNIX_TIMESTAMP(mp.test_time) - UNIX_TIMESTAMP(mp.check_time)
                ) &gt; 900
            when 1 then 1
            else 0
            end as upTimeOut,

            case
                (
                    UNIX_TIMESTAMP(mp.test_time) - UNIX_TIMESTAMP(mp.check_time)
                ) &gt; 900
            when 0 then 1
            else 0
            end as upNoTimeOut,

            (
                UNIX_TIMESTAMP(mp.test_finish_time) - UNIX_TIMESTAMP(mp.report_time)
            ) as exportCostSecond,

            case
                (
                    UNIX_TIMESTAMP(mp.test_finish_time) - UNIX_TIMESTAMP(mp.report_time)
                ) &gt; 600
            when 1 then 1
            else 0
            end as exportTimeOut,

            case
                (
                    UNIX_TIMESTAMP(mp.test_finish_time) - UNIX_TIMESTAMP(mp.report_time)
                ) &gt; 600
            when 0 then 1
            else 0
            end as exportNoTimeOut

        from
            jdh_medical_promise mp
        left join
            jdh_station_service_item_rel sir on mp.service_item_id = sir.service_item_id
            and mp.station_id = sir.station_id
        where
        1=1
        <if test="queryTimeSummaryQuery.createTimeStart != null">
            and mp.create_time &gt; #{queryTimeSummaryQuery.createTimeStart},
        </if>

        <if test="queryTimeSummaryQuery.createTimeEnd != null">
            and mp.create_time &lt; #{queryTimeSummaryQuery.createTimeStart},
        </if>

        <if test="queryTimeSummaryQuery.medicalPromiseId != null">
            and mp.medical_promise_id = #{queryTimeSummaryQuery.medicalPromiseId},
        </if>

        <if test="queryTimeSummaryQuery.stationId != null">
            and mp.station_id = #{queryTimeSummaryQuery.stationId},
        </if>
            and mp.test_time is not null
    </select>

</mapper>