<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePoMapper">


    <resultMap id="BaseResultMap"
               type="com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo">
        <result column="vertical_code" jdbcType="VARCHAR" property="verticalCode"/>
        <result column="service_type" jdbcType="VARCHAR" property="serviceType"/>
        <result column="user_pin" jdbcType="VARCHAR" property="userPin"/>
        <result column="promise_id" jdbcType="BIGINT" property="promiseId"/>
        <result column="voucher_id" jdbcType="BIGINT" property="voucherId"/>
        <result column="freeze" jdbcType="INTEGER" property="freeze"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="branch" jdbcType="VARCHAR" property="branch"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        vertical_code, service_type, user_pin, promise_id, voucher_id,
        freeze, version, branch, yn, create_user,update_user,create_time,update_time
    </sql>


    <select id="timeoutWrittenOffList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_promise
        where promise_status in
        <foreach item="itm" index="idx" collection="statusList" open="(" separator="," close=")">
            #{itm}
        </foreach>
        and create_time >=  #{startTime,jdbcType=TIMESTAMP}
        and create_time   <![CDATA[ < #{endTime,jdbcType=TIMESTAMP}  ]]>
        and vertical_code = #{verticalCode,jdbcType=VARCHAR }
        and yn = 1
        limit #{limitStart},#{limitSize}
    </select>


    <select id="closeOfExpirationList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_promise
        where promise_status in
        <foreach item="itm" index="idx" collection="statusList" open="(" separator="," close=")">
            #{itm}
        </foreach>
        and expire_date >=  #{startTime,jdbcType=TIMESTAMP}
        and expire_date   <![CDATA[ < #{endTime,jdbcType=TIMESTAMP}  ]]>
        and vertical_code = #{verticalCode,jdbcType=VARCHAR }
        and yn = 1
        limit #{limitStart},#{limitSize}
    </select>

    <select id="appointmentDayBefore" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jdh_promise
        where promise_status in
        <foreach item="itm" index="idx" collection="statusList" open="(" separator="," close=")">
            #{itm}
        </foreach>
        and appointment_start_time >=  #{startTime,jdbcType=TIMESTAMP}
        and appointment_start_time   <![CDATA[ < #{endTime,jdbcType=TIMESTAMP}  ]]>
        and vertical_code = #{verticalCode,jdbcType=VARCHAR }
        and yn = 1
        limit #{limitStart},#{limitSize}
    </select>

    <select id="ping" resultType="java.lang.Integer">
        SELECT 1 FROM DUAL
    </select>

</mapper>