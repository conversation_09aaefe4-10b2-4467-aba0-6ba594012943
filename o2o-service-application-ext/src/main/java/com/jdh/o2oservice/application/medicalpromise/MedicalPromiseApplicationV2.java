package com.jdh.o2oservice.application.medicalpromise;

import com.jdh.o2oservice.export.medicalpromise.cmd.AnomalyOperateCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedPromiseSubStatusCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseCallbackCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseDispatchCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.AbnormalTemplateDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseCallbackResultDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.AbnormalTemplateRequest;
import com.jdh.o2oservice.export.medicalpromise.query.QueryMedPromiseAndShipRequest;
import com.jdh.o2oservice.export.report.dto.RangeValueDTO;
import com.jdh.o2oservice.export.report.dto.StructQuickReportResultIndicatorDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/4
 */
public interface MedicalPromiseApplicationV2 {

    /**
     * 实验室派发
     * @param medicalPromiseDispatchCmd
     * @return
     */
    Boolean storeDisPatch(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd);

    /**
     * 通过履约单查询运单信息
     * @param queryMedPromiseAndShipRequest
     * @return
     */
    List<MedicalPromiseDTO> queryMedPromiseAndShip(QueryMedPromiseAndShipRequest queryMedPromiseAndShipRequest);


    /**
     * 异常操作
     * @return
     */
    Boolean sampleAnomalyOperate(AnomalyOperateCmd anomalyOperateCmd);


    /**
     * 检测单状态回传
     * @param medicalPromiseCallbackCmd
     * @return
     */
    MedicalPromiseCallbackResultDTO medicalPromiseCallBack(MedicalPromiseCallbackCmd medicalPromiseCallbackCmd);


    /**
     * 根据请求查询异常模板
     * @param templateRequest 异常模板请求对象
     * @return 异常模板DTO对象
     */
    AbnormalTemplateDTO queryAbnormalTemplate(AbnormalTemplateRequest templateRequest);

    RangeValueDTO getCtRangeValue(StructQuickReportResultIndicatorDTO indicatorDTO);

    List<RangeValueDTO>  packNumberRangeValue(StructQuickReportResultIndicatorDTO indicatorDTO, String expression );


    /**
     * 同意让步检测
     * @param
     */
    void exeConcessionCheck(MedPromiseSubStatusCmd cmd);

}
