package com.jdh.o2oservice.application.product.service.impl;

import org.junit.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.PrecisionModel;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 测试拓扑异常修复功能
 */
public class ProductApplicationImplTopologyTest {

    private final GeometryFactory geometryFactory = new GeometryFactory(new PrecisionModel(PrecisionModel.FLOATING), 4326);
    private final ProductApplicationImpl productApplication = new ProductApplicationImpl();

    @Test
    public void testEnsureClosed_WithOpenPolygon() {
        // 测试开放多边形（未闭合）
        Coordinate[] openCoords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0),
            new Coordinate(1, 1),
            new Coordinate(0, 1)
        };
        
        Coordinate[] closedCoords = ProductApplicationImpl.ensureClosed(openCoords);
        
        // 验证结果是闭合的
        assertEquals(5, closedCoords.length);
        assertTrue(closedCoords[0].equals2D(closedCoords[closedCoords.length - 1]));
    }

    @Test
    public void testEnsureClosed_WithClosedPolygon() {
        // 测试已闭合多边形
        Coordinate[] closedCoords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0),
            new Coordinate(1, 1),
            new Coordinate(0, 1),
            new Coordinate(0, 0)
        };
        
        Coordinate[] result = ProductApplicationImpl.ensureClosed(closedCoords);
        
        // 验证结果长度不变
        assertEquals(5, result.length);
        assertTrue(result[0].equals2D(result[result.length - 1]));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testEnsureClosed_WithInsufficientPoints() {
        // 测试点数不足的情况
        Coordinate[] insufficientCoords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0)
        };
        
        ProductApplicationImpl.ensureClosed(insufficientCoords);
    }

    @Test
    public void testPolygonOverlap_WithValidPolygons() {
        // 测试两个重叠的多边形
        Coordinate[] polygon1 = {
            new Coordinate(0, 0),
            new Coordinate(2, 0),
            new Coordinate(2, 2),
            new Coordinate(0, 2)
        };
        
        Coordinate[] polygon2 = {
            new Coordinate(1, 1),
            new Coordinate(3, 1),
            new Coordinate(3, 3),
            new Coordinate(1, 3)
        };
        
        boolean overlaps = productApplication.polygonOverlap(geometryFactory, polygon1, polygon2);
        assertTrue("两个重叠的多边形应该返回true", overlaps);
    }

    @Test
    public void testPolygonOverlap_WithInvalidCoordinates() {
        // 测试无效坐标的情况
        Coordinate[] invalidCoords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0)
        };
        
        Coordinate[] validCoords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0),
            new Coordinate(1, 1),
            new Coordinate(0, 1)
        };
        
        // 应该返回false而不是抛出异常
        boolean overlaps = productApplication.polygonOverlap(geometryFactory, invalidCoords, validCoords);
        assertFalse("无效坐标应该返回false", overlaps);
    }

    @Test
    public void testProcessAdjacentDuplicates_WithTopologyIssues() {
        // 测试可能导致拓扑异常的几何对象
        List<Coordinate[]> problematicCoordinates = new ArrayList<>();
        
        // 添加一个可能有问题的多边形（自相交）
        Coordinate[] selfIntersecting = {
            new Coordinate(0, 0),
            new Coordinate(2, 2),
            new Coordinate(2, 0),
            new Coordinate(0, 2)
        };
        problematicCoordinates.add(selfIntersecting);
        
        // 添加一个正常的多边形
        Coordinate[] normal = {
            new Coordinate(1, 1),
            new Coordinate(3, 1),
            new Coordinate(3, 3),
            new Coordinate(1, 3)
        };
        problematicCoordinates.add(normal);
        
        // 应该能够处理而不抛出异常
        List<Coordinate[]> result = productApplication.processAdjacentDuplicates(geometryFactory, problematicCoordinates);
        assertNotNull("结果不应该为null", result);
        assertTrue("结果应该包含至少一个元素", result.size() >= 1);
    }

    @Test
    public void testProcessAdjacentDuplicates_WithEmptyInput() {
        // 测试空输入
        List<Coordinate[]> emptyList = new ArrayList<>();
        List<Coordinate[]> result = productApplication.processAdjacentDuplicates(geometryFactory, emptyList);
        
        assertNotNull("结果不应该为null", result);
        assertTrue("空输入应该返回空结果", result.isEmpty());
    }

    @Test
    public void testProcessAdjacentDuplicates_WithSingleElement() {
        // 测试单个元素
        List<Coordinate[]> singleElement = new ArrayList<>();
        Coordinate[] coords = {
            new Coordinate(0, 0),
            new Coordinate(1, 0),
            new Coordinate(1, 1),
            new Coordinate(0, 1)
        };
        singleElement.add(coords);
        
        List<Coordinate[]> result = productApplication.processAdjacentDuplicates(geometryFactory, singleElement);
        
        assertNotNull("结果不应该为null", result);
        assertEquals("单个元素应该返回单个结果", 1, result.size());
    }

    @Test
    public void testProcessAdjacentDuplicatesWithIntersection_OverlappingPolygons() {
        // 测试重叠多边形的交集线条保留
        List<Coordinate[]> overlappingPolygons = new ArrayList<>();

        // 第一个多边形
        Coordinate[] polygon1 = {
            new Coordinate(0, 0),
            new Coordinate(3, 0),
            new Coordinate(3, 3),
            new Coordinate(0, 3)
        };
        overlappingPolygons.add(polygon1);

        // 第二个多边形（与第一个有重叠）
        Coordinate[] polygon2 = {
            new Coordinate(2, 2),
            new Coordinate(5, 2),
            new Coordinate(5, 5),
            new Coordinate(2, 5)
        };
        overlappingPolygons.add(polygon2);

        ProductApplicationImpl.GeometryProcessResult result =
            productApplication.processAdjacentDuplicatesWithIntersection(geometryFactory, overlappingPolygons);

        assertNotNull("结果不应该为null", result);
        assertNotNull("并集坐标不应该为null", result.getUnionCoordinates());
        assertNotNull("交集线条不应该为null", result.getIntersectionLines());

        // 验证有交集线条被保留
        assertTrue("应该有交集线条", result.getIntersectionLines().size() > 0);

        log.info("并集坐标数量: {}", result.getUnionCoordinates().size());
        log.info("交集线条数量: {}", result.getIntersectionLines().size());
    }

    @Test
    public void testProcessAdjacentDuplicatesWithIntersection_NonOverlappingPolygons() {
        // 测试不重叠多边形
        List<Coordinate[]> nonOverlappingPolygons = new ArrayList<>();

        // 第一个多边形
        Coordinate[] polygon1 = {
            new Coordinate(0, 0),
            new Coordinate(1, 0),
            new Coordinate(1, 1),
            new Coordinate(0, 1)
        };
        nonOverlappingPolygons.add(polygon1);

        // 第二个多边形（不重叠）
        Coordinate[] polygon2 = {
            new Coordinate(2, 2),
            new Coordinate(3, 2),
            new Coordinate(3, 3),
            new Coordinate(2, 3)
        };
        nonOverlappingPolygons.add(polygon2);

        ProductApplicationImpl.GeometryProcessResult result =
            productApplication.processAdjacentDuplicatesWithIntersection(geometryFactory, nonOverlappingPolygons);

        assertNotNull("结果不应该为null", result);
        assertEquals("不重叠的多边形应该保持原数量", 2, result.getUnionCoordinates().size());
        assertEquals("不重叠的多边形不应该有交集线条", 0, result.getIntersectionLines().size());
    }
}
