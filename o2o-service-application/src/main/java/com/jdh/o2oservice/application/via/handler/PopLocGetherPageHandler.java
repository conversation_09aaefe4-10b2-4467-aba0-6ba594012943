package com.jdh.o2oservice.application.via.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.DateConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.EnvTypeEnum;
import com.jdh.o2oservice.base.enums.RelationTypeEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.rpc.PopLocProviderRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.ServiceRpcBO;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.base.model.CredentialNumber;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.User;
import com.jdh.o2oservice.base.model.UserName;
import com.jdh.o2oservice.core.domain.support.basic.rpc.PopLocStoreInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.StoreRpcBO;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.*;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * POP_LOC模式预约聚合页
 *
 * <AUTHOR>
 * @date 2024/01/02
 */
@Slf4j
@Service
public class PopLocGetherPageHandler extends AbstractViaDataFillHandler implements MapAutowiredKey{


    /**
     * promiseRepository
     */
    @Autowired
    private PromiseRepository promiseRepository;

    /**
     * skuInfoRpc
     */
    @Resource
    private SkuInfoRpc skuInfoRpc;

    /**
     * popLocStoreInfoRpc
     */
    @Autowired
    private PopLocStoreInfoRpc popLocStoreInfoRpc;
    /**
     *
     */
    @Resource
    private PopLocProviderRpc popLocProviderRpc;

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return ViaPageEnum.APPOINT_GETHER.getScene() + "_" + BusinessModeEnum.POP_LOC.getCode();
    }

    /**
     * checkParam
     *
     * @param ctx ctx
     */
    private void checkParam(FillViaConfigDataContext ctx){
        //场景
        AssertUtils.hasText(ctx.getScene(), SupportErrorCode.VIA_CONFIG_NOT_EXIT);
        //页面类型
        AssertUtils.hasText(ctx.getPageType(),SupportErrorCode.GETHER_PAGE_TYPE_NOT_EXIT);
        AssertUtils.isFalse(ViaPageTypeEnum.contain(ctx.getPageType()),SupportErrorCode.GETHER_PAGE_TYPE_NOT_EXIT);

        //修改预约
        if(ViaPageTypeEnum.MODIFY_APPOINT.getType().equals(ctx.getPageType())){
            AssertUtils.hasText(ctx.getPromiseId(),"promiseId 信息缺失");
        }

        //先买后约
        if(ViaPageTypeEnum.BUY_FIRST.getType().equals(ctx.getPageType())){
            AssertUtils.hasText(ctx.getPromiseId(),"promiseId 信息缺失");
        }

        //重新预约
        if(ViaPageTypeEnum.RE_APPOINT.getType().equals(ctx.getPageType())){
            AssertUtils.hasText(ctx.getPromiseId(),"promiseId 信息缺失");
        }

        //买约一体
        if(ViaPageTypeEnum.BUY_APPOINT.getType().equals(ctx.getPageType())){
            AssertUtils.hasText(ctx.getServiceId(),"serviceId 信息缺失");
        }

    }

    /**
     * 填充数据
     * todo 复杂度
     * @param ctx ctx
     */
    @Override
    public void handle(FillViaConfigDataContext ctx) {
        log.info("PopLocGetherPageHandler handle ctx:{}",JSON.toJSONString(ctx));
        //入参基本校验
        checkParam(ctx);
        ViaConfig viaConfig = ctx.getViaConfig();
        // 聚合页要区分 pageType (买约一体、先买后预约、修改预约、重新预约)。进行基本信息回显
        //修改预约
        boolean modify = ViaPageTypeEnum.MODIFY_APPOINT.getType().equals(ctx.getPageType());
        //先买后约
        boolean buyFirst = ViaPageTypeEnum.BUY_FIRST.getType().equals(ctx.getPageType());
        //买约一体
        boolean buyAppoint = ViaPageTypeEnum.BUY_APPOINT.getType().equals(ctx.getPageType());
        //重新预约
        boolean reAppoint = ViaPageTypeEnum.RE_APPOINT.getType().equals(ctx.getPageType());

        //=========================== 基本信息
        //1、履约单信息，只有修改预约，重新预约，先买后约才有
        JdhPromise jdhPromise = null;
        String promiseId = ctx.getPromiseId();
        if(modify || buyFirst || reAppoint){
            jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(Long.parseLong(promiseId)).build());
            if(Objects.isNull(jdhPromise)){
                throw new BusinessException(SupportErrorCode.VIA_PROMISE_INFO_NOT_EXIT);
            }
            //越权校验
            if(!ctx.getUserPin().equals(jdhPromise.getUserPin())){
                throw new BusinessException(SupportErrorCode.VIA_PROMISE_INFO_NOT_EXIT);
            }
        }
        log.info("PopLocGetherPageHandler handle jdhPromise:{}",JSON.toJSONString(jdhPromise));

        //门店信息
        String storeId = "";
        StoreRpcBO storeRpcBO = null;
        if(modify || reAppoint || buyAppoint || buyFirst){
            // 优先路径上的storeId
            PromiseStation store = Objects.isNull(jdhPromise) ? null : jdhPromise.getStore();
            storeId = StrUtil.isNotEmpty(ctx.getStoreId()) ? ctx.getStoreId() : Objects.isNull(store) ? "" : store.getStoreId();
            storeRpcBO = StrUtil.isEmpty(storeId) ? null : popLocStoreInfoRpc.queryById(Long.parseLong(storeId));
        }
        log.info("PopLocGetherPageHandler handle storeId:{},storeRpcBO:{}",storeId,JSON.toJSONString(storeRpcBO));

        //预约时间信息
        PromiseAppointmentTime appointmentTime = null;
        if(modify){
            appointmentTime = jdhPromise.getAppointmentTime();
        }
        if(buyAppoint && Objects.nonNull(ctx.getDateType()) && StrUtil.isNotEmpty(ctx.getAppointmentStartTime()) && StrUtil.isNotEmpty(ctx.getAppointmentEndTime())){
            appointmentTime = new PromiseAppointmentTime();
            appointmentTime.setDateType(ctx.getDateType());
            appointmentTime.setAppointmentStartTime(LocalDateTime.parse(ctx.getAppointmentStartTime(), DateTimeFormatter.ofPattern(DateConstant.YMDHS)));
            appointmentTime.setAppointmentEndTime(LocalDateTime.parse(ctx.getAppointmentEndTime(), DateTimeFormatter.ofPattern(DateConstant.YMDHS)));
        }
        log.info("PopLocGetherPageHandler handle appointmentTime:{}",JSON.toJSONString(appointmentTime));

        //serviceId
        String serviceId = "";
        if(modify || buyFirst || reAppoint){
            serviceId = jdhPromise.getServices().get(0).getServiceId().toString();
        }
        if(buyAppoint){
            serviceId = ctx.getServiceId();
        }
        log.info("PopLocGetherPageHandler handle serviceId:{}",serviceId);

        //服务信息
        RpcSkuBO skuInfoRpcBo = skuInfoRpc.getCrsSkuBoBySkuId(serviceId);
        log.info("PopLocGetherPageHandler handle skuInfoRpcBo:{}",JSON.toJSONString(skuInfoRpcBo));

        //环境信息
        EnvTypeEnum envTypeEnum = EnvTypeEnum.get(ctx.getEnvType());
        log.info("PopLocGetherPageHandler handle envTypeEnum:{}",envTypeEnum);

        //公共提交参数
        Map<String, Object> actionCommonParams = new HashMap<>(NumConstant.NUM_4);
        actionCommonParams.put("verticalCode",viaConfig.getVerticalCode());
        actionCommonParams.put("serviceId",serviceId);
        actionCommonParams.put("serviceType",viaConfig.getServiceType());
        actionCommonParams.put("promiseId", promiseId);
        actionCommonParams.put("storeId", storeId);
        log.info("PopLocGetherPageHandler handle actionCommonParams:{}",JSON.toJSONString(actionCommonParams));

        //套餐信息
        // [{"outServiceId":"10096175641876","outServiceName":"测试商品","serviceId":10096175641876}]
        List<Map<String,Object>> services = new ArrayList<>();
        Map<String,Object> service = new HashMap<>(NumConstant.NUM_4);
        if (StringUtils.isNotBlank(ctx.getOutServiceId())){
            service.put("outServiceId",ctx.getOutServiceId());
        }else{
            service.put("outServiceId",serviceId);
        }
        service.put("outServiceName",skuInfoRpcBo.getSkuName());
        service.put("serviceId",serviceId);
        services.add(service);

        //===========================处理楼层信息
        List<ViaFloorInfo> floorList = viaConfig.getFloorList();
        Map<String, ViaFloorInfo> floorInfoMap = floorList.stream().collect(Collectors.toMap(ViaFloorInfo::getFloorCode, obj -> obj, (key1, key2) -> key1));
        //=========>>>>> 1、头图
        ViaFloorInfo viaFloorInfo = floorInfoMap.get(ViaFloorEnum.GETHER_SUMMARY_INFO.getFloorCode());
        ViaFloorConfig infoSummaryFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        infoSummaryFloorConfig.setMainTitle(skuInfoRpcBo.getSkuName());

        //=========>>>>> 2、预约人表单信息
        ViaFloorInfo appointUserFloorInfo = floorInfoMap.get(ViaFloorEnum.GETHER_APPOINT_USER_INFO.getFloorCode());
        List<ViaFloorConfig> appointUserFloorConfigList = appointUserFloorInfo.getFloorConfigList();
        Map<String, ViaFloorConfig> appointUserFloorConfigMap = appointUserFloorConfigList.stream().collect(Collectors.toMap(ViaFloorConfig::getFloorCode, obj -> obj, (key1, key2) -> key1));
        ViaFloorConfig submitFormFloorConfig = appointUserFloorConfigMap.get(ViaFloorEnum.GETHER_APPOINT_FORM_INFO.getFloorCode());
        List<ViaFormItem> formItemList = submitFormFloorConfig.getFormItemList();
        Iterator<ViaFormItem> formItemListIterator = formItemList.iterator();
        while (formItemListIterator.hasNext()){
            ViaFormItem viaFormItem = formItemListIterator.next();
            Map<String, Object> value = new HashMap<>(NumConstant.NUM_8);
            Map<String, Object> tempValue = new HashMap<>(NumConstant.NUM_4);

            User user = Optional.ofNullable(jdhPromise).map(JdhPromise::findOnlyPatient).orElseGet(JdhPromisePatient::new);

            //常用预约人
            if(ViaFormTypeEnum.COMMON_APPOINT_USER.getType().equals(viaFormItem.getFormType())){
                if(modify){
                    if(Objects.nonNull(user.getPatientId())){
                        tempValue.put("patientId", user.getPatientId());
                        value.put("user", tempValue);
                        viaFormItem.setValue(JSON.toJSONString(value));
                    }
                    viaFormItem.setDisabled(Boolean.TRUE);
                }
                viaFormItem.getAction().getParams().putAll(actionCommonParams);
            }

            //用户姓名
            if(modify && ViaFormTypeEnum.USER_NAME.getType().equals(viaFormItem.getFormType())){
                UserName userName = user.getUserName();
                if(Objects.nonNull(userName)){
                    tempValue.put("name", userName.encrypt());
                    tempValue.put("nameMask", userName.getName());
                    value.put("user",tempValue);
                    viaFormItem.setValue(JSON.toJSONString(value));
                }
                viaFormItem.setDisabled(Boolean.TRUE);
            }

            //电话
            if(ViaFormTypeEnum.TELEPHONE.getType().equals(viaFormItem.getFormType())){
                if(modify){
                    PhoneNumber phoneNumber = user.getPhoneNumber();
                    if(Objects.nonNull(phoneNumber)){
                        tempValue.put("phone", phoneNumber.encrypt());
                        tempValue.put("phoneMask", phoneNumber.mask());
                        value.put("user", tempValue);
                        viaFormItem.setValue(JSON.toJSONString(value));
                    }
                    viaFormItem.setDisabled(Boolean.TRUE);
                }
            }

            //验证码
            if(ViaFormTypeEnum.SMS_CODE.getType().equals(viaFormItem.getFormType())){
                if(modify){
                    formItemListIterator.remove();
                }else{
                    ViaActionInfo smsCodeAction = viaFormItem.getAction();
                    smsCodeAction.getParams().putAll(actionCommonParams);
                }
            }

            //证件号
            if(modify && ViaFormTypeEnum.ID_CARD_NO.getType().equals(viaFormItem.getFormType())){
                CredentialNumber credentialNum = user.getCredentialNum();
                if(Objects.nonNull(credentialNum)){
                    tempValue.put("credentialNo", credentialNum.encrypt());
                    tempValue.put("credentialNoMask", credentialNum.mask());
                    tempValue.put("credentialType", credentialNum.getCredentialType());
                    value.put("user", tempValue);
                    viaFormItem.setValue(JSON.toJSONString(value));
                }
                viaFormItem.setDisabled(Boolean.TRUE);
            }
            //性别
            if(ViaFormTypeEnum.GENDER.getType().equals(viaFormItem.getFormType())){
                if(modify){
                    tempValue.put("gender", user.getGender());
                    value.put("user", tempValue);
                    viaFormItem.setValue(JSON.toJSONString(value));
                    viaFormItem.setDisabled(Boolean.TRUE);
                }
            }
            //生日
            if(ViaFormTypeEnum.BIRTHDAY.getType().equals(viaFormItem.getFormType())){
                if(modify){
                    tempValue.put("birthday", user.getBirthday().getBirth());
                    value.put("user", tempValue);
                    viaFormItem.setValue(JSON.toJSONString(value));
                    viaFormItem.setDisabled(Boolean.TRUE);
                }
            }
            //婚否
            if(ViaFormTypeEnum.MARRIAGE.getType().equals(viaFormItem.getFormType())){
                if(modify){
                    if(Objects.nonNull(user.getMarriage())){
                        tempValue.put("marriage", user.getMarriage());
                        value.put("user", tempValue);
                        viaFormItem.setValue(JSON.toJSONString(value));
                    }
                    viaFormItem.setDisabled(Boolean.TRUE);
                }
            }
            //与本人关系
            if(ViaFormTypeEnum.RELATION.getType().equals(viaFormItem.getFormType())){
                if(modify){
                    if(Objects.nonNull(user.getRelativesType())){
                        tempValue.put("relativesType", user.getRelativesType());
                        tempValue.put("relationName", RelationTypeEnum.getEnumByType(user.getRelativesType()).getDesc());
                        value.put("user", tempValue);
                        viaFormItem.setValue(JSON.toJSONString(value));
                    }
                    viaFormItem.setDisabled(Boolean.TRUE);
                }
            }
        }

        //=========>>>>> 3、预约其他信息(区域、门店、套餐、时间)
        ViaFloorInfo appointInfoFloorInfo = floorInfoMap.get(ViaFloorEnum.GETHER_APPOINT_INFO.getFloorCode());
        List<ViaFloorConfig> appointInfoFloorConfigList = appointInfoFloorInfo.getFloorConfigList();
        Map<String, ViaFloorConfig> appointInfoFloorConfigMap = appointInfoFloorConfigList.stream().collect(Collectors.toMap(ViaFloorConfig::getFloorCode, obj -> obj, (key1, key2) -> key1));
        ViaFloorConfig appointInfoFloorConfig = appointInfoFloorConfigMap.get(ViaFloorEnum.GETHER_APPOINT_OTHER_INFO.getFloorCode());
        List<ViaFormItem> appointInfoFormList = appointInfoFloorConfig.getFormItemList();
        for (ViaFormItem viaFormItem : appointInfoFormList) {
            ViaActionInfo formItemAction = viaFormItem.getAction();
            //门店
            if(ViaFormTypeEnum.STORE.getType().equals(viaFormItem.getFormType())){
                String storeListUrl = formItemAction.getUrl();
                //修改预约 买约一体 不允许改门店
                if(modify || buyAppoint){
                    viaFormItem.setDisabled(Boolean.TRUE);
                }

                //https://pop-store-m.jd.com/addCart/list?appSource={0}&wareId={1}&venderId={2}&storeGroupId={3}&selStoreId={4}&lat={5}&lng={6}
                //门店id不为空
                //兼容前端跳出页面，选择store后回跳页面
                String storeList = MessageFormat.format(storeListUrl,
                        EnvTypeEnum.JD_TARO == envTypeEnum ? "jdHealthTaro" : "jdHealthH5",
                        serviceId,
                        skuInfoRpcBo.getVenderId().toString(),
                        skuInfoRpcBo.getLocGroupId(),
                        StrUtil.isEmpty(storeId) ? "" : storeId,
                        StrUtil.isEmpty(ctx.getLat()) ? "" : ctx.getLat(),
                        StrUtil.isEmpty(ctx.getLng()) ? "" : ctx.getLng());
                formItemAction.setUrl(storeList);
                viaFormItem.setValue(Objects.isNull(storeRpcBO) ? null : JSON.toJSONString(storeRpcBO));

            }
            //套餐
            if(ViaFormTypeEnum.PACKAGE.getType().equals(viaFormItem.getFormType())){
                //修改预约 买约一体 不允许改套餐
                if(modify || buyAppoint){
                    viaFormItem.setDisabled(Boolean.TRUE);
                }
                if(buyFirst || buyAppoint){
                    Map<String,Object> value = new HashMap<>(NumConstant.NUM_2);
                    if (StringUtils.isBlank(ctx.getOutServiceId())){
                        value.put("outServiceId",ctx.getServiceId());
                    }else{
                        value.put("outServiceId",ctx.getOutServiceId());
                    }
                    // 买约一体跳到预约聚合页面时候回显套餐名称
                    List<ServiceRpcBO> outServiceList = popLocProviderRpc.listGoods(serviceId, null, storeId);
                    outServiceList.stream().filter(e -> StringUtils.equals(e.getOutServiceId(), ctx.getOutServiceId())).findFirst().ifPresent(obj -> {
                        value.put("outServiceName", obj.dynamicGoodsName());
                    });

                    value.put("serviceId",ctx.getServiceId());
                    viaFormItem.setValue(JSON.toJSONString(value));
                }else if(modify || reAppoint){
                    PromiseService basicService = jdhPromise.findBasicService();
                    if (Objects.nonNull(basicService)){
                        Map<String,Object> value = new HashMap<>(NumConstant.NUM_2);
                        value.put("outServiceId",basicService.getOutServiceId());
                        value.put("outServiceName", basicService.getOutServiceName());
                        value.put("serviceId",basicService.getServiceId());
                        viaFormItem.setValue(JSON.toJSONString(value));
                    }
                }
                formItemAction.setParams(actionCommonParams);
            }
            //时间
            if(ViaFormTypeEnum.DATE.getType().equals(viaFormItem.getFormType())){
                if(Objects.nonNull(appointmentTime)){
                    viaFormItem.setValue(JSON.toJSONString(appointmentTime.buildAppointmentTimeFieldMap()));
                }
                Map<String,Object> params = new HashMap<>(actionCommonParams);
                params.put("storeId",storeId);
                formItemAction.setParams(params);
                if(buyAppoint){
                    viaFormItem.setDisabled(Boolean.TRUE);
                }
            }
        }

        //=========>>>>> 4、温馨提示

        //=========>>>>> 5、底部协议&按钮
        ViaFloorInfo footerButtonsFloorInfo = floorInfoMap.get(ViaFloorEnum.GETHER_FOOTER_BUTTONS.getFloorCode());
        List<ViaFloorConfig> footerButtonsFloorConfigList = footerButtonsFloorInfo.getFloorConfigList();
        ViaFloorConfig footerButtonsFloorConfig = footerButtonsFloorConfigList.get(0);
        List<ViaBtnInfo> btnList = footerButtonsFloorConfig.getBtnList();
        //买约一体
        if(buyAppoint){
            Iterator<ViaBtnInfo> iterator = btnList.iterator();
            while (iterator.hasNext()){
                ViaBtnInfo viaBtnInfo = iterator.next();
                ViaActionInfo btnAction = viaBtnInfo.getAction();
                //立即预约
                if (ViaBtnCodeEnum.BUY_APPOINT_SUBMIT_APPOINT_BTN.getCode().equals(viaBtnInfo.getCode())) {
                    //如果是买约一体，action要先掉草稿，后调获取结算页链接
                    btnAction.setType(ActionType.REQUEST.getCode());
                    Map<String, Object> params = new HashMap<>(actionCommonParams);
                    params.put("pt",ctx.getPt());
                    params.put("preSaleStart",ctx.getPreSaleStart());
                    params.put("bybtSceneStart",ctx.getBybtSceneStart());
                    params.put("services",services);
                    btnAction.setParams(params);
                }else{
                    iterator.remove();
                }
            }
        }

        //先买后约,或重新预约
        if(buyFirst || reAppoint){
            Iterator<ViaBtnInfo> iterator = btnList.iterator();
            while (iterator.hasNext()){
                ViaBtnInfo viaBtnInfo = iterator.next();
                ViaActionInfo btnAction = viaBtnInfo.getAction();
                //立即预约
                if (ViaBtnCodeEnum.BUY_FIRST_SUBMIT_APPOINT_BTN.getCode().equals(viaBtnInfo.getCode())) {
                    //如果是先买后约，action直接是提交预约
                    Map<String, Object> params = new HashMap<>(actionCommonParams);
                    params.put("promiseId",jdhPromise.getPromiseId().toString());
                    params.put("services",services);
                    btnAction.setParams(params);

                    ViaActionInfo nextAction = btnAction.getNextAction();
                    String url = nextAction.getUrl();
                    String jumpUrl = MessageFormat.format(url,promiseId);
                    nextAction.setUrl(jumpUrl);
                }else{
                    iterator.remove();
                }
            }
        }

        //修改预约
        if(modify){
            Map<String, Object> params = new HashMap<>(actionCommonParams);
            params.put("promiseId",jdhPromise.getPromiseId().toString());

            Iterator<ViaBtnInfo> iterator = btnList.iterator();
            while (iterator.hasNext()) {
                ViaBtnInfo viaBtnInfo = iterator.next();
                ViaActionInfo btnAction = viaBtnInfo.getAction();
                //修改提交
                if(ViaBtnCodeEnum.MODIFY_APPOINT_BTN.getCode().equals(viaBtnInfo.getCode())){
                    btnAction.setParams(params);

                    ViaActionInfo nextAction = btnAction.getNextAction();
                    String url = nextAction.getUrl();
                    String jumpUrl = MessageFormat.format(url,promiseId);
                    nextAction.setUrl(jumpUrl);
                } else{
                    iterator.remove();
                }
            }
        }

    }
}
