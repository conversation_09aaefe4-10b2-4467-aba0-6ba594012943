package com.jdh.o2oservice.application.promise.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseHistoryApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.promise.service.PromiseTimelineApplication;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.ServiceRecordStatusEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelIdentifier;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecord;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelServiceRecordRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelServiceRecordDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseHistoryRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.securitynumber.enums.SecurityNumberBizCallTypeEnum;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailForManDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkHistoryDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkDetailForManRequest;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseHistoryDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedPromiseHistoryRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDetailDto;
import com.jdh.o2oservice.export.promise.dto.PromiseTimelineDto;
import com.jdh.o2oservice.export.promise.enums.PromiseTimelineActionEnum;
import com.jdh.o2oservice.export.promise.enums.PromiseTimelineExtendFieldEnum;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.QueryPromiseTimelineRequest;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import com.jdh.o2oservice.export.support.query.QueryCallRecordRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.omg.CORBA.PRIVATE_MEMBER;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 履约单时间轴 应用服务
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Service
@Slf4j
public class PromiseTimelineApplicationImpl implements PromiseTimelineApplication {

    @Autowired
    private PromiseApplication promiseApplication;

    @Autowired
    private AngelWorkApplication angelWorkApplication;

    @Autowired
    private MedPromiseHistoryApplication medPromiseHistoryApplication;

    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    @Autowired
    private PromiseHistoryRepository promiseHistoryRepository;

    @Resource
    private AngelServiceRecordRepository angelServiceRecordRepository;

    @Resource
    private AngelTaskRepository angelTaskRepository;

    @Resource
    private CallRecordApplication callRecordApplication;

    @Resource
    private AngelRepository angelRepository;

    @Override
    public PromiseTimelineDto queryPromiseTimeline(QueryPromiseTimelineRequest request) {
        log.info("PromiseTimelineApplicationImpl -> queryPromiseTimeline request={}", JSON.toJSONString(request));

        Long promiseId = request.getPromiseId();

        PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(promiseId).build());
        if (Objects.isNull(promiseDto)) {
            log.warn("PromiseTimelineApplicationImpl -> queryPromiseTimeline 履约单不存在, promiseId={}", promiseId);
            throw new BusinessException(BusinessErrorCode.JDH_PROMISE_NOT_EXIST);
        }

        // 查询履约单历史记录
        List<JdhPromiseHistory> promiseHistoryList = findPromiseHistoryList(promiseId);

        // 查询服务者工单详情
        AngelWorkDetailForManDto angelWorkDetailForManDto = findAngelWorkDetailForManDto(promiseId);

        // 查询检测单历史记录
        List<MedPromiseHistoryDTO> medPromiseHistoryList = findMedPromiseHistoryList(promiseId);

        // 构建时间轴
        List<PromiseTimelineDetailDto> timelineDetailList = buildPromiseTimeline(promiseDto, promiseHistoryList, angelWorkDetailForManDto, medPromiseHistoryList);

        PromiseTimelineDto result = new PromiseTimelineDto();
        result.setPromiseTimelineDetailDtoList(timelineDetailList);

        log.info("PromiseTimelineApplicationImpl -> queryPromiseTimeline 成功, promiseId={}, timelineSize={}", promiseId, timelineDetailList != null ? timelineDetailList.size() : 0);

        return result;

    }

    /**
     * 查询服务者工单详情
     *
     * @param promiseId 履约单ID
     * @return AngelWorkDetailForManDto
     */
    private AngelWorkDetailForManDto findAngelWorkDetailForManDto(Long promiseId) {
        try {
            AngelWorkDetailForManRequest manQuery = new AngelWorkDetailForManRequest();
            manQuery.setPromiseId(promiseId);
//            manQuery.setMedPromiseId(medPromiseId);
            return angelWorkApplication.queryAngelWorkForMan(manQuery);
        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> findAngelWorkDetailForManDto error, promiseId={}", promiseId, e);
            return null;
        }
    }

    /**
     * 查询履约单历史记录
     *
     * @param promiseId 履约单ID
     * @return List<JdhPromiseHistory>
     */
    private List<JdhPromiseHistory> findPromiseHistoryList(Long promiseId) {
        try {
            List<JdhPromiseHistory> promiseHistories = promiseHistoryRepository.findList(PromiseHistoryRepQuery.builder().promiseId(promiseId).build());
            log.info("PromiseTimelineApplicationImpl -> findPromiseHistoryList promiseHistories:{}", JSON.toJSONString(promiseHistories));
            return promiseHistories;
        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> findPromiseHistoryList error, promiseId={}", promiseId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询检测单历史记录
     *
     * @param promiseId 履约单ID
     * @return List<MedPromiseHistoryDTO>
     */
    private List<MedPromiseHistoryDTO> findMedPromiseHistoryList(Long promiseId) {
        try {
            List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseRequest.builder().promiseId(promiseId).build());
            if (CollectionUtils.isEmpty(medicalPromiseDTOList)) {
                log.info("PromiseTimelineApplicationImpl -> findMedPromiseHistoryList medicalPromiseDTOList is empty, promiseId={}", promiseId);
                return Collections.emptyList();
            }
            return medPromiseHistoryApplication.queryMedPromiseHistoryList(MedPromiseHistoryRequest.builder().medicalPromiseIds(medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toSet())).build());
        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> findMedPromiseHistoryList error, promiseId={}", promiseId, e);
            return Collections.emptyList();
        }
    }


    /**
     * 构建履约时间轴
     *
     * @param promiseDto               履约单
     * @param promiseHistoryList       履约单历史记录
     * @param angelWorkDetailForManDto 服务者工单详情
     * @param medPromiseHistoryList    检测单历史记录
     * @return List<PromiseTimelineDetailDto>
     */
    private List<PromiseTimelineDetailDto> buildPromiseTimeline(PromiseDto promiseDto, List<JdhPromiseHistory> promiseHistoryList, AngelWorkDetailForManDto angelWorkDetailForManDto, List<MedPromiseHistoryDTO> medPromiseHistoryList) {

        try {
            List<PromiseTimelineDetailDto> timelineList = new ArrayList<>();

            // 1. 添加履约单历史事件
            if (CollUtil.isNotEmpty(promiseHistoryList)) {
                promiseHistoryList.forEach(history -> {
                    PromiseTimelineDetailDto promiseEvent = convertPromiseHistoryToTimeline(promiseDto, history);
                    if (promiseEvent != null) {
                        timelineList.add(promiseEvent);
                    }
                });
            }

            // 2. 添加服务者工单相关事件
            if (angelWorkDetailForManDto != null && CollUtil.isNotEmpty(angelWorkDetailForManDto.getWorkHistoryDtoList())) {
                angelWorkDetailForManDto.getWorkHistoryDtoList().forEach(history -> {
                    PromiseTimelineDetailDto workEvent = convertAngelWorkHistoryToTimeline(history, angelWorkDetailForManDto);
                    if (workEvent != null) {
                        timelineList.add(workEvent);
                    }
                });
            }

            // 3. 添加检测单历史事件
            if (CollUtil.isNotEmpty(medPromiseHistoryList)) {
                medPromiseHistoryList.forEach(history -> {
                    PromiseTimelineDetailDto medEvent = convertMedPromiseHistoryToTimeline(history);
                    if (medEvent != null) {
                        timelineList.add(medEvent);
                    }
                });
            }

            // 4. 评估单列表
            AngelServiceRecordDBQuery query = AngelServiceRecordDBQuery.builder()
                    .promiseId(promiseDto.getPromiseId())
                    .build();
            List<AngelServiceRecord> angelServiceRecordList = angelServiceRecordRepository.findList(query);
            if (CollectionUtils.isNotEmpty(angelServiceRecordList)) {
                AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
                angelTaskDBQuery.setTaskIds(angelServiceRecordList.stream().map(AngelServiceRecord::getTaskId).collect(Collectors.toList()));
                List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
                List<PromiseTimelineDetailDto> medEventList = convertAngelServiceRecordToTimeline(angelServiceRecordList, angelTaskList);
                if (CollectionUtils.isNotEmpty(medEventList)) {
                    timelineList.addAll(medEventList);
                }
            }

            // 5. 电话沟通记录
            QueryCallRecordRequest queryCallRecordRequest = new QueryCallRecordRequest();
            queryCallRecordRequest.setPromiseId(promiseDto.getPromiseId());
            List<CallRecordDto> callRecordDtos = callRecordApplication.queryCallRecordList(queryCallRecordRequest);
            if (CollectionUtils.isNotEmpty(callRecordDtos)) {
                timelineList.addAll(convertCallRecordToTimeline(callRecordDtos));
            }

            // 6. 按时间倒序排序
            timelineList.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));

            log.info("PromiseTimelineApplicationImpl -> buildPromiseTimeline 构建时间轴完成, timelineSize={}", timelineList.size());
            return timelineList;

        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> buildPromiseTimeline error", e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换电话沟通记录为时间轴事件
     *
     * @param callRecordDtos
     * @return
     */
    private List<PromiseTimelineDetailDto> convertCallRecordToTimeline(List<CallRecordDto> callRecordDtos) {
        try {
            if (CollectionUtils.isEmpty(callRecordDtos)) {
                return Collections.emptyList();
            }

            List<PromiseTimelineDetailDto> timelineList = new ArrayList<>();
            callRecordDtos.forEach(callRecordDto -> {
                SecurityNumberBizCallTypeEnum callTypeEnum = SecurityNumberBizCallTypeEnum.getEnumByCode(callRecordDto.getBizCallType());
                if (callTypeEnum == null) {
                    return;
                }
                PromiseTimelineDetailDto timelineEvent = new PromiseTimelineDetailDto();
                timelineEvent.setCreateTime(callRecordDto.getCreateTime());
                timelineEvent.setActionEnum(PromiseTimelineActionEnum.CALLED_WORK);
                Map<String, Object> extendMap = new HashMap<>();
                extendMap.put(PromiseTimelineExtendFieldEnum.CALL_DURATION.getField(), callRecordDto.getCallDuration());
                JdhAngel angel = angelRepository.queryByUniqueId(JdhAngelRepQuery.builder().angelPin(callRecordDto.getAngelPin()).build());
                String angelName = "服务者";
                if (angel != null) {
                    angelName = angel.getAngelName();
                }
                switch (callTypeEnum) {
                    case APPOINTMENT_TO_ANGEL:
                        extendMap.put(PromiseTimelineExtendFieldEnum.CALLER.getField(), "用户");
                        extendMap.put(PromiseTimelineExtendFieldEnum.CALLEE.getField(), angelName);
                        break;
                    case SERVICED_TO_ANGEL:
                        extendMap.put(PromiseTimelineExtendFieldEnum.CALLER.getField(), "用户");
                        extendMap.put(PromiseTimelineExtendFieldEnum.CALLEE.getField(), angelName);
                        break;
                    case ANGEL_TO_APPOINTMENT:
                        extendMap.put(PromiseTimelineExtendFieldEnum.CALLER.getField(), angelName);
                        extendMap.put(PromiseTimelineExtendFieldEnum.CALLEE.getField(), "用户");
                        break;
                    case ANGEL_TO_SERVICED:
                        extendMap.put(PromiseTimelineExtendFieldEnum.CALLER.getField(), angelName);
                        extendMap.put(PromiseTimelineExtendFieldEnum.CALLEE.getField(), "用户");
                        break;
                    default:
                        return ;
                }
                timelineEvent.setExtend(JSON.toJSONString(extendMap));
                timelineList.add(timelineEvent);
            });

            return timelineList;
        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> convertCallRecordToTimeline error", e);
            return null;
        }
    }

    /**
     * 转换评估单为时间轴事件
     *
     * @param angelServiceRecordList
     * @param angelTaskList
     * @return
     */
    private List<PromiseTimelineDetailDto> convertAngelServiceRecordToTimeline(List<AngelServiceRecord> angelServiceRecordList, List<AngelTask> angelTaskList) {
        try {
            if (CollectionUtils.isEmpty(angelServiceRecordList) || CollectionUtils.isEmpty(angelTaskList)) {
                return null;
            }

            List<PromiseTimelineDetailDto> timelineList = new ArrayList<>();
            angelServiceRecordList.forEach(angelServiceRecord -> {
                PromiseTimelineDetailDto timelineEvent = new PromiseTimelineDetailDto();
                timelineEvent.setCreateTime(angelServiceRecord.getUpdateTime());
                timelineEvent.setActionEnum(PromiseTimelineActionEnum.COMPLETE_ASSESS);
                Map<String, Object> extendMap = new HashMap<>();
                String assessResult = null;
                if (ServiceRecordStatusEnum.HIGH_RISK.getStatus().equals(angelServiceRecord.getStatus())) {
                    assessResult = "不通过";
                } else if (ServiceRecordStatusEnum.FINISH.getStatus().equals(angelServiceRecord.getStatus())) {
                    assessResult = "通过";
                }
                if (StringUtils.isNotBlank(assessResult)) {
                    extendMap.put(PromiseTimelineExtendFieldEnum.ASSESS_RESULT.getField(), assessResult);
                    AngelTask angelTask = angelTaskList.stream().filter(item -> item.getTaskId().equals(angelServiceRecord.getTaskId())).findFirst().orElse(null);
                    if (angelTask != null) {
                        extendMap.put(PromiseTimelineExtendFieldEnum.ASSESS_PATIENT.getField(), angelTask.getJdhAngelTaskExtVo().getPatientName());
                    }
                }
                timelineEvent.setExtend(JSON.toJSONString(extendMap));
                timelineList.add(timelineEvent);
            });

            return timelineList;
        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> convertAngelServiceRecordToTimeline error", e);
            return null;
        }
    }

    /**
     * 转换服务者工单历史为时间轴事件
     *
     * @param history         工单历史
     * @param angelWorkDetail 工单详情
     * @return PromiseTimelineDetailDto
     */
    private PromiseTimelineDetailDto convertAngelWorkHistoryToTimeline(AngelWorkHistoryDto history, AngelWorkDetailForManDto angelWorkDetail) {
        try {
            if (history.getCreateTime() == null) {
                return null;
            }

            // 过滤掉不需要显示的状态变化
            if (excludeAngelWorkHistory(history)) {
                return null;
            }

            PromiseTimelineDetailDto timelineEvent = new PromiseTimelineDetailDto();

            AngelWorkStatusEnum workStatusEnum = AngelWorkStatusEnum.getEnumByCode(history.getAfterStatus());

            switch (workStatusEnum) {
                case RECEIVED:
                    timelineEvent.setActionEnum(PromiseTimelineActionEnum.RECEIVED_WORK);
                    break;
                case WAIT_SERVICE:
                    timelineEvent.setActionEnum(PromiseTimelineActionEnum.DEPART_WORK);
                    break;
                case ARRIVED:
                    timelineEvent.setActionEnum(PromiseTimelineActionEnum.ARRIVED_WORK);
                    break;
                case SERVICING:
                    timelineEvent.setActionEnum(PromiseTimelineActionEnum.SERVICING_WORK);
                    break;
                case SERVICED:
                    timelineEvent.setActionEnum(PromiseTimelineActionEnum.SERVICED_WORK);
                    break;
                default:
                    return null;
            }

            timelineEvent.setCreateTime(history.getCreateTime());

            // 设置服务者姓名等扩展信息
            Map<String, Object> extendMap = new HashMap<>();
            if (angelWorkDetail != null && angelWorkDetail.getAngelName() != null) {
                extendMap.put(PromiseTimelineExtendFieldEnum.SERVICER_NAME.getField(), angelWorkDetail.getAngelName());
            }
            timelineEvent.setExtend(JSON.toJSONString(extendMap));

            return timelineEvent;

        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> convertAngelWorkHistoryToTimeline error", e);
            return null;
        }
    }

    /**
     * 转换检测单历史为时间轴事件
     *
     * @param history 检测单历史
     * @return PromiseTimelineDetailDto
     */
    private PromiseTimelineDetailDto convertMedPromiseHistoryToTimeline(MedPromiseHistoryDTO history) {
        return null;
    }

    /**
     * 转换履约单历史为时间轴事件
     *
     * @param promiseDto 履约单
     * @param history    履约单历史
     * @return PromiseTimelineDetailDto
     */
    private PromiseTimelineDetailDto convertPromiseHistoryToTimeline(PromiseDto promiseDto, JdhPromiseHistory history) {
        try {
            if (history.getCreateTime() == null) {
                return null;
            }

            // 过滤掉不需要显示的状态变化
            if (excludePromiseHistory(history)) {
                return null;
            }

            PromiseTimelineDetailDto timelineEvent = new PromiseTimelineDetailDto();

            // 根据履约单状态变化映射到对应的时间轴事件
            // 这里可以根据具体的履约单状态来映射不同的事件类型
            JdhPromiseStatusEnum promiseStatus = JdhPromiseStatusEnum.convert(history.getAfterStatus());
            switch (promiseStatus) {
                case APPOINTMENT_ING:
                    timelineEvent.setActionEnum(PromiseTimelineActionEnum.CREATE_PROMISE);
                    break;
                case MODIFY_ING:
                    timelineEvent.setActionEnum(PromiseTimelineActionEnum.MODIFY_PROMISE);
                    appendTimelineExtendByPromiseModify(timelineEvent, history);
                    break;
                case COMPLETE:
                    timelineEvent.setActionEnum(PromiseTimelineActionEnum.COMPLETE_PROMISE);
                    break;
                case INVALID:
                    timelineEvent.setActionEnum(PromiseTimelineActionEnum.INVALID_PROMISE);
                    break;
                default:
                    return null;
            }

            timelineEvent.setCreateTime(history.getCreateTime());

            return timelineEvent;

        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> convertPromiseHistoryToTimeline error", e);
            return null;
        }
    }

    /**
     * 过滤掉不需要显示的履约单历史
     *
     * @param history
     * @return
     */
    private boolean excludePromiseHistory(JdhPromiseHistory history) {
        if (Objects.isNull(history) || Objects.isNull(history.getAfterStatus())) {
            return true;
        }
        if (history.getAfterStatus().equals(history.getBeforeStatus())) {
            return true;
        }
        return false;
    }

    /**
     * 过滤掉不需要显示的履约单历史
     *
     * @param history
     * @return
     */
    private boolean excludeAngelWorkHistory(AngelWorkHistoryDto history) {
        if (Objects.isNull(history) || Objects.isNull(history.getAfterStatus())) {
            return true;
        }
        if (history.getAfterStatus().equals(history.getBeforeStatus())) {
            return true;
        }
        return false;
    }

    /**
     * 根据履约单修改信息，设置时间轴扩展信息
     *
     * @param timelineEvent
     * @param history
     */
    private void appendTimelineExtendByPromiseModify(PromiseTimelineDetailDto timelineEvent, JdhPromiseHistory history) {
        try {
            String historyExtend = history.getExtend();
            if (StringUtils.isBlank(historyExtend)) {
                return;
            }
            JSONObject historyExtendJson = JSON.parseObject(historyExtend);
            JSONObject extendMap = new JSONObject();
            String extend = timelineEvent.getExtend();
            if (StringUtils.isNotBlank(extend)) {
                extendMap = JSON.parseObject(extend);
            }
            extendMap.put(PromiseTimelineExtendFieldEnum.OLD_APPOINTTIME.getField(), TimeUtils.dateTimeToStr(historyExtendJson.getJSONObject("beforeTime").getDate("appointmentStartTime")));
            extendMap.put(PromiseTimelineExtendFieldEnum.NEW_APPOINTTIME.getField(), TimeUtils.dateTimeToStr(historyExtendJson.getJSONObject("afterTime").getDate("appointmentStartTime")));
            timelineEvent.setExtend(JSON.toJSONString(extendMap));
        } catch (Exception e) {
            log.error("PromiseTimelineApplicationImpl -> appendTimelineExtendByPromiseModify error", e);
        }
    }

}
