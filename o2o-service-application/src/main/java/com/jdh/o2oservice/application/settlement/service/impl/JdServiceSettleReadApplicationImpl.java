package com.jdh.o2oservice.application.settlement.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.cjgexpress.utils.StringUtils;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.angel.service.ActivityApplication;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.settlement.convert.JdServiceSettleConvert;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleReadApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementUpdateContext;
import com.jdh.o2oservice.core.domain.settlement.enums.*;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlement;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlementDetail;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementEbs;
import com.jdh.o2oservice.core.domain.settlement.model.SettlementMonthGroupDetailSum;
import com.jdh.o2oservice.core.domain.settlement.repository.AngelSettlementRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.db.SettlementEbsRepository;
import com.jdh.o2oservice.core.domain.settlement.rpc.HySettleRpc;
import com.jdh.o2oservice.core.domain.settlement.vo.AngelCashOutVo;
import com.jdh.o2oservice.core.domain.settlement.vo.BankCardDetailVo;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawAccountVo;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawDetailVo;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.export.angel.dto.AngelActivityRecruitmentDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelActivityRecruitmentRequest;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.settlement.SettlementEbsRequest;
import com.jdh.o2oservice.export.settlement.cmd.AngelCashOutCmd;
import com.jdh.o2oservice.export.settlement.cmd.JdSettlementStatusCmd;
import com.jdh.o2oservice.export.settlement.dto.*;
import com.jdh.o2oservice.export.settlement.query.AngelAccountMoneySummaryRequest;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.jdh.o2oservice.base.constatnt.DateConstant.YMDHMS;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 2:36 下午
 * @Description:
 */
@Service
@Slf4j
public class JdServiceSettleReadApplicationImpl implements JdServiceSettleReadApplication {
    /**
     *
     */
    @Autowired
    private AngelSettlementRepository angelSettlementRepository;

    /**
     *
     */
    @Autowired
    private JdOrderRepository jdOrderRepository;
    /**
     *
     */
    @Autowired
    private HySettleRpc hySettleRpc;
    /**
     *
     */
    @Autowired
    private SettlementEbsRepository settlementEbsRepository;

    /**
     * jimRedisService
     */
    @Resource
    private Cluster jimClient;
    /**
     *
     */
    @Autowired
    private AngelApplication angelApplication;
    /**
     *
     */
    @Autowired
    private  DuccConfig duccConfig;

    @Autowired
    private ActivityApplication activityApplication;

    /**
     *
     * @param query
     * @return
     */
    @Override
    public PageDto<AngelSettlementDto> querySettlementPage(AngelSettleQuery query) {
        log.info("JdServiceSettleReadApplicationImpl querySettlementPage query={}", JSON.toJSONString(query));
        if (StringUtils.isBlank(query.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        query.setAngelId(jdhAngelDto.getAngelId());
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        //如果请求当前数据，则设置数据范围
        if (Boolean.TRUE.equals(query.getSelectToday())) {
            Date date = new Date();
            queryContext.setCreateTimeStart(DateUtil.beginOfDay(date));
            queryContext.setCreateTimeEnd(DateUtil.endOfDay(date));
        }
        //新版本
        if (Objects.equals(query.getVersion(), CommonConstant.TWO)) {
            queryContext.setSettlementType(query.getSettlementType());
            //如果没有传settlementType，默认查收入 + 支出
            if (Objects.isNull(queryContext.getSettlementType())) {
                queryContext.setSettlementTypeList(Lists.newArrayList(SettleTypeEnum.INCOME.getType(),SettleTypeEnum.EXPEND.getType()));
            }
            //如果查询非提现数据，查询settle_status=冻结中、已结算的
            if (!Objects.equals(SettleTypeEnum.WITHDRAW.getType(),queryContext.getSettlementType())) {
                queryContext.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
            }
        } else {
            /**查聚合，不查询初始化状态*/
            if (SettleTypeEnum.INCOME.getType().equals(query.getSettlementType())){
                queryContext.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
            }
        }
        Page<AngelSettlement> res = angelSettlementRepository.querySettlementPage(queryContext);
        return JdServiceSettleConvert.ins.entity2AngelSettlementDtoPage(res);
    }

    /**
     * 分页查询护士账户金额明细-按日期并返回汇总数据
     * @param query
     * @return
     */
    @Override
    public AngelSettlementDateSummaryPageDto querySettleSummaryAndPageByDate(AngelSettleQuery query) {
        if (Objects.isNull(query) || org.apache.commons.lang3.StringUtils.isBlank(query.getCreateDateStart()) || org.apache.commons.lang3.StringUtils.isBlank(query.getCreateDateEnd())) {
            return AngelSettlementDateSummaryPageDto.builder().build();
        }
        log.info("JdServiceSettleReadApplicationImpl querySettleSummaryAndPageByDate query={}", JSON.toJSONString(query));
        if (StringUtils.isBlank(query.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        //查询护士ID
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        query.setAngelId(jdhAngelDto.getAngelId());
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        //按照分页查询明细数据
        if (Objects.isNull(queryContext.getSettlementType())) {
            //如果查询条件没有增加金额类型过滤，则默认查询收入 + 支出 + 提现 所有明细。收入和支出需要限制只查询冻结中和已结算的
            queryContext.setApplySql("((settlement_type in (1,2) and settle_status in (1,2)) or (settlement_type = 3))");
        }
        if (Objects.nonNull(queryContext.getSettlementType()) && !Objects.equals(SettleTypeEnum.WITHDRAW.getType(),queryContext.getSettlementType())) {
            //如果入参有金额类型过滤，收入和支出类型，需要增加setSettleStatus，只查询冻结中和已结算的；提现类型不需要处理
            queryContext.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
        }
        Page<AngelSettlement> res = angelSettlementRepository.querySettlementPage(queryContext);

        //按照时间范围统计收入、支出和提现金额
        //时间范围设置开始时间00:00和结束时间23:59:59
        Map<String, BigDecimal> stringBigDecimalMap = querySettleTotal(query);

        //返回结果
        return AngelSettlementDateSummaryPageDto.builder()
                .createDateStart(query.getCreateDateStart())
                .createDateEnd(query.getCreateDateEnd())
                .income(stringBigDecimalMap.getOrDefault("income", BigDecimal.ZERO))
                .expend(stringBigDecimalMap.getOrDefault("expend", BigDecimal.ZERO))
                .withdraw(stringBigDecimalMap.getOrDefault("withdraw", BigDecimal.ZERO))
                .pageData(JdServiceSettleConvert.ins.entity2AngelSettlementDtoPage(res)).build();
    }

    /**
     * 分页查询护士账户金额明细-按月并返回汇总数据
     * @param query
     * @return
     */
    @Override
    public PageDto<AngelSettlementMonthSummaryDto> querySettleSummaryAndPageByMonth(AngelSettleQuery query) {
        if (Objects.isNull(query)) {
            return new PageDto<>();
        }
        log.info("JdServiceSettleReadApplicationImpl querySettleSummaryAndPageByMonth query={}", JSON.toJSONString(query));
        if (StringUtils.isBlank(query.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        //查询护士ID
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        query.setAngelId(jdhAngelDto.getAngelId());
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);

        //根据订单查询明细
        if (Objects.nonNull(queryContext.getOrderId())) {
            //兼容父子单场景
            JdOrder orderDetail = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(queryContext.getOrderId()).build());
            if (Objects.isNull(orderDetail)) {
                return new PageDto<>();
            }
            List<JdOrder> orders = new ArrayList<>();
            //1. orderId为父单，需要获取所有子单号
            if (Objects.equals(OrderStatusEnum.ORDER_SPLIT.getStatus(), orderDetail.getOrderStatus())) {
                orders = jdOrderRepository.findOrderListByOrderId(JdOrderIdentifier.builder().orderId(queryContext.getOrderId()).build());
            }
            //2. orderId为子单，需要获取父单号 + 其他子单号
            else if (Objects.nonNull(orderDetail.getParentId())){
                orders = jdOrderRepository.findOrderListByOrderId(JdOrderIdentifier.builder().orderId(orderDetail.getParentId()).build());
            }
            else {
                orders = Lists.newArrayList(orderDetail);
            }
            if (CollectionUtils.isEmpty(orders)) {
                return new PageDto<>();
            }
            //查询所有orders的结算数据
            queryContext.setOrderIdList(orders.stream().map(JdOrder::getOrderId).collect(Collectors.toList()));
            List<AngelSettlement> angelSettlementList = angelSettlementRepository.querySettlementList(queryContext);
            //将数据按照月份分组，然后按月份组装返回数据
            Map<String, List<AngelSettlement>> month2SettleMap = angelSettlementList.stream().collect(Collectors.groupingBy(angelSettlement -> DateUtil.format(angelSettlement.getCreateTime(), DatePattern.NORM_MONTH_FORMAT)));
            //线程安全list
            List<AngelSettlementMonthSummaryDto> result = Lists.newCopyOnWriteArrayList();
            month2SettleMap.entrySet().parallelStream().forEach(entry -> result.add(buildAngelSettlementMonthSummaryDto(jdhAngelDto.getAngelId(), entry.getValue(), entry.getKey())));
            //月份降序排序
            result.sort((o1, o2) -> o2.getMonth().compareTo(o1.getMonth()));
            PageDto<AngelSettlementMonthSummaryDto> pageDto = new PageDto<>();
            pageDto.setTotalCount(result.size());
            pageDto.setList(result);
            return pageDto;
        }
        //根据指定月份查询明细
        else if (org.apache.commons.lang3.StringUtils.isNotBlank(queryContext.getQueryMonth())) {
            List<AngelSettlement> angelSettlementList = angelSettlementRepository.querySettlementList(queryContext);
            AngelSettlementMonthSummaryDto summaryDto = buildAngelSettlementMonthSummaryDto(jdhAngelDto.getAngelId(), angelSettlementList, queryContext.getQueryMonth());
            PageDto<AngelSettlementMonthSummaryDto> pageDto = new PageDto<>();
            pageDto.setPageSize(queryContext.getPageSize());
            pageDto.setPageNum(1);
            pageDto.setTotalPage(1);
            pageDto.setTotalCount(1);
            pageDto.setList(Lists.newArrayList(summaryDto));
            return pageDto;
        }
        //查询该护士有结算记录的月份（全量）
        List<SettlementMonthGroupDetailSum> monthGroupDetailSumList = angelSettlementRepository.querySettlementMonthGroupDetailSum(queryContext);
        List<String> settleMonthList = CollectionUtils.isEmpty(monthGroupDetailSumList) ? new ArrayList<>() : monthGroupDetailSumList.stream().map(SettlementMonthGroupDetailSum::getYearMonth).sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        //如果该护士没有任何结算数据
        if (CollectionUtils.isEmpty(settleMonthList)) {
            return new PageDto<>();
        }
        //需要查的月份
        List<String> subList = getSubList(settleMonthList, queryContext.getOffsetMonth(), queryContext.getPageSize());
        if (CollectionUtils.isEmpty(subList)) {
            return new PageDto<>();
        }
        //因为subList月份是降序排序的，所以只需取集合第一个月份、最后一个月份查询数据即可
        String lastMonth = subList.get(0);
        String firstMonth = subList.get(subList.size() - 1);
        //设置查询结束时间
        DateTime lastMonthTime = DateUtil.parse(lastMonth, DatePattern.NORM_MONTH_FORMAT);
        queryContext.setCreateTimeEnd(DateUtil.endOfMonth(lastMonthTime));
        //设置查询开始时间
        DateTime firstMonthTime = DateUtil.parse(firstMonth, DatePattern.NORM_MONTH_FORMAT);
        queryContext.setCreateTimeStart(DateUtil.beginOfMonth(firstMonthTime));
        //查询结算数据
        List<AngelSettlement> angelSettlementList = angelSettlementRepository.querySettlementList(queryContext);
        if (CollectionUtils.isEmpty(angelSettlementList)) {
            return new PageDto<>();
        }
        //将数据按照月份分组，然后按月份组装返回数据
        Map<String, List<AngelSettlement>> month2SettleMap = angelSettlementList.stream().collect(Collectors.groupingBy(angelSettlement -> DateUtil.format(angelSettlement.getCreateTime(), DatePattern.NORM_MONTH_FORMAT)));
        //线程安全list
        List<AngelSettlementMonthSummaryDto> result = Lists.newCopyOnWriteArrayList();
        month2SettleMap.entrySet().parallelStream().forEach(entry -> result.add(buildAngelSettlementMonthSummaryDto(jdhAngelDto.getAngelId(), entry.getValue(), entry.getKey())));
        //月份降序排序
        result.sort((o1, o2) -> o2.getMonth().compareTo(o1.getMonth()));
        PageDto<AngelSettlementMonthSummaryDto> pageDto = new PageDto<>();
        pageDto.setPageSize(queryContext.getPageSize());
        pageDto.setTotalCount(settleMonthList.size());
        pageDto.setList(result);
        return pageDto;
    }

    /**
     * 组装月维度结算数据
     * @param angelSettlementList
     * @param queryMonth
     * @return
     */
    private static AngelSettlementMonthSummaryDto buildAngelSettlementMonthSummaryDto(Long angelId, List<AngelSettlement> angelSettlementList, String queryMonth) {
        BigDecimal income = BigDecimal.ZERO;
        BigDecimal expend = BigDecimal.ZERO;
        BigDecimal withdraw = BigDecimal.ZERO;
        List<AngelSettlementDateSummaryDto> dateDetailList = new ArrayList<>();
        Map<String, AngelSettlementDateSummaryDto> date2SummaryMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(angelSettlementList)) {
            for (AngelSettlement angelSettlement : angelSettlementList) {
                //收入、支出类型中，初始化状态的数据过滤掉
                if (SettleStatusEnum.INIT.getType().equals(angelSettlement.getSettleStatus())) {
                    continue;
                }
                if (SettleTypeEnum.INCOME.getType().equals(angelSettlement.getSettlementType())) {
                    income = income.add(angelSettlement.getSettleAmount());
                } else if (SettleTypeEnum.EXPEND.getType().equals(angelSettlement.getSettlementType())) {
                    expend = expend.add(angelSettlement.getSettleAmount());
                } else if (SettleTypeEnum.WITHDRAW.getType().equals(angelSettlement.getSettlementType())) {
                    withdraw = withdraw.add(angelSettlement.getSettleAmount());
                }
                String date = DateUtil.formatDate(angelSettlement.getCreateTime());
                AngelSettlementDateSummaryDto angelSettlementDateSummaryDto = date2SummaryMap.get(date);
                if (Objects.isNull(angelSettlementDateSummaryDto)) {
                    angelSettlementDateSummaryDto = AngelSettlementDateSummaryDto.builder().date(date).settleDetailList(new ArrayList<>()).build();
                    date2SummaryMap.put(date, angelSettlementDateSummaryDto);
                    dateDetailList.add(angelSettlementDateSummaryDto);
                }
                angelSettlementDateSummaryDto.getSettleDetailList().add(JdServiceSettleConvert.ins.entity2AngelSettlementDto(angelSettlement));
            }
        }
        //日期排序 + 同一天内时间排序，均为降序
        List<AngelSettlementDateSummaryDto> collect = dateDetailList.stream()
                .peek(dto -> dto.getSettleDetailList().sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())))
                .sorted((o1, o2) -> o2.getDate().compareTo(o1.getDate()))
                .collect(Collectors.toList());

        //设置查询余额截止时间
        DateTime dateTime = DateUtil.parse(queryMonth, DatePattern.NORM_MONTH_FORMAT);
        //查询月份余额
        AngelSettlementQueryContext incomeQuery = new AngelSettlementQueryContext();
        incomeQuery.setAngelId(angelId);
        incomeQuery.setCreateTimeEnd(DateUtil.endOfMonth(dateTime));
        incomeQuery.setApplySql("((settlement_type in (1,2) and settle_status in (1,2)) or (settlement_type = 3))");
        BigDecimal accountBalance = SpringUtil.getBean(AngelSettlementRepository.class).querySettlementAmountTot(incomeQuery);

        //组装返回值
        return AngelSettlementMonthSummaryDto.builder()
                .month(queryMonth)
                .income(income)
                .expend(expend)
                .withdraw(withdraw)
                .accountBalance(accountBalance)
                .dateDetailList(collect)
                .build();
    }

    /**
     * 根据偏移量和长度获取子集合
     * @param list
     * @param offset
     * @param size
     * @return
     */
    public static List<String> getSubList(List<String> list, String offset, Integer size) {
        List<String> result = new ArrayList<>();
        if (list == null || list.isEmpty() || size == null || size <= 0) {
            return result;
        }

        ListIterator<String> iterator = list.listIterator();
        // 如果 offset 为 null，直接从头开始
        if (offset == null) {
            for (int i = 0; i < size && iterator.hasNext(); i++) {
                result.add(iterator.next());
            }
            return result;
        }

        // 找到 offset 的位置
        while (iterator.hasNext()) {
            String current = iterator.next();
            if (current.equals(offset)) {
                break;
            }
        }

        // 从 offset 之后开始收集元素
        for (int i = 0; i < size && iterator.hasNext(); i++) {
            result.add(iterator.next());
        }
        return result;
    }

    /**
     *
     * @param query
     * @return
     */
    @Override
    public AngelSettlementDto querySettlementDetailList(AngelSettleQuery query) {
        log.info("JdServiceSettleReadApplicationImpl querySettlementDetailList query={}", JSON.toJSONString(query));
        if (StringUtils.isBlank(query.getUserPin()) || Objects.isNull(query.getSettleId())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        List<AngelSettlement> settlementList = angelSettlementRepository.querySettlementList(queryContext);
        if (CollectionUtils.isEmpty(settlementList)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelSettlementDto settlementDto = JdServiceSettleConvert.ins.entity2AngelSettlementDto(settlementList.get(0));
        //提现详情查互医
        if (query.getQuerySettleDetail()) {
            if (Objects.equals(settlementDto.getSettlementType(), SettleTypeEnum.WITHDRAW.getType())) {
                WithdrawDetailVo detailVo = hySettleRpc.queryWithdrawRecordDetail(settlementDto.getSettlementNo(),query.getUserPin(),null);
                JdServiceSettleConvert.ins.packWithdrawDetailVo(settlementDto, detailVo);
            } else {
                AngelSettlement angelSettlement = settlementList.get(0);
                Date dataCleansingTimestamp = DateUtil.parse(duccConfig.getDataCleansingTimestamp(),YMDHMS);
                //如果可以查到promiseId，或者查询的是已结算的状态，则不为itemType=3or4的情况，还用settleId进行查询
                if (Objects.nonNull(angelSettlement.getPromiseId())&& angelSettlement.getCreateTime().after(dataCleansingTimestamp)) {
                    /**第二次查明细，只查询初始状态，且需要用settleId去置换promiseId查询*/
                    queryContext.setPromiseId(settlementList.get(0).getPromiseId());
                    queryContext.setSettleStatus(SettleStatusEnum.INIT.getType());
                    /**不用settleId查询*/
                    queryContext.setSettleId(null);
                }
                List<AngelSettlement> detailList = angelSettlementRepository.querySettlementList(queryContext);
                if (CollectionUtils.isNotEmpty(detailList)) {
                    List<Long> settleIdList = detailList.stream().map(AngelSettlement::getSettleId).collect(Collectors.toList());
                    queryContext.setSettleIdList(settleIdList);
                    List<AngelSettlementDetail> angelSettlementDetails = angelSettlementRepository.querySettlementDetailList(queryContext);
                    //将相同费项的金额group一下再展示，每个费项一条数据
                    //2025.6.13 夜间、即时、上门、节假日、派单加价合并成上门费
                    ArrayList<String> codes = Lists.newArrayList(JdOrderFeeTypeEnum.NIGHT_SERVICE_FEE.getCode(),JdOrderFeeTypeEnum.TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY.getCode()
                            ,JdOrderFeeTypeEnum.HOME_VISIT.getCode(),JdOrderFeeTypeEnum.TIME_PERIOD_TIME_TYPE_HOLIDAY.getCode(),JdOrderFeeTypeEnum.DISPATCH_MARKUP_FEE.getCode()
                            ,JdOrderFeeTypeEnum.PEAK_SERVICE_FEE.getCode(),JdOrderFeeTypeEnum.DYNAMIC.getCode());
                    Map<String, List<AngelSettlementDetailDto>> map = Optional.ofNullable(JdServiceSettleConvert.ins.entityAngelSettlementDetailDtos(angelSettlementDetails)).map(List::stream).orElseGet(Stream::empty)
                            .collect(Collectors.groupingBy(angelSettlementDetailDto -> codes.contains(angelSettlementDetailDto.getFeeName()) ? JdOrderFeeTypeEnum.HOME_VISIT.getCode() : angelSettlementDetailDto.getFeeName()));

                    List<AngelSettlementDetailDto> detailDtos = new ArrayList<>();
                    for (Map.Entry<String, List<AngelSettlementDetailDto>> entry : map.entrySet()) {
                        if (org.apache.commons.lang3.StringUtils.isBlank(entry.getKey()) || CollectionUtils.isEmpty(entry.getValue())) {
                            continue;
                        }
                        AngelSettlementDetailDto target = new AngelSettlementDetailDto();
                        BeanUtils.copyProperties(entry.getValue().get(0), target);
                        target.setSettleId(query.getSettleId());
                        //金额sum
                        BigDecimal tot = entry.getValue().stream()
                                .map(AngelSettlementDetailDto::getSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        target.setSettleAmount(tot);
                        JdOrderFeeTypeEnum orderFeeTypeEnum = JdOrderFeeTypeEnum.getByCode(entry.getKey());
                        target.setFeeDescription(Objects.nonNull(orderFeeTypeEnum) ? orderFeeTypeEnum.getDescription() : "");
                        target.setFeeName(entry.getKey());
                        detailDtos.add(target);
                    }

                    List<AngelSettlementDetailDto> collect = detailDtos.stream().sorted(Comparator.comparing(AngelSettlementDetailDto::getSettleAmount, Comparator.nullsLast(Comparator.reverseOrder()))).collect(Collectors.toList());
                    settlementDto.setDetailList(collect);                }
            }
        }

        // 护士邀请活动佣金
        if (AngelSettleItemTypeEnum.ACTIVITY.getType().equals(settlementDto.getItemType())){
            AngelActivityRecruitmentRequest angelActivityRequest = new AngelActivityRecruitmentRequest();
            angelActivityRequest.setAngelActivityId(settlementDto.getItemSourceId());
            AngelActivityRecruitmentDto activityConfigAngelDto = activityApplication.queryRecruitmentAngelActivity(angelActivityRequest);
            log.info("querySettlementDetailList angelActivityRequest={}, activityConfigAngelDto={}", JSON.toJSONString(angelActivityRequest), JSON.toJSONString(activityConfigAngelDto));
            if (activityConfigAngelDto != null && activityConfigAngelDto.getActivityConfigRule() != null){
                settlementDto.setActivityConfigRule(JSON.parseObject(JSON.toJSONString(activityConfigAngelDto.getActivityConfigRule()), ActivityConfigAngelRecruitmentRuleDto.class));
            }
        }

        JdServiceSettleConvert.ins.packDetailMap(settlementDto);
        return settlementDto;
    }

    /**
     * 查询账单明细
     *
     * @param query
     * @return
     */
    @Override
    public AngelSettlementDto querySettlement(AngelSettleQuery query) {
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        List<AngelSettlement> settlementList = angelSettlementRepository.querySettlementList(queryContext);
        if (CollectionUtils.isEmpty(settlementList)) {
            return null;
        }
        AngelSettlementDto settlementDto = JdServiceSettleConvert.ins.entity2AngelSettlementDto(settlementList.get(0));
        if (query.getQuerySettleDetail()) {
            List<AngelSettlementDetail> detailList = angelSettlementRepository.querySettlementDetailList(queryContext);
            settlementDto.setDetailList(JdServiceSettleConvert.ins.entityAngelSettlementDetailDtos(detailList));
        }

        return settlementDto;
    }

    /**
     *
     * @param query
     * @return
     */
    @Override
    public Map<String, BigDecimal> querySettleTotal(AngelSettleQuery query) {
        if (StringUtils.isBlank(query.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        Map<String, BigDecimal> result = new HashMap<>();
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        query.setAngelId(jdhAngelDto.getAngelId());
        //如果入参设置了查询开始日期和查询结束日期，转成CreateDate
        if (Objects.nonNull(query.getCreateDateStart())) {
            DateTime dateTime = DateUtil.parseDate(query.getCreateDateStart());
            query.setCreateTimeStart(DateUtil.beginOfDay(dateTime));
        }
        if (Objects.nonNull(query.getCreateDateEnd())) {
            DateTime dateTime = DateUtil.parseDate(query.getCreateDateEnd());
            query.setCreateTimeEnd(DateUtil.endOfDay(dateTime));
        }
        AngelSettlementQueryContext incomeQuery = JdServiceSettleConvert.ins.query2Context(query);
        incomeQuery.setSettlementType(SettleTypeEnum.INCOME.getType());
        incomeQuery.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
        BigDecimal incomeTot = angelSettlementRepository.querySettlementAmountTot(incomeQuery);
        result.put("income", incomeTot);

        AngelSettlementQueryContext expendQuery = JdServiceSettleConvert.ins.query2Context(query);
        expendQuery.setSettlementType(SettleTypeEnum.EXPEND.getType());
        BigDecimal expendTot = angelSettlementRepository.querySettlementAmountTot(expendQuery);
        result.put("expend", expendTot);
        //提现金额
        AngelSettlementQueryContext withdrawQuery = JdServiceSettleConvert.ins.query2Context(query);
        withdrawQuery.setSettlementType(SettleTypeEnum.WITHDRAW.getType());
        BigDecimal withDrawTot = angelSettlementRepository.querySettlementAmountTot(withdrawQuery);
        result.put("withdraw", withDrawTot);
        //近一年
        AngelSettlementQueryContext yearQuery = JdServiceSettleConvert.ins.getTotAngelSettlementQueryContext(query);
        BigDecimal yearSettlementTot = angelSettlementRepository.querySettlementAmountTot(yearQuery);
        result.put("year", yearSettlementTot);

        return result;
    }

    /**
     *
     * @param query
     * @return
     */
    @Override
    public AngelSettlementMoneyDto queryAngelSettlementMoneyDto(AngelSettleQuery query) {
        if (StringUtils.isBlank(query.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        query.setAngelId(jdhAngelDto.getAngelId());
        //查询账户余额、可提现金额、不可提现金额
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        WithdrawAccountVo accountVo = hySettleRpc.queryWithdrawAccount(queryContext);
        AngelSettlementMoneyDto res = JdServiceSettleConvert.ins.vo2AngelSettlementMoneyDto(accountVo);

        //查询累计收入
        queryContext.setSettlementType(SettleTypeEnum.INCOME.getType());
        queryContext.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
        BigDecimal totalComeTot = angelSettlementRepository.querySettlementAmountTot(queryContext);
        res.setTotSettleAmount(totalComeTot);
        //查询今日收入
        DateTime today = DateUtil.date();
        queryContext.setCreateTimeStart(DateUtil.beginOfDay(today));
        queryContext.setCreateTimeEnd(DateUtil.endOfDay(today));
        BigDecimal incomeTot = angelSettlementRepository.querySettlementAmountTot(queryContext);
        res.setToDaySettleAmount(incomeTot);
        return res;
    }

    /**
     *
     * @param query
     * @return
     */
    @Override
    public AngelBankDto queryBindBank(AngelSettleQuery query) {
        if (StringUtils.isBlank(query.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        query.setAngelId(jdhAngelDto.getAngelId());
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        BankCardDetailVo detailVo = hySettleRpc.queryBankCardDetail(queryContext);
        return JdServiceSettleConvert.ins.vo2AngelBankDto(detailVo);
    }

    /**
     *
     * @param cashOutCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitCashOut(AngelCashOutCmd cashOutCmd) {
        if (StringUtils.isBlank(cashOutCmd.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(cashOutCmd.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        cashOutCmd.setAngelId(jdhAngelDto.getAngelId());
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.REPEAT_CHECK_PRE, cashOutCmd.getUserPin());
        try {
            if (jimClient.exists(redisKey)) {
                throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_REPEAT);
            }
            // 加缓存 分布式锁
            jimClient.setEx(redisKey, "1", 1, TimeUnit.SECONDS);
            //提现重试
            if (Objects.nonNull(cashOutCmd.getSettlementNo())) {
                return retrySubmitCashOut(cashOutCmd);
            } else {
                AngelSettlement angelSettlement = JdServiceSettleConvert.ins.packAngelSettlement(cashOutCmd);
                angelSettlement.setCashTime(new Date());
                Long settleId = angelSettlementRepository.save(angelSettlement);
                if (Objects.isNull(settleId)) {
                    throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
                }
                AngelCashOutVo angelCashOutVo = JdServiceSettleConvert.ins.cmd2AngelCashOutVo(cashOutCmd);
                Long settlementNo = hySettleRpc.withdraw(angelCashOutVo);
                if (Objects.isNull(settlementNo)) {
                    throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
                }
                //成功更新互医结算id
                angelSettlement.setSettlementNo(settlementNo);
                // 更新 状态为处理中
                angelSettlement.setSettleId(settleId);
                angelSettlement.setCashStatus(CashStatusEnum.CASHING.getType());
                Long updateId = angelSettlementRepository.save(angelSettlement);
                if (Objects.isNull(updateId)) {
                    throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
                }
                return settleId;
            }
        }catch(ArgumentsException e) {
            log.error("JdServiceSettleReadApplicationImpl.submitCashOut ArgumentsException={}",e);
            throw e;
        }catch(SystemException e) {
            log.error("JdServiceSettleReadApplicationImpl.submitCashOut SystemException={}",e);
            throw e;
        }catch(BusinessException e) {
            log.error("JdServiceSettleReadApplicationImpl.submitCashOut BusinessException={}",e);
            throw e;
        }catch(Exception e) {
            log.error("JdServiceSettleReadApplicationImpl.submitCashOut e={}",e);
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
        }finally{
            jimClient.del(redisKey);
        }
    }

    /**
     * 更新提现结果
     *
     * @param angelSettlementDto
     * @return
     */
    @Override
    public Boolean updateCashOutResult(AngelSettlementDto angelSettlementDto) {
        AngelSettlement angelSettlement = JdServiceSettleConvert.ins.dto2AngelSettlement(angelSettlementDto);
        Long id = angelSettlementRepository.save(angelSettlement);
        if (Objects.isNull(id)) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_RESULT_UPDATE_FAILED);
        }
        if (CollectionUtils.isNotEmpty(angelSettlementDto.getDetailList())) {
            List<AngelSettlementDetail> detailList = JdServiceSettleConvert.ins.dto2AngelSettlementDetails(angelSettlementDto.getDetailList());
            angelSettlementRepository.batchSaveAngelSettlementAndDetail(null, detailList);
        }
        return Boolean.TRUE;
    }

    /**
     * 查询护士结算信息
     *
     * @param angelSettlQuery
     * @return
     */
    @Override
    public PageDto<AngelSettlementDto> querySettlementPageBySettleTime(AngelSettleQuery angelSettlQuery) {
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(angelSettlQuery);
        Page<AngelSettlement> res = angelSettlementRepository.querySettlementPage(queryContext);
        return JdServiceSettleConvert.ins.entity2AngelSettlementDtoPage(res);
    }

    /**
     * @param jdSettlementStatusCmd
     */
    @Override
    public Integer updateSettleStatusBySettleIdList(JdSettlementStatusCmd jdSettlementStatusCmd) {
        AngelSettlementUpdateContext angelSettlementUpdateContext = JdServiceSettleConvert.ins.query2UpdateContext(jdSettlementStatusCmd);
        return angelSettlementRepository.updateSettleStatusBySettleIdList(angelSettlementUpdateContext);
    }

    /**
     * 查询ebs收入
     * @param settlementEbsRequest
     * @return
     */
    @Override
    public List<JdhSettlementEbs> querySettlementEbs(SettlementEbsRequest settlementEbsRequest) {
        JdhSettlementEbs jdhSettlementEbs = new JdhSettlementEbs();
        jdhSettlementEbs.setPreId(settlementEbsRequest.getPreId());
        List<JdhSettlementEbs> list = settlementEbsRepository.queryJdhSettlementEbsList(jdhSettlementEbs);
        return list;
    }

    /**
     * 查询护士账户金额汇总
     * @param request
     * @return
     */
    @Override
    public AngelAccountMoneySummaryDto queryAngelAccountMoneySummary(AngelAccountMoneySummaryRequest request) {
        log.info("JdServiceSettleReadApplicationImpl queryAngelAccountMoneySummary request={}", JSON.toJSONString(request));
        if (StringUtils.isBlank(request.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(request.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelSettlementQueryContext queryContext = new AngelSettlementQueryContext();
        queryContext.setUserPin(request.getUserPin());
        queryContext.setAngelId(jdhAngelDto.getAngelId());
        queryContext.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
        queryContext.setSettlementTypeList(Arrays.asList(SettleTypeEnum.INCOME.getType(),SettleTypeEnum.EXPEND.getType()));

        Date date = new Date();
        queryContext.setCreateTimeStart(DateUtil.beginOfDay(date));
        queryContext.setCreateTimeEnd(DateUtil.endOfDay(date));
        List<AngelSettlement> angelSettlements = angelSettlementRepository.querySettlementList(queryContext);
        log.info("JdServiceSettleReadApplicationImpl queryAngelAccountMoneySummary angelSettlements={}", JSON.toJSONString(angelSettlements));

        //收入、支出对应列表数据
        Map<SettleItemGroupTypeEnum, List<AngelSettlement>> type2SettlementMap = Optional.ofNullable(angelSettlements).orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(AngelSettlement::getSettleItemTypeGroupEnum));
        log.info("JdServiceSettleReadApplicationImpl queryAngelAccountMoneySummary type2SettlementMap={}", JSON.toJSONString(type2SettlementMap));

        //取出收入列表数据，按照展示项分组
        BigDecimal todayIncome = BigDecimal.ZERO;

        List<AngelAccountMoneySummaryDetailDto> todayIncomeDetailList = new ArrayList<>();
        for (SettleItemGroupTypeEnum settleItemGroupTypeEnum : SettleItemGroupTypeEnum.getIncomeGroupEnumList()) {
            List<AngelSettlement> list = type2SettlementMap.getOrDefault(settleItemGroupTypeEnum, new ArrayList<>());
            BigDecimal reduce = list.stream().map(AngelSettlement::getSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            todayIncomeDetailList.add(AngelAccountMoneySummaryDetailDto.builder().itemTypeGroup(settleItemGroupTypeEnum.getType()).groupName(settleItemGroupTypeEnum.getDesc()).money(reduce).build());
            todayIncome = todayIncome.add(reduce);
        }

        //取出支出列表数据，按照展示项分组
        BigDecimal todayExpend = BigDecimal.ZERO;

        List<AngelAccountMoneySummaryDetailDto> todayExpendDetailList = new ArrayList<>();
        for (SettleItemGroupTypeEnum settleItemGroupTypeEnum : SettleItemGroupTypeEnum.getExpendGroupEnumList()) {
            List<AngelSettlement> list = type2SettlementMap.getOrDefault(settleItemGroupTypeEnum, new ArrayList<>());
            BigDecimal reduce = list.stream().map(AngelSettlement::getSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            todayIncomeDetailList.add(AngelAccountMoneySummaryDetailDto.builder().itemTypeGroup(settleItemGroupTypeEnum.getType()).groupName(settleItemGroupTypeEnum.getDesc()).money(reduce).build());
            todayExpend = todayExpend.add(reduce);
        }

        AngelAccountMoneySummaryDto dto = new AngelAccountMoneySummaryDto();
        dto.setAngelId(jdhAngelDto.getAngelId());
        dto.setTodayIncome(todayIncome);
        dto.setTodayIncomeDetailList(todayIncomeDetailList);
        dto.setTodayExpend(todayExpend);
        dto.setTodayExpendDetailList(todayExpendDetailList);
        return dto;
    }

    /**
     * 查询护士账户金额子分类字典列表
     * @param angelSettleQuery
     * @return
     */
    @Override
    public List<AngelSettleItemGroupTypeDto> queryAngelSettleItemGroupTypeDictList(AngelSettleQuery angelSettleQuery) {
        if (Objects.isNull(angelSettleQuery) || Objects.isNull(angelSettleQuery.getSettlementType())) {
            return Collections.emptyList();
        }
        //查询收入的分类
        if (Objects.equals(angelSettleQuery.getSettlementType(), SettleTypeEnum.INCOME.getType())) {
            return SettleItemGroupTypeEnum.getIncomeGroupEnumList().stream().map(settleItemGroupTypeEnum -> AngelSettleItemGroupTypeDto.builder().type(settleItemGroupTypeEnum.getType()).desc(settleItemGroupTypeEnum.getDesc()).settlementType(settleItemGroupTypeEnum.getSettlementType()).build()).collect(Collectors.toList());
        } else if (Objects.equals(angelSettleQuery.getSettlementType(), SettleTypeEnum.EXPEND.getType())) {
            return SettleItemGroupTypeEnum.getExpendGroupEnumList().stream().map(settleItemGroupTypeEnum -> AngelSettleItemGroupTypeDto.builder().type(settleItemGroupTypeEnum.getType()).desc(settleItemGroupTypeEnum.getDesc()).settlementType(settleItemGroupTypeEnum.getSettlementType()).build()).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 提现失败重试
     *
     * @param cashOutCmd
     */
    private Long retrySubmitCashOut(AngelCashOutCmd cashOutCmd) {
        AngelSettlementQueryContext queryContext = new AngelSettlementQueryContext();
        queryContext.setAngelId(cashOutCmd.getAngelId());
        queryContext.setSettlementNo(cashOutCmd.getSettlementNo());
        queryContext.setSettlementType(SettleTypeEnum.WITHDRAW.getType());
        List<AngelSettlement> cashDetails = angelSettlementRepository.querySettlementList(queryContext);
        if (CollectionUtils.isEmpty(cashDetails)) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_NOT_EXISTS);
        }
        AngelSettlement cashDetail = cashDetails.get(0);
        if (!Objects.equals(cashDetail.getCashStatus(), CashStatusEnum.CASH_FAIL.getType())) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_NOT);
        }
        //更新提现时间
        cashDetail.setCashTime(new Date());
        cashDetail.setUpdateUser(cashOutCmd.getUserPin());
        Long setId = angelSettlementRepository.save(cashDetail);
        if (Objects.isNull(setId)) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
        }
        //互医重试
        Boolean tryRes = hySettleRpc.retryWithdraw(cashOutCmd.getSettlementNo());
        if (!tryRes) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
        }
        cashDetail.setCashStatus(CashStatusEnum.CASHING.getType());
        setId = angelSettlementRepository.save(cashDetail);
        if (Objects.isNull(setId)) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
        }
        return setId;
    }

}
