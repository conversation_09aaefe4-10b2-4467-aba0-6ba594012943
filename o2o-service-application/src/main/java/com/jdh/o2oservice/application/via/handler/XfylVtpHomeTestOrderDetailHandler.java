package com.jdh.o2oservice.application.via.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelEcologyApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.riskassessment.service.RiskAssessmentApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.application.via.common.ViaComponentDomainService;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.EnvTypeEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessment;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentQuery;
import com.jdh.o2oservice.core.domain.product.enums.ProductAggregateCodeEnum;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseHistoryRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.VoucherRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.*;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.UserName;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.ScriptBo;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.UserPromisegoBo;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.*;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.core.domain.trade.enums.TradeAggregateEnum;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.JdhAngelEcologyQueryRequest;
import com.jdh.o2oservice.export.angelpromise.dto.AngelTrackDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelTrackQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.product.query.PromiseDangerLevelRequest;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderItemDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderServiceFeeInfoDTO;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * XfylVtpHomeTest VTP对接
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Slf4j
@Service
public class XfylVtpHomeTestOrderDetailHandler extends AbstractViaDataFillHandler implements MapAutowiredKey {

    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private VoucherRepository voucherRepository;

    /**
     * promiseHistoryRepository
     */
    @Autowired
    private PromiseHistoryRepository promiseHistoryRepository;

    /**
     * angelApplication
     */
    @Autowired
    private AngelApplication angelApplication;

    /**
     * angelPromiseApplication
     */
    @Autowired
    private AngelPromiseApplication angelPromiseApplication;

    /**
     * medicalPromiseApplication
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * tradeApplication
     */
    @Autowired
    private TradeApplication tradeApplication;

    /**
     * productApplication
     */
    @Autowired
    private ProductApplication productApplication;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * angelWorkApplication
     */
    @Autowired
    private AngelWorkApplication angelWorkApplication;

    @Autowired
    private HomePromiseDetailHandler handler;

    @Autowired
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private CallRecordApplication callRecordApplication;

    @Resource
    private AngelEcologyApplication angelEcologyApplication;

    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private JdhSkuRepository jdhSkuRepository;

    @Resource
    private RiskAssessmentApplication riskAssessmentApplication;

    @Resource
    private PromiseApplication promiseApplication;

    @Resource
    private ViaComponentDomainService viaComponentDomainService;

    @Resource
    private MedicalReportApplication medicalReportApplication;

    /**
     * checkParam
     *
     * @param ctx ctx
     */
    private void checkParam(FillViaConfigDataContext ctx) {
        //场景
        AssertUtils.hasText(ctx.getScene(), SupportErrorCode.VIA_CONFIG_NOT_EXIT);
        AssertUtils.hasText(ctx.getOrderId(), SupportErrorCode.VIA_ORDER_ID_NOT_EXIT);
    }

    /**
     * 命中状态映射
     * {
     * "statusExpression": "!include(seq.list(1,7,8,9),orderStatus) && include(seq.list(1,2),promiseStatus)",
     * "mainTitle": "待护士接单",
     * "title":"正在通知护士接单，预计 <span>{angelWorkReceiveTime}</span> 前接单",
     * "titleDynamicField":["angelWorkReceiveTime"]
     * "dynamicCursorMinutes": 30,
     * "mainIcon": "https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     * "promiseTitle":"上门信息",
     * "promiseTitleDynamicField":[]
     * "stepGuideFinishCodeList":[],
     * "stepGuideFinishIcon":"https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     * "stepGuideProcessCodeList":["angelReceiveWork"],
     * "stepGuideProcessIcon":"https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     * "stepGuideWaitCodeList":["service","submitTest","testing","reported"],
     * "stepGuideWaitIcon":"https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     * "footerButtonCodeList": ["refundBtn","contactCustomerBtn","rePurchaseBtn"],
     * "hiddenFloorCode":["promiseAngelInfo","promideCodeInfo"],
     * "hiddenPatientFieldList":[],
     * "hiddenAngelInfoList":[],
     * "hiddenAngelBtnList":[]
     * }
     *
     * @param statusMapping 订单状态映射
     * @param order         order
     * @param
     * @param
     * @return {@link ViaStatusMapping}
     */
    private ViaStatusMapping hitOrderStatusMapping(JdOrderDTO order, List<ViaStatusMapping> statusMapping) {
        log.info("XfylVtpHomeTestOrderDetailHandler hitOrderStatusMapping statusMapping:{}", JSON.toJSONString(statusMapping));
        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus", Objects.isNull(order) ? null : order.getOrderStatus());

        for (ViaStatusMapping viaStatusMapping : statusMapping) {
            if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE).execute(param)) {
                return viaStatusMapping;
            }
        }
        log.info("XfylVtpHomeTestOrderDetailHandler hitOrderStatusMapping not hit");
        throw new SystemException(SupportErrorCode.VIA_STATUS_MAPPING_ERROR);
    }

    private ViaStatusMapping hitPromiseStatusMapping(JdOrderDTO order, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList, List<ViaStatusMapping> promiseStatusMapping, Integer riskAssessmentStatus) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus", Objects.isNull(order) ? null : order.getOrderStatus());
        param.put("promiseStatus", Objects.isNull(jdhPromise) ? null : jdhPromise.getPromiseStatus());
        param.put("medPromiseStatusList", CollUtil.isEmpty(medicalPromiseList) ? null : medicalPromiseList.stream().map(MedicalPromiseDTO::getStatus).collect(Collectors.toList()));
        param.put("freeze", jdhPromise.getFreeze());
        param.put("riskAssessmentStatus", riskAssessmentStatus);

        // 查询履约单风险等级
        PromiseDangerLevelRequest highRiskLevelRequest = PromiseDangerLevelRequest.builder()
                .promiseId(jdhPromise.getPromiseId())
                .build();
        List<Integer> promiseDangerLevelList = promiseApplication.queryPromiseDangerLevel(highRiskLevelRequest);
        boolean highRiskAssessmentFlag = false;
        if (CollectionUtils.isNotEmpty(promiseDangerLevelList)){
            // 检查高危风险等级
            highRiskAssessmentFlag = promiseDangerLevelList.stream().anyMatch(s -> Arrays.asList(3).contains(s));
        }
        param.put("highRiskAssessmentFlag", highRiskAssessmentFlag);
        log.info("XfylVtpHomeTestOrderDetailHandler#hitPromiseStatusMapping.param={}", JSON.toJSONString(param));
        for (ViaStatusMapping viaStatusMapping : promiseStatusMapping) {
            if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE).execute(param)) {
                return viaStatusMapping;
            }
        }
        return null;
    }


    /**
     * 处理楼层列表
     *
     * @param ctx           上下文
     * @param statusMapping 状态映射
     * @param jdOrder       JD订单
     * @param jdhVouchers   服务凭证List
     */
    private void dealFloorList(FillViaConfigDataContext ctx, ViaStatusMapping statusMapping, JdOrderDTO jdOrder, List<JdhVoucher> jdhVouchers) {
        log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList ctx={}, statusMapping={}, jdOrder={}, jdhVouchers={}"
                , JSON.toJSONString(ctx), JSON.toJSONString(statusMapping), JSON.toJSONString(jdOrder), JSON.toJSONString(jdhVouchers));
        ViaConfig viaConfig = ctx.getViaConfig();
        String userPin = ctx.getUserPin();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (ViaFloorInfo viaFloorInfo : viaConfig.getFloorList()) {
            //订单状态
            if (ViaFloorEnum.PROMISE_ORDER_STATUS.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelOrderStatus(viaFloorInfo, jdOrder), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList handelOrderStatus exception", exception);
                    return null;
                }));
            }

            //卡券信息
            if (ViaFloorEnum.VTP_VOUCHER_LIST_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelVtpVoucherListInfo(viaFloorInfo, jdhVouchers, jdOrder, viaConfig, ctx), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList handelVtpVoucherListInfo exception", exception);
                    return null;
                }));
            }

            //购买商品信息
            if (ViaFloorEnum.PROMISE_ORDER_SKU_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelOrderSkuInfo(viaConfig, viaFloorInfo, jdOrder), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList handelOrderSkuInfo exception", exception);
                    return null;
                }));
            }

            //企微卡片信息 weChatCardInfo
            if (ViaFloorEnum.PROMISE_WECHAT_CARD_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelWeChatCardInfo(viaFloorInfo, jdOrder, userPin), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList handelWeChatCardInfo exception", exception);
                    return null;
                }));
            }

            //订单信息
            if (ViaFloorEnum.PROMISE_ORDER_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelOrderInfo(viaFloorInfo, jdOrder), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList handelOrderInfo exception", exception);
                    return null;
                }));
            }

            //底部按钮
            if (ViaFloorEnum.PROMISE_FOOTER_BUTTONS.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleFooterButtons(ctx, viaFloorInfo, statusMapping, jdOrder, jdhVouchers), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList handleFooterButtons exception", exception);
                    return null;
                }));
            }
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }


    private void handelVtpVoucherListInfo(ViaFloorInfo viaFloorInfo, List<JdhVoucher> jdhVouchers, JdOrderDTO jdOrderDTO, ViaConfig viaConfig, FillViaConfigDataContext ctx) {
        //如果没有Voucher，直接返回信息：卡券信息生成中，请稍后
        if (CollectionUtils.isEmpty(jdhVouchers)) {
            List<ViaFloorConfig> floorConfigList = new ArrayList<>();
            List<ViaStatusMapping> voucherStatusMapping = viaConfig.getVoucherStatusMapping();
            log.info("XfylVtpHomeTestOrderDetailHandler handelVtpVoucherListInfo coupons voucherStatusMapping={}", JSON.toJSONString(voucherStatusMapping));
            // wareType int 商品类型（"0":一般商品 "1":延保商品 "2":赠品结构（可能是赠品或者附件））
            String extend = jdOrderDTO.getExtend();
            if (StringUtils.isBlank(extend)){
                return;
            }
            Map<String, Object> param = new HashMap<>();
            param.put("wareType", JSON.parseObject(extend).getInteger("wareType"));
            for (ViaStatusMapping viaStatusMapping : voucherStatusMapping) {
                if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE).execute(param)) {
                    ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
                    viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                    viaFloorConfig.setMainIcon(viaStatusMapping.getMainIcon());
                    floorConfigList.add(viaFloorConfig);
                }
            }
            viaFloorInfo.setFloorConfigList(floorConfigList);
            log.info("XfylVtpHomeTestOrderDetailHandler handelVtpVoucherListInfo coupons viaFloorInfo={}", JSON.toJSONString(viaFloorInfo));
            return;
        }

        List<ViaVoucherInfoDto> voucherInfoDtos = new ArrayList<>();
        AtomicInteger i = new AtomicInteger(NumConstant.NUM_1);
        jdhVouchers.forEach(jdhVoucher->{
            log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.jdhVoucher={}", JSON.toJSONString(jdhVoucher));
            ViaVoucherInfoDto viaVoucherInfoDto = new ViaVoucherInfoDto();
            viaVoucherInfoDto.setViaVoucherStatus(jdhVoucher.getStatus());
            // 冻结
            if (NumConstant.NUM_1.equals(jdhVoucher.getFreeze())){
                viaVoucherInfoDto.setViaVoucherStatus(NumConstant.NUM_1000);
            }
            viaVoucherInfoDto.setMainTitle("卡券" + i.get());
            //总次数
            viaVoucherInfoDto.setTotalNum(jdhVoucher.getPromiseNum());
            // 过期时间
            viaVoucherInfoDto.setExpireDate(jdhVoucher.getExpireDate());
            //查询当前voucher下的服务单jdhPromises
            List<JdhPromise> jdhPromises = promiseRepository.findList(PromiseRepQuery.builder().voucherIds(Collections.singletonList(jdhVoucher.getVoucherId())).build());
            log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.jdhPromises={}", JSON.toJSONString(jdhPromises));
            // 待预约状态
            List<Integer> waitPromiseStatus = Arrays.asList(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus(),JdhPromiseStatusEnum.CANCEL_SUCCESS.getStatus());
            // 过滤非待履约状态的jdhPromises
            List<JdhPromise> validPromises = Optional.ofNullable(jdhPromises).map(List::stream).orElseGet(Stream::empty).filter(jdhPromise ->
                    !waitPromiseStatus.contains(jdhPromise.getPromiseStatus())).collect(Collectors.toList());
            log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.validPromises={}", JSON.toJSONString(validPromises));
            //使用次数
            viaVoucherInfoDto.setUsedNum(validPromises.size());
            //剩余次数
            viaVoucherInfoDto.setRemainingNum(viaVoucherInfoDto.getTotalNum() - viaVoucherInfoDto.getUsedNum());
            //使用进度
            viaVoucherInfoDto.setUseProgressRate(new BigDecimal(viaVoucherInfoDto.getUsedNum()).divide(new BigDecimal(viaVoucherInfoDto.getTotalNum()), 2, RoundingMode.DOWN));
            log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.before.viaVoucherInfoDto={}", JSON.toJSONString(viaVoucherInfoDto));

            //过滤待履约状态的jdhPromises
            List<JdhPromise> waitPromises = Optional.ofNullable(jdhPromises).map(List::stream).orElseGet(Stream::empty).filter(jdhPromise ->
                    waitPromiseStatus.contains(jdhPromise.getPromiseStatus())).collect(Collectors.toList());
            log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.waitPromises={}", JSON.toJSONString(waitPromises));
            // 判断卡券按钮
            ViaStatusMapping viaStatusMapping = hitVoucherStatusMapping(viaConfig.getVoucherStatusMapping(), viaVoucherInfoDto, waitPromises, ctx);
            log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.viaStatusMapping={}", JSON.toJSONString(viaStatusMapping));
            if (viaStatusMapping != null){
                viaVoucherInfoDto.setBtnList(viaStatusMapping.getBtnList());
            }
            log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.viaVoucherInfoDto={}", JSON.toJSONString(viaVoucherInfoDto));

            //服务单实体构建
            List<ViaPromiseInfoDto> viaPromiseInfoDtos = new ArrayList<>();
            validPromises.forEach(validPromise->{
                log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.validPromise={}", JSON.toJSONString(validPromise));
                List<MedicalPromiseDTO> medicalPromiseDTOS = new ArrayList<>();
                if (Objects.nonNull(validPromise)) {
                    medicalPromiseDTOS = queryMedicalPromiseList(validPromise);
                }
                ViaPromiseInfoDto viaPromiseInfoDto = constructViaPromiseInfoDto(jdOrderDTO, validPromise, medicalPromiseDTOS, viaConfig, ctx);
                log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.viaPromiseInfoDto={}", JSON.toJSONString(viaPromiseInfoDto));
                if (viaPromiseInfoDto != null){
                    viaPromiseInfoDto.setPromiseId(validPromise.getPromiseId());
                    viaPromiseInfoDto.setPromiseStatus(validPromise.getPromiseStatus());
                    viaPromiseInfoDtos.add(viaPromiseInfoDto);
                }
            });
            //服务单实体赋值
            viaVoucherInfoDto.setPromiseInfoDtos(viaPromiseInfoDtos);
            i.getAndIncrement();
            voucherInfoDtos.add(viaVoucherInfoDto);
            log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.after.viaVoucherInfoDto={}", JSON.toJSONString(viaVoucherInfoDto));
        });

        List<ViaFloorConfig> floorConfigList = new ArrayList<>();
        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
        /**
         * 卡券生成状态
         * 0-成功
         * 1-单独购买场景下，当支付成功后，卡券信息未生成
         * 2-赠品场景下，当主品支付成功妥投之前，卡券信息亦未生成
         */
        viaFloorConfig.setViaStatus(NumConstant.NUM_0);
        // 卡券排序
        log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo sort before voucherInfoDtos={}", JSON.toJSONString(voucherInfoDtos));
        viaFloorConfig.setVoucherInfoDtos(this.sortViaVoucherInfoDto(voucherInfoDtos));
        floorConfigList.add(viaFloorConfig);
        log.info("XfylVtpHomeTestOrderDetailHandler#handelVtpVoucherListInfo.viaFloorConfig={},floorConfigList={}", JSON.toJSONString(viaFloorConfig), JSON.toJSONString(floorConfigList));
        viaFloorInfo.setFloorConfigList(floorConfigList);
    }

    private List<ViaVoucherInfoDto> sortViaVoucherInfoDto(List<ViaVoucherInfoDto> voucherInfoDtos){
        try {
            if (CollectionUtils.isEmpty(voucherInfoDtos)){
                return Lists.newArrayList();
            }
            voucherInfoDtos.forEach(v->{
                // 待预约
                v.setSort(NumConstant.NUM_8);
                List<ViaPromiseInfoDto> promiseList = v.getPromiseInfoDtos();
                if (CollectionUtils.isNotEmpty(promiseList)){
                    ViaPromiseInfoDto promise = promiseList.get(0);

                    List<Integer> medicalPromiseStatus = new ArrayList<>();
                    List<MedicalPromiseDTO> medicalPromiseList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseRequest.builder().promiseId(promise.getPromiseId()).build());
                    if (CollectionUtils.isNotEmpty(medicalPromiseList)){
                        medicalPromiseStatus = medicalPromiseList.stream().map(MedicalPromiseDTO::getStatus).distinct().collect(Collectors.toList());
                    }
                    // 查询风险评估
                    RiskAssessmentQuery req = new RiskAssessmentQuery();
                    req.setPromiseId(promise.getPromiseId());
                    List<RiskAssessment> riskAssessmentList = riskAssessmentApplication.queryRiskAssessmentList(req);
                    Integer riskAssessmentStatus = null;
                    if (CollectionUtils.isNotEmpty(riskAssessmentList)){
                        riskAssessmentStatus = riskAssessmentList.get(0).getRiskAssessmentStatus();
                    }

                    // 查询履约单风险等级
                    PromiseDangerLevelRequest highRiskLevelRequest = new PromiseDangerLevelRequest();
                    highRiskLevelRequest.setPromiseId(promise.getPromiseId());
                    if (CollectionUtils.isNotEmpty(medicalPromiseList)){
                        highRiskLevelRequest.setSkuId(medicalPromiseList.get(0).getServiceId());
                    }
                    List<Integer> promiseDangerLevelList = promiseApplication.queryPromiseDangerLevel(highRiskLevelRequest);
                    // 检查高危风险等级
                    boolean highRiskAssessmentFlag = false;
                    if (CollectionUtils.isNotEmpty(promiseDangerLevelList)){
                        highRiskAssessmentFlag = promiseDangerLevelList.stream().anyMatch(s -> Arrays.asList(3).contains(s));
                    }

                    // 待评估 > 待护士接单 > 待护士上门 > 护士服务中 > 送检中 > 实验室检测中 > 待预约 > 报告已生 > 冻结（退款中） > 作废（退款成功）
                    if (RiskAssessmentStatusEnum.WAITING_ASS.getStatus().equals(riskAssessmentStatus) && highRiskAssessmentFlag){
                        // 待评估
                        v.setSort(NumConstant.NUM_1);
                    } else if (Arrays.asList(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus(),JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus(),JdhPromiseStatusEnum.MODIFY_ING.getStatus()).contains(promise.getPromiseStatus())){
                        // 待护士接单
                        v.setSort(NumConstant.NUM_2);
                    } else if (Arrays.asList(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus(),JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus()).contains(promise.getPromiseStatus())){
                        // 待护士上门
                        v.setSort(NumConstant.NUM_3);
                    } else if (Arrays.asList(JdhPromiseStatusEnum.SERVICE_READY.getStatus()).contains(promise.getPromiseStatus())){
                        // 护士上门中
                        v.setSort(NumConstant.NUM_4);
                    } else if (JdhPromiseStatusEnum.SERVICING.getStatus().equals(promise.getPromiseStatus())){
                        // 护士服务中
                        v.setSort(NumConstant.NUM_5);
                    } else if (JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus().equals(promise.getPromiseStatus()) && !medicalPromiseStatus.contains(4) && !medicalPromiseStatus.contains(5) && !medicalPromiseStatus.contains(6)){
                        // 送检中
                        v.setSort(NumConstant.NUM_6);
                    } else if (JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus().equals(promise.getPromiseStatus()) && medicalPromiseStatus.contains(4) && !medicalPromiseStatus.contains(5) && !medicalPromiseStatus.contains(6)){
                        // 实验室检测中
                        v.setSort(NumConstant.NUM_7);
                    } else if ((JdhPromiseStatusEnum.COMPLETE.getStatus().equals(promise.getPromiseStatus()) || medicalPromiseStatus.contains(5) || medicalPromiseStatus.contains(6))){
                        // 报告已生成
                        v.setSort(NumConstant.NUM_9);
                    } else if (NumConstant.NUM_1000.equals(v.getViaVoucherStatus())){
                        // 冻结（退款中）
                        v.setSort(NumConstant.NUM_10);
                    } else if (NumConstant.NUM_4.equals(v.getViaVoucherStatus())){
                        // 作废（退款成功）
                        v.setSort(NumConstant.NUM_11);
                    }
                }
            });
            // 排序 1 2 3
            voucherInfoDtos.sort(Comparator.comparing(ViaVoucherInfoDto::getSort));
        } catch (Exception e) {
            log.error("XfylVtpHomeTestOrderDetailHandler sortViaVoucherInfoDto error e", e);
        }
        return voucherInfoDtos;
    }

    /**
     * 构建服务单详情
     *
     * @param
     * @param jdOrderDTO
     * @param medicalPromiseDTOS
     * @return
     */
    private ViaPromiseInfoDto constructViaPromiseInfoDto(JdOrderDTO jdOrderDTO, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseDTOS, ViaConfig viaConfig, FillViaConfigDataContext ctx) {
        log.info("XfylHomeTestOrderDetailHandler constructViaPromiseInfoDto medicalPromiseDTOS={}, viaConfig={}", JSON.toJSONString(medicalPromiseDTOS), JSON.toJSONString(viaConfig));

        // 查询风险评估
        RiskAssessmentQuery req = new RiskAssessmentQuery();
        req.setPromiseId(jdhPromise.getPromiseId());
        List<RiskAssessment> riskAssessmentList = riskAssessmentApplication.queryRiskAssessmentList(req);
        Integer riskAssessmentStatus = null;
        if (CollectionUtils.isNotEmpty(riskAssessmentList)){
            riskAssessmentStatus = riskAssessmentList.get(0).getRiskAssessmentStatus();
        }

        // ==>>>> 过滤statusMapping
        ViaStatusMapping promiseStatusMapping = hitPromiseStatusMapping(jdOrderDTO, jdhPromise, medicalPromiseDTOS, viaConfig.getPromiseStatusMapping(), riskAssessmentStatus);
        // ==>>>> 移除当前状态下隐藏的楼层
        log.info("XfylVtpHomeTestOrderDetailHandler#constructViaPromiseInfoDto.promiseStatusMapping={}", JSON.toJSONString(promiseStatusMapping));
        if (promiseStatusMapping == null){
            return null;
        }
        List<ViaPromiseFloorInfoDto> promiseFloorList = JSON.parseArray(JSON.toJSONString(viaConfig.getPromiseFloorList()), ViaPromiseFloorInfoDto.class);
        log.info("XfylHomeTestOrderDetailHandler handle before promiseFloorList:{}", JSON.toJSONString(promiseFloorList));
        List<ViaPromiseFloorInfoDto> copyPromiseFloorList = clearHiddenPromiseFloor(promiseStatusMapping, promiseFloorList);
        log.info("XfylHomeTestOrderDetailHandler handle after copyPromiseFloorList:{}", JSON.toJSONString(copyPromiseFloorList));
        // ==>>>> promise的楼层处理
        return dealPromiseFloorList(viaConfig, promiseStatusMapping, jdOrderDTO, jdhPromise, Objects.isNull(medicalPromiseDTOS) ? null : medicalPromiseDTOS, copyPromiseFloorList, ctx);
    }

    /**
     * TODO 处理服务单各个楼层
     *
     * @param viaConfig
     * @param promiseStatusMapping
     * @param jdOrderDTO
     * @param jdhPromise
     * @param medicalPromiseDTOS
     */
    private ViaPromiseInfoDto dealPromiseFloorList(ViaConfig viaConfig, ViaStatusMapping promiseStatusMapping, JdOrderDTO jdOrderDTO, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseDTOS, List<ViaPromiseFloorInfoDto> copyPromiseFloorList, FillViaConfigDataContext ctx) {
        List<JdhPromiseHistory> promiseHistoryCf = queryPromiseHistory(jdhPromise);
        log.info("XfylVtpHomeTestOrderDetailHandler#dealPromiseFloorList.viaConfig={},promiseStatusMapping={},jdOrderDTO={},jdhPromise={},medicalPromiseDTOS={}", JSON.toJSONString(viaConfig), JSON.toJSONString(promiseStatusMapping), JSON.toJSONString(jdOrderDTO), JSON.toJSONString(jdhPromise), JSON.toJSONString(medicalPromiseDTOS));
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        ViaPromiseInfoDto viaPromiseInfoDto = new ViaPromiseInfoDto();

        // 填充模版数据
        Map<String, Object> sourceData = Maps.newHashMap();
        sourceData.put(PromiseAggregateEnum.PROMISE.getCode(), jdhPromise);
        sourceData.put(TradeAggregateEnum.ORDER.getCode(), jdOrderDTO);
        sourceData.put("statusMapping", promiseStatusMapping);
        sourceData.put("ctx", ctx);

        List<ViaPromiseFloorInfoDto> viaPromiseFloorInfoDtoList = new ArrayList<>();
        for (ViaPromiseFloorInfoDto viaPromiseFloorInfoDto : copyPromiseFloorList) {
            //履约信息 - 服务者 promiseAngelInfo
            if (ViaFloorEnum.PROMISE_ANGEL_INFO.getFloorCode().equals(viaPromiseFloorInfoDto.getPromiseFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseAngelInfo(viaPromiseFloorInfoDto, promiseStatusMapping, jdhPromise, null, ctx), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList promiseAngelInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 消费码 promiseCodeInfo
            if (ViaFloorEnum.PROMISE_CODE_INFO.getFloorCode().equals(viaPromiseFloorInfoDto.getPromiseFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseCodeInfo(viaPromiseFloorInfoDto, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList promiseCodeInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 被服务者 promisePatientInfo
            if (ViaFloorEnum.PROMISE_PATIENT_INFO.getFloorCode().equals(viaPromiseFloorInfoDto.getPromiseFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelPromisePatientInfo(viaPromiseFloorInfoDto, promiseStatusMapping, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList promisePatientInfo exception", exception);
                    return null;
                }));
            }

            //样本信息楼层 materialInfo
            if (ViaFloorEnum.PROMISE_MATERIAL_INFO.getFloorCode().equals(viaPromiseFloorInfoDto.getPromiseFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelMaterialInfo(viaConfig, viaPromiseFloorInfoDto, jdhPromise, null), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList materialInfo exception", exception);
                    return null;
                }));
            }

            if (ViaFloorEnum.PROMISE_FLOOR_FOOTER_BUTTONS.getFloorCode().equals(viaPromiseFloorInfoDto.getPromiseFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseFooterButtons(ctx, viaConfig, viaPromiseFloorInfoDto, promiseStatusMapping, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList promiseFooterButtons exception", exception);
                    return null;
                }));
            }

            //履约信息 - 送检中 promiseUnderInspectionInfo
            if (ViaFloorEnum.PROMISE_UNDER_INSPECTION_INFO.getFloorCode().equals(viaPromiseFloorInfoDto.getPromiseFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseUnderInspectionInfo(viaPromiseFloorInfoDto, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList promiseUnderInspectionInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 检测中 promiseLaboratoryTestingInfo
            if (ViaFloorEnum.PROMISE_LABORATORY_TESTING_INFO.getFloorCode().equals(viaPromiseFloorInfoDto.getPromiseFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseLaboratoryTestingInfo(viaPromiseFloorInfoDto, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList promiseLaboratoryTestingInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 报告已生成 promiseViewReportInfo
            if (ViaFloorEnum.PROMISE_VIEW_REPORT_INFO.getFloorCode().equals(viaPromiseFloorInfoDto.getPromiseFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseViewReportInfo(viaPromiseFloorInfoDto, jdhPromise,ctx), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList promiseViewReportInfo exception", exception);
                    return null;
                }));
            }

            //履约信息 - 报告已生成 promiseViewReportInfo
            if (ViaFloorEnum.PROMISE_VIEW_REPORT_INFO2.getFloorCode().equals(viaPromiseFloorInfoDto.getPromiseFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseViewReportInfo2(viaConfig,viaPromiseFloorInfoDto, sourceData, medicalPromiseDTOS, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestOrderDetailHandler dealFloorList promiseViewReportInfo exception", exception);
                    return null;
                }));
            }

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            ViaPromiseFloorInfoDto copyViaPromiseFloorInfoDto = JSON.parseObject(JSON.toJSONString(viaPromiseFloorInfoDto), ViaPromiseFloorInfoDto.class);
            viaPromiseFloorInfoDtoList.add(copyViaPromiseFloorInfoDto);
        }
        viaPromiseInfoDto.setViaPromiseFloorInfoDtoList(viaPromiseFloorInfoDtoList);
        log.info("XfylVtpHomeTestOrderDetailHandler#dealPromiseFloorListn.before.viaPromiseInfoDto={}", JSON.toJSONString(viaPromiseInfoDto));
        viaPromiseInfoDto.setMainTitle(promiseStatusMapping.getMainTitle());

        if (CollectionUtils.isNotEmpty(jdhPromise.getServices()) && StringUtils.isNotBlank(viaPromiseInfoDto.getMainTitle())){
            JdhSku jdhSku = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(jdhPromise.getServices().get(0).getServiceId()).build());
            if (Objects.nonNull(jdhSku)){
                viaPromiseInfoDto.setMainTitle(productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), viaPromiseInfoDto.getMainTitle()));
            }
        }

        String title = "";
        try {
            String aggregateStatus = promiseStatusMapping.getAggregateStatus();
            if (AGGREGATE_STATUS_ETA.contains(aggregateStatus)) {
                // 获取表达式配置value配置的容器
                Map<String, Object> containerMap = promiseStatusMapping.getContainer();

                // todo 临时添加，修改预约时间不走promise预测
                List<JdhPromiseHistory> promiseHistories = promiseHistoryRepository.findList(PromiseHistoryRepQuery.builder().promiseId(jdhPromise.getPromiseId()).build());
                if (CollectionUtils.isNotEmpty(promiseHistories)){
                    boolean userSubmitModifyFlag = promiseHistories.stream().anyMatch(p -> NumConstant.NUM_5.equals(p.getAfterStatus()) && "userSubmitModify".equals(p.getEventCode()));
                    containerMap.put("userSubmitModifyFlag", userSubmitModifyFlag);
                }

                UserPromisegoBo userPromisegoBo = this.queryUserPromiseGoInfo(jdhPromise, aggregateStatus);
                log.info("XfylVtpHomeTestOrderDetailHandler dealPromiseFloorList userPromisegoBo={}", JSON.toJSONString(userPromisegoBo));
                if (Objects.nonNull(userPromisegoBo)){
                    if (Objects.nonNull(userPromisegoBo.getCurrScript())){
                        containerMap.put("currScript", EntityUtil.getFiledDefaultNull(userPromisegoBo.getCurrScript(), ScriptBo::getScriptContent));
                    }
                    if (Objects.nonNull(userPromisegoBo.getTermScript())){
                        containerMap.put("termScript", EntityUtil.getFiledDefaultNull(userPromisegoBo.getTermScript(), ScriptBo::getScriptContent));
                    }
                    if (Objects.nonNull(userPromisegoBo.getWarmTipScript())){
                        containerMap.put("warmTipScript", EntityUtil.getFiledDefaultNull(userPromisegoBo.getWarmTipScript(), ScriptBo::getScriptContent));
                    }
                }
                log.info("XfylVtpHomeTestOrderDetailHandler dealPromiseFloorList containerMap={}", JSON.toJSONString(containerMap));

                // 解析出二级标题
                title = promiseStatusMapping.getTitle();
                if (StringUtils.isNotBlank(title)) {
                    Expression expression = AviatorEvaluator.compile(title, true);
                    Object res = expression.execute(containerMap);
                    title = Objects.toString(res, "");
                }
            }

            if (StringUtils.isBlank(title)){
                title  = handler.calcPromiseTime(jdhPromise, medicalPromiseDTOS, null);
            }
           log.info("XfylVtpHomeTestOrderDetailHandler#dealPromiseFloorListn.title={}",title);
        }catch (Exception e){
            log.error("XfylVtpHomeTestOrderDetailHandler#dealPromiseFloorListn has error",e);
        }
        viaPromiseInfoDto.setTitle(title);

        viaPromiseInfoDto.setMainDesc(promiseStatusMapping.getNoticeTip());
        viaPromiseInfoDto.setMainIcon(promiseStatusMapping.getMainIcon());
        // 视图Promise状态映射
        ViaStatusMapping viaStatusMapping = this.hitPromiseStatusMapping(jdhPromise, viaConfig.getStatusMapping());
        log.info("XfylVtpHomeTestOrderDetailHandler#dealPromiseFloorListn.viaStatusMapping={}",JSON.toJSONString(viaStatusMapping));
        // 预约详情
        ViaActionInfo action = JSON.parseObject(JSON.toJSONString(viaStatusMapping.getViaActionInfo()), ViaActionInfo.class);
        String url = EntityUtil.fillUrlByBean(action.getUrl(), jdhPromise);
        url = getUrlByEnvType(ctx.getEnvType(), url);
        action.setUrl(url);

        viaPromiseInfoDto.setAction(action);
        log.info("XfylVtpHomeTestOrderDetailHandler#dealPromiseFloorListn.after.viaPromiseInfoDto={}", JSON.toJSONString(viaPromiseInfoDto));
        return viaPromiseInfoDto;
    }


    private ViaStatusMapping hitPromiseStatusMapping(JdhPromise jdhPromise, List<ViaStatusMapping> statusMapping) {
        Map<String, Object> param = new HashMap<>();
        param.put("promiseStatus", jdhPromise.getPromiseStatus());
        for (ViaStatusMapping viaStatusMapping : statusMapping) {
            if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE).execute(param)) {
                return viaStatusMapping;
            }
        }
        throw new SystemException(SupportErrorCode.VIA_STATUS_MAPPING_ERROR);
    }

    private void handlePromiseAngelInfo(ViaPromiseFloorInfoDto viaPromiseFloorInfoDto, ViaStatusMapping promiseStatusMapping, JdhPromise jdhPromise, List<JdhPromiseHistory> promiseHistories, FillViaConfigDataContext ctx) {
        // 查询护士信息
        log.info("XfylVtpHomeTestOrderDetailHandler#handlePromiseAngelInfo.viaPromiseFloorInfoDto={},promiseStatusMapping={},jdhPromise={},promiseHistories={}", JSON.toJSONString(viaPromiseFloorInfoDto), JSON.toJSONString(promiseStatusMapping), JSON.toJSONString(jdhPromise), JSON.toJSONString(promiseHistories));
        List<ViaPromiseFloorConfig> viaPromiseFloorConfigs = viaPromiseFloorInfoDto.getViaPromiseFloorConfigs();
        Iterator<ViaPromiseFloorConfig> iterator = viaPromiseFloorConfigs.iterator();
        AngelWorkDetailDto angelWork = queryAngelDto(jdhPromise.getPromiseId());
        log.info("XfylHomeTestOrderDetailHandler handlePromiseAngelInfo promiseId={}, angelWork:{}", jdhPromise.getPromiseId(), JSON.toJSONString(angelWork));
        while (iterator.hasNext()) {
            ViaPromiseFloorConfig viaPromiseFloorConfig = iterator.next();
            List<String> hiddenAngelFieldList = promiseStatusMapping.getHiddenAngelFieldList();
            if (CollectionUtils.isNotEmpty(hiddenAngelFieldList) && hiddenAngelFieldList.contains(viaPromiseFloorConfig.getFieldKey())) {
                iterator.remove();
                continue;
            }
            if (ViaAngelInfoFieldEnum.HOME_DATE.getField().equals(viaPromiseFloorConfig.getFieldKey())) {
                //不合适
                if (JdhPromiseStatusEnum.SERVICING.getStatus().equals(jdhPromise.getPromiseStatus())) {
                    Date servicingTime = jdhPromise.getUpdateTime();
                    if (CollectionUtils.isNotEmpty(promiseHistories)){
                        Optional<JdhPromiseHistory> jdhPromiseHistory = promiseHistories.stream().filter(ele -> JdhPromiseStatusEnum.SERVICING.getStatus().equals(ele.getAfterStatus())).findFirst();
                        if (jdhPromiseHistory.isPresent()) {
                            JdhPromiseHistory history = jdhPromiseHistory.get();
                            servicingTime = history.getCreateTime();
                        }
                    }
                    viaPromiseFloorConfig.setFieldValue(TimeUtils.dateTimeToStr(servicingTime, TimeFormat.LONG_PATTERN_LINE) + " 已上门");
                } else {
                    JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(jdhPromise.getVerticalCode());
                    if(Objects.nonNull(jdhVerticalBusiness) && BusinessModeEnum.checkTestAndCare(jdhVerticalBusiness.getBusinessModeCode())) {
                        viaPromiseFloorConfig.setFieldValue("预约 " + getFullAppointmentDateDesc(jdhPromise.getAppointmentTime()) + " 上门");
                    }
                }
                continue;
            }
            if (ViaAngelInfoFieldEnum.ANGEL_NAME.getField().equals(viaPromiseFloorConfig.getFieldKey())) {
                if (Objects.isNull(angelWork)) {
                    iterator.remove();
                } else {
                    String angelName = angelWork.getAngelName();
                    String angelType = AngelWorkTypeEnum.getEnumByCode(angelWork.getWorkType()).getAngelType();
                    if (CollectionUtils.isNotEmpty(jdhPromise.getServices()) && StringUtils.isNotBlank(angelType)){
                        JdhSku jdhSku = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(jdhPromise.getServices().get(0).getServiceId()).build());
                        if (Objects.nonNull(jdhSku)){
                            angelType = productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), angelType);
                        }
                    }
                    viaPromiseFloorConfig.setFieldValue(angelName.charAt(0)+ "*" + angelType);
                }
                continue;
            }
            if (ViaAngelInfoFieldEnum.ANGEL_PHONE.getField().equals(viaPromiseFloorConfig.getFieldKey())) {
                if (Objects.isNull(angelWork)) {
                    iterator.remove();
                } else {
                    viaPromiseFloorConfig.setFieldValue(angelWork.getAngelPhone());
                }
                continue;
            }

            if (ViaAngelInfoFieldEnum.HEAD_IMG.getField().equals(viaPromiseFloorConfig.getFieldKey())) {
                if (Objects.nonNull(angelWork)) {
                    viaPromiseFloorConfig.setFieldValue(angelWork.getAngelHeadImg());
                } else {
                    iterator.remove();
                }
                continue;
            }

            // 按钮
            List<ViaBtnInfo> btnList = viaPromiseFloorConfig.getBtnList();
            log.info("XfylVtpHomeTestOrderDetailHandler#handlePromiseAngelInfo.viaPromiseFloorInfoDto.btnList={}", JSON.toJSONString(btnList));
            if (CollUtil.isNotEmpty(btnList)) {
                Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
                while (btnInfoIterator.hasNext()) {
                    ViaBtnInfo btnInfo = btnInfoIterator.next();
                    List<String> hiddenAngelBtnList = promiseStatusMapping.getHiddenAngelBtnList();
                    if (CollectionUtils.isNotEmpty(hiddenAngelBtnList) && hiddenAngelBtnList.contains(btnInfo.getCode())) {
                        btnInfoIterator.remove();
                        continue;
                    }
                    //查看位置
                    if (ViaAngelInfoBtnEnum.VIEW_POSITION.getBtn().equals(btnInfo.getCode())) {
                        if (!AngelWorkStatusEnum.WAIT_SERVICE.getType().equals(angelWork.getStatus())){
                            log.info("XfylVtpHomeTestOrderDetailHandler handlePromiseAngelInfo no WAIT_SERVICE");
                            btnInfoIterator.remove();
                            continue;
                        }else {
                            try {
                                AngelTrackQuery angelTrackQuery = new AngelTrackQuery();
                                angelTrackQuery.setPromiseId(jdhPromise.getPromiseId());
                                AngelTrackDto track = angelWorkApplication.getTransferTrack(angelTrackQuery);
                                log.info("XfylVtpHomeTestOrderDetailHandler handlePromiseAngelInfo getTransferTrack angelTrackQuery={}, track={}", JSON.toJSONString(angelTrackQuery), JSON.toJSONString(track));
                                if (Objects.nonNull(track) && StringUtils.isNotBlank(track.getTrackUrl())) {
                                    try {
                                        ViaActionInfo action = btnInfo.getAction();
                                        String url = track.getTrackUrl();
                                        log.info("XfylVtpHomeTestOrderDetailHandler handlePromiseAngelInfo trackUrl={}",url);
                                        String phone = callRecordApplication.getAppointmentPhone(jdhPromise);
                                        PhoneNumber phoneNumber = new PhoneNumber();
                                        String encodedPhone = URLEncoder.encode(phoneNumber.encrypt(phone), "UTF-8");
                                        url = url+"&userType=appointmentUser&encryptPhone="+encodedPhone;
                                        String jumpUrl = "openapp.jdmobile://virtual?params={\"category\":\"jump\",\"des\":\"m\",\"url\":\"{url}\"}";
                                        if (EnvTypeEnum.JD_APP.getCode().equals(ctx.getEnvType())) {
                                            url = jumpUrl.replace("{url}", url);
                                        }
                                        action.setUrl(url);
                                    } catch (Exception e) {
                                        log.error("XfylVtpHomeTestOrderDetailHandler handlePromiseAngelInfo trackUrl error e", e);
                                    }
                                } else {
                                    btnInfoIterator.remove();
                                    continue;
                                }
                            } catch (Exception e) {
                                log.error("XfylVtpHomeTestOrderDetailHandler handlePromiseAngelInfo getTransferTrack error e", e);
                            }
                        }
                    }
                }
            }

            if(ViaAngelInfoFieldEnum.CONTACT_ANGEL.getField().equals(viaPromiseFloorConfig.getFieldKey())){
                if(Objects.isNull(angelWork)){
                    iterator.remove();
                }else{
                    if (Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType(),AngelWorkStatusEnum.SERVICING.getType())
                            .contains(angelWork.getStatus())){
                        ViaActionInfo action = viaPromiseFloorConfig.getAction();
                        if (action != null){
                            action.getParams().put("promiseId",angelWork.getPromiseId());
                            ViaActionInfo nextAction = action.getNextAction();
                            if (nextAction != null){
                                nextAction.getParams().put("promiseId",angelWork.getPromiseId());
                            }
                        }
                    }else {
                        iterator.remove();
                        continue;
                    }
                }
                continue;
            }
        }
        log.info("XfylHomeTestOrderDetailHandler handlePromiseAngelInfo viaPromiseFloorInfoDto:{}", JSON.toJSONString(viaPromiseFloorInfoDto));
    }

    /**
     * 隐藏promise楼层
     *
     * @param promiseStatusMapping
     * @param promiseFloorList
     */
    private  List<ViaPromiseFloorInfoDto> clearHiddenPromiseFloor(ViaStatusMapping promiseStatusMapping, List<ViaPromiseFloorInfoDto> promiseFloorList) {
        List<ViaPromiseFloorInfoDto> copyPromiseFloorList = new ArrayList<>();
        Iterator<ViaPromiseFloorInfoDto> iterator = promiseFloorList.iterator();
        while (iterator.hasNext()) {
            ViaPromiseFloorInfoDto viaPromiseFloorInfoDto = iterator.next();
            List<String> hiddenFloorCodeList = promiseStatusMapping.getHiddenFloorCode();
            if (CollectionUtils.isNotEmpty(hiddenFloorCodeList) && !hiddenFloorCodeList.contains(viaPromiseFloorInfoDto.getPromiseFloorCode())) {
                ViaPromiseFloorInfoDto copyViaPromiseFloorInfoDto = JSON.parseObject(JSON.toJSONString(viaPromiseFloorInfoDto),ViaPromiseFloorInfoDto.class);
                copyPromiseFloorList.add(copyViaPromiseFloorInfoDto);
            }
        }
        return copyPromiseFloorList;
    }

    /**
     * 处理订单状态
     *
     * @param viaFloorInfo
     * @param jdOrder
     */

    private void handelOrderStatus(ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder) {
        log.info("XfylVtpHomeTestOrderDetailHandler handelOrderStatus viaFloorInfo={},jdOrder={}", JSON.toJSONString(viaFloorInfo), JSON.toJSONString(jdOrder));
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();
        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();
            if (ViaOrderInfoFieldEnum.ORDER_STATUS_DESC.getField().equals(viaFloorConfig.getFieldKey())) {
                Map<String,Object> param = new HashMap<>();
                param.put("orderStatus",jdOrder.getOrderStatus());
                JSONArray jsonArray = JSON.parseArray(viaFloorConfig.getValue());
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject obj = jsonArray.getJSONObject(i);
                    if ((boolean) AviatorEvaluator.compile(obj.getString("statusExpression"), Boolean.TRUE).execute(param)){
                        viaFloorConfig.setFieldValue(obj.getString("orderStatusName"));
                        viaFloorConfig.setIcon(obj.getString("icon"));
                        viaFloorConfig.setValue("");
                        return;
                    }
                }
                continue;
            }
        }
    }


    /**
     * 处理订单信息
     *
     * @param viaFloorInfo
     * @param jdOrder
     */

    private void handelOrderInfo(ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder) {
        log.info("XfylVtpHomeTestOrderDetailHandler handelOrderInfo viaFloorInfo={},jdOrder={}", JSON.toJSONString(viaFloorInfo), JSON.toJSONString(jdOrder));
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();
        while (iterator.hasNext()) {
            ViaFloorConfig viaFloorConfig = iterator.next();
            if (ViaOrderInfoFieldEnum.ORDER_ID.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderId())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderId().toString());
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ORDER_AMOUNT.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getOrderAmount())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getOrderAmount().toPlainString());
                    List<JdOrderServiceFeeInfoDTO> jdOrderServiceFeeInfos = jdOrder.getJdOrderServiceFeeInfos();
                    viaFloorConfig.setValue(JSON.toJSONString(jdOrderServiceFeeInfos));
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ORDER_CREATE_TIME.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getCreateTime())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(TimeUtils.dateTimeToStr(jdOrder.getCreateTime(), TimeFormat.LONG_PATTERN_LINE));
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.PAY_TYPE_DESC.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getPayType())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(PayTypeEnum.getDescOfType(jdOrder.getPayType().toString()));
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ORDER_USER_PHONE.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getAddressInfo()) || StrUtil.isBlank(jdOrder.getAddressInfo().getMobile())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(new PhoneNumber(jdOrder.getAddressInfo().getMobile()).mask());
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ORDER_USER_NAME.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getAddressInfo()) || StrUtil.isBlank(jdOrder.getAddressInfo().getName())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(new UserName(jdOrder.getAddressInfo().getName()).mask());
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ADDRESS_DETAIL.getField().equals(viaFloorConfig.getFieldKey())) {
                if (Objects.isNull(jdOrder.getAddressInfo()) || StrUtil.isBlank(jdOrder.getAddressInfo().getFullAddress())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getAddressInfo().getFullAddress());
                }
                continue;
            }
            if (ViaOrderInfoFieldEnum.ORDER_REMARK.getField().equals(viaFloorConfig.getFieldKey())) {
                if (StrUtil.isEmpty(jdOrder.getRemark())) {
                    iterator.remove();
                } else {
                    viaFloorConfig.setFieldValue(jdOrder.getRemark());
                }
            }
            if(ViaOrderInfoFieldEnum.ORDER_DISCOUNT.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getOrderDiscount()) || jdOrder.getOrderDiscount().compareTo(new BigDecimal("0")) <= 0){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(jdOrder.getOrderDiscount().toString());
                }
                continue;
            }
            if(ViaOrderInfoFieldEnum.ORDER_COUPON.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getOrderCoupon()) || jdOrder.getOrderCoupon().compareTo(new BigDecimal("0")) <= 0){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(jdOrder.getOrderCoupon().toString());
                }
                continue;
            }

        }
    }

    /**
     * 订单SKU信息
     * {
     * "floorCode": "orderSkuInfo",
     * "floorName": "订单商品信息",
     * "floorConfigList": [
     * {
     * "value":{
     * "skuAmount": 6490,
     * "skuExpireDate": "2025-04-15",
     * "skuImage": "jfs/t1/235524/3/11500/108038/65a13846F3d18a04d/d079e0fd4563a6cf.jpg",
     * "skuName": "京东到家快检 呼吸道病毒细菌上门检测,常见呼吸道感染、甲流、乙流、支原体、多重病毒细菌一次性检测，附赠采样试剂盒",
     * "skuNo": "100077601060",
     * "skuNum": 2
     * },
     * "action": {
     * "url": "https://laputa.jd.com/jdh-healthcare-pass/detection/detectionProductDetail?sku=100102462832",
     * "type": "jump"
     * }
     * },
     * {
     * "value":{
     * "skuAmount": 6490,
     * "skuExpireDate": "2025-04-15",
     * "skuImage": "jfs/t1/235524/3/11500/108038/65a13846F3d18a04d/d079e0fd4563a6cf.jpg",
     * "skuName": "京东到家快检 呼吸道病毒细菌上门检测,常见呼吸道感染、甲流、乙流、支原体、多重病毒细菌一次性检测，附赠采样试剂盒",
     * "skuNo": "100077601060",
     * "skuNum": 2
     * },
     * "action": {
     * "url": "https://laputa.jd.com/jdh-healthcare-pass/detection/detectionProductDetail?sku=100102462832",
     * "type": "jump"
     * }
     * }
     * ]
     * }
     *
     * @param viaConfig    VIA配置
     * @param viaFloorInfo 通用楼层信息
     * @param jdOrder      JD订单
     */
    private void handelOrderSkuInfo(ViaConfig viaConfig, ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder) {
        log.info("XfylVtpHomeTestOrderDetailHandler handelOrderSkuInfo viaConfig={},viaFloorInfo={},jdOrder={}", JSON.toJSONString(viaConfig), JSON.toJSONString(viaFloorInfo), JSON.toJSONString(jdOrder));
        List<JdOrderItemDTO> jdOrderItemList = jdOrder.getJdOrderItemList();
        List<ViaFloorConfig> floorConfigList = new ArrayList<>();

        for (JdOrderItemDTO jdOrderItemDTO : jdOrderItemList) {
            ViaActionInfo action = new ViaActionInfo();
            action.setUrl(MessageFormat.format(viaConfig.getSkuDetailUrl(), jdOrderItemDTO.getSkuId().toString()));
            action.setType(ActionType.JUMP.getCode());
            //如果是加项商品，跳转url为空，即在订详无法跳转
            if (IsAddedEnum.IS_ADDED.getValue().equals(jdOrderItemDTO.getIsAdded())) {
                action.setUrl(null);
            }
            floorConfigList.add(ViaFloorConfig.builder().value(JSON.toJSONString(jdOrderItemDTO)).action(action).build());
        }

        viaFloorInfo.setFloorConfigList(floorConfigList);
    }

    /**
     * 处理样本信息楼层
     * {
     * "floorCode": "materialInfo",
     * "floorName": "样本信息",
     * "floorConfigList": [
     * {
     * "title": "*杰伦 | JD24281231298",
     * "viaStatus":1,
     * "statusDesc":"送检中",
     * "targetUrl":""
     * }
     * ]
     * }
     *
     * @param jdhPromise jdhPromise
     */
    private void handelMaterialInfo(ViaConfig viaConfig, ViaPromiseFloorInfoDto viaPromiseFloorInfoDto, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList) {
        if (CollUtil.isNotEmpty(medicalPromiseList)) {
            Map<Long, JdhPromisePatient> promisePatientMap = jdhPromise.getPatients().stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, Function.identity()));
            List<ViaStatusMapping> medicalStatusMapping = viaConfig.getMedicalStatusMapping();
            List<ViaPromiseFloorConfig> viaPromiseFloorConfigs = new ArrayList<>();
            //按人归堆，如果一个人下的某一条 已出报告 只展示人名 和查看按钮
            //展示效果：
            //张三            已出报告
            //李四 | JD1111    检测中
            //李四 | JD1111    检测中
            Map<Long, List<MedicalPromiseDTO>> patientMedPromiseList = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromisePatientId));
            for (Map.Entry<Long, List<MedicalPromiseDTO>> entry : patientMedPromiseList.entrySet()) {
                List<MedicalPromiseDTO> medPromiseList = entry.getValue();
                //是否有已出报告,且没有退款
                List<MedicalPromiseDTO> reportedList = medPromiseList.stream().filter(ele -> (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(ele.getStatus())) && !(JdhFreezeEnum.FREEZE.getStatus().equals(ele.getFreeze()) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(ele.getStatus()))).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(reportedList)) {
                    ViaPromiseFloorConfig viaPromiseFloorConfig = new ViaPromiseFloorConfig();
                    MedicalPromiseDTO reportedMedPromise = reportedList.get(0);
                    JdhPromisePatient jdhPromisePatient = promisePatientMap.get(reportedMedPromise.getPromisePatientId());
                    viaPromiseFloorConfig.setTitle(Objects.isNull(jdhPromisePatient.getUserName()) ? "" : jdhPromisePatient.getUserName().mask() + "的检测报告");
                    fillMedPromiseFloorConfig(viaPromiseFloorConfig, reportedMedPromise, medicalStatusMapping, jdhPromisePatient, jdhPromise);
                    viaPromiseFloorConfigs.add(viaPromiseFloorConfig);
                } else {
                    for (MedicalPromiseDTO medicalPromiseDTO : medPromiseList) {
                        // 冻结或者作废不展示
                        if (JdhFreezeEnum.FREEZE.getStatus().equals(medicalPromiseDTO.getFreeze()) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseDTO.getStatus())) {
                            continue;
                        }
                        ViaPromiseFloorConfig viaPromiseFloorConfig = new ViaPromiseFloorConfig();
                        JdhPromisePatient jdhPromisePatient = promisePatientMap.get(medicalPromiseDTO.getPromisePatientId());
                        viaPromiseFloorConfig.setTitle(jdhPromisePatient.getUserName().mask() + " | " + medicalPromiseDTO.getSpecimenCode());
                        fillMedPromiseFloorConfig(viaPromiseFloorConfig, medicalPromiseDTO, medicalStatusMapping, jdhPromisePatient, jdhPromise);
                        viaPromiseFloorConfigs.add(viaPromiseFloorConfig);
                    }
                }
            }
            viaPromiseFloorInfoDto.setViaPromiseFloorConfigs(viaPromiseFloorConfigs);
        }
        log.info("XfylHomeTestOrderDetailHandler handelMaterialInfo viaFloorInfo:{}", JSON.toJSONString(viaPromiseFloorInfoDto));
    }

    /**
     * fillMedPromiseFloorConfig
     *
     * @param viaPromiseFloorConfig
     * @param medPromise            medPromise
     * @param medicalStatusMapping  medicalStatusMapping
     * @param jdhPromisePatient     jdhPromisePatient
     * @param jdhPromise            jdhPromise
     */
    private void fillMedPromiseFloorConfig(ViaPromiseFloorConfig viaPromiseFloorConfig, MedicalPromiseDTO medPromise, List<ViaStatusMapping> medicalStatusMapping, JdhPromisePatient jdhPromisePatient, JdhPromise jdhPromise) {
        for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
            if (viaStatusMapping.getStatusList().contains(medPromise.getStatus())) {
                //TODO 核实
                viaPromiseFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                viaPromiseFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                List<ViaBtnInfo> btnList = viaStatusMapping.getBtnList();
                if (CollUtil.isNotEmpty(btnList)) {
                    List<ViaBtnInfo> newBtnList = new ArrayList<>();
                    for (ViaBtnInfo viaBtnInfo : btnList) {
                        if (ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode().equals(viaBtnInfo.getCode())) {
                            if (Objects.nonNull(jdhPromisePatient) && Objects.nonNull(jdhPromisePatient.getPatientId())) {
                                ViaBtnInfo newBtnInfo = JSON.parseObject(JSON.toJSONString(viaBtnInfo), ViaBtnInfo.class);
                                newBtnInfo.getAction().setUrl(MessageFormat.format(newBtnInfo.getAction().getUrl(), jdhPromise.getSourceVoucherId(), jdhPromisePatient.getPatientId().toString()));
                                newBtnList.add(newBtnInfo);
                            }
                        }
                    }
                    viaPromiseFloorConfig.setBtnList(newBtnList);
                }
                break;
            }
        }
    }

    /**
     * 处理底部按钮
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdOrder       JD订单
     */
    private void handleFooterButtons(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping, JdOrderDTO jdOrder, List<JdhVoucher> jdhVouchers) {
        //申请退款 refundBtn
        //再次购买 rePurchaseBtn
        //联系客服 contactCustomerBtn
        //取消订单 cancelOrderBtn
        //立即支付 payNowBtn
        ViaConfig viaConfig = ctx.getViaConfig();
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        log.info("XfylVtpHomeTestOrderDetailHandler handleFooterButtons btnList={}, statusMapping={}, jdOrder={}, jdhVouchers={}", JSON.toJSONString(btnList), JSON.toJSONString(statusMapping), JSON.toJSONString(jdOrder), JSON.toJSONString(jdhVouchers));

        Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
        while (btnInfoIterator.hasNext()) {
            ViaBtnInfo btnInfo = btnInfoIterator.next();
            log.info("XfylVtpHomeTestOrderDetailHandler handleFooterButtons btnInfo={}", JSON.toJSONString(btnInfo));
            Map<String, Object> actionCommonParams = new HashMap<>();
            actionCommonParams.put("verticalCode", viaConfig.getVerticalCode());
            actionCommonParams.put("serviceType", viaConfig.getServiceType());
            actionCommonParams.put("envType", ctx.getEnvType());

            List<String> footerButtonCodeList = statusMapping.getFooterButtonCodeList();
            if (CollectionUtils.isNotEmpty(footerButtonCodeList) && !footerButtonCodeList.contains(btnInfo.getCode())) {
                btnInfoIterator.remove();
                continue;
            }
            ViaActionInfo action = btnInfo.getAction();

            //申请退款
            if (ViaBtnCodeEnum.REFUND_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    if (CollectionUtils.isNotEmpty(jdhVouchers)){
                        List<Long> voucherIds = jdhVouchers.stream().map(JdhVoucher::getVoucherId).collect(Collectors.toList());
                        // 查询履约单
                        List<JdhPromise> jdhPromises = promiseRepository.findList(PromiseRepQuery.builder().voucherIds(voucherIds).build());
                        log.info("XfylVtpHomeTestOrderDetailHandler handleFooterButtons jdhPromises={}", JSON.toJSONString(jdhPromises));
                        Boolean angelOutFlag = checkAngelOutFlag(jdhPromises);
                        log.info("XfylVtpHomeTestOrderDetailHandler handleFooterButtons angelOutFlag={}", angelOutFlag);

                        // 护士已出门，隐藏申请退款按钮
                        if (angelOutFlag){
                            btnInfoIterator.remove();
                            continue;
                        }

                        action.setParams(new HashMap<>(actionCommonParams));
                        ViaActionInfo nextAction = action.getNextAction();
                        actionCommonParams.put("orderId", jdOrder.getOrderId());
                        actionCommonParams.put("refundType", 1);
                        actionCommonParams.put("refundSource", "1");
                        actionCommonParams.put("voucherId", jdhPromises.get(0).getVoucherId());
                        actionCommonParams.put("promiseId", jdhPromises.get(0).getPromiseId());
                        nextAction.setParams(actionCommonParams);

                        // voucher还生成时，可以展示退款按钮
                    }else{
                        action.setParams(new HashMap<>(actionCommonParams));
                        ViaActionInfo nextAction = action.getNextAction();
                        actionCommonParams.put("orderId", jdOrder.getOrderId());
                        actionCommonParams.put("refundType", 1);
                        actionCommonParams.put("refundSource", "1");
                        nextAction.setParams(actionCommonParams);
                    }
                } catch (Exception e) {
                    log.error("XfylVtpHomeTestOrderDetailHandler handleFooterButtons refundBtn fail", e);
                    btnInfoIterator.remove();
                    continue;
                }
            }

            //再次购买
            if (ViaBtnCodeEnum.RE_PURCHASE_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    String jumpUrl = MessageFormat.format(btnInfo.getJumpUrlRule(), jdOrder.getJdOrderItemList().get(0).getSkuId().toString());
                    action.setUrl(jumpUrl);
                } catch (Exception e) {
                    log.error("XfylHomeTestOrderDetailHandler handleFooterButtons rePurchaseBtn fail", e);
                    btnInfoIterator.remove();
                    continue;
                }
            }

            //联系客服
            if(ViaBtnCodeEnum.CONTACT_CUSTOMER_BTN.getCode().equals(btnInfo.getCode())){
                try {
                    String jumpUrl = JSON.parseObject(btnInfo.getJumpUrlRule(), new TypeReference<Map<String, String>>() {
                    }).get(EnvTypeEnum.get(ctx.getEnvType()).getCode());
                    log.info("XfylHomeTestOrderDetailHandler handleFooterButtons jumpUrl={}, venderId={}, envType={}, orderId={}"
                            , jumpUrl, jdOrder.getVenderId(), ctx.getEnvType(), jdOrder.getOrderId());
                    if (EnvTypeEnum.JD_APP.getCode().equals(ctx.getEnvType())) {
                        //openapp.jdmobile://virtual?params={"category":"jump","des":"jd_dongdong_chat","entry":"jd_sdk_kjzydxy","orderId":"{0}"}
                        action.setUrl(jumpUrl.replace("{0}", jdOrder.getVenderId()).replace("{1}", jdOrder.getOrderId().toString()));
                    } else {
                        //https://jdcs.m.jd.com/chat/index.action?entry=jd_sdk_kjzydxy&orderId={0}
                        action.setUrl(MessageFormat.format(jumpUrl, jdOrder.getVenderId(), jdOrder.getOrderId().toString()));
                    }
                }catch (Exception e){
                    log.error("XfylHomeTestOrderDetailHandler handleFooterButtons contactCustomerBtn fail", e);
                    btnInfoIterator.remove();
                    continue;
                }
            }

            //取消订单
            if (ViaBtnCodeEnum.CANCEL_ORDER_BTN.getCode().equals(btnInfo.getCode())) {
                actionCommonParams.put("orderId", jdOrder.getOrderId().toString());
                action.setParams(actionCommonParams);
            }

            //立即支付
            if (ViaBtnCodeEnum.PAY_NOW_BTN.getCode().equals(btnInfo.getCode())) {
                actionCommonParams.put("orderId", jdOrder.getOrderId().toString());
                if (StrUtil.isNotBlank(ctx.getOpenId())) {
                    actionCommonParams.put("openId", ctx.getOpenId());
                }
                if (StrUtil.isNotBlank(ctx.getCallWxType())) {
                    actionCommonParams.put("callWxType", ctx.getCallWxType());
                }
                action.setParams(actionCommonParams);
            }
        }

        log.info("XfylHomeTestOrderDetailHandler handleFooterButtons viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 处理底部按钮
     *
     * @param viaConfig               通用楼层配置
     * @param viaPromiseFloorInfoDto  promise楼层信息
     * @param statusMapping           状态映射
     * @param jdhPromise              JD履约单
     */
    private void handlePromiseFooterButtons(FillViaConfigDataContext ctx, ViaConfig viaConfig, ViaPromiseFloorInfoDto viaPromiseFloorInfoDto, ViaStatusMapping statusMapping, JdhPromise jdhPromise) {
        //护士评价按钮 evaluateAngelBtn
        ViaPromiseFloorConfig viaFloorConfig = viaPromiseFloorInfoDto.getViaPromiseFloorConfigs().get(0);
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        log.info("XfylVtpHomeTestOrderDetailHandler handlePromiseFooterButtons btnList={}, statusMapping={}, jdhPromise={}", JSON.toJSONString(btnList), JSON.toJSONString(statusMapping), JSON.toJSONString(jdhPromise));

        Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
        while (btnInfoIterator.hasNext()) {
            ViaBtnInfo btnInfo = btnInfoIterator.next();
            log.info("XfylVtpHomeTestOrderDetailHandler handlePromiseFooterButtons btnInfo={}", JSON.toJSONString(btnInfo));
            Map<String, Object> actionCommonParams = new HashMap<>();
            actionCommonParams.put("verticalCode", viaConfig.getVerticalCode());
            actionCommonParams.put("serviceType", viaConfig.getServiceType());
            actionCommonParams.put("envType", ctx.getEnvType());

            List<String> footerButtonCodeList = statusMapping.getFooterButtonCodeList();
            if (CollectionUtils.isEmpty(footerButtonCodeList) || !footerButtonCodeList.contains(btnInfo.getCode())) {
                btnInfoIterator.remove();
                continue;
            }
            ViaActionInfo action = btnInfo.getAction();

            //护士评价
            if (ViaBtnCodeEnum.EVALUATE_ANGEL_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    Map<Long, String> urlMap = angelEcologyApplication.queryAngelEcologyPageUrl(
                            JdhAngelEcologyQueryRequest.builder().envType(ctx.getEnvType()).orderId(Long.valueOf(jdhPromise.getSourceVoucherId())).build());
                    String jumpUrl = urlMap.get(jdhPromise.getPromiseId());
                    //有链接设置url
                    if (StringUtils.isNotBlank(jumpUrl)) {
                        action.setUrl(jumpUrl);
                    } else {//无连接不返回按钮
                        btnInfoIterator.remove();
                    }
                } catch (Exception e) {
                    log.error("XfylHomeTestOrderDetailHandler handlePromiseFooterButtons EVALUATE_ANGEL_BTN fail", e);
                    btnInfoIterator.remove();
                    continue;
                }
            }
        }

        log.info("XfylHomeTestOrderDetailHandler handlePromiseFooterButtons viaFloorInfo:{}", JSON.toJSONString(viaPromiseFloorInfoDto));
    }

    private Boolean checkAngelOutFlag(List<JdhPromise> jdhPromises) {
        List<Long> promisesIds = jdhPromises.stream().map(JdhPromise::getPromiseId).collect(Collectors.toList());
        AngelWorkDBQuery req = new AngelWorkDBQuery();
        req.setPromiseIds(promisesIds);
        List<AngelWork> angelWorkList = angelWorkRepository.findList(req);
        log.info("XfylVtpHomeTestOrderDetailHandler checkAngelOutFlag angelWorkList={}", JSON.toJSONString(angelWorkList));

        if (CollectionUtils.isNotEmpty(angelWorkList)){
            List<Integer> angelWorkStatusList = Arrays.asList(AngelWorkStatusEnum.WAIT_SERVICE.getType(), AngelWorkStatusEnum.SERVICING.getType(), AngelWorkStatusEnum.SERVICED.getType()
                    , AngelWorkStatusEnum.DELIVERING.getType(),AngelWorkStatusEnum.COMPLETED.getType(),AngelWorkStatusEnum.REFUNDED.getType(),AngelWorkStatusEnum.EXPIRED.getType());
            // 护士已出门
            List<AngelWork> angelOutList = angelWorkList.stream().filter(a -> angelWorkStatusList.contains(a.getWorkStatus())).collect(Collectors.toList());
            log.info("XfylVtpHomeTestOrderDetailHandler checkAngelOutFlag angelOutList={}", JSON.toJSONString(angelOutList));
            if (CollectionUtils.isNotEmpty(angelOutList)){
                return true;
            }
        }
        return false;
    }


    /**
     * 履约信息 - 被服务者
     * {
     * "floorCode": "promisePatientInfo",
     * "floorName": "履约被服务者信息",
     * "floorConfigList": [
     * {
     * "fieldKey":"appointmentTime",
     * "fieldValue": "2024-01-01 00:00 - 01:00"
     * },
     * {
     * "fieldKey":"patientList",
     * "fieldValue":[
     * {
     * "userName":"*先生"
     * },
     * {
     * "userName":"*女士"
     * }
     * ]
     * },
     * {
     * "fieldKey":"patientAddress",
     * "fieldValue": "北京大兴区旧宫地区旧宫新苑南区14号楼二单元302"
     * },
     * {
     * "fieldKey":"remark",
     * "fieldValue": "上门前请提前联系"
     * }
     * ]
     * }
     *
     * @param viaPromiseFloorInfoDto 通用楼层信息
     * @param statusMapping          状态映射
     * @param jdhPromise             jdhPromise
     */
    private void handelPromisePatientInfo(ViaPromiseFloorInfoDto viaPromiseFloorInfoDto, ViaStatusMapping statusMapping, JdhPromise jdhPromise) {
        log.info("XfylVtpHomeTestOrderDetailHandler#handelPromisePatientInfo.viaPromiseFloorInfoDto={},statusMapping={},jdhPromise={}", JSON.toJSONString(viaPromiseFloorInfoDto), JSON.toJSONString(statusMapping), JSON.toJSONString(jdhPromise));
        List<ViaPromiseFloorConfig> promiseFloorConfigs = viaPromiseFloorInfoDto.getViaPromiseFloorConfigs();
        Iterator<ViaPromiseFloorConfig> iterator = promiseFloorConfigs.iterator();
        while (iterator.hasNext()) {
            ViaPromiseFloorConfig viaPromiseFloorConfig = iterator.next();
            List<String> hiddenPatientFieldList = statusMapping.getHiddenPatientFieldList();
            if (CollectionUtils.isNotEmpty(hiddenPatientFieldList) && hiddenPatientFieldList.contains(viaPromiseFloorConfig.getFieldKey())) {
                iterator.remove();
                continue;
            }

            if (ViaPatientInfoFieldEnum.APPOINTMENT_TIME.getField().equals(viaPromiseFloorConfig.getFieldKey())) {
                viaPromiseFloorConfig.setFieldValue(getFullAppointmentDateDesc(jdhPromise.getAppointmentTime()));
            }
            if (ViaPatientInfoFieldEnum.PATIENT_ADDRESS.getField().equals(viaPromiseFloorConfig.getFieldKey())) {
                viaPromiseFloorConfig.setFieldValue(jdhPromise.getStore().getStoreAddr());
            }
            if (ViaPatientInfoFieldEnum.PATIENT_LIST.getField().equals(viaPromiseFloorConfig.getFieldKey())) {
                List<JdhPromisePatient> patients = jdhPromise.getPatients();
                List<Map<String, String>> value = new ArrayList<>();
                for (JdhPromisePatient patient : patients) {
                    value.add(MapUtil.builder("userName", patient.getUserName().mask()).build());
                }
                viaPromiseFloorConfig.setFieldValue(JSON.toJSONString(value));
            }
            if (ViaPatientInfoFieldEnum.REMARK.getField().equals(viaPromiseFloorConfig.getFieldKey())) {
                List<JdhPromiseExtend> promiseExtends = jdhPromise.getPromiseExtends();
                for (JdhPromiseExtend promiseExtend : promiseExtends) {
                    if (promiseExtend.getAttribute().equals(PromiseExtendKeyEnum.ORDER_REMARK.getFiledKey())) {
                        viaPromiseFloorConfig.setFieldValue(promiseExtend.getOrderRemark());
                    }
                }
            }
        }
        log.info("XfylHomeTestOrderDetailHandler handelPromisePatientInfo viaPromiseFloorInfoDto:{}", JSON.toJSONString(viaPromiseFloorInfoDto));
    }

    /**
     * 获取完整预约日期描述
     *
     * @param appointmentTime 预约时间
     * @return {@link String}
     */
    private String getFullAppointmentDateDesc(DomainAppointmentTime appointmentTime) {
        Date tomorrow = DateUtil.tomorrow().toJdkDate();
        String dateDesc = " ";
        if (DateUtil.isSameDay(new Date(), TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()))) {
            dateDesc = "[今天]";
        }
        if (DateUtil.isSameDay(tomorrow, TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()))) {
            dateDesc = "[明天]";
        }
        return appointmentTime.formatAppointDate() + dateDesc + appointmentTime.formatAppointTimeDesc();
    }

    /**
     * handlePromiseCodeInfo
     * {
     * "floorCode": "promiseCodeInfo",
     * "floorName": "履约消费码信息",
     * "floorConfigList": [
     * {
     * "promiseCode":"1231",
     * "noticeTip":"请在护士上门后出示"
     * }
     * ]
     * }
     *
     * @param viaPromiseFloorInfoDto viaFloorInfo
     * @param jdhPromise             jdhPromise
     */
    private void handlePromiseCodeInfo(ViaPromiseFloorInfoDto viaPromiseFloorInfoDto, JdhPromise jdhPromise) {
        ViaPromiseFloorConfig viaPromiseFloorConfig = viaPromiseFloorInfoDto.getViaPromiseFloorConfigs().get(0);
        log.info("XfylVtpHomeTestOrderDetailHandler#handlePromiseCodeInfo.viaPromiseFloorInfoDto={},jdhPromise={}", JSON.toJSONString(viaPromiseFloorInfoDto), JSON.toJSONString(jdhPromise));
        viaPromiseFloorConfig.setPromiseCode(jdhPromise.getCode());
        log.info("XfylVtpHomeTestOrderDetailHandler handlePromiseCodeInfo viaPromiseFloorConfig:{}", JSON.toJSONString(viaPromiseFloorConfig));
    }

    private void handlePromiseUnderInspectionInfo(ViaPromiseFloorInfoDto viaPromiseFloorInfoDto, JdhPromise jdhPromise) {
        log.info("XfylVtpHomeTestOrderDetailHandler#handlePromiseUnderInspectionInfo.viaPromiseFloorInfoDto={},jdhPromise={}", JSON.toJSONString(viaPromiseFloorInfoDto), JSON.toJSONString(jdhPromise));
        List<ViaPromiseFloorConfig> viaPromiseFloorConfigs = viaPromiseFloorInfoDto.getViaPromiseFloorConfigs();
        ViaPromiseFloorConfig viaPromiseFloorConfig = viaPromiseFloorConfigs.get(0);
        JSONObject obj = JSON.parseObject(viaPromiseFloorConfig.getFieldValue());
        List<Map<String,Object>> inspectionReportList = new ArrayList<>();
        List<JdhPromisePatient> patients = jdhPromise.getPatients();
        if (CollectionUtils.isNotEmpty(patients)){
            for (JdhPromisePatient patient : patients) {
                Map<String,Object> inspectionReportMap = new HashMap<>();
                inspectionReportMap.put("viaStatus",obj.getInteger("viaStatus"));
                inspectionReportMap.put("name",obj.getString("name"));
                String maskPatientName = "*" + patient.getUserName().getName().substring(1);
                inspectionReportMap.put("patientExpand",MessageFormat.format(obj.getString("patientExpand"), maskPatientName));
                inspectionReportList.add(inspectionReportMap);
            }
            viaPromiseFloorConfig.setFieldValue(JSON.toJSONString(inspectionReportList));
        }
        log.info("XfylVtpHomeTestOrderDetailHandler handlePromiseUnderInspectionInfo viaPromiseFloorConfig:{}", JSON.toJSONString(viaPromiseFloorConfig));
    }

    private void handlePromiseLaboratoryTestingInfo(ViaPromiseFloorInfoDto viaPromiseFloorInfoDto, JdhPromise jdhPromise) {
        log.info("XfylVtpHomeTestOrderDetailHandler#handlePromiseLaboratoryTestingInfo.viaPromiseFloorInfoDto={},jdhPromise={}", JSON.toJSONString(viaPromiseFloorInfoDto), JSON.toJSONString(jdhPromise));
        List<ViaPromiseFloorConfig> viaPromiseFloorConfigs = viaPromiseFloorInfoDto.getViaPromiseFloorConfigs();
        ViaPromiseFloorConfig viaPromiseFloorConfig = viaPromiseFloorConfigs.get(0);
        JSONObject obj = JSON.parseObject(viaPromiseFloorConfig.getFieldValue());
        List<Map<String,Object>> inspectionReportList = new ArrayList<>();
        List<JdhPromisePatient> patients = jdhPromise.getPatients();
        if (CollectionUtils.isNotEmpty(patients)){
            for (JdhPromisePatient patient : patients) {
                Map<String,Object> inspectionReportMap = new HashMap<>();
                inspectionReportMap.put("viaStatus",obj.getInteger("viaStatus"));
                inspectionReportMap.put("name",obj.getString("name"));
                String maskPatientName = "*" + patient.getUserName().getName().substring(1);
                inspectionReportMap.put("patientExpand",MessageFormat.format(obj.getString("patientExpand"), maskPatientName));
                inspectionReportList.add(inspectionReportMap);
            }
            viaPromiseFloorConfig.setFieldValue(JSON.toJSONString(inspectionReportList));
        }
        log.info("XfylVtpHomeTestOrderDetailHandler handlePromiseLaboratoryTestingInfo viaPromiseFloorConfig:{}", JSON.toJSONString(viaPromiseFloorConfig));
    }

    private void handlePromiseViewReportInfo(ViaPromiseFloorInfoDto viaPromiseFloorInfoDto, JdhPromise jdhPromise, FillViaConfigDataContext ctx) {
        log.info("XfylVtpHomeTestOrderDetailHandler#handlePromiseViewReportInfo.viaPromiseFloorInfoDto={},jdhPromise={}", JSON.toJSONString(viaPromiseFloorInfoDto), JSON.toJSONString(jdhPromise));
        List<ViaPromiseFloorConfig> viaPromiseFloorConfigs = viaPromiseFloorInfoDto.getViaPromiseFloorConfigs();
        ViaPromiseFloorConfig viaPromiseFloorConfig = viaPromiseFloorConfigs.get(0);
        JSONObject obj = JSON.parseObject(viaPromiseFloorConfig.getFieldValue());
        List<Map<String,Object>> inspectionReportList = new ArrayList<>();
        List<JdhPromisePatient> patients = jdhPromise.getPatients();
        if (CollectionUtils.isNotEmpty(patients)){
            for (JdhPromisePatient patient : patients) {
                Map<String,Object> inspectionReportMap = new HashMap<>();
                inspectionReportMap.put("viaStatus",obj.getInteger("viaStatus"));
                inspectionReportMap.put("name",obj.getString("name"));
                String maskPatientName = "*" + patient.getUserName().getName().substring(1);
                inspectionReportMap.put("patientExpand",MessageFormat.format(obj.getString("patientExpand"), maskPatientName));

                JSONObject viaBtnInfoObj = JSON.parseObject(obj.getString("viaBtnInfo"));
                Map<String,Object> actionMap = new HashMap<>();
                actionMap.put("type",viaBtnInfoObj.getString("type"));
                String url = MessageFormat.format(viaBtnInfoObj.getString("url"), String.valueOf(jdhPromise.getPromiseId()), String.valueOf(patient.getPatientId()));
                url = getUrlByEnvType(ctx.getEnvType(), url);
                actionMap.put("url",url);

                inspectionReportMap.put("viaBtnInfo",actionMap);

                inspectionReportList.add(inspectionReportMap);
            }
            viaPromiseFloorConfig.setFieldValue(JSON.toJSONString(inspectionReportList));
        }
        log.info("XfylVtpHomeTestOrderDetailHandler handlePromiseViewReportInfo viaPromiseFloorConfig:{}", JSON.toJSONString(viaPromiseFloorConfig));
    }

    private void handlePromiseViewReportInfo2(ViaConfig viaConfig,ViaPromiseFloorInfoDto viaPromiseFloorInfoDto, Map<String, Object> sourceData
            , List<MedicalPromiseDTO> medicalPromiseList, JdhPromise jdhPromise) {
        log.info("XfylVtpHomeTestOrderDetailHandler#handlePromiseViewReportInfo2.viaPromiseFloorInfoDto={},jdhPromise={}", JSON.toJSONString(viaPromiseFloorInfoDto), JSON.toJSONString(jdhPromise));
        if(CollUtil.isNotEmpty(medicalPromiseList)){
            Map<Long, JdhPromisePatient> promisePatientMap = jdhPromise.getPatients().stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, Function.identity()));
            List<ViaStatusMapping> medicalStatusMapping = viaConfig.getMedicalStatusMapping();
            List<ViaPromiseFloorConfig> viaFloorConfigList = new ArrayList<>();
            //按人归堆，如果一个人下的某一条 已出报告 只展示人名 和查看按钮
            //展示效果：
            //张三            已出报告
            //李四 | JD1111    检测中
            //李四 | JD1111    检测中
            MedicalPromiseDTO completeMed = medicalPromiseList.stream().filter(p -> Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(), p.getStatus())).findFirst().orElse(null);
            Boolean completeExist = Objects.nonNull(completeMed) ? Boolean.TRUE : Boolean.FALSE;
            Map<Long, List<MedicalPromiseDTO>> patientMedPromiseList = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromisePatientId));
            for (Map.Entry<Long, List<MedicalPromiseDTO>> entry : patientMedPromiseList.entrySet()) {
                List<MedicalPromiseDTO> medPromiseList = entry.getValue();

                Map<Integer, List<MedicalPromiseDTO>> showTypeToList = medPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getReportShowType));
                ViaPromiseFloorConfig viaFloorConfig = new ViaPromiseFloorConfig();
                List<ViaBtnInfo> newBtnList = new ArrayList<>();
                viaFloorConfig.setBtnList(newBtnList);
                AtomicReference<Boolean> report = new AtomicReference<>(Boolean.FALSE);
                viaFloorConfigList.add(viaFloorConfig);
                MedicalPromiseDTO first = medPromiseList.get(0);
                JdhPromisePatient patient = promisePatientMap.get(first.getPromisePatientId());
                //如果已出报告，则有title
                if (completeExist){
                    viaFloorConfig.setTitle(Objects.isNull(patient.getUserName()) ? "" : patient.getUserName().mask() + "的检测报告");
                }
                showTypeToList.forEach((showType,list)->{

                    //如果是结构化页面
                    if (Objects.equals(ReportShowTypeEnum.STRUCT.getType(),showType)){
                        //判断是否有出报告的检测单
                        //是否有已出报告,且没有退款
                        List<MedicalPromiseDTO> reportedList = list.stream().filter(ele ->
                                (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(ele.getStatus())) &&
                                        !(JdhFreezeEnum.FREEZE.getStatus().equals(ele.getFreeze())
                                                || MedicalPromiseStatusEnum.INVALID.getStatus().equals(ele.getStatus()))
                        ).collect(Collectors.toList());
                        String tip = list.stream().map(MedicalPromiseDTO::getServiceItemName).collect(Collectors.joining(","));
                        log.info("handelMaterialInfo->name={},reportedList={},tip={}",patient.getUserName(),reportedList,tip);
                        //如果出报告
                        if(CollUtil.isNotEmpty(reportedList)){
                            report.set(Boolean.TRUE);

                            MedicalPromiseDTO reportedMedPromise = reportedList.get(0);
                            JdhPromisePatient jdhPromisePatient = promisePatientMap.get(reportedMedPromise.getPromisePatientId());

                            for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
                                log.info("handelMaterialInfo->,statusList={},stats={}",JSON.toJSONString(viaStatusMapping.getStatusList()),reportedMedPromise.getStatus());
                                if (viaStatusMapping.getStatusList().contains(reportedMedPromise.getStatus())) {
                                    viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                                    viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                                    List<ViaBtnInfo> btnList = viaStatusMapping.getBtnList();
                                    if(CollUtil.isNotEmpty(btnList)){
                                        for (ViaBtnInfo viaBtnInfo : btnList) {
                                            if(ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode().equals(viaBtnInfo.getCode())){
                                                if(Objects.nonNull(jdhPromisePatient) && Objects.nonNull(jdhPromisePatient.getPatientId())){
                                                    ViaBtnInfo newBtnInfo = JSON.parseObject(JSON.toJSONString(viaBtnInfo),ViaBtnInfo.class);
                                                    newBtnInfo.getAction().setUrl(MessageFormat.format(newBtnInfo.getAction().getUrl(),jdhPromise.getSourceVoucherId(),jdhPromisePatient.getPatientId().toString(),jdhPromise.getPromiseId().toString()));
                                                    newBtnInfo.setBtnTip(tip);
                                                    newBtnInfo.setLevel(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                                                    newBtnList.add(newBtnInfo);
                                                }
                                            }
                                        }
                                    }
                                    break;
                                }
                            }

                        }else {
                            //如果未出报告
                            for (MedicalPromiseDTO medicalPromiseDTO : list) {
                                ViaBtnInfo newBtnInfo = new ViaBtnInfo();
                                newBtnList.add(newBtnInfo);
                                newBtnInfo.setStyle("text");
                                String btnTip = completeExist ? medicalPromiseDTO.getServiceItemName() : patient.getUserName().mask() + " | " + medicalPromiseDTO.getSpecimenCode();
                                newBtnInfo.setBtnTip(btnTip);
                                for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
                                    log.info("handelMaterialInfo2->,statusList={},stats={}",JSON.toJSONString(viaStatusMapping.getStatusList()),medicalPromiseDTO.getStatus());
                                    if (viaStatusMapping.getStatusList().contains(medicalPromiseDTO.getStatus())) {
                                        newBtnInfo.setName(viaStatusMapping.getStatusDesc());
                                        newBtnInfo.setLevel(viaStatusMapping.getViaStatus());
                                        if (!Boolean.TRUE.equals(report.get())){
                                            viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                                            viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                                        }
                                        break;
                                    }
                                }
                            }

                        }

                    }else {
                        //如果是PDF页面
                        //遍历
                        //判断是否出报告
                        //如果出报告
                        //如果未出报告
                        for (MedicalPromiseDTO medPromise : list) {
                            ViaBtnInfo newBtnInfo = new ViaBtnInfo();
                            newBtnList.add(newBtnInfo);
                            String btnTip = completeExist ? medPromise.getServiceItemName() : patient.getUserName().mask() + " | " + medPromise.getSpecimenCode();
                            newBtnInfo.setBtnTip(btnTip);
                            //如果出报告了
                            if (Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(),medPromise.getStatus())){
                                report.set(Boolean.TRUE);
                                newBtnInfo.setName("去查看");
                                newBtnInfo.setStyle("primaryBordered");
                                ViaActionInfo ai = new ViaActionInfo();
                                newBtnInfo.setAction(ai);
                                Map<String,Object> params = new HashMap<>();
                                params.put("medicalPromiseId",medPromise.getMedicalPromiseId());
                                ai.setParams(params);
                                ai.setType("request");
                                ai.setFunctionId("jdh_o2oservice_queryUrlForCenter");
                                newBtnInfo.setLevel(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                                viaFloorConfig.setViaStatus(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                                viaFloorConfig.setStatusDesc("已出报告");
                            }else {
                                //如果未出报告
                                newBtnInfo.setStyle("text");
                                for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
                                    if (viaStatusMapping.getStatusList().contains(medPromise.getStatus())) {
                                        newBtnInfo.setName(viaStatusMapping.getStatusDesc());
                                        newBtnInfo.setLevel(viaStatusMapping.getViaStatus());
                                        if (!Boolean.TRUE.equals(report.get())){
                                            viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                                            viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                    }
                });

                if (CollectionUtils.isNotEmpty(newBtnList)){
                    newBtnList.sort(Comparator.comparing(ViaBtnInfo::getLevel).reversed());
                }
            }
            viaPromiseFloorInfoDto.setViaPromiseFloorConfigs(viaFloorConfigList);
        }
        log.info("XfylHomeTestOrderDetailHandler handelMaterialInfo viaFloorInfo:{}", JSON.toJSONString(viaPromiseFloorInfoDto));
    }


    /**
     * 隐藏楼层
     *
     * @param statusMapping 状态映射
     * @param floorList     楼层列表
     */
    private void clearHiddenFloor(ViaStatusMapping statusMapping, List<ViaFloorInfo> floorList) {
        Iterator<ViaFloorInfo> iterator = floorList.iterator();
        while (iterator.hasNext()) {
            ViaFloorInfo viaFloorInfo = iterator.next();
            List<String> hiddenFloorCodeList = statusMapping.getHiddenFloorCode();
            if (CollectionUtils.isNotEmpty(hiddenFloorCodeList) && hiddenFloorCodeList.contains(viaFloorInfo.getFloorCode())) {
                iterator.remove();
            }
        }
    }

    /**
     * 填充数据
     *
     * @param ctx ctx
     */
    @Override
    @SuppressWarnings("all")
    public void handle(FillViaConfigDataContext ctx) {
        log.info("XfylVtpHomeTestOrderDetailHandler handle ctx:{}", JSON.toJSONString(ctx));
        // ==>>>> 入参校验
        checkParam(ctx);
        ViaConfig viaConfig = ctx.getViaConfig();

        // ==>>>> 数据获取
        //查订单
        JdOrderDTO jdOrder = tradeApplication.getOrderDetail(OrderDetailParam.builder().orderId(ctx.getOrderId()).pin(ctx.getUserPin()).querySource("C").build());
        log.info("XfylVtpHomeTestOrderDetailHandler handle jdOrder:{}", JSON.toJSONString(jdOrder));
        if (Objects.isNull(jdOrder)) {
            throw new SystemException(SupportErrorCode.VIA_ORDER_INFO_NOT_EXIT);
        }
        //判断是否为拆单场景
        String sourceVoucherId = Objects.nonNull(jdOrder.getParentId()) && jdOrder.getParentId() > 0 ? jdOrder.getParentId().toString() : jdOrder.getOrderId().toString();
        //查服务凭证
        List<JdhVoucher> jdhVouchers = voucherRepository.listByQuery(VoucherRepQuery.builder().sourceVoucherId(sourceVoucherId).build());
        log.info("XfylVtpHomeTestOrderDetailHandler.jdhVouchers={}", JSON.toJSONString(jdhVouchers));
        try {
            // ==>>>> 先看订单状态，对大楼层进行处理
            ViaStatusMapping statusMapping = hitOrderStatusMapping(jdOrder, viaConfig.getStatusMapping());
            log.info("XfylVtpHomeTestOrderDetailHandler handle statusMapping:{}", JSON.toJSONString(statusMapping));

            // ==>>>> 移除当前状态下隐藏的楼层
            List<ViaFloorInfo> floorList = viaConfig.getFloorList();
            clearHiddenFloor(statusMapping, floorList);
            log.info("XfylVtpHomeTestOrderDetailHandler handle floorList:{}, statusMapping={}", JSON.toJSONString(floorList), JSON.toJSONString(statusMapping));

            // ==>>>> 楼层处理
            dealFloorList(ctx, statusMapping, jdOrder, jdhVouchers);
            log.info("XfylVtpHomeTestOrderDetailHandler handle viaConfig:{}", JSON.toJSONString(viaConfig));
        } catch (Exception e) {
            log.error("XfylVtpHomeTestOrderDetailHandler handle error", e);
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_HAND_ERROR);
        }
    }


    /**
     * compileAviator
     *
     * @param viaConfig VIA配置
     */
    private void compileAviator(ViaConfig viaConfig) {
        try {
            for (ViaStatusMapping viaStatusMapping : viaConfig.getStatusMapping()) {
                if (StrUtil.isNotBlank(viaStatusMapping.getStatusExpression())) {
                    AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE);
                }
            }
        } catch (Exception e) {
            log.info("XfylVtpHomeTestOrderDetailHandler handle compileAviator exception", e);
        }
    }

    /**
     * 查询SKU信息
     *
     * @param skuId SKU ID
     * @return {@link JdhSkuDto}
     */
    private JdhSkuDto querySkuInfo(Long skuId) {
        try {
            Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder().querySkuCoreData(Boolean.TRUE).queryServiceItem(Boolean.TRUE).skuIdList(Sets.newHashSet(skuId)).build());
            return jdhSkuDtoMap.get(skuId);
        } catch (Exception e) {
            log.info("XfylVtpHomeTestOrderDetailHandler handle querySkuInfo exception", e);
            return null;
        }
    }

    /**
     * queryPromiseHistory
     *
     * @param jdhPromise jdh承诺
     * @return {@link List}<{@link JdhPromiseHistory}>
     */
    private List<JdhPromiseHistory> queryPromiseHistory(JdhPromise jdhPromise) {
        try {
            List<JdhPromiseHistory> promiseHistories = promiseHistoryRepository.findList(PromiseHistoryRepQuery.builder().promiseId(jdhPromise.getPromiseId()).build());
            log.info("XfylVtpHomeTestOrderDetailHandler handle queryPromiseHistory promiseHistories:{}", JSON.toJSONString(promiseHistories));
            return promiseHistories;
        } catch (Exception e) {
            log.info("XfylVtpHomeTestOrderDetailHandler handle queryPromiseHistory exception", e);
            return null;
        }
    }

    /**
     * queryMedicalPromiseList
     *
     * @param jdhPromise jdhPromise
     * @return {@link List}<{@link MedicalPromiseDTO}>
     */
    private List<MedicalPromiseDTO> queryMedicalPromiseList(JdhPromise jdhPromise) {
        try {
            MedicalPromiseListRequest medPromiseRequest = new MedicalPromiseListRequest();
            medPromiseRequest.setPromiseId(jdhPromise.getPromiseId());
            medPromiseRequest.setItemDetail(Boolean.TRUE);
            medPromiseRequest.setPatientDetail(Boolean.TRUE);
            List<MedicalPromiseDTO> medicalPromiseList = medicalPromiseApplication.queryMedicalPromiseList(medPromiseRequest);
            log.info("XfylVtpHomeTestOrderDetailHandler handle queryMedicalPromiseList medicalPromiseList:{}", JSON.toJSONString(medicalPromiseList));
            return medicalPromiseList;
        } catch (Exception e) {
            log.info("XfylVtpHomeTestOrderDetailHandler handle queryMedicalPromiseList exception", e);
            return null;
        }
    }

    /**
     * 处理检测单项目信息，订单详情的检测单信息是根据用户归堆展示的，每个用户是一个groupInfoList。
     *
     * @param viaFloorInfo 通用楼层信息
     */
    private void handleFooterMedicalPromise(ViaFloorInfo viaFloorInfo, Map<String, Object> sourceData, List<MedicalPromiseDTO> medicalPromiseList) {

        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        // 过滤已经冻结和作废的检测单
        medicalPromiseList = medicalPromiseList.stream().filter(e -> !Objects.equals(e.getFreeze(), YnStatusEnum.YES.getCode())).filter(e -> !Objects.equals(e.getStatus(), MedicalPromiseStatusEnum.INVALID.getStatus())).collect(Collectors.toList());

        Map<String, List<MedicalPromiseDTO>> medicalPromiseMap = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getName));

        List<ViaGroupInfo> groups = Lists.newArrayList();
        medicalPromiseMap.forEach((name, list) -> {
            ViaGroupInfo groupInfo = new ViaGroupInfo();
            groupInfo.setTitle(name);
            List<ViaFormItem> items = Lists.newArrayListWithExpectedSize(list.size());
            for (MedicalPromiseDTO medicalPromiseDTO : list) {
                ViaFormItem item = new ViaFormItem();
                item.setFormName(medicalPromiseDTO.getServiceItemName());
                item.setFormType(ViaFormTypeEnum.SPECIMEN_CODE.getType());
                item.setParamField(ViaFormTypeEnum.SPECIMEN_CODE.getType());
                item.setValue(ViaFormTypeEnum.SPECIMEN_CODE.getType());
                // 这个表单项除了value之外需要额外的属性，这个属性可能是前端需要，也可能是action需要
                Map<String, Object> extMap = Maps.newHashMap();
                extMap.put("medicalPromiseId", medicalPromiseDTO.getMedicalPromiseId());
                extMap.put("promiseId", medicalPromiseDTO.getPromiseId());
                extMap.put("specimenCode", medicalPromiseDTO.getSpecimenCode());
                item.setExtMap(extMap);
                item.setPlaceholder("请扫码/输入采集管上的条码");
                item.setRequired(Boolean.TRUE);
                items.add(item);
            }
            groupInfo.setItems(items);
            groups.add(groupInfo);
        });

        viaFloorConfig.setGroupInfoList(groups);
        // 按钮初始化（提交样本信息按钮）
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        if (CollectionUtils.isNotEmpty(btnList)) {
            Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
            while (btnInfoIterator.hasNext()) {
                ViaBtnInfo btnInfo = btnInfoIterator.next();
                btnInfo.init(sourceData);
            }
        }
        log.info("XfylHomeTestOrderDetailHandler handleFooterMedicalPromise viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 处理采样教程楼层，当前只有自采样的业务需要采样教程。
     * 互医的场景可能有多个SKU，但是只取了一个SKU的采样教程，业务上保证互医渠道的SKU配置一个通用的采样教程。
     * 理想情况下采样教程应该取检测项目维度的。
     *
     * @param viaFloorInfo 通用楼层信息
     */
    private void handleFooterSampleCourse(ViaFloorInfo viaFloorInfo, Map<String, Object> sourceData, JdhPromise jdhPromise) {
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);

        Long serviceId = jdhPromise.getServices().get(0).getServiceId();
        JdhSkuRequest request = new JdhSkuRequest();
        request.setSkuId(serviceId);
        JdhSkuDto skuDto = productApplication.queryAggregationJdhSkuInfo(request);
        skuDto.setTutorialUrl(skuDto.getTutorialUrl());
        sourceData.put(ProductAggregateCodeEnum.JDH_PRODUCT_SERVICE.getCode(), skuDto);
        ViaActionInfo action = viaFloorConfig.getAction();
        action.init(sourceData);
        log.info("XfylHomeTestOrderDetailHandler handleFooterSampleCourse viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 只能用展示层，因为展示层才有剩余次数
     *
     * @param voucherStatusMapping
     * @param viaVoucherInfoDto
     * @return
     */
    private ViaStatusMapping hitVoucherStatusMapping(List<ViaStatusMapping> voucherStatusMapping, ViaVoucherInfoDto viaVoucherInfoDto, List<JdhPromise> waitPromises, FillViaConfigDataContext ctx) {
        Map<String, Object> param = new HashMap<>();
        param.put("viaVoucherStatus", viaVoucherInfoDto.getViaVoucherStatus());
        param.put("remainingNum", viaVoucherInfoDto.getRemainingNum());
        log.info("XfylHomeTestOrderDetailHandler hitVoucherStatusMapping param={}", JSON.toJSONString(param));
        for (ViaStatusMapping viaStatusMapping : voucherStatusMapping) {
            if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE).execute(param)) {
                // copy 一个使用
                ViaStatusMapping copyVia = JSON.parseObject(JSON.toJSONString(viaStatusMapping),ViaStatusMapping.class);

                List<ViaBtnInfo> btnList = copyVia.getBtnList();
                if (CollectionUtils.isNotEmpty(btnList)){
                    ViaBtnInfo viaBtnInfo = btnList.get(0);
                    // 立即预约
                    if (ViaBtnCodeEnum.BUY_FIRST_SUBMIT_APPOINT_BTN.getCode().equals(viaBtnInfo.getCode()) && CollectionUtils.isNotEmpty(waitPromises)){
                        String url = EntityUtil.fillUrlByBean(viaBtnInfo.getAction().getUrl(), waitPromises.get(0));
                        url = getUrlByEnvType(ctx.getEnvType(), url);
                        viaBtnInfo.getAction().setUrl(url);
                    }
                }
                return copyVia;
            }
        }
        return null;
    }

    private String getUrlByEnvType(String envType, String url){
        String jumpUrl = "openapp.jdmobile://virtual?params={\"category\":\"jump\",\"des\":\"m\",\"url\":\"{url}\"}";
        if (EnvTypeEnum.JD_APP.getCode().equals(envType)) {
            return jumpUrl.replace("{url}", url);
        }
        return url;
    }

    /**
     * queryAngelDto
     *
     * @param promiseId promiseId
     * @return {@link JdhAngelDto}
     */
    private AngelWorkDetailDto queryAngelDto(Long promiseId) {
        try {
            AngelWorkQuery angelWorkQuery = new AngelWorkQuery();
            angelWorkQuery.setPromiseId(promiseId);
            AngelWorkDetailDto workDetailDto = angelPromiseApplication.queryWorkListOrRecently(angelWorkQuery);
            log.info("XfylHomeTestOrderDetailHandler queryAngelDto,workDetailDto:{}", JSON.toJSONString(workDetailDto));
            /*AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelId(Long.parseLong(workDetailDto.getAngelId()));
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            log.info("XfylHomeTestOrderDetailHandler queryAngelDto,jdhAngelDto:{}", JSON.toJSONString(jdhAngelDto));*/
            return workDetailDto;
        } catch (Exception e) {
            log.error("XfylHomeTestOrderDetailHandler queryAngelDto error", e);
            return null;
        }
    }

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return ViaPageEnum.VTP_ORDER_DETAIL.getScene() + "_" + BusinessModeEnum.ANGEL_TEST.getCode() + "_" + ServiceTypeEnum.TEST.getServiceType();
    }
}
