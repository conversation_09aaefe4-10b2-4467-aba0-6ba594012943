package com.jdh.o2oservice.application.angelpromise.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.addresstranslation.api.base.BaseAddressInfo;
import com.jd.addresstranslation.api.base.JDAddressRequestInfo;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelLocationApplication;
import com.jdh.o2oservice.application.angel.service.AngelScheduleApplication;
import com.jdh.o2oservice.application.angelpromise.AngelPromiseQueryApplication;
import com.jdh.o2oservice.application.angelpromise.context.AngelWorkListDateModuleContext;
import com.jdh.o2oservice.application.angelpromise.context.AngelWorkQueryContext;
import com.jdh.o2oservice.application.angelpromise.context.AngelWorkShowTemplateContext;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseApplicationConverter;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseWorkApplicationConverter;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkReadApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.AngelWorkDetailShowTemplateConfig;
import com.jdh.o2oservice.base.ducc.model.AngelWorkEnumConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.VoiceTypeEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.rpc.NurseVisitInsuranceResourceRpc;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NurseVisitPolicyBo;
import com.jdh.o2oservice.core.domain.angel.rpc.param.NurseVisitQueryParam;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelActionExpressDuccBo;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelActionRuleDuccBo;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelTaskExtStateBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskExtStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelWorkListQueryContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelWorkStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.DeliverContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelWorkEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.factory.JdhAngelWorkFactory;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelTaskDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelWorkDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelWorkExtVo;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseExtend;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.core.domain.support.basic.dict.model.DictInfo;
import com.jdh.o2oservice.core.domain.support.basic.dict.repository.DictRepository;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DirectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.DirectionRequestParam;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.rpc.AngelRealTrackRpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.AngelLocationBo;
import com.jdh.o2oservice.core.domain.support.rpc.param.AngelLocationRealParam;
import com.jdh.o2oservice.core.domain.support.securitynumber.model.CallRecord;
import com.jdh.o2oservice.core.domain.support.securitynumber.repository.CallRecordRepository;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.core.domain.trade.enums.PromiseServiceStartTimeModuleTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderTrackRpcService;
import com.jdh.o2oservice.core.domain.trade.vo.JdOrderExtendVo;
import com.jdh.o2oservice.export.angel.dto.AngelLocationDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angelpromise.cmd.*;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.*;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseBindSpecimenCodeCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseCmdSpecimenCode;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.PromiseServiceStartTimeDescDTO;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.PromiseServiceStartTimeDescRequest;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseAppointmentTimeDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.StoreInfoRequest;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.command.ParseVoiceCmd;
import com.jdh.o2oservice.export.support.dto.CallRecordDetailDto;
import com.jdh.o2oservice.export.support.dto.CallRecordGroupDto;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.support.query.QueryCallRecordRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author:lichen55
 * @createTime: 2024-04-18 17:12
 * @Description: 服务者工单application
 */
@Slf4j
@Component
public class AngelPromiseApplicationImpl implements AngelPromiseApplication, AngelPromiseQueryApplication {
    /** */
    @Resource
    private AngelWorkDomainService angelWorkDomainService;
    /** */
    @Resource
    private AngelWorkRepository angelWorkRepository;
    /** */
    @Resource
    private AngelTaskRepository angelTaskRepository;
    /** */
    @Resource
    private JdhFileRepository jdhFileRepository;
    /**
     * 实验室服务
     */
    @Resource
    private ProviderStoreRepository providerStoreRepository;
    /** */
    @Resource
    private AngelShipDomainService angelShipDomainService;
    /** */
    @Resource
    private DuccConfig duccConfig;
    /** */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;
    /** */
    @Resource
    private ProductServiceItemApplication productServiceItemApplication;
    /** */
    @Resource
    private ProductApplication productApplication;
    /** */
    @Resource
    private AngelTaskDomainService angelTaskDomainService;
    /** */
    @Resource
    private PromiseApplication promiseApplication;
    /** */
    @Resource
    private DirectionServiceRpc directionServiceRpc;

    /** */
    @Resource
    private RedisLockUtil redisLockUtil;
    /** */
    @Resource
    private AngelApplication angelApplication;

    /**
     * 通话记录
     */
    @Resource
    private CallRecordApplication callRecordApplication;

    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Value("${topics.reach.file.submit.topicyfb}")
    private String voiceTopic;

    /**
     *
     */
    @Resource
    private AngelLocationApplication angelLocationApplication;

    @Resource
    private ProviderStoreApplication providerStoreApplication;
    /** */
    @Resource
    private FileManageApplication fileManageApplication;
    /** */
    @Resource
    private FileManageService fileManageService;
    /** */
    @Resource
    private NurseVisitInsuranceResourceRpc nurseVisitInsuranceResourceRpc;
    /** */
    @Value("${o2o.angelPromise.orderListLink}")
    private String orderListLink;
    /** */
    @Value("${o2o.angelPromise.orderDetailLink}")
    private String orderDetailLink;

    /**
     * angelScheduleApplication
     */
    @Autowired
    private AngelScheduleApplication angelScheduleApplication;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * 地址服务
     */
    @Resource
    AddressRpc addressRpc;

    /**
     * 工单历史
     */
    @Resource
    private AngelWorkHistoryRepository angelWorkHistoryRepository;

    /**
     * 呼叫记录
     */
    @Resource
    private CallRecordRepository callRecordRepository;

    @Resource
    private AngelServiceRecordApplication angelServiceRecordApplication;

    @Autowired
    private PromiseRepository promiseRepository;

    @Autowired
    private JdOrderRepository jdOrderRepository;

    @Resource
    private AngelRealTrackRpc angelRealTrackRpc;

    @Resource
    private AngelWorkReadApplication angelWorkReadApplication;

    @Autowired
    private OrderTrackRpcService orderTrackRpcService;

    /**
     * 字典表存储
     */
    @Resource
    private DictRepository dictRepository;

    /**
     * 服务者工单
     * @param detailQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelWorkDto querySimpleAngelWork(AngelWorkDetailQuery detailQuery) {
        AngelWorkDBQuery dbQuery = AngelPromiseApplicationConverter.instance.detailQuery2DBQuery(detailQuery);
        AngelWork angelWork = angelWorkRepository.findAngelWork(dbQuery);
        return AngelPromiseApplicationConverter.instance.entity2WorkDto(angelWork);
    }

    /**
     * 查询服务者工单聚合数据
     * @param angelWorkCountQuery
     * @return
     */
    @Override
    public List<AngelWorkGroupCountDto> queryAngelWorkGroupCountDto(AngelWorkCountQuery angelWorkCountQuery) {
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setAngelIds(angelWorkCountQuery.getAngelIds());
        angelWorkDBQuery.setStatusList(angelWorkCountQuery.getStatusList());
        angelWorkDBQuery.setServiceStartTimeBegin(angelWorkCountQuery.getServiceStartTimeBegin());
        angelWorkDBQuery.setServiceStartTimeEnd(angelWorkCountQuery.getServiceStartTimeEnd());
        angelWorkDBQuery.setColumn(angelWorkCountQuery.getColumn());
        return AngelPromiseWorkApplicationConverter.INS.convertAngelWorkGroupCountDto(angelWorkRepository.findAngelWorkGroupCount(angelWorkDBQuery));
    }

    /**
     * 查询工单信息（仅工单）
     *
     * @param angelWorkQuery
     * @return
     */
    @Override
    public AngelWorkDetailDto queryAngelWork(AngelWorkQuery angelWorkQuery) {
        boolean paramBool = Objects.isNull(angelWorkQuery) || (Objects.isNull(angelWorkQuery.getWorkId()) && Objects.isNull(angelWorkQuery.getPromiseId()));
        if(paramBool){
            log.error("[AngelPromiseApplicationImpl->queryAngelWork], angelWorkQuery={}", JSON.toJSONString(angelWorkQuery));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        }
        log.info("[AngelPromiseApplicationImpl->queryAngelWork] angelWorkQuery:{}", JSONObject.toJSONString(angelWorkQuery));
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setWorkIds(Objects.nonNull(angelWorkQuery.getWorkId()) ? Lists.newArrayList(angelWorkQuery.getWorkId()) : null);
        angelWorkDBQuery.setPromiseId(angelWorkQuery.getPromiseId());
        angelWorkDBQuery.setStatusList(CollectionUtils.isNotEmpty(angelWorkQuery.getWorkStatus()) ? Lists.newArrayList(angelWorkQuery.getWorkStatus()) : AngelWorkStatusEnum.getValidStatus());
        angelWorkDBQuery.setCreateTimeOrderByAsc(true);
        List<AngelWork> list = angelWorkRepository.findList(angelWorkDBQuery);
        if(CollectionUtils.isEmpty(list)){
            log.error("[AngelPromiseApplicationImpl->queryAngelWork],工单信息不存在");
            return null;
        }
        AngelWork angelWork = list.get(0);
        angelWork.fulfilAngelHeadImg();
        return AngelPromiseApplicationConverter.instance.convertToAngelWorkDetailDto(angelWork);
    }

    /**
     * 查询工单列表或者最近一次的无效工单
     *
     * @param angelWorkQuery 工单参数
     * @return
     */
    @Override
    public AngelWorkDetailDto queryWorkListOrRecently(AngelWorkQuery angelWorkQuery) {
        AngelWorkDetailDto angelWorkDetailDto = queryAngelWork(angelWorkQuery);
        if(Objects.nonNull(angelWorkDetailDto)){
            return angelWorkDetailDto;
        }
        angelWorkQuery.setWorkStatus(Sets.newHashSet(AngelWorkStatusEnum.CANCEL.getType()));
        return queryAngelWork(angelWorkQuery);
    }

    /** */
    @Override
    public AngelWorkDto queryAngelWorkDetail(AngelWorkDetailQuery detailQuery) {
        AngelWorkDBQuery dbQuery = AngelPromiseApplicationConverter.instance.detailQuery2DBQuery(detailQuery);
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkDetailForPage] dbQuery:{}", JSONObject.toJSONString(dbQuery));
        AngelWork angelWork = angelWorkDomainService.queryWork(detailQuery.getWorkId());

        return AngelPromiseApplicationConverter.instance.entity2WorkDto(angelWork);
    }

    @Override
    public AngelWorkDto queryAngelWorkDetailForPage(AngelWorkDetailQuery detailQuery) {
        detailQuery.setAngelId(authCheck(detailQuery.getUserPin()));

        //检查数据权限
        authorityCheck(detailQuery.getWorkId(), detailQuery.getUserPin());

        //1、获取工单详情
        AngelWorkDBQuery dbQuery = AngelPromiseApplicationConverter.instance.detailQuery2DBQuery(detailQuery);
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkDetailForPage] dbQuery:{}", JSONObject.toJSONString(dbQuery));
        AngelWork angelWork = angelWorkDomainService.queryWork(detailQuery.getWorkId());

        //1.1 是否开启了护理单配置
        Boolean serviceRecordConfigFlag = angelServiceRecordApplication.checkAngelServiceRecordConfig(AngelServiceRecordQuery.builder().workId(angelWork.getWorkId()).build());
        angelWork.setNeedNurseSheet(serviceRecordConfigFlag);

        //2、查虚拟状态导航栏配置
        List<Integer> virtualStatusList = duccConfig.getAngelVirtualStatusByWorkType(angelWork.getWorkType());
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkDetailForPage] virtualStatusList:{}", JSONObject.toJSONString(virtualStatusList));

        //3、获取跳转地址信息
        Map<String, String> jumpUrlMap = duccConfig.getAngelPromiseJumpUrlMap();

        //3、查检验单
        List<MedicalPromiseDTO> medicalPromises = queryMedicalPromise(Lists.newArrayList(angelWork.getPromiseId()), null);
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkDetailForPage] medicalPromises:{}", JSONObject.toJSONString(medicalPromises));

        //4、查服务项目
        Set<Long> serviceItemIds = medicalPromises.stream().map(medical -> Long.valueOf(medical.getServiceItemId())).collect(Collectors.toSet());
        List<ServiceItemDto> serviceItemDtos = angelWorkReadApplication.queryServiceItemDtoList(medicalPromises, serviceItemIds);
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkDetailForPage] serviceItemDtos:{}", JSONObject.toJSONString(serviceItemDtos));

        //5、查商品
        Map<Long, JdhSkuDto> longJdhSkuDtoMap = null;
        Map<Long, JdhSkuDto> allLongJdhSkuDtoMap = null;
        if(CollectionUtils.isNotEmpty(medicalPromises)){
            Set<Long> medServiceItemIds = medicalPromises.stream().map(medical -> Long.valueOf(medical.getServiceId())).collect(Collectors.toSet());
            longJdhSkuDtoMap = querySkuBySkuIds(medServiceItemIds, Boolean.FALSE);
        }

        //5.1、查询全部商品
        if(Objects.nonNull(angelWork.getJdhAngelWorkExtVo()) && Objects.nonNull(angelWork.getJdhAngelWorkExtVo().getAngelOrder())
                && CollectionUtils.isNotEmpty(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getSkuIds())) {
            allLongJdhSkuDtoMap = querySkuBySkuIds(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getSkuIds(), Boolean.FALSE);
        }else {
            log.info("AngelPromiseApplicationImpl->queryAngelWorkDetailForPage,扩展信息不存在!angelWork={}", JSON.toJSONString(angelWork));
        }

        log.info("[AngelPromiseApplicationImpl->queryAngelWorkDetailForPage] serviceItemDtos:{}", JSONObject.toJSONString(serviceItemDtos));

        //6查询保险信息
        NurseVisitPolicyBo nurseVisitPolicyBo = null;
        if(StringUtils.isNotBlank(angelWork.getInsureId())){
            NurseVisitQueryParam nurseVisitQueryParam = new NurseVisitQueryParam();
            nurseVisitQueryParam.setPin(angelWork.getAngelPin());
            nurseVisitQueryParam.setPolicyId(angelWork.getInsureId());
            nurseVisitQueryParam.setInsuranceCode(duccConfig.getInsuranceCode());
            nurseVisitPolicyBo = nurseVisitInsuranceResourceRpc.queryPolicy(nurseVisitQueryParam);
        }
        //7护士上门检测类型，需要查询对应实验室履约单，拿到服务项目及分派的实验室。在页面展示服务要求、样本存放要求等
        Map<Long, JdhStationServiceItemRel> serviceItem2StationItemRelDto = getStationServiceItemRelMap(angelWork, medicalPromises);

        //8、构建服务者履约工单信息上下文
        AngelWorkQueryContext context = new AngelWorkQueryContext(angelWork, virtualStatusList, medicalPromises, serviceItemDtos, jumpUrlMap, longJdhSkuDtoMap, allLongJdhSkuDtoMap,nurseVisitPolicyBo, serviceItem2StationItemRelDto,duccConfig,fileManageService);
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkDetailForPage] context:{}", JSONObject.toJSONString(context));
        //9、计算距离和骑行时长
        if(context.checkIsQueryDirection()){
            AngelLocationDto location = angelLocationApplication.getLocation(Long.valueOf(angelWork.getAngelId()));
            if(Objects.nonNull(location)){
                DirectionResultBO direction = getDirectionResult(StringUtils.join(location.getLatitude(), ",", location.getLongitude()), angelWork.takeTargetLocation());
                context.setDirection(direction);
            }
        }
        //10、设置通话记录
        QueryCallRecordRequest queryCallRecordRequest = new QueryCallRecordRequest();
        queryCallRecordRequest.setPromiseId(angelWork.getPromiseId());
        //queryCallRecordRequest.setShowAISummary(false);
        CallRecordGroupDto callRecordGroupDto = callRecordApplication.queryCallRecordByGroup(queryCallRecordRequest);
        context.setCallRecordGroupDto(callRecordGroupDto);
        //11、设置履约单
        PromiseDto promise = promiseApplication.findPromiseByPromiseId(PromiseIdRequest.builder().promiseId(angelWork.getPromiseId()).build());
        context.setPromiseDto(promise);
        return context.mergeWorkDto();
    }

    /**
     * 查询工单实验室列表
     *
     * @param detailQuery 入参
     * @return AngelWorkDto
     */
    @Override
    public List<AngelWorkSpecimenDto> queryAngelWorkSpecimens(AngelWorkDetailQuery detailQuery) {
        //1、获取工单详情
        AngelWorkDBQuery dbQuery = AngelPromiseApplicationConverter.instance.detailQuery2DBQuery(detailQuery);
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkSpecimens] dbQuery:{}", JSONObject.toJSONString(dbQuery));
        AngelWork angelWork = angelWorkDomainService.queryWork(detailQuery.getWorkId());

        //3、查检验单
        List<MedicalPromiseDTO> medicalPromises = queryMedicalPromise(Lists.newArrayList(angelWork.getPromiseId()), null);
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkSpecimens] medicalPromises:{}", JSONObject.toJSONString(medicalPromises));

        //8、构建服务者履约工单信息上下文
        AngelWorkQueryContext context = new AngelWorkQueryContext(angelWork, null, medicalPromises, null, null,null, null,null, null,duccConfig,fileManageService);

        return context.takeAngelWorkSpecimen(medicalPromises);

    }

    /**
     * 护士点击已到达
     *
     * @param submitArrivedCmd
     * @return
     */
    @Override
    public AngelArrivedDTO submitArrived(SubmitArrivedCmd submitArrivedCmd) {
        AssertUtils.nonNull(submitArrivedCmd, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(submitArrivedCmd.getWorkId(), "工单id不能为空");
        AssertUtils.hasText(submitArrivedCmd.getAngelPin(), "操作人不允许为空");

        String unionCacheKey = submitArrivedCmd.getWorkId() + (Boolean.TRUE.equals(submitArrivedCmd.getForceConfirm()) ? "true" : "false");
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.ANGEL_WORK_ARRIVED_ACTION, unionCacheKey);
        boolean lockFlag = redisLockUtil.tryLock(lockKey, UUID.fastUUID().toString(),
                RedisKeyEnum.ANGEL_WORK_ARRIVED_ACTION.getExpireTime(), RedisKeyEnum.ANGEL_WORK_ARRIVED_ACTION.getExpireTimeUnit());
        if (!lockFlag) {
            throw new BusinessException(SystemErrorCode.REPEAT_SUBMIT);
        }
        //检查数据权限
        AngelWork angelWork = authorityCheck(submitArrivedCmd.getWorkId(), submitArrivedCmd.getAngelPin());
        if (angelWork.getJdhAngelWorkExtVo() != null && angelWork.getJdhAngelWorkExtVo().getAngelArrivedTime() != null) {
            throw new BusinessException(SystemErrorCode.REPEAT_SUBMIT);
        }
        if (!Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType()).contains(angelWork.getWorkStatus())) {
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_STATUS_CAN_NOT_EXECUTE);
        }
        if (StringUtils.isBlank(submitArrivedCmd.getLatitude()) || StringUtils.isBlank(submitArrivedCmd.getLongitude())) {
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelPin(submitArrivedCmd.getAngelPin());
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            if (jdhAngelDto != null) {
                AngelLocationDto location = angelLocationApplication.getLocation(jdhAngelDto.getAngelId());
                if (location != null && location.getLatitude() != null && location.getLongitude() != null) {
                    submitArrivedCmd.setLatitude(location.getLatitude().toString());
                    submitArrivedCmd.setLongitude(location.getLongitude().toString());
                }
            }
        }

        AngelArrivedDTO angelArrivedDTO = new AngelArrivedDTO();
        AngelActionRuleDuccBo actionRuleDuccBo = JSON.parseObject(duccConfig.getAngelActionCheckRule(), AngelActionRuleDuccBo.class);
        if (actionRuleDuccBo == null) {
            angelArrivedDTO.setContent("请确认您已到达服务地点");
        } else {
            AngelActionExpressDuccBo angelActionExpressDuccBo = actionRuleDuccBo.getArrivedActionExpress();
            if (StringUtils.isNotBlank(submitArrivedCmd.getLatitude()) && StringUtils.isNotBlank(submitArrivedCmd.getLongitude())) {
                String failMessage = clickActionCheck(angelWork, submitArrivedCmd.getLatitude(), submitArrivedCmd.getLongitude(), angelActionExpressDuccBo);
                if (StringUtils.isNotBlank(failMessage)) {
                    angelArrivedDTO.setContent(failMessage);
                }
            } else {
                angelArrivedDTO.setContent("请确认您已到达服务地点");
            }
        }

        if (StringUtils.isBlank(angelArrivedDTO.getContent())) {
            saveArrived(angelWork, submitArrivedCmd);
        } else {
            if (Boolean.TRUE.equals(submitArrivedCmd.getForceConfirm())) {
                saveArrived(angelWork, submitArrivedCmd);
                angelArrivedDTO.setContent(null);
            }
        }
        return angelArrivedDTO;
    }

    /**
     * 护士点击开始服务
     *
     * @param submitInServiceCmd
     * @return
     */
    @Override
    public AngelInServiceDTO submitInService(SubmitInServiceCmd submitInServiceCmd) {
        AssertUtils.nonNull(submitInServiceCmd, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(submitInServiceCmd.getWorkId(), "工单id不能为空");
        AssertUtils.hasText(submitInServiceCmd.getAngelPin(), "操作人不允许为空");

        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.ANGEL_WORK_IN_SERVICE_ACTION, submitInServiceCmd.getWorkId());
        boolean lockFlag = redisLockUtil.tryLock(lockKey, UUID.fastUUID().toString(),
                RedisKeyEnum.ANGEL_WORK_IN_SERVICE_ACTION.getExpireTime(), RedisKeyEnum.ANGEL_WORK_IN_SERVICE_ACTION.getExpireTimeUnit());
        if (!lockFlag) {
            throw new BusinessException(SystemErrorCode.REPEAT_SUBMIT);
        }
        //检查数据权限
        AngelWork angelWork = authorityCheck(submitInServiceCmd.getWorkId(), submitInServiceCmd.getAngelPin());
        if (!Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType()).contains(angelWork.getWorkStatus())) {
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_STATUS_CAN_NOT_EXECUTE);
        }
        if (StringUtils.isBlank(submitInServiceCmd.getLatitude()) || StringUtils.isBlank(submitInServiceCmd.getLongitude())) {
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelPin(submitInServiceCmd.getAngelPin());
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            if (jdhAngelDto != null) {
                AngelLocationDto location = angelLocationApplication.getLocation(jdhAngelDto.getAngelId());
                if (location != null && location.getLatitude() != null && location.getLongitude() != null) {
                    submitInServiceCmd.setLatitude(location.getLatitude().toString());
                    submitInServiceCmd.setLongitude(location.getLongitude().toString());
                }
            }
        }
        AngelInServiceDTO angelInServiceDTO = new AngelInServiceDTO();
        AngelActionRuleDuccBo actionRuleDuccBo = JSON.parseObject(duccConfig.getAngelActionCheckRule(), AngelActionRuleDuccBo.class);
        if (actionRuleDuccBo == null) {
            angelInServiceDTO.setShowCodeVerification(true);
            return angelInServiceDTO;
        }
        if (StringUtils.isNotBlank(submitInServiceCmd.getLatitude()) && StringUtils.isNotBlank(submitInServiceCmd.getLongitude())) {
            AngelActionExpressDuccBo angelActionExpressDuccBo = actionRuleDuccBo.getInServiceActionExpress();
            String failMessage = clickActionCheck(angelWork, submitInServiceCmd.getLatitude(), submitInServiceCmd.getLongitude(), angelActionExpressDuccBo);
            if (StringUtils.isNotBlank(failMessage)) {
                angelInServiceDTO.setShowCodeVerification(true);
                return angelInServiceDTO;
            } else {
                //查工单信息，比对工单状态
                AngelWork aw = angelWorkDomainService.queryWork(submitInServiceCmd.getWorkId());
                //发布已校验通过验证码事件，变更工单为服务中
                updateTaskStatusChecked(aw);
                angelInServiceDTO.setShowCodeVerification(false);
                return angelInServiceDTO;
            }
        } else {
            angelInServiceDTO.setShowCodeVerification(true);
            return angelInServiceDTO;
        }
    }

    /**
     * 查询服务单外呼记录
     *
     * @param param
     * @return
     */
    @Override
    public AngelCallRecordsDTO queryCallRecords(AngelWorkDetailQuery param) {
        //查询通话记录列表
        if (Objects.equals("queryCallRecords",param.getScene())) {
            return getAngelCallRecordsDTO(param);
        }
        AngelWork angelWork = authorityCheck(param.getWorkId(), param.getUserPin());
        List<CallRecord> list = queryCallSuccessRecord(angelWork);
        boolean existCallSuccessRecords = false;
        AngelCallRecordsDTO angelCallRecordsDTO = new AngelCallRecordsDTO();
        if (CollUtil.isNotEmpty(list)) {
            existCallSuccessRecords = true;
        }
        angelCallRecordsDTO.setExistCallSuccessRecords(existCallSuccessRecords);
        return angelCallRecordsDTO;
    }

    /**
     *
     * @param param
     * @return
     */
    private AngelCallRecordsDTO getAngelCallRecordsDTO(AngelWorkDetailQuery param) {
        //没有查询类型默认返回空
        if (Objects.isNull(param.getQueryCallRecordTargetType())) {
            return AngelCallRecordsDTO.builder().existCallSuccessRecords(false).callRecordDetailList(new ArrayList<>()).build();
        }
        AngelWork angelWork = authorityCheck(param.getWorkId(), param.getUserPin());
        if (angelWork == null) {
            return AngelCallRecordsDTO.builder().existCallSuccessRecords(false).callRecordDetailList(new ArrayList<>()).build();
        }
        //查询通话记录
        QueryCallRecordRequest queryCallRecordRequest = new QueryCallRecordRequest();
        queryCallRecordRequest.setPromiseId(angelWork.getPromiseId());
        queryCallRecordRequest.setShowAISummary(true);
        CallRecordGroupDto callRecordGroupDto = callRecordApplication.queryCallRecordByGroup(queryCallRecordRequest);
        Map<String, List<CallRecordDetailDto>> groupMap = callRecordGroupDto.getGroupMap();
        if (MapUtils.isEmpty(groupMap)) {
            return AngelCallRecordsDTO.builder().existCallSuccessRecords(false).callRecordDetailList(new ArrayList<>()).build();
        }

        //查询与预约人的通话记录
        if (Objects.equals(1, param.getQueryCallRecordTargetType())) {
            //如果工单中有预约人手机号记录，取出手机号对应记录返回
            if (Objects.nonNull(angelWork.getJdhAngelWorkExtVo()) && Objects.nonNull(angelWork.getJdhAngelWorkExtVo().getAngelOrder())
                    && Objects.nonNull(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getAppointPhone())) {
                List<CallRecordDetailDto> callRecordDetailDtos = groupMap.get(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getAppointPhone());
                if (CollectionUtils.isNotEmpty(callRecordDetailDtos)) {
                    callRecordDetailDtos.forEach(callRecordDetailDto -> {
                        callRecordDetailDto.setUserData(null);
                        callRecordDetailDto.setAngelPin(null);
                        callRecordDetailDto.setUserPin(null);
                    });
                }
                return AngelCallRecordsDTO.builder().existCallSuccessRecords(CollectionUtils.isNotEmpty(callRecordDetailDtos)).callRecordDetailList(callRecordDetailDtos).build();
            }
            return AngelCallRecordsDTO.builder().existCallSuccessRecords(false).callRecordDetailList(new ArrayList<>()).build();
        }
        //查询与被检测人的通话记录
        else if (Objects.equals(2, param.getQueryCallRecordTargetType())) {
            //查询被检测人的手机号：通过履约单查询
            PromiseDto promise = promiseApplication.findPromiseByPromiseId(PromiseIdRequest.builder().promiseId(angelWork.getPromiseId()).build());
            Map<String, String> id2PhoneMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(promise.getPatients())){
                id2PhoneMap = promise.getPatients().stream().filter(promisePatientDto -> Objects.nonNull(promisePatientDto.getPhoneNumber()) && StringUtils.isNotBlank(promisePatientDto.getPhoneNumber().getPhone()))
                        .collect(Collectors.toMap(promisePatientDto -> String.valueOf(promisePatientDto.getPromisePatientId()), promisePatientDto -> promisePatientDto.getPhoneNumber().getPhone(), (o, o2) -> o2));
            }
            String phone = id2PhoneMap.getOrDefault(param.getPatientId(), "");
            List<CallRecordDetailDto> callRecordDetailDtos = groupMap.get(phone);
            if (CollectionUtils.isNotEmpty(callRecordDetailDtos)) {
                callRecordDetailDtos.forEach(callRecordDetailDto -> {
                    callRecordDetailDto.setUserData(null);
                    callRecordDetailDto.setAngelPin(null);
                    callRecordDetailDto.setUserPin(null);
                });
            }
            return AngelCallRecordsDTO.builder().existCallSuccessRecords(CollectionUtils.isNotEmpty(callRecordDetailDtos)).callRecordDetailList(callRecordDetailDtos).build();
        }

        return AngelCallRecordsDTO.builder().existCallSuccessRecords(false).callRecordDetailList(new ArrayList<>()).build();
    }

    /**
     * 根据查询条件获取工单基本信息
     *
     * @param angelWorkDetailQuery 查询条件对象
     * @return 工单基本信息DTO
     */
    @Override
    public AngelWorkDto queryAngelWorkBaseInfo(AngelWorkDetailQuery angelWorkDetailQuery) {
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelWorkDetailQuery.getWorkId()).build());
        return AngelPromiseApplicationConverter.instance.entity2WorkDto(angelWork);
    }

    /**
     * 提交服务任务完成信息
     *
     * @param cmd 入参
     * @return Boolean
     */
    @Override
    public Boolean updateSoundRecording(SubmitSoundRecordingCmd cmd) {
        AngelWork angelWork = angelWorkRepository.findAngelWork(AngelWorkDBQuery.builder().workIds(Lists.newArrayList(cmd.getWorkId())).build());
        // 修改
        Set<Long> fileIds = cmd.getFiles().stream().map(SubFile::getFileId).map(Long::valueOf).collect(Collectors.toSet());
        angelWork.updateSoundRecording(fileIds);
        // 保存
        angelWorkRepository.save(angelWork);
        return true;
    }

    /**
     * 提交医疗证明文件。
     *
     * @param cmd 提交医疗证明文件的命令对象。
     * @return 提交是否成功。
     */
    @Override
    public Boolean submitMedicalCertificateFile(SubmitMedicalCertificateFileCmd cmd) {

        //判断是否是删除
        Boolean delete = CollectionUtils.isEmpty(cmd.getFileIds()) ? Boolean.TRUE : Boolean.FALSE;

        //订单不为空，则修改订单扩展字段
        if (Objects.nonNull(cmd.getOrderId())){
            JdOrder jdOrder = jdOrderRepository.find(JdOrderIdentifier.builder().orderId(cmd.getOrderId()).build());

            String extend = jdOrder.getExtend();
            JdOrderExtendVo jdOrderExtendVo = null;

            if (StringUtil.isBlank(extend)){
                if (delete){
                    return Boolean.TRUE;
                }
                jdOrderExtendVo = new JdOrderExtendVo();
            }else {
                jdOrderExtendVo = JSON.parseObject(extend, JdOrderExtendVo.class);
            }

            jdOrderExtendVo.setMedicalCertificateFileIds(cmd.getFileIds());
            jdOrder.setExtend(JsonUtil.toJSONString(jdOrderExtendVo));
            jdOrderRepository.updateOrderByOrderId(jdOrder);


        }else if (Objects.nonNull(cmd.getPromiseId())){
            //履约单ID不为空，则修改履约单扩展字段
            JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(cmd.getPromiseId()).build());
            List<JdhPromiseExtend> promiseExtends = promise.getPromiseExtends();

            JdhPromiseExtend jdhPromiseExtend = null;
            if (CollectionUtils.isNotEmpty(promiseExtends)) {
                jdhPromiseExtend = promiseExtends.stream().filter(p -> StringUtil.equals(PromiseExtendKeyEnum.MEDICAL_CERTIFICATE_FILE_IDS.getFiledKey(), p.getAttribute())).findFirst().orElse(null);

            }
            if (Objects.isNull(jdhPromiseExtend)){
                if (delete){
                    return Boolean.TRUE;
                }
                jdhPromiseExtend = new JdhPromiseExtend();
                jdhPromiseExtend.setPromiseId(cmd.getPromiseId());
                jdhPromiseExtend.setAttribute(PromiseExtendKeyEnum.MEDICAL_CERTIFICATE_FILE_IDS.getFiledKey());
            }
            if (delete){
                promiseRepository.deletePromiseExtend(jdhPromiseExtend);
                return Boolean.TRUE;
            }

            jdhPromiseExtend.setValue(JsonUtil.toJSONString(cmd.getFileIds()));
            promiseRepository.savePromiseExtend(jdhPromiseExtend);
        }
        return Boolean.TRUE;
    }

    /**
     * 查询接通通话的呼叫记录
     * @param angelWork
     * @return
     */
    private List<CallRecord> queryCallSuccessRecord(AngelWork angelWork) {
        //检查数据权限
        CallRecord cr = new CallRecord();
        cr.setPromiseId(angelWork.getPromiseId());
        cr.setConnectedStatus(1);
        cr.setAngelPin(angelWork.getAngelPin());
        List<CallRecord> callRecordList = callRecordRepository.queryList(cr);
        if (CollUtil.isEmpty(callRecordList)) {
            return Collections.emptyList();
        }
        return callRecordList;
    }

    /**
     * 护士点击已到达、已服务校验距离逻辑
     *
     * @param angelWork
     * @param lat
     * @param lon
     * @param angelActionExpressDuccBo
     * @return
     */
    private String clickActionCheck(AngelWork angelWork, String lat, String lon, AngelActionExpressDuccBo angelActionExpressDuccBo) {
        if (angelActionExpressDuccBo != null && Boolean.TRUE.equals(angelActionExpressDuccBo.getOnOff())) {
            PromiseDto promiseDto = queryPromiseById(angelWork.getPromiseId());
            if (promiseDto == null) {
                throw new BusinessException(PromiseErrorCode.PROMISE_NOT_EXISTS);
            }
            GisPointBo lngLatByAddress = addressRpc.getLngLatByAddress(promiseDto.getStore().getStoreAddr());
            double distance = GeoDistanceUtil.calculateDistance(Double.parseDouble(lat), Double.parseDouble(lon), lngLatByAddress.getLatitude().doubleValue(),lngLatByAddress.getLongitude().doubleValue());
            Map<String, Object> expParam = new HashMap<>();
            expParam.put("angelWork", angelWork);
            expParam.put("distance", (int) distance);
            Boolean ret = (Boolean) AviatorEvaluator.compile(angelActionExpressDuccBo.getExpress(), Boolean.TRUE).execute(expParam);
            log.info("clickActionCheck,distance={},ret={},angelWork={},express={}", distance,ret,JSON.toJSONString(angelWork), angelActionExpressDuccBo.getExpress());
            if (!Boolean.TRUE.equals(ret) && StringUtils.isNotBlank(angelActionExpressDuccBo.getFailMessage())) {
                return String.format(angelActionExpressDuccBo.getFailMessage(), (int) distance);
            }
        }
        return null;
    }

    /**
     * 查询实验室对应服务项目配置
     * @param angelWork
     * @param medicalPromises
     * @return
     */
    private Map<Long, JdhStationServiceItemRel> getStationServiceItemRelMap(AngelWork angelWork, List<MedicalPromiseDTO> medicalPromises) {
        Map<Long, JdhStationServiceItemRel> serviceItem2StationItemRelDto = new HashMap<>();
        //待服务、已出门、服务中状态的服务单详情页面，检测项目信息展示管理端设置的对应实验室的要求
        List<Integer> showStatusList = Lists.newArrayList(AngelWorkStatusEnum.INIT.getType(),AngelWorkStatusEnum.WAIT_RECEIVE.getType()
                ,AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType()
                , AngelWorkStatusEnum.SERVICING.getType(),AngelWorkStatusEnum.SERVICED.getType());
        if (Objects.equals(angelWork.getWorkType(), AngelWorkTypeEnum.NURSE.getType()) && showStatusList.contains(angelWork.getWorkStatus())) {
            log.info("[AngelPromiseApplicationImpl->queryAngelWorkDetailForPage] 护士上门检测查询实验室履约单，拿到服务项目及分派的实验室。在页面展示服务要求、样本存放要求 angelWork={}", JSON.toJSONString(angelWork));
            //如果分派了实验室，查询实验室对应的项目配置信息
            List<JdhStationServiceItemRel> params = CollectionUtils.isEmpty(medicalPromises) ? Lists.newArrayList() : medicalPromises.stream().filter(medicalPromiseDTO -> StringUtils.isNotBlank(medicalPromiseDTO.getServiceItemId()) && StringUtils.isNotBlank(medicalPromiseDTO.getStationId())).map(medicalPromiseDTO -> JdhStationServiceItemRel.builder().serviceItemId(Long.valueOf(medicalPromiseDTO.getServiceItemId())).stationId(medicalPromiseDTO.getStationId()).build()).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(params)) {
                //查询实验室对应服务项目信息
                List<JdhStationServiceItemRel> jdhStationServiceItemRels = providerStoreRepository.queryStationServiceItemList(params);
                //实验室ID：服务项目id：数据
                HashBasedTable<String, Long, JdhStationServiceItemRel> objectObjectObjectHashBasedTable = HashBasedTable.create();
                jdhStationServiceItemRels.forEach(jdhStationServiceItemRel -> {
                    objectObjectObjectHashBasedTable.put(jdhStationServiceItemRel.getStationId(), jdhStationServiceItemRel.getServiceItemId(), jdhStationServiceItemRel);
                });
                //遍历分派了实验室的实验室履约单，获取服务项目对应的实验室配置：服务项目ID：配置信息
                medicalPromises.stream().filter(medicalPromiseDTO -> StringUtils.isNotBlank(medicalPromiseDTO.getServiceItemId()) && StringUtils.isNotBlank(medicalPromiseDTO.getStationId())).forEach(medicalPromiseDTO -> {
                    JdhStationServiceItemRel serviceItemRel = objectObjectObjectHashBasedTable.get(medicalPromiseDTO.getStationId(), Long.valueOf(medicalPromiseDTO.getServiceItemId()));
                    if (Objects.nonNull(serviceItemRel)) {
                        serviceItem2StationItemRelDto.put(serviceItemRel.getServiceItemId(), serviceItemRel);
                    }
                });
            }
        }
        return serviceItem2StationItemRelDto;
    }

    @Override
    @LogAndAlarm
    public List<AngelWorkDto> queryAngelWorkList(AngelWorkListQuery query) {
        AngelWorkListQueryContext queryContext = AngelPromiseApplicationConverter.instance.convertToAngelWorkListQueryContext(query);
        List<AngelWork> angelWorks = angelWorkDomainService.queryWorks(queryContext);
        return AngelPromiseApplicationConverter.instance.entity2WorkDtoList(angelWorks);
    }

    @Override
    public AngelWorkListDto queryAngelWorkListForPage(AngelWorkListQuery query) {
        query.setAngelId(String.valueOf(authCheck(query.getUserPin())));
        //1、获取工单信息
        Page<AngelWork> workPage;
        AngelWorkListDateModuleContext moduleContext = null;

        //如果只查有效
        if (Boolean.TRUE.equals(query.getQueryValid())){
            query.setStatusList(duccConfig.getWorkQueryValidStatusList());
        }

        boolean isScheduleListStatus = Boolean.FALSE;
        if(Objects.nonNull(query.getDateModuleQuery()) && query.getDateModuleQuery().getNeedDateModule()){
            //如果开启了日期组件
            moduleContext = buildDateModule(query);
            log.info("[AngelPromiseApplicationImpl->queryAngelWorkListForPage] moduleContext:{}", JSONObject.toJSONString(moduleContext));
            workPage = moduleContext.getTargetDayWorkPage();
            isScheduleListStatus = Boolean.TRUE;
        }else{
            AngelWorkListQueryContext queryContext = AngelPromiseApplicationConverter.instance.convertToAngelWorkListQueryContext(query);
            workPage = angelWorkDomainService.queryWorkPage(queryContext);
        }
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkListForPage] workPage:{}", JSONObject.toJSONString(workPage));

        PageDto<AngelWorkListNodeDto> page = null;
        if(Objects.nonNull(workPage) && CollectionUtils.isNotEmpty(workPage.getRecords())){
            //3、查检验单
            Set<Long> promiseIds = workPage.getRecords().stream().map(AngelWork::getPromiseId).collect(Collectors.toSet());
            List<MedicalPromiseDTO> medicalPromises = queryMedicalPromise(Lists.newArrayList(promiseIds), null);
            log.info("[AngelPromiseApplicationImpl->queryAngelWorkListForPage] medicalPromises:{}", JSONObject.toJSONString(medicalPromises));

            //4、查服务项目
            Set<Long> serviceItemIds = medicalPromises.stream().map(medical -> Long.valueOf(medical.getServiceItemId())).collect(Collectors.toSet());
            List<ServiceItemDto> serviceItemDtos = angelWorkReadApplication.queryServiceItemDtoList(medicalPromises, serviceItemIds);
            log.info("[AngelPromiseApplicationImpl->queryAngelWorkListForPage] serviceItemDtos:{}", JSONObject.toJSONString(serviceItemDtos));

            //5、构建当前页数据
            page = AngelPromiseWorkApplicationConverter.INS.convert2PageWorkDto(workPage, serviceItemDtos, medicalPromises, orderDetailLink);

            // 补充服务时间字段
            this.enhanceServiceStartTimeDesc(page, promiseIds, isScheduleListStatus);

            this.bulidAngelWorkDeliveryData(promiseIds,query,medicalPromises,page);
        }

        AngelWorkListDto result = new AngelWorkListDto();
        result.setOrderListLink(orderListLink);
        if(Objects.nonNull(query.getDateModuleQuery()) && query.getDateModuleQuery().getNeedDateModule()){
            result.setAngelWorkDateModules(moduleContext.getAngelWorkDateModules());
            result.setAngelWorks(page);
        }else{
            result.setAngelWorks(page);
        }
        return result;
    }

    private void bulidAngelWorkDeliveryData(Set<Long> promiseIds,AngelWorkListQuery query,List<MedicalPromiseDTO> medicalPromises,PageDto<AngelWorkListNodeDto> page) {
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getList())){
            return ;
        }
        Map<Long, List<MedicalPromiseDTO>> medicalPromiseMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(medicalPromises)){
            medicalPromiseMap =  medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromiseId));
        }
        //查履约单
        List<PromiseDto> promiseDtoList = queryPromiseInfo(Lists.newArrayList(promiseIds));
        Map<Long, PromiseDto> promiseDtoMap = Maps.newHashMap();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(promiseDtoList)){
            promiseDtoMap = promiseDtoList.stream().collect(Collectors.toMap(PromiseDto::getPromiseId, Function.identity(), (o, n)->o));
        }
        for (AngelWorkListNodeDto dto : page.getList()) {
            PromiseDto promiseDto = promiseDtoMap.get(dto.getPromiseId());
            //即时单 标签
            if(promiseDto != null){
                PromiseAppointmentTimeDto promiseAppointmentTimeDto = promiseDto.getAppointmentTime();
                if(promiseAppointmentTimeDto != null && promiseAppointmentTimeDto.getIsImmediately() != null){
                    String format = CommonConstant.YMDHM2;
                    if(Objects.nonNull(query.getDateModuleQuery()) && query.getDateModuleQuery().getNeedDateModule()){
                        format = CommonConstant.HM;
                    }
                    if(promiseAppointmentTimeDto.getIsImmediately()){
                        dto.setPromiseAppointmentType(AngelPromiseAppointmentTypeEnum.IMMEDIATELY.getType());
                        dto.setPromiseAppointmentTypeDesc(AngelPromiseAppointmentTypeEnum.IMMEDIATELY.getDesc());
                        String deliverySampleTime = DateUtil.offsetMinute(promiseAppointmentTimeDto.getAppointmentStartTime(),90).toString(format);
                        dto.setDeliverySampleTimeDesc(deliverySampleTime+"前送达实验室");
                        String serviceTime = DateUtil.offsetMinute(promiseAppointmentTimeDto.getAppointmentStartTime(),60).toString(format);
                        dto.setServiceStartTimeDesc(serviceTime+"前开始服务");
                    }else{
                        dto.setPromiseAppointmentType(AngelPromiseAppointmentTypeEnum.APPOINTMENT.getType());
                        dto.setPromiseAppointmentTypeDesc(AngelPromiseAppointmentTypeEnum.APPOINTMENT.getDesc());
                        String deliverySampleTime = DateUtil.offsetMinute(promiseAppointmentTimeDto.getAppointmentStartTime(),90).toString(format);
                        dto.setDeliverySampleTimeDesc(deliverySampleTime+"前送达实验室");
                        String serviceTime = TimeUtils.dateTimeToStr(promiseAppointmentTimeDto.getAppointmentStartTime(), TimeFormat.getByPattern(format))+"-"+TimeUtils.dateTimeToStr(promiseAppointmentTimeDto.getAppointmentEndTime(), TimeFormat.DATE_PATTERN_HM_SIMPLE);
                        dto.setServiceStartTimeDesc(serviceTime+"开始服务");
                    }
                }
            }
            //查询服务者实时位置，如果有实时位置计算上门地址之间距离和时长；如果没有实时位置则不展示
            AngelLocationDto location = angelLocationApplication.getLocation(Long.valueOf(query.getAngelId()));
            Boolean querySwitch = duccConfig.getStationDistanceRealTimeQuerySwitch();
            if (Objects.nonNull(location)) {
                DirectionResultBO directionResult = directionServiceRpc.getDirectionResult(DirectionRequestParam.builder()
                        .fromLocation(String.format("%s,%s", location.getLatitude(), location.getLongitude()))
                        .toLocation(String.format("%s,%s", dto.getLatitude(), dto.getLongitude()))
                        .travelMode(DirectionServiceRpc.TravelMode.BICYCLING).build());
                if (Objects.nonNull(directionResult)) {
                    dto.setDoorDistance(GeoDistanceUtil.distanceDynamicUnit2(directionResult.getDistance().intValue()));
                }
            }

            //组装实验室信息
            List<MedicalPromiseDTO> medicalPromiseDTOs = medicalPromiseMap.get(dto.getPromiseId());
            if(CollectionUtils.isEmpty(medicalPromiseDTOs)){
                continue;
            }
            Map<String, List<MedicalPromiseDTO>> stationPromiseMap = medicalPromiseDTOs.stream()
                    .filter(medical -> Objects.nonNull(medical.getStationId()))
                    .collect(Collectors.groupingBy(MedicalPromiseDTO::getStationId));
            if(MapUtils.isNotEmpty(stationPromiseMap)){
                List<AngelWorkSpecimenDto> result = new ArrayList<>();
                log.info("[AngelPromiseApplicationImpl -> bulidAngelWorkDeliveryData],stationPromiseMap={}", JSON.toJSONString(stationPromiseMap));
                for(Map.Entry<String, List<MedicalPromiseDTO>> entry : stationPromiseMap.entrySet()){
                    List<MedicalPromiseDTO> medicalPromiseDTOList = entry.getValue().stream().filter(medicalPromise-> medicalPromise.getDeliveryType() != null && DeliveryTypeEnum.SELF_DELIVERY.getType().equals(medicalPromise.getDeliveryType())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(medicalPromiseDTOList)){
                        continue;
                    }
                    MedicalPromiseDTO stationPromise = entry.getValue().get(0);
                    AngelWorkSpecimenDto specimenDto = new AngelWorkSpecimenDto();
                    specimenDto.setReceiverId(stationPromise.getStationId());
                    specimenDto.setReceiverName(stationPromise.getStationName());
                    specimenDto.setReceiverPhone(new PhoneNumber(stationPromise.getStationPhone()).mask());
                    specimenDto.setReceiverPhoneEncrypt(new PhoneNumber(stationPromise.getStationPhone()).encrypt());
                    specimenDto.setReceiverFullAddress(stationPromise.getStationAddress());
                    specimenDto.setStationDeliveryType(stationPromise.getDeliveryType());
                    if(Boolean.TRUE.equals(querySwitch)) {
                        GisPointBo lngLatByAddress = addressRpc.getLngLatByAddress(stationPromise.getStationAddress());
                        if (Objects.nonNull(lngLatByAddress)) {
                            specimenDto.setReceiverLat(lngLatByAddress.getLatitude().doubleValue());
                            specimenDto.setReceiverLng(lngLatByAddress.getLongitude().doubleValue());
                            //配送距离
                            DirectionResultBO direction = directionServiceRpc.getDirectionResult(DirectionRequestParam.builder()
                                    .fromLocation(String.format("%s,%s", dto.getLatitude(), dto.getLongitude()))
                                    .toLocation(String.format("%s,%s", specimenDto.getReceiverLat(), specimenDto.getReceiverLng()))
                                    .travelMode(DirectionServiceRpc.TravelMode.BICYCLING).build());
                            if (Objects.nonNull(direction)) {
                                specimenDto.setPatientToStationDistance(GeoDistanceUtil.distanceDynamicUnit2(direction.getDistance().intValue()));
                            }
                        }
                    }else {
                        if (StringUtils.isNotEmpty(stationPromise.getExtendDto().getStationLat())) {
                            specimenDto.setReceiverLat(Double.parseDouble(stationPromise.getExtendDto().getStationLat()));
                        }
                        if (StringUtils.isNotEmpty(stationPromise.getExtendDto().getStationLng())) {
                            specimenDto.setReceiverLng(Double.parseDouble(stationPromise.getExtendDto().getStationLng()));
                        }
                        //配送距离
                        Integer distance = stationPromise.getExtendDto().getDistanceToUser();
                        if (distance != null) {
                            specimenDto.setPatientToStationDistance(GeoDistanceUtil.distanceDynamicUnit2(distance));
                        }
                    }
                    result.add(specimenDto);
                }
                dto.setAngelStations(result);
                //处理自送检测单类型
                if(!(AngelWorkStatusEnum.COMPLETED.getType().equals(dto.getStatus())
                        || AngelWorkStatusEnum.REFUNDED.getType().equals(dto.getStatus())
                        || AngelWorkStatusEnum.CANCEL.getType().equals(dto.getStatus()))) {
                    if (CollectionUtils.isEmpty(result)) {
                        dto.setAngelWorkDeliveryType(null);
                        dto.setAngelWorkDeliveryTypeDesc(null);
                    } else if (CommonConstant.ONE == stationPromiseMap.size()) {
                        dto.setAngelWorkDeliveryType(AngelWorkDeliveryTypeEnum.SELF_DELIVERY.getType());
                        dto.setAngelWorkDeliveryTypeDesc(AngelWorkDeliveryTypeEnum.SELF_DELIVERY.getDesc());
                    } else {
                        dto.setAngelWorkDeliveryType(AngelWorkDeliveryTypeEnum.ANY_SELF_DELIVERY.getType());
                        dto.setAngelWorkDeliveryTypeDesc(AngelWorkDeliveryTypeEnum.ANY_SELF_DELIVERY.getDesc());
                    }
                }else{
                    dto.setAngelWorkDeliveryType(null);
                    dto.setAngelWorkDeliveryTypeDesc(null);
                }
            }
            if (CollectionUtils.isEmpty(dto.getAngelStations())) {
                dto.setDeliverySampleTimeDesc(null);
            }
        }

    }


    /**
     * 查工单详情页配置模板
     * @param templateQuery
     * @return
     */
    @Override
    public List<AngelWorkShowTemplateDto> queryAngelWorkShowTemplate(AngelWorkShowTemplateQuery templateQuery) {
        AssertUtils.nonNull(templateQuery.getWorkId(), "工单id不能为空");
        AssertUtils.hasText(templateQuery.getPageSign(), "页面标识不能为空");

        //数据权限检查
        authorityCheck(templateQuery.getWorkId(), templateQuery.getUserPin());

        AngelWork angelWork = angelWorkDomainService.queryWork(templateQuery.getWorkId());
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkShowTemplate] angelWork:{}", JSONObject.toJSONString(angelWork));
        String workDetailTemplateMap = duccConfig.getWorkDetailTemplateMap();

        JSONObject jsonObject = JSON.parseObject(workDetailTemplateMap);
        Map<String, List<AngelWorkDetailShowTemplateConfig>> showTemplateMap = Maps.newHashMap();
        jsonObject.entrySet().stream().forEach(entry -> showTemplateMap.put(entry.getKey(), JSON.parseArray(JSON.toJSONString(entry.getValue()), AngelWorkDetailShowTemplateConfig.class)));
        Set<Long> skuIds = angelWork.getJdhAngelWorkExtVo().getAngelOrder().getSkuIds();
        if(CollectionUtils.isEmpty(skuIds)){
            log.error("[AngelPromiseApplicationImpl->queryAngelWorkShowTemplate] 订单skuid信息为空,JdhAngelWorkExtVo={}", JSONObject.toJSONString(angelWork.getJdhAngelWorkExtVo()));
        }

        Map<Long, JdhSkuDto> skuMap = querySkuBySkuIds(skuIds, Boolean.FALSE);
        if(MapUtils.isEmpty(skuMap)){
            return null;
        }

        //查询文件链接
        JdhAngelWorkExtVo jdhAngelWorkExtVo = angelWork.getJdhAngelWorkExtVo();
        Set<Long> serviceFileIds = Sets.newHashSet();
        if(Objects.nonNull(jdhAngelWorkExtVo)){
            List<Long> clothingFileIds = jdhAngelWorkExtVo.getClothingFileIds();
            List<Long> wasteDestroyFileIds = jdhAngelWorkExtVo.getWasteDestroyFileIds();
            List<Long> serviceRecordFileIds = jdhAngelWorkExtVo.getServiceRecordFileIds();
            if(CollectionUtils.isNotEmpty(clothingFileIds)){
                serviceFileIds.addAll(clothingFileIds);
            }
            if(CollectionUtils.isNotEmpty(wasteDestroyFileIds)) {
                serviceFileIds.addAll(wasteDestroyFileIds);
            }
            if(CollectionUtils.isNotEmpty(serviceRecordFileIds)) {
                serviceFileIds.addAll(serviceRecordFileIds);
            }
        }
        Set<Long> confirmImgList = Sets.newHashSet();
        List<AngelTask> angelTasks = angelWork.getAngelTasks();
        angelTasks.stream().forEach(item -> {
            if(Objects.nonNull(item.getJdhAngelTaskExtVo()) && CollectionUtils.isNotEmpty(item.getJdhAngelTaskExtVo().getVisitRecordImg())){
                confirmImgList.addAll(item.getJdhAngelTaskExtVo().getVisitRecordImg());
            }
            if(Objects.nonNull(item.getJdhAngelTaskExtVo()) && Objects.nonNull(item.getJdhAngelTaskExtVo().getLetterOfConsentFileId())){
                confirmImgList.add(item.getJdhAngelTaskExtVo().getLetterOfConsentFileId());
            }
            if(Objects.nonNull(item.getJdhAngelTaskExtVo()) && Objects.nonNull(item.getJdhAngelTaskExtVo().getElectSignatureImg())){
                confirmImgList.add(item.getJdhAngelTaskExtVo().getElectSignatureImg());
            }
        });
        Set<Long> allFileIds = CollectionUtils.union(serviceFileIds, confirmImgList).stream().collect(Collectors.toSet());
        Map<Long, String> urlMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(allFileIds)){
            GenerateGetUrlCommand generateGetUrlCommand = new GenerateGetUrlCommand();
            generateGetUrlCommand.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
            generateGetUrlCommand.setFileIds(allFileIds);
            generateGetUrlCommand.setIsPublic(true);
            List<FilePreSignedUrlDto> list = fileManageApplication.generateGetUrl(generateGetUrlCommand);
            urlMap = list.stream().collect(Collectors.toMap(FilePreSignedUrlDto::getFileId, FilePreSignedUrlDto::getUrl, (k1, k2)->k1));
            log.info("[AngelPromiseApplicationImpl->queryAngelWorkShowTemplate] list:{}", JSONObject.toJSONString(list));
        }

        AngelWorkShowTemplateContext templateContext = new AngelWorkShowTemplateContext(angelWork, templateQuery, showTemplateMap, skuMap, urlMap);
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkShowTemplate] templateContext:{}", JSONObject.toJSONString(templateContext));

        String url = fileManageService.getPdfSignatureContract(DomainEnum.ANGEL_PROMISE.getCode(), AngelPromiseFileBizTypeEnum.LETTER_OF_CONSENT.getBizType());
        return templateContext.merge(url);


    }

    /**
     * 送达
     *
     * @param deliverCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deliver(DeliverCmd deliverCmd) {
        AssertUtils.nonNull(deliverCmd.getWorkId(), "服务者工单不能为空");
        AssertUtils.nonNull(deliverCmd.getLaboratoryId(), "实验室id不能为空");
        AssertUtils.nonNull(deliverCmd.getShipId(), "运单id不能为空");

        //如果是强制妥投，需要进行相关字段校验
        if(deliverCmd.getIsDelivered()!=null && deliverCmd.getIsDelivered()){
            AssertUtils.nonNull(deliverCmd.getDeliveredType(), "妥投原因不能为空");
            AssertUtils.nonNull(deliverCmd.getImageIds(), "上传图片不能为空");
            if(deliverCmd.getDeliveredType().equals(0)){
                AssertUtils.nonNull(deliverCmd.getDeliveredDesc(), "妥投原因不能为空");
            }else{
                //获取字典
                Map<String, List<DictInfo>> dic = dictRepository.queryMultiDictList(Collections.singleton("deliveredTypeDict"));
                List<DictInfo> dictInfoList = dic.get("deliveredTypeDict");
                Map<Integer,String> dictMap = new HashMap<>();
                if(CollectionUtils.isNotEmpty(dictInfoList)){
                    dictInfoList.forEach(dictInfo -> {
                        if(dictInfo.getValue() != null){
                            dictMap.put(Integer.parseInt(dictInfo.getValue().toString()),dictInfo.getLabel());
                        }
                    });
                }
                deliverCmd.setDeliveredDesc(dictMap.get(deliverCmd.getDeliveredType()));
            }
        }

        //防重锁
        String lockKey = MessageFormat.format(RedisKeyEnum.DELIVER_LOCK_KEY.getRedisKeyPrefix(), deliverCmd.getShipId());
        boolean lockFlag = redisLockUtil.tryLock(lockKey, UUID.fastUUID().toString(),
                RedisKeyEnum.WORK_CODE_VERIFICATION_LOCK_KEY.getExpireTime(), RedisKeyEnum.WORK_CODE_VERIFICATION_LOCK_KEY.getExpireTimeUnit());
        if(!lockFlag){
            throw new BusinessException(AngelPromiseBizErrorCode.REPEAT_DO);
        }

        //数据权限检查
        authorityCheck(Long.valueOf(deliverCmd.getWorkId()), deliverCmd.getUserPin());

        DeliverContext deliverContext =  AngelPromiseApplicationConverter.instance.convertToDeliverContext(deliverCmd);
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(Long.valueOf(deliverContext.getWorkId())).build());
        if(Objects.isNull(angelWork)){
            log.error("[AngelShipDomainServiceImpl.deliver],工单信息不存在.deliverContext={}", com.alibaba.fastjson.JSON.toJSONString(deliverContext));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }

        //获取实验室的经纬度
        StoreInfoRequest request = new StoreInfoRequest();
        request.setStationId(deliverCmd.getLaboratoryId());
        StoreInfoDto storeInfoDto = providerStoreApplication.queryStationInfo(request);
        if (Objects.isNull(storeInfoDto) || StringUtils.isBlank(storeInfoDto.getLat()) || StringUtils.isBlank(storeInfoDto.getLng())){
            log.error("[AngelShipDomainServiceImpl.deliver],没有查询到实验室经纬度信息!deliverCmd={}", JSON.toJSONString(deliverCmd));
            throw new BusinessException(AngelPromiseBizErrorCode.LABORATORY_GEO_NOT_EXIST);
        }
        deliverContext.setAngelId(Long.valueOf(angelWork.getAngelId()));
        deliverContext.setLaboratoryLatitude(Double.valueOf(storeInfoDto.getLat()));
        deliverContext.setLaboratoryLongitude(Double.valueOf(storeInfoDto.getLng()));

        AngelLocationDto location = new AngelLocationDto();
        if(Objects.isNull(deliverCmd.getAngelLat()) || Objects.isNull(deliverCmd.getAngelLng())) {
            location = angelLocationApplication.getLocation(Long.valueOf(angelWork.getAngelId()));
            if(Objects.isNull(location) || Objects.isNull(location.getLatitude())){
                log.error("[AngelShipDomainServiceImpl.deliver],没有查询到护士经纬度信息!deliverCmd={}", JSON.toJSONString(deliverCmd));
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_POSITION_INFO_NOT_FOUND);
            }
        }else {
            location.setLatitude(deliverCmd.getAngelLat());
            location.setLongitude(deliverCmd.getAngelLng());
        }
        deliverContext.setAngelLatitude(location.getLatitude());
        deliverContext.setAngelLongitude(location.getLongitude());
        //获取护士详细地址
        if(deliverContext.getAngelLatitude()!=null && deliverContext.getAngelLongitude()!=null) {
            JDAddressRequestInfo addressRequest = new JDAddressRequestInfo();
            addressRequest.setLng(new BigDecimal(deliverContext.getAngelLongitude()));
            addressRequest.setLat(new BigDecimal(deliverContext.getAngelLatitude()));
            BaseAddressInfo addressResult = orderTrackRpcService.getAddressByLatAndLon(addressRequest);
            if(addressResult != null){
                deliverContext.setAngelAddressDetail(addressResult.getProvinceName()+addressResult.getCityName()+addressResult.getDistrictName()+addressResult.getDetailAddress());
            }
        }
        deliverContext.setAngelWork(angelWork);
        return angelShipDomainService.selfDeliveryFinish(deliverContext);
    }

    @Override
    public Boolean checkAngelAndStationDistance(AngelStationDistanceCmd angelStationDistanceCmd) {
        AssertUtils.nonNull(angelStationDistanceCmd.getWorkId(), "服务者工单不能为空");
        AssertUtils.nonNull(angelStationDistanceCmd.getAngelId(), "护士id不能为空");
        AssertUtils.nonNull(angelStationDistanceCmd.getShipId(), "运单id不能为空");
        AssertUtils.nonNull(angelStationDistanceCmd.getLaboratoryId(), "实验室id不能为空");

        //数据权限检查
        authorityCheck(Long.valueOf(angelStationDistanceCmd.getWorkId()), angelStationDistanceCmd.getUserPin());

        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(Long.valueOf(angelStationDistanceCmd.getWorkId())).build());
        if(Objects.isNull(angelWork)){
            log.error("[AngelShipDomainServiceImpl.checkAngelAndStationDistance],工单信息不存在.angelStationDistanceCmd={}", com.alibaba.fastjson.JSON.toJSONString(angelStationDistanceCmd));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }

        //获取实验室的经纬度
        StoreInfoRequest request = new StoreInfoRequest();
        request.setStationId(angelStationDistanceCmd.getLaboratoryId());
        StoreInfoDto storeInfoDto = providerStoreApplication.queryStationInfo(request);
        if (Objects.isNull(storeInfoDto) || StringUtils.isBlank(storeInfoDto.getLat()) || StringUtils.isBlank(storeInfoDto.getLng())){
            log.error("[AngelShipDomainServiceImpl.checkAngelAndStationDistance],没有查询到实验室经纬度信息!angelStationDistanceCmd={}", JSON.toJSONString(angelStationDistanceCmd));
            throw new BusinessException(AngelPromiseBizErrorCode.LABORATORY_GEO_NOT_EXIST);
        }

        AngelLocationDto location = angelLocationApplication.getLocation(Long.valueOf(angelWork.getAngelId()));

        //检查护士配送完成的距离是否在规则范围内容
        Double maxGeoDistance = duccConfig.getMaxGeoDistance();
        if (Objects.isNull(maxGeoDistance)) {
            maxGeoDistance = CommonConstant.FIFTY;
        }
        double distance = GeoDistanceUtil.calculateDistance(location.getLatitude(), location.getLongitude(), Double.valueOf(storeInfoDto.getLat()), Double.valueOf(storeInfoDto.getLng()));
        if (distance > maxGeoDistance) {
            log.info("[[AngelShipDomainServiceImpl.checkAngelAndStationDistance],点击运单完成未满足指定的距离限制.maxGeoDistance={},distance={}]", maxGeoDistance, distance);
            return false;
        }
        return true;
    }

    @Override
    public Boolean codeVerification(AngelWorkCodeVerificationCmd codeCmd) {
        if(Objects.isNull(codeCmd) || Objects.isNull(codeCmd.getWorkId()) || StringUtils.isBlank(codeCmd.getCode())){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        }
        //防重锁
        String lockKey = MessageFormat.format(RedisKeyEnum.WORK_CODE_VERIFICATION_LOCK_KEY.getRedisKeyPrefix(), codeCmd.getWorkId());
        boolean lockFlag = redisLockUtil.tryLock(lockKey, UUID.fastUUID().toString(),
                RedisKeyEnum.WORK_CODE_VERIFICATION_LOCK_KEY.getExpireTime(), RedisKeyEnum.WORK_CODE_VERIFICATION_LOCK_KEY.getExpireTimeUnit());
        if(!lockFlag){
            throw new BusinessException(AngelPromiseBizErrorCode.REPEAT_DO);
        }

        //检查数据权限
        authorityCheck(codeCmd.getWorkId(), codeCmd.getUserPin());

        //查工单信息，比对工单状态
        AngelWork angelWork = angelWorkDomainService.queryWork(codeCmd.getWorkId());
        log.info("[AngelPromiseApplicationImpl->codeVerification] angelWork:{}", JSONObject.toJSONString(angelWork));
        if(!AngelWorkStatusEnum.WAIT_SERVICE.getType().equals(angelWork.getWorkStatus())){
            throw new BusinessException(AngelPromiseBizErrorCode.VERIFICATION_CODE_IN_ERROR_STATUS);
        }

        //查询履约单获取兑换码信息
        PromiseDto promiseDto = queryPromiseById(angelWork.getPromiseId());
        log.info("[AngelPromiseApplicationImpl->codeVerification] promiseDto:{}", JSONObject.toJSONString(promiseDto));
        if(!codeCmd.getCode().equals(promiseDto.getCode())){
            return Boolean.FALSE;
        }
        return updateTaskStatusChecked(angelWork);
    }

    @Override
    public List<AngelPlanChargeDto> queryAngelPlanCharge(AngelPlanChargeQuery query) {
        //依赖护士结算模块
        return null;
    }

    @Override
    public Boolean bindSpecimenCode(AngelWorkBindSpecimenCodeCmd specimenCodeCmd) {
        if(Objects.isNull(specimenCodeCmd)
                || Objects.isNull(specimenCodeCmd.getWorkId())
                || Objects.isNull(specimenCodeCmd.getMedicalPromiseId()) || StringUtils.isBlank(specimenCodeCmd.getSpecimenCode()) || StringUtils.isBlank(specimenCodeCmd.getPatientId()) ){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        }

        String lockKey = MessageFormat.format(RedisKeyEnum.BIND_SPECIMEN_CODE_LOCK_KEY.getRedisKeyPrefix(), specimenCodeCmd.getWorkId(), specimenCodeCmd.getPatientId());
        boolean lockFlag = redisLockUtil.tryLock(lockKey, UUID.fastUUID().toString(),
                RedisKeyEnum.BIND_SPECIMEN_CODE_LOCK_KEY.getExpireTime(), RedisKeyEnum.BIND_SPECIMEN_CODE_LOCK_KEY.getExpireTimeUnit());
        if(!lockFlag){
            throw new BusinessException(AngelPromiseBizErrorCode.REPEAT_DO);
        }

        //检查数据权限
        authorityCheck(specimenCodeCmd.getWorkId(), specimenCodeCmd.getUserPin());

        //查工单信息，比对工单状态
        AngelWork angelWork = angelWorkDomainService.queryWork(specimenCodeCmd.getWorkId());
        log.info("[AngelPromiseApplicationImpl->bindSpecimenCode] angelWork:{}", JSONObject.toJSONString(angelWork));

        //调用实验室接口绑定样本条码
        Boolean bindResult = bindSpecimenCode(specimenCodeCmd, angelWork);
        if(!bindResult){
            throw new BusinessException(AngelPromiseBizErrorCode.BIND_SPECIMEN_CODE_ERROR);
        }
        return true;
    }

    @Override
    public List<AngelWorkEnumDto> queryEnum(AngelWorkEnumQuery enumQuery) {
        List<AngelWorkEnumConfig> angelWorkEnumConfigs = duccConfig.getAngelWorkEnumConfigs(enumQuery.getEnumType());
        return AngelPromiseApplicationConverter.instance.AngelWorkEnumConfigToDtoList(angelWorkEnumConfigs);
    }

    /**
     * 确认检测任务信息
     * 1、查询文件集合；
     * 2、同意书追加用户签名；
     * 3、查询任务；
     * 4、保存任务
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.angelpromise.service.impl.AngelPromiseApplicationImpl.confirmTaskInfo")
    public Boolean confirmTaskInfo(ConfirmTaskInfoCmd cmd) {
        AssertUtils.hasText(cmd.getUserPin(), "angelPin is require");
        AssertUtils.nonNull(cmd.getWorkId(), "workId is require");
        AssertUtils.nonNull(cmd.getTaskId(), "taskId is require");

        authorityCheck(cmd.getWorkId(), cmd.getUserPin());

        List<JdhFileIdentifier> identifiers = Lists.newArrayList();
        if(Objects.nonNull(cmd.getSignatureFileId())){
            identifiers.add(new JdhFileIdentifier(cmd.getSignatureFileId()));
        }
        if(Objects.nonNull(cmd.getContractFileId())){
            identifiers.add(new JdhFileIdentifier(cmd.getContractFileId()));
        }
        if(CollectionUtils.isNotEmpty(cmd.getMedicalCertificateFileIds())){
            for (Long medicalCertificateFileId : cmd.getMedicalCertificateFileIds()) {
                identifiers.add(new JdhFileIdentifier(medicalCertificateFileId));
            }
        }
        // 仓储查询
        List<JdhFile> files = jdhFileRepository.findList(identifiers, cmd.getUserPin());
        AngelTask task = angelTaskRepository.find(new AngelTaskIdentifier(cmd.getTaskId()));
        // 确认患者信息
        task.confirmPatientInfo(files);
        // 存储
        angelTaskRepository.save(task);

        // 修改状态
        AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(cmd.getWorkId()));
        if(Objects.isNull(angelWork)){
            log.error("[AngelPromiseApplicationImpl.confirmTaskInfo],工单信息不存在!");
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        AngelTaskExtStateBo stateBo = AngelTaskExtStateBo.builder()
                .taskId(task.getTaskId())
                .taskExtStatus(AngelWorkTypeEnum.NURSE.getType().equals(angelWork.getWorkType()) ? AngelBizExtStatusEnum.CONFIRM_IDENTITY.getType() : AngelBizExtStatusEnum.CARE_CONFIRM_IDENTITY.getType())
                .build();
        List<AngelTaskExtStateBo> taskExtStateBos = Lists.newArrayList(stateBo);
        AngelTaskExtStatusContext statusContext = AngelTaskExtStatusContext.builder()
                .workId(task.getWorkId())
                .angelTaskExtStateBoList(taskExtStateBos)
                .angelTaskEventTypeEnum(AngelTaskBizExtEventTypeEnum.ANGEL_TASK_EXT_EVENT_CONFIRM)
                .build();
        angelTaskDomainService.executeTaskExt(statusContext);
        return Boolean.TRUE;
    }

    /**
     * 数据越权校验
     * @param workId
     * @param angelPin
     * @return
     */
    private AngelWork authorityCheck(Long workId, String angelPin){
        AngelWork work = angelWorkRepository.authorityFind(new AngelWorkIdentifier( workId), angelPin);
        if (Objects.isNull(work)){
            throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
        }
        return work;
    }
    /**
     * 提交服务任务完成信息
     * （1）如果是稍后提交，则只保存文件，不发送事件；
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.angelpromise.service.impl.AngelPromiseApplicationImpl.submitWorkCompleteInfo")
    public Boolean submitWorkCompleteInfo(SubmitWorkCompleteInfoCmd cmd) {
        AssertUtils.hasText(cmd.getUserPin(), "angelPin is require");
        AssertUtils.nonNull(cmd.getWorkId(), "workId is require");
        AssertUtils.nonNull(cmd.getIsDelay(), "isDelay is require");
        // 越权查询
        AngelWork work = authorityCheck(cmd.getWorkId(), cmd.getUserPin());
        // 完成
        work.complete(cmd.getClothingFileIds(), cmd.getWasteDestroyFileIds(), cmd.getServiceRecordFileIds());
        // 存储
        angelWorkRepository.save(work);

        AngelWorkEventBody angelWorkEventBody = new AngelWorkEventBody();
        angelWorkEventBody.setWorkId(work.getWorkId());
        eventCoordinator.publish(EventFactory.newDefaultEvent(work, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_SUBMIT_SERVICE_RECORDS, angelWorkEventBody));
        return Boolean.TRUE;
    }

    @Override
    public Boolean submitSoundRecording(SubmitSoundRecordingCmd cmd) {
        AssertUtils.hasText(cmd.getUserPin(), "angelPin is require");
        AssertUtils.nonNull(cmd.getWorkId(), "workId is require");
        AssertUtils.isNotEmpty(cmd.getFiles(), "files is require");
        // 越权校验
        AngelWork work = authorityCheck(cmd.getWorkId(), cmd.getUserPin());

        // 排序
        cmd.getFiles().sort(Comparator.comparing(SubFile::getOrder));
        List<Long> fileIds = cmd.getFiles().stream().map(e -> Long.valueOf(e.getFileId())).collect(Collectors.toList());
        // 修改
        work.submitSoundRecording(Sets.newHashSet(fileIds));
        // 保存
        angelWorkRepository.save(work);

        CompletableFuture.runAsync(()->{

            try {
                ParseVoiceCmd voiceCmd = ParseVoiceCmd.builder()
                        .domianCode(DomainEnum.ANGEL_PROMISE.getCode())
                        .aggregateCode(AngelWorkAggregateEnum.WORK.getCode())
                        .aggregateId(String.valueOf(cmd.getWorkId()))
                        .fileIds(fileIds)
                        .voiceType(VoiceTypeEnum.ANGEL_SERVICE_SOUND.getType())
                        .build();
                log.info("AngelPromiseApplicationImpl -> sendMessage cmd:{}", JSON.toJSONString(cmd));
                Message message = new Message(voiceTopic, JSON.toJSONString(voiceCmd), String.valueOf(cmd.getWorkId()));
                log.info("AngelPromiseApplicationImpl-> sendMessage message={}", JSON.toJSONString(message));
                reachStoreProducer.send(message);
            }catch (Exception e){
                log.error("AngelPromiseApplicationImpl-> sendMessage error:{}", e.getMessage());
            }
        });

        return Boolean.TRUE;
    }

    /**
     * 护士取消工单判断是否超过3小时不能取消
     *
     * @param angelWorkQuery 入参
     * @return Boolean
     */
    @Override
    public Boolean checkCancelTimeout(AngelWorkQuery angelWorkQuery) {
        AssertUtils.nonNull(angelWorkQuery.getWorkId(), "工单id不能为空");

        AngelWork angelWork = authorityCheck(angelWorkQuery.getWorkId(), angelWorkQuery.getUserPin());
        if(Objects.isNull(angelWork)){
            log.error("[AngelPromiseApplicationImpl.checkCancelTimeout],工单信息不存在!angelWorkQuery={}", JSON.toJSONString(angelWorkQuery));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        Date workStartTime = angelWork.getWorkStartTime();
        LocalDateTime localDateTime = TimeUtils.dateToLocalDateTime(workStartTime);
        long hourAbs = Math.abs(Duration.between(localDateTime, LocalDateTime.now()).toHours());
        log.error("[AngelPromiseApplicationImpl.checkCancelTimeout],当前时间和距离服务开始时间{}小时!", hourAbs);
        if(hourAbs < Long.valueOf(duccConfig.getCancelAngelWorkHour())){
            log.error("[AngelWorkExecuteExportImpl.execute],当前时间和距离服务开始时间小于3小时,不能取消工单!");
            throw new BusinessException(AngelPromiseBizErrorCode.WORK_CANCEL_SMALL_THREE_HOUR_ERROR);
        }
        return true;
    }

    /**
     * 检测单绑定条码
     * @param specimenCodeCmd
     * @param angelWork
     * @return
     */
    private Boolean bindSpecimenCode(AngelWorkBindSpecimenCodeCmd specimenCodeCmd, AngelWork angelWork){
        log.info("[AngelPromiseApplicationImpl -> bindSpecimenCode], specimenCodeCmd={}, angelWork={}", JSON.toJSONString(specimenCodeCmd), JSON.toJSONString(angelWork));
        MedicalPromiseCmdSpecimenCode specimenCodeBind = new MedicalPromiseCmdSpecimenCode();
        specimenCodeBind.setSpecimenCode(specimenCodeCmd.getSpecimenCode());
        specimenCodeBind.setMedicalPromiseId(specimenCodeCmd.getMedicalPromiseId());
        MedicalPromiseBindSpecimenCodeCmd bindSpecimenCodeCmd = new MedicalPromiseBindSpecimenCodeCmd();
        bindSpecimenCodeCmd.setSpecimenCodeList(Lists.newArrayList(specimenCodeBind));
        bindSpecimenCodeCmd.setVerticalCode(angelWork.getVerticalCode());
        bindSpecimenCodeCmd.setServiceType(angelWork.getServiceType());
        log.info("[AngelPromiseApplicationImpl -> bindSpecimenCode], bindSpecimenCodeCmd={}", JSON.toJSONString(bindSpecimenCodeCmd));
        return medicalPromiseApplication.batchBindSpecimenCode(bindSpecimenCodeCmd);
    }

    /**
     * 根据skuId查商品详情
     * @param skuIds
     * @param isQueryServiceItem
     * @return
     */
    private Map<Long, JdhSkuDto> querySkuBySkuIds(Set<Long> skuIds, Boolean isQueryServiceItem){
        JdhSkuListRequest skuRequest = new JdhSkuListRequest();
        skuRequest.setSkuIdList(skuIds);
        skuRequest.setQuerySkuCoreData(Boolean.TRUE);
        skuRequest.setQueryServiceItem(Boolean.FALSE);
        return productApplication.queryJdhSkuInfoList(skuRequest);
    }

    /**
     * 查履约单
     * @param promiseId
     * @return
     */
    private PromiseDto queryPromiseById(Long promiseId){
        PromiseIdRequest request = new PromiseIdRequest();
        request.setPromiseId(promiseId);
        PromiseDto promiseDto = promiseApplication.findByPromiseId(request);
        return promiseDto;
    }

    /**
     * 查检验单
     * @param promiseIds
     * @return
     */
    private List<MedicalPromiseDTO> queryMedicalPromise(List<Long> promiseIds, List<Long> patientIds){
        if(CollectionUtils.isEmpty(promiseIds) && CollectionUtils.isEmpty(patientIds)){
            return null;
        }
        MedicalPromiseListRequest promiseListRequest = new MedicalPromiseListRequest();
        promiseListRequest.setPromiseIdList(Lists.newArrayList(promiseIds));
        promiseListRequest.setPromisePatientIdList(patientIds);
        promiseListRequest.setInvalid(false);
        List<MedicalPromiseDTO> medicalPromises = medicalPromiseApplication.queryMedicalPromiseList(promiseListRequest);
        return medicalPromises;
    }

    /**
     * 查检履约单
     * @param promiseIds
     * @return
     */
    private List<PromiseDto> queryPromiseInfo(List<Long> promiseIds){
        if(CollectionUtils.isEmpty(promiseIds)){
            return null;
        }
        return promiseApplication.findByPromiseListByPromiseIds(promiseIds);
    }

    /**
     * 查商品域服务项目
     *
     * @param serviceItemIds
     * @param stationId
     * @return
     */
    private List<ServiceItemDto> queryServiceItems(Set<Long> serviceItemIds, String stationId){
        if(CollectionUtils.isEmpty(serviceItemIds)){
            return null;
        }
        ServiceItemQuery itemQuery = ServiceItemQuery.builder().itemIds(serviceItemIds).stationId(stationId).build();
        return productServiceItemApplication.queryServiceItemList(itemQuery);
    }

    /**
     * 查询日期组件
     * @param query
     * @return
     */
    private AngelWorkListDateModuleContext buildDateModule(AngelWorkListQuery query){
        AngelWorkListQueryContext queryContext = AngelPromiseApplicationConverter.instance.convertToAngelWorkListQueryContext(query);

        //初始化日期组件，查询日期范围对应的工单列表
        AngelWorkListDateModuleContext moduleContext = new AngelWorkListDateModuleContext(query.getDateModuleQuery());
        queryContext.setServiceTimeOrderByAsc(Boolean.TRUE);
        LocalDate start = moduleContext.getSelectDay();
        /**AngelWorkDBQuery
         * 日程查询特殊处理：当前work的服务开始时间（workStartTime）取的是预约时间，但是预约时间和接单时间可能跨天。所以如果传入的startDay未当天
         * 时，需要将当天之前的所有待服务的
         */
        queryContext.setStartDay(TimeUtils.localDateToStr(start));


        AngelWorkDBQuery dbQuery = new AngelWorkDBQuery(queryContext);
        dbQuery.refresh(moduleContext.getNow(), query.getDateModuleQuery().getDateModuleSize());
        dbQuery.setServiceTimeOrderByAsc(Boolean.TRUE);
        List<AngelWork> angelWorks = angelWorkRepository.findList(dbQuery);
        log.info("[AngelPromiseApplicationImpl->queryAngelWorkList] angelWorks:{}", JSONObject.toJSONString(angelWorks));
        if(CollectionUtils.isEmpty(angelWorks)){
            return moduleContext;
        }

        //根据工单列表返回情况填充组件状态
        moduleContext.buildDateModule(angelWorks);

        //获取选中状态的组件
        LocalDate selectDay = moduleContext.getSelectDay();
        queryContext.setEndDay(TimeUtils.localDateToStr(selectDay));
        Page<AngelWork> workPage = angelWorkDomainService.queryWorkPage(queryContext);
        moduleContext.setTargetDayWorkPage(workPage);
        return moduleContext;
    }

    /**
     * 骑行规划
     * @param fromLocation
     * @param toLocation
     * @return
     */
    private DirectionResultBO getDirectionResult(String fromLocation, String toLocation){
        DirectionRequestParam param = DirectionRequestParam.builder()
                .fromLocation(fromLocation)
                .toLocation(toLocation)
                .travelMode(DirectionServiceRpc.TravelMode.BICYCLING)
                .build();
        return directionServiceRpc.getDirectionResult(param);
    }


    /**
     * 越权校验
     * @param pin
     * @return
     */
    private Long authCheck(String pin){
        AssertUtils.hasText(pin, SystemErrorCode.LOGIN_FAILURE);
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(pin);
        JdhAngelDto angelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(angelDto)){
            throw new BusinessException(SystemErrorCode.DATA_AUTHORITY);
        }
        return angelDto.getAngelId();
    }

    /**
     * 护士提交验证码事件
     * 更新工单状态
     *
     * @param angelWork
     * @return
     */
    private boolean updateTaskStatusChecked (AngelWork angelWork) {
        //获取业务身份下业务扩展状态
        BusinessContext BusinessContext = new BusinessContext();
        BusinessContext.setVerticalCode(angelWork.getVerticalCode());
        BusinessContext.setServiceType(angelWork.getServiceType());
        BusinessContext.initVertical();
        Integer extStatus = angelTaskDomainService.getVerticalStatus(BusinessContext);
        if(Objects.isNull(extStatus)){
            log.error("[AngelPromiseApplicationImpl->codeVerification]，垂直业务身份下没有查询到扩展状态!");
            throw new BusinessException(AngelPromiseBizErrorCode.VERTICAL_EXTENSION_NOT_EXIST);
        }

        //验证通过修改状态
        List<AngelTaskExtStateBo> taskExtStateBos = angelWork.getAngelTasks().stream()
                .map(task -> AngelTaskExtStateBo.builder().taskId(task.getTaskId()).taskExtStatus(extStatus).build())
                .collect(Collectors.toList());
        AngelTaskExtStatusContext statusContext = AngelTaskExtStatusContext.builder()
                .workId(angelWork.getWorkId())
                .angelTaskExtStateBoList(taskExtStateBos)
                .angelTaskEventTypeEnum(AngelTaskBizExtEventTypeEnum.ANGEL_TASK_EXT_EVENT_CHECKED)
                .build();
        log.info("[AngelPromiseApplicationImpl->codeVerification] statusContext:{}", JSONObject.toJSONString(statusContext));
        return angelTaskDomainService.executeTaskExt(statusContext);
    }

    /**
     * 保存到达记录
     * @param angelWork
     * @param submitArrivedCmd
     */
    private void saveArrived(AngelWork angelWork, SubmitArrivedCmd submitArrivedCmd) {
        Date now = new Date();
        JdhAngelWorkExtVo jdhAngelWorkExtVo = angelWork.getJdhAngelWorkExtVo();
        if (jdhAngelWorkExtVo == null) {
            jdhAngelWorkExtVo = new JdhAngelWorkExtVo();
        }
        jdhAngelWorkExtVo.setAngelArrivedTime(now);
        angelWork.setJdhAngelWorkExtVo(jdhAngelWorkExtVo);
        angelWorkRepository.save(angelWork);

        //查询服务者位置信息
        AngelLocationRealParam angelLocationRealParam = new AngelLocationRealParam();
        angelLocationRealParam.setSourceId(angelWork.getSourceId());
        angelLocationRealParam.setPromiseId(angelWork.getPromiseId());
        angelLocationRealParam.setWorkId(angelWork.getWorkId());
        angelLocationRealParam.setAngelId(angelWork.getAngelId());
        AngelLocationBo angelLocationBo = angelRealTrackRpc.queryAngelLocationRealTrack(angelLocationRealParam);

        AngelWorkStatusContext workHisContext = AngelWorkStatusContext.builder()
                .angelLocationBo(angelLocationBo)
                .angelWorkEventTypeEnum(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_ARRIVED)
                .eventCode(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_ARRIVED.getCode())
                .workStatus(angelWork.getWorkStatus())
                .angelId(angelWork.getAngelId())
                .cPin(angelWork.getAngelPin())
                .build();
        angelWork.setWorkStatus(angelWork.getWorkStatus());
        workHisContext.setAngelWork(angelWork);
        AngelWorkHistory history = JdhAngelWorkFactory.createHistory(workHisContext);
        angelWorkHistoryRepository.save(history);
    }

    /**
     * 查询 服务开始时间描述
     * @param page 返回参数
     * @param promiseIds promiseIdList
     */
    private void enhanceServiceStartTimeDesc(PageDto<AngelWorkListNodeDto> page, Set<Long> promiseIds, Boolean isScheduleListStatus) {
        if (CollectionUtils.isEmpty(promiseIds) || page == null || CollectionUtils.isEmpty(page.getList())) {
            return;
        }

        try {
            // 1. 批量查询Promise数据
            List<PromiseDto> promiseDtoList = promiseApplication.findJdhPromiseList(
                    PromiseRepQuery.builder()
                            .promiseIds(new ArrayList<>(promiseIds))
                            .build()
            );

            // 2. 构建Promise映射表
            Map<Long, PromiseDto> promiseMap = Optional.ofNullable(promiseDtoList)
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .collect(Collectors.toMap(
                            PromiseDto::getPromiseId,
                            Function.identity(),
                            (first, second) -> first
                    ));

            // 3. 预获取枚举值避免循环中重复查找
            final String functionId = PromiseServiceStartTimeModuleTypeEnum.SCHEDULE_LIST.getFunctionName();

            // 4. 批量处理节点数据
            page.getList().forEach(node -> {
                PromiseDto promiseDto = promiseMap.get(node.getPromiseId());
                if (promiseDto != null) {
                    PromiseServiceStartTimeDescDTO promiseServiceStartTimeDescDTO = this.queryPromiseServiceStartTimeDesc(promiseDto, functionId);
                    if (promiseServiceStartTimeDescDTO != null) {
                        node.setServiceStartTimeDesc(promiseServiceStartTimeDescDTO.getServiceStartTimeDesc());
                        if (Boolean.TRUE.equals(isScheduleListStatus)) {
                            node.setServiceStartTimeDesc(promiseServiceStartTimeDescDTO.getScheduleListServiceTime());
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("AngelPromiseApplicationImpl -> enhanceServiceStartTimeDesc error, promiseIds: {}", promiseIds, e);
        }
    }

    private PromiseServiceStartTimeDescDTO queryPromiseServiceStartTimeDesc(PromiseDto promiseDto, String functionId) {
        PromiseServiceStartTimeDescRequest request = PromiseServiceStartTimeDescRequest.builder()
                .promiseCreateTime(promiseDto.getCreateTime())
                .appointmentTime(promiseDto.getAppointmentTime())
                .promiseId(promiseDto.getPromiseId())
                .functionId(functionId)
                .sourceVoucherId(promiseDto.getSourceVoucherId())
                .build();
        return promiseApplication.queryPromiseServiceStartTimeDesc(request);
    }
}
