package com.jdh.o2oservice.application.settlement.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.health.medical.examination.export.dto.XfylManApprovalResultDto;
import com.jd.health.medical.examination.export.param.supplier.XfylManApprovalRecordParam;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.product.listener.ImportProductIndicatorListenerContext;
import com.jdh.o2oservice.application.settlement.convert.JdServiceSettleConvert;
import com.jdh.o2oservice.application.settlement.listener.ImportAngelSettleAdjustListener;
import com.jdh.o2oservice.application.settlement.service.JdhAngelSettleAdjustApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import cn.hutool.core.bean.BeanUtil;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.enums.XfylApprovalStatusTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkHistory;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkHistoryDbQuery;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.settlement.bo.ServerSettleAmountBo;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettleAdjustQueryContext;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.enums.*;
import com.jdh.o2oservice.core.domain.settlement.model.*;
import com.jdh.o2oservice.core.domain.settlement.repository.AngelSettlementRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.JdhAngelSettleAdjustRepository;
import com.jdh.o2oservice.core.domain.settlement.rpc.HySettleRpc;
import com.jdh.o2oservice.core.domain.settlement.rpc.XfylManApprovalServiceRpc;
import com.jdh.o2oservice.core.domain.settlement.vo.AngelAddAccountAmountVo;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.enums.FileOperationStatusEnum;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.model.JdhImportExportTask;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileTaskRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.trade.enums.JdOrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettleAdjustCmd;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettleAdjustFileCmd;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementDto;
import com.jdh.o2oservice.export.settlement.dto.JdhAngelSettleAdjustDto;
import com.jdh.o2oservice.export.settlement.query.AngelSettleAdjustQuery;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务结算调账
 * <AUTHOR>
 * @Date 2024/5/8 14:57
 **/
@Slf4j
@Service("jdhAngelSettleAdjustApplication")
public class JdhAngelSettleAdjustApplicationImpl implements JdhAngelSettleAdjustApplication {
    /**
     * jdhAngelSettleAdjustRepository
     */
    @Autowired
    private JdhAngelSettleAdjustRepository jdhAngelSettleAdjustRepository;
    /** */
    @Resource
    private AngelApplication angelApplication;
    /**
     *
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    /**
     * 文件服务
     */
    @Resource
    private FileManageApplication fileManageApplication;
    /**
     * angelRepository
     */
    @Autowired
    private AngelRepository angelRepository;

    @Autowired
    private JdOrderRepository jdOrderRepository;
    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository jdhPromiseRepository;
    @Resource
    private AngelWorkRepository angelWorkRepository;
    @Resource
    private AngelWorkHistoryRepository angelWorkHistoryRepository;
    /**
     * angelSettlementRepository
     */
    @Autowired
    private AngelSettlementRepository angelSettlementRepository;

    /**
     * jdOrderApplication
     */
    @Autowired
    private JdOrderApplication jdOrderApplication;
    /** */
    @Resource
    private JdhFileTaskRepository jdhFileTaskRepository;
    /**
     * 服务者
     */
    @Resource
    FileManageService fileManageService;
    /**
     *
     */
    @Autowired
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * 文件服务仓
     */
    @Resource
    JdhFileRepository jdhFileRepository;
    /**
     * xfylManApprovalServiceRpc
     */
    @Resource
    XfylManApprovalServiceRpc xfylManApprovalServiceRpc;
    /**
     * hySettleRpc
     */
    @Autowired
    private HySettleRpc hySettleRpc;


    /**
     * 分页查询护士调账数据
     *
     * @param angelSettleAdjustQuery
     * @return page
     */
    @Override
    public PageDto<JdhAngelSettleAdjustDto> querySettleAdjustPage(AngelSettleAdjustQuery angelSettleAdjustQuery) {
        if(StringUtil.isNotBlank(angelSettleAdjustQuery.getAngelName()) || Objects.nonNull(angelSettleAdjustQuery.getAngelId())){
            List<Long> angelIdList = filterAngelIdAndName(angelSettleAdjustQuery.getAngelName(),angelSettleAdjustQuery.getAngelId());
            angelSettleAdjustQuery.setAngelIdList(angelIdList);
        }

        AngelSettleAdjustQueryContext queryContext = JdServiceSettleConvert.ins.vo2AngelSettleAdjustQueryContext(angelSettleAdjustQuery);
        Page<JdhAngelSettleAdjust> page = jdhAngelSettleAdjustRepository.querySettlementPage(queryContext);
        PageDto<JdhAngelSettleAdjustDto> pageDto = JdServiceSettleConvert.ins.entity2AngelSettleJustDtoPage(page);
        if(Objects.nonNull(pageDto)){
            List<JdhAngelSettleAdjustDto> list = pageDto.getList();
            if(CollectionUtil.isNotEmpty(list)){
                for(JdhAngelSettleAdjustDto jdhAngelSettleAdjustDto : list){
                    jdhAngelSettleAdjustDto.setAdjustTypeDesc(SettleItemTypeEnum.getSettleTypeDescByType(jdhAngelSettleAdjustDto.getAdjustType()));
                    jdhAngelSettleAdjustDto.setApprovalStatusDesc(XfylApprovalStatusTypeEnum.getDescOfType(jdhAngelSettleAdjustDto.getApprovalStatus()));
                    jdhAngelSettleAdjustDto.setAdjustDateStr(DateUtil.formatDate(jdhAngelSettleAdjustDto.getAdjustDate(),CommonConstant.YMDHMS));
                    jdhAngelSettleAdjustDto.setCashDateStr(DateUtil.formatDate(jdhAngelSettleAdjustDto.getCashDate(),CommonConstant.YMD));
                }
            }
        }
        return pageDto;
    }

    /**
     * 查询护士信息
     *
     * @param angelId
     * @return
     */
    @Override
    public JdhAngelDto queryAngelInfo(Long angelId) {
        return getAngelDto(angelId);
    }

    /**
     *
     * @param angelId
     * @return
     */
    private JdhAngelDto getAngelDto(Long angelId){
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelId(angelId);
        JdhAngelDto angelDto = angelApplication.queryByAngelInfo(angelRequest);
        if(Objects.isNull(angelDto)){
            throw new BusinessException(SettleErrorCode.ANGEL_INFO_NULL);
        }
        return angelDto;
    }

    /**
     * 保存护士调账
     *
     * @param request
     * @return
     */
    @Override
    public Boolean saveAngelSettleAdjust(AngelSettleAdjustCmd request) {
        AssertUtils.hasText(request.getAdjustAmount(),SettleErrorCode.ADJUST_AMOUNT_IS_NULL);
        String twoDays = LocalDate.now().plusDays(CommonConstant.TWO).toString();
        String cashDate = request.getCashDate();
        if(twoDays.compareTo(cashDate) > 0){
            throw new BusinessException(SettleErrorCode.CASH_DATE_T_ADD_TWO);
        }

        JdhAngelDto angelDto = getAngelDto(request.getAngelId());
        if(!angelDto.getAngelName().equals(request.getAngelName())){
            throw new BusinessException(SettleErrorCode.CHECK_ANGEL_NAME_FAILED);
        }

        BigDecimal amount = new BigDecimal(request.getAdjustAmount());
        if(amount.compareTo(new BigDecimal("99999.99")) > 0 || amount.compareTo(new BigDecimal("-99999.99")) < 0 ){
            throw new BusinessException(SettleErrorCode.ADJUST_AMOUNT_OUT);
        }
        JdhAngelSettleAdjust jdhAngelSettleAdjust = JdServiceSettleConvert.ins.cmd2JdhAngelSettleAdjust(request);
        jdhAngelSettleAdjust.setNethpDocId(angelDto.getNethpDocId());
        jdhAngelSettleAdjust.setJobNature(angelDto.getJobNature());
        saveAdjustAndAddApprovalRecord(jdhAngelSettleAdjust);
        return true;
    }

    /**
     * 保存调账 + 发起审批
     * @param jdhAngelSettleAdjust
     */
    private void saveAdjustAndAddApprovalRecord(JdhAngelSettleAdjust jdhAngelSettleAdjust){
        Long adjustId = generateIdFactory.getId();
        jdhAngelSettleAdjust.setAdjustId(adjustId);
        jdhAngelSettleAdjust.setAdjustDate(new Date());
        jdhAngelSettleAdjust.setAdjustTypeDesc(SettleItemTypeEnum.getSettleTypeDescByType(jdhAngelSettleAdjust.getAdjustType()));
        Integer result = jdhAngelSettleAdjustRepository.save(jdhAngelSettleAdjust);
        if(result > 0){
            JdhAngelSettleAdjustJoySkyBo jdhAngelSettleAdjustJoySkyBo = new JdhAngelSettleAdjustJoySkyBo();
            jdhAngelSettleAdjustJoySkyBo.setApplyErp(jdhAngelSettleAdjust.getApplyErp());
            jdhAngelSettleAdjustJoySkyBo.setOrgId(jdhAngelSettleAdjust.getOrgId());
            List<ImportAngelSettleAdjust> importAngelSettleAdjustList = new ArrayList<>();
            ImportAngelSettleAdjust importAngelSettleAdjust = JdServiceSettleConvert.ins.import2ImportAngelSettleAdjust(jdhAngelSettleAdjust);
            importAngelSettleAdjustList.add(importAngelSettleAdjust);
            jdhAngelSettleAdjustJoySkyBo.setImportAngelSettleAdjustList(importAngelSettleAdjustList);
            addApprovalRecord(jdhAngelSettleAdjustJoySkyBo);
        }
    }

    /**
     * 保存调账 + 发起审批
     * @param succList
     * @param applyErp
     * @param orgId
     */
    private void batchSaveAdjustAndAddApprovalRecord(List<ImportAngelSettleAdjust> succList,String applyErp,String orgId){
        for(ImportAngelSettleAdjust request : succList){
            JdhAngelSettleAdjust jdhAngelSettleAdjust = JdServiceSettleConvert.ins.import2JdhAngelSettleAdjust(request);
            jdhAngelSettleAdjust.setApplyErp(applyErp);
            Long adjustId = generateIdFactory.getId();
            request.setAdjustId(adjustId);
            jdhAngelSettleAdjust.setAdjustId(adjustId);
            jdhAngelSettleAdjust.setAdjustDate(new Date());
            jdhAngelSettleAdjust.setAdjustTypeDesc(SettleItemTypeEnum.getSettleTypeDescByType(jdhAngelSettleAdjust.getAdjustType()));
            jdhAngelSettleAdjustRepository.save(jdhAngelSettleAdjust);
        }
        JdhAngelSettleAdjustJoySkyBo jdhAngelSettleAdjustJoySkyBo = new JdhAngelSettleAdjustJoySkyBo();
        jdhAngelSettleAdjustJoySkyBo.setApplyErp(applyErp);
        jdhAngelSettleAdjustJoySkyBo.setOrgId(orgId);
        jdhAngelSettleAdjustJoySkyBo.setImportAngelSettleAdjustList(succList);
        addApprovalRecord(jdhAngelSettleAdjustJoySkyBo);
    }

    /**
     * 发起结算调账joysky流程
     * @param jdhAngelSettleAdjustJoySkyBo
     */
    private void addApprovalRecord(JdhAngelSettleAdjustJoySkyBo jdhAngelSettleAdjustJoySkyBo){
        try {
            XfylManApprovalRecordParam approvalRecordParam = new XfylManApprovalRecordParam();
            approvalRecordParam.setBusinessType(16);
            approvalRecordParam.setType(9);
            approvalRecordParam.setCreateUser(jdhAngelSettleAdjustJoySkyBo.getApplyErp());
            approvalRecordParam.setBusinessApprovalParam(JsonUtil.toJSONString(jdhAngelSettleAdjustJoySkyBo));
            XfylManApprovalResultDto xfylManApprovalResultDto = xfylManApprovalServiceRpc.addAdjustManApprovalRecord(approvalRecordParam);
            if(Objects.nonNull(xfylManApprovalResultDto)){
                List<Long> adjustIdList = jdhAngelSettleAdjustJoySkyBo.getImportAngelSettleAdjustList().stream().map(ImportAngelSettleAdjust::getAdjustId).collect(Collectors.toList());
                JdhAngelSettleAdjust jdhAngelSettleAdjust = new JdhAngelSettleAdjust();
                jdhAngelSettleAdjust.setApprovalNo(xfylManApprovalResultDto.getApprovalNo());
                jdhAngelSettleAdjust.setApprovalStatus(XfylApprovalStatusTypeEnum.STATUS_TO_APPROVAL.getType());
                jdhAngelSettleAdjust.setApprovalUrl(xfylManApprovalResultDto.getExtendUrl());
                jdhAngelSettleAdjust.setAdjustIdList(adjustIdList);
                jdhAngelSettleAdjustRepository.updateApprovalByAdjustIdList(jdhAngelSettleAdjust);
            }
        } catch (Throwable e){
            log.error("发起结算调账joysky流程异常, jdhAngelSettleAdjustJoySkyBo={}", JSON.toJSONString(jdhAngelSettleAdjustJoySkyBo),e);
            List<Long> adjustIdList = jdhAngelSettleAdjustJoySkyBo.getImportAngelSettleAdjustList().stream().map(ImportAngelSettleAdjust::getAdjustId).collect(Collectors.toList());
            JdhAngelSettleAdjust jdhAngelSettleAdjust = new JdhAngelSettleAdjust();
            jdhAngelSettleAdjust.setApprovalStatus(XfylApprovalStatusTypeEnum.STATUS_TO_EXCEPTION.getType());
            jdhAngelSettleAdjust.setAdjustIdList(adjustIdList);
            jdhAngelSettleAdjustRepository.updateApprovalByAdjustIdList(jdhAngelSettleAdjust);
        }
    }

    /**
     * 导出护士调账数据
     *
     * @param angelSettleAdjustQuery
     * @return page
     */
    @Override
    public Boolean exportSettleAdjust(AngelSettleAdjustQuery angelSettleAdjustQuery) {
        if(StringUtil.isNotBlank(angelSettleAdjustQuery.getAngelName()) || Objects.nonNull(angelSettleAdjustQuery.getAngelId())){
            List<Long> angelIdList = filterAngelIdAndName(angelSettleAdjustQuery.getAngelName(),angelSettleAdjustQuery.getAngelId());
            angelSettleAdjustQuery.setAngelIdList(angelIdList);
        }
        AngelSettleAdjustQueryContext queryContext = JdServiceSettleConvert.ins.vo2AngelSettleAdjustQueryContext(angelSettleAdjustQuery);
        Integer settlementCount = jdhAngelSettleAdjustRepository.querySettlementCount(queryContext);
        if(Objects.nonNull(settlementCount) && settlementCount == CommonConstant.ZERO){
            throw new BusinessException(SettleErrorCode.EXPORT_SETTLE_DATA_ZERO);
        }
        if(Objects.nonNull(settlementCount) && settlementCount > FileExportTypeEnum.ANGEL_SETTLE_ADJUST_EXPORT.getMaxCount()){
            throw new BusinessException(SettleErrorCode.EXPORT_SETTLE_DATA_OUT);
        }

        //1、构建上下文
        Map<String, Object> ctx = BeanUtil.beanToMap(angelSettleAdjustQuery);
        ctx.put("scene", FileExportTypeEnum.ANGEL_SETTLE_ADJUST_EXPORT.getType());
        ctx.put("userPin",angelSettleAdjustQuery.getUserPin());
        ctx.put("operationType", FileExportTypeEnum.ANGEL_SETTLE_ADJUST_EXPORT.getType());

        //2、调用通用文件导入能力
        return fileManageApplication.export(ctx);
    }

    /**
     *
     * @param angelSettleAdjustFileCmd
     */
    @Override
    public Boolean batchSubmitAngelAdjust(AngelSettleAdjustFileCmd angelSettleAdjustFileCmd) {
        JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(angelSettleAdjustFileCmd.getFileId()).build());
        AssertUtils.nonNull(jdhFile,"上传非法文件");
        JdhImportExportTask task = bulidJdhImportExportTask(jdhFile,angelSettleAdjustFileCmd.getApplyErp());
        jdhFileTaskRepository.add(task);
        angelSettleAdjustFileCmd.setTaskId(task.getTaskId());

        CompletableFuture.runAsync(() -> excelAllSheetAnalysis(angelSettleAdjustFileCmd,jdhFile),
                executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
        return Boolean.TRUE;
    }

    /**
     *
     * @param angelSettleAdjustFileCmd
     * @param jdhFile
     */
    private void excelAllSheetAnalysis(AngelSettleAdjustFileCmd angelSettleAdjustFileCmd,JdhFile jdhFile) {
        JdhImportExportTask task = new JdhImportExportTask();
        task.setTaskId(angelSettleAdjustFileCmd.getTaskId());
        try{
            InputStream inputStream = fileManageService.get(jdhFile.getFilePath());
            ImportProductIndicatorListenerContext context = new ImportProductIndicatorListenerContext();
            context.setErp(angelSettleAdjustFileCmd.getApplyErp());
            ImportAngelSettleAdjustListener readListener = new ImportAngelSettleAdjustListener(angelSettleAdjustFileCmd.getApplyErp());
            EasyExcelFactory.read(inputStream, ImportAngelSettleAdjust.class, readListener).sheet().doRead();
            //
            FilePreSignedUrlDto filePreSignedUrlDto = readListener.getFilePreSignedUrlDto();
            if (readListener.getIsFail()) {
                JdhFile jdhFailFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(filePreSignedUrlDto.getFileId()).build());
                task.setFileUrl(jdhFailFile.getFilePath());
                task.setStatus(FileOperationStatusEnum.FAIL.getStatus());
            }else{
                List<ImportAngelSettleAdjust> succList = readListener.getSuccList();
                log.info("[JdhAngelSettleAdjustApplicationImpl.excelAllSheetAnalysis] succList={}",succList);
                if(CollUtil.isNotEmpty(succList)){
                    batchSaveAdjustAndAddApprovalRecord(succList,angelSettleAdjustFileCmd.getApplyErp(),angelSettleAdjustFileCmd.getOrgId());
                }
                task.setStatus(FileOperationStatusEnum.SUCCESS.getStatus());
            }
        }catch (Exception e){
            log.error("[JdhAngelSettleAdjustApplicationImpl.excelAllSheetAnalysis] error=",e);
            String failReason = e.getMessage().substring(0,255);
            task.setFailMsg(failReason);
            task.setStatus(FileOperationStatusEnum.FAIL.getStatus());
        }
        jdhFileTaskRepository.update(task);
    }

    /**
     * 创建导入文件任务
     * @param jdhFile
     * @param userPin
     * @return
     */
    public static JdhImportExportTask bulidJdhImportExportTask(JdhFile jdhFile,String userPin){
        JdhImportExportTask task = new JdhImportExportTask();
        Long taskId = SpringUtil.getBean(GenerateIdFactory.class).getId();
        task.setTaskId(taskId);
        task.setOperatorId(userPin);
        task.setScene(FileExportTypeEnum.ANGEL_SETTLE_ADJUST_EXPORT.getType());
        task.setOperationType(FileExportTypeEnum.ANGEL_SETTLE_ADJUST_UPLOAD.getType());
        task.setStatus(FileOperationStatusEnum.PROCESSING.getStatus());
        task.setFileName(jdhFile.getFileName() + taskId);
        task.setFileUrl(jdhFile.getFilePath());
        task.setExpireTime(CommonConstant.EXPIRE_TIME);
        return task;
    }


    /**
     * 分页查询护士结算
     *
     * @param angelSettleQuery
     * @return page
     */
    @Override
    public PageDto<AngelSettlementDto> queryAngelSettlePage(AngelSettleQuery angelSettleQuery) {
        this.dealAngelSettleQuery(angelSettleQuery);
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(angelSettleQuery);
//        if(Objects.nonNull(angelSettleQuery.getItemType()) && (AngelSettleItemTypeEnum.ADJUST.getType().equals(angelSettleQuery.getItemType())
//            || AngelSettleItemTypeEnum.INCENTIVE.getType().equals(angelSettleQuery.getItemType()))){
//            queryContext.setSettleStatus(null);
//        }else{
            queryContext.setSettleStatus(0);
//        }
        Page<AngelSettlement> res = angelSettlementRepository.querySettlementPage(queryContext);
        return convertPageDto(res);
    }

    private void dealAngelSettleQuery(AngelSettleQuery angelSettleQuery){
        if(StringUtil.isNotBlank(angelSettleQuery.getAngelName()) || Objects.nonNull(angelSettleQuery.getAngelId())){
            List<Long> angelIdList = filterAngelIdAndName(angelSettleQuery.getAngelName(),angelSettleQuery.getAngelId());
            angelSettleQuery.setAngelIdList(angelIdList);
        }
        Integer settleStatus = angelSettleQuery.getSettleStatus();
        if(Objects.nonNull(settleStatus)){
            if(SettleStatusEnum.FREEZE.getType().equals(settleStatus)){
                if(Objects.isNull(angelSettleQuery.getCashTimeStart())){
                    angelSettleQuery.setCashTimeStart(new Date());
                }
            }else if(SettleStatusEnum.SETTLED.getType().equals(settleStatus)){
                if(Objects.isNull(angelSettleQuery.getCashTimeEnd())){
                    angelSettleQuery.setCashTimeEnd(new Date());
                }
            }
        }
    }

    /**
     * 导出护士结算数据
     *
     * @param angelSettleQuery
     * @return page
     */
    @Override
    public Boolean exportAngelSettleList(AngelSettleQuery angelSettleQuery) {
        this.dealAngelSettleQuery(angelSettleQuery);
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(angelSettleQuery);
//        if(Objects.nonNull(angelSettleQuery.getItemType()) && (AngelSettleItemTypeEnum.ADJUST.getType().equals(angelSettleQuery.getItemType())
//                || AngelSettleItemTypeEnum.INCENTIVE.getType().equals(angelSettleQuery.getItemType()))){
//            queryContext.setSettleStatus(null);
//        }else{
            queryContext.setSettleStatus(0);
//        }
        Integer settlementCount = angelSettlementRepository.querySettlementCount(queryContext);
        if(Objects.nonNull(settlementCount) && settlementCount == CommonConstant.ZERO){
            throw new BusinessException(SettleErrorCode.EXPORT_SETTLE_DATA_ZERO);
        }
        if(Objects.nonNull(settlementCount) && settlementCount > FileExportTypeEnum.ANGEL_SETTLE_EXPORT.getMaxCount()){
            throw new BusinessException(SettleErrorCode.EXPORT_SETTLE_DATA_OUT);
        }
        //1、构建上下文
        Map<String, Object> ctx = BeanUtil.beanToMap(angelSettleQuery);
        ctx.put("scene", FileExportTypeEnum.ANGEL_SETTLE_EXPORT.getType());
        ctx.put("userPin",angelSettleQuery.getUserPin());
        ctx.put("operationType", FileExportTypeEnum.ANGEL_SETTLE_EXPORT.getType());
        //2、调用通用文件导入能力
        return fileManageApplication.export(ctx);
    }

    /**
     * 查询
     * @param orderId
     * @return
     */
    @Override
    public String queryOrderSettleFeeDetail(Long orderId) {
        JdOrder jdOrder = jdOrderApplication.queryJdOrderByOrderId(orderId);
        if (Objects.nonNull(jdOrder.getParentId()) && jdOrder.getParentId()>0){
            orderId = jdOrder.getParentId();
        }
        String settleTypeAmount = "";
        JdOrderExt jdOrderExt = jdOrderApplication.findJdOrderExtDetail(orderId,JdOrderExtTypeEnum.SERVICE_FEE_SNAPSHOT.getExtType());
        if(Objects.isNull(jdOrderExt)){
            return settleTypeAmount;
        }
        String extContext = jdOrderExt.getExtContext();
        if(StringUtil.isEmpty(extContext)){
            return settleTypeAmount;
        }

        ServerSettleAmountBo serverSettleAmountBo = JsonUtil.parseObject(extContext,ServerSettleAmountBo.class);
        settleTypeAmount = serverSettleAmountBo.getSettleTypeAmountMap().toString();
        return settleTypeAmount;
    }

    /**
     * 调账审批结果:
     *
     * @param angelSettleAdjustQuery
     */
    @Override
    public void submitSettleAdjustApproval(AngelSettleAdjustQuery angelSettleAdjustQuery) {
        String approvalNo = angelSettleAdjustQuery.getApprovalNo();
        AngelSettleAdjustQueryContext queryContext = new AngelSettleAdjustQueryContext();
        queryContext.setApprovalNo(approvalNo);
        List<JdhAngelSettleAdjust> jdhAngelSettleAdjustList = jdhAngelSettleAdjustRepository.queryAngelSettleAdjust(queryContext);
        if(CollectionUtil.isNotEmpty(jdhAngelSettleAdjustList)){
            if(XfylApprovalStatusTypeEnum.STATUS_APPROVAL_PASS.getType().equals(angelSettleAdjustQuery.getApprovalStatus())){
                for(JdhAngelSettleAdjust jdhAngelSettleAdjust : jdhAngelSettleAdjustList){
                    if(Objects.nonNull(jdhAngelSettleAdjust) && jdhAngelSettleAdjust.getApprovalStatus() != angelSettleAdjustQuery.getApprovalStatus()){
                        if(jdhAngelSettleAdjust.getCashDate().before(new Date())){
                            Date newCashDate = new Date(jdhAngelSettleAdjust.getCashDate().getTime() + CommonConstant.TWO_DAY_MILLS);
                            jdhAngelSettleAdjust.setCashDate(newCashDate);
                        }
                        Boolean result = saveAngelSettlementAndAdjustRecord(jdhAngelSettleAdjust);
                        if(result){
                            addWithdrawAccountAmount(jdhAngelSettleAdjust);
                        }
                    }else{
                        log.info("[JdhAngelSettleAdjustApplicationImpl.submitSettleAdjustApproval] angelSettleAdjustQuery={}",angelSettleAdjustQuery);
                    }
                }
            }else{
                JdhAngelSettleAdjust updateJdhAngelSettleAdjust = new JdhAngelSettleAdjust();
                updateJdhAngelSettleAdjust.setApprovalStatus(angelSettleAdjustQuery.getApprovalStatus());
                updateJdhAngelSettleAdjust.setApprovalNo(approvalNo);
                jdhAngelSettleAdjustRepository.updateApprovalByAapprovalNo(updateJdhAngelSettleAdjust);
            }
        }
    }

    /**
     *
     * @param jdhAngelSettleAdjust
     * @return
     */
    private Boolean saveAngelSettlementAndAdjustRecord(JdhAngelSettleAdjust jdhAngelSettleAdjust){
        jdhAngelSettleAdjust.setApprovalStatus(XfylApprovalStatusTypeEnum.STATUS_APPROVAL_PASS.getType());
        jdhAngelSettleAdjustRepository.updateApprovalByAapprovalNo(jdhAngelSettleAdjust);
        executeSaveAngelSettleAndDetail(jdhAngelSettleAdjust);
        return Boolean.TRUE;
    }
    /**
     *
     * @param jdhAngelSettleAdjust
     * @return
     */
    private Boolean addWithdrawAccountAmount(JdhAngelSettleAdjust jdhAngelSettleAdjust){
        AngelAddAccountAmountVo angelAddAccountAmountVo = new AngelAddAccountAmountVo();
        angelAddAccountAmountVo.setAccountId(getNethpDocId(jdhAngelSettleAdjust));
        angelAddAccountAmountVo.setAmount( jdhAngelSettleAdjust.getAdjustAmount() );
        angelAddAccountAmountVo.setRecordTime(new Date());
        angelAddAccountAmountVo.setWithdrawalTime(jdhAngelSettleAdjust.getCashDate());
        angelAddAccountAmountVo.setChangeBusinessId(generateIdFactory.getIdStr());
        angelAddAccountAmountVo.setFeeType(BigDecimal.ZERO.compareTo(jdhAngelSettleAdjust.getAdjustAmount()) > 0 ? SettleItemTypeEnum.DEDUCT_ADJUST.getHuYiFeeType() : SettleItemTypeEnum.ADJUST.getHuYiFeeType());
        angelAddAccountAmountVo.setOutBusinessData(JsonUtil.toJSONString(angelAddAccountAmountVo));
        return hySettleRpc.addWithdrawAccountAmount(angelAddAccountAmountVo);
    }

    /**
     *
     * @param jdhAngelSettleAdjust
     * @return
     */
    private Long getNethpDocId(JdhAngelSettleAdjust jdhAngelSettleAdjust){
        Long nethpDocId = jdhAngelSettleAdjust.getNethpDocId();
        if(Objects.isNull(nethpDocId)){
            return nethpDocId;
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelId(jdhAngelSettleAdjust.getAngelId());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if(Objects.nonNull(jdhAngelDto) && Objects.nonNull(jdhAngelDto.getNethpDocId())){
            return jdhAngelDto.getNethpDocId();
        }else{
            throw new BusinessException(TradeErrorCode.ANGEL_INFO_NULL);
        }
    }

    /**
     *
     * @param res
     * @return
     */
    private PageDto<AngelSettlementDto> convertPageDto(Page<AngelSettlement> res){
        PageDto<AngelSettlementDto> pageDto = JdServiceSettleConvert.ins.entity2AngelSettlementDtoPage(res);
        if(Objects.nonNull(pageDto)){
            List<AngelSettlementDto> angelSettlementList = pageDto.getList();
            if(CollUtil.isNotEmpty(angelSettlementList)){
                Map<Long,JdhAngel> jdhAngelMap = getJdhAngelMap(angelSettlementList);
                Map<Long, JdOrder> jdOrderMap = getOrderMap(angelSettlementList);
                Map<Long,JdhPromise> jdPromiseMap = getPromiseMap(angelSettlementList);
                Map<Long,String> workFinishTimeMap = getAngelWorkMap(angelSettlementList);

                for(AngelSettlementDto angelSettlement : angelSettlementList){
                    JdhAngel jdhAngel = jdhAngelMap.get(angelSettlement.getAngelId());
                    if(Objects.nonNull(jdhAngel)){
                        angelSettlement.setAngelName(jdhAngel.getAngelName());
                    }
                    JdOrder jdOrder = jdOrderMap.get(angelSettlement.getOrderId());
                    if(Objects.nonNull(jdOrder)){
                        angelSettlement.setPaymentTime(jdOrder.getPaymentTime());
                        angelSettlement.setPaymentTimeStr(DateUtil.formatDate(jdOrder.getPaymentTime(),CommonConstant.YMDHMS));
                    }
                    JdhPromise jdhPromise = jdPromiseMap.get(angelSettlement.getPromiseId());
                    if(Objects.nonNull(jdhPromise)){
                        angelSettlement.setAppointmentStartTime(jdhPromise.getAppointmentTime().formatAppointmentStartTime());
                    }
                    if(angelSettlement.getExpectSettleTime().before(new Date())){
                        angelSettlement.setSettleStatusDesc(SettleStatusEnum.SETTLED.getDesc());
                    }else {
                        angelSettlement.setSettleStatusDesc(SettleStatusEnum.FREEZE.getDesc());
                    }
                    angelSettlement.setSettleDateStr(DateUtil.formatDate(angelSettlement.getSettleTime(),CommonConstant.YMDHMS));
                    angelSettlement.setItemTypeDesc(SettleItemTypeEnum.getSettleTypeDescByType(angelSettlement.getItemType()));
                    angelSettlement.setCashDateStr(DateUtil.formatDate(angelSettlement.getExpectSettleTime(),CommonConstant.YMD));
                    if(CollUtil.isNotEmpty(workFinishTimeMap)){
                        angelSettlement.setWorkFinishedTimeStr(workFinishTimeMap.get(angelSettlement.getPromiseId()));
                    }
                }
            }
        }
        return pageDto;
    }

    /**
     * 过滤护士
     * @param angelName
     * @param angelId
     * @return
     */
    private List<Long> filterAngelIdAndName(String angelName,Long angelId){
        List<Long> angelIdList = new ArrayList<>();
        if(StringUtil.isNotBlank(angelName)){
            JdhAngelRepQuery query = new JdhAngelRepQuery();
            query.setAngelId(angelId);
            query.setAngelName(angelName);
            List<JdhAngel> list = angelRepository.findList(query);
            if(CollUtil.isNotEmpty(list)){
                angelIdList.addAll(list.stream().map(JdhAngel::getAngelId).collect(Collectors.toList()));
            }else{
                angelIdList.add(0L);
            }
        }else{
            if(Objects.nonNull(angelId)){
                angelIdList.add(angelId);
            }
        }
        return angelIdList;
    }

    /**
     *
     * @param angelSettlementList
     * @return
     */
    private Map<Long,JdhAngel> getJdhAngelMap(List<AngelSettlementDto> angelSettlementList){
        List<Long> angelIdList = angelSettlementList.stream().map(AngelSettlementDto::getAngelId).distinct().collect(Collectors.toList());
        JdhAngelRepQuery query = new JdhAngelRepQuery();
        query.setAngelIdList(angelIdList);
        List<JdhAngel> list = angelRepository.findList(query);
        if(CollUtil.isNotEmpty(list)){
            Map<Long,JdhAngel> jdhAngelMap = list.stream().collect(Collectors.toMap(JdhAngel::getAngelId, Function.identity(), (k1, k2) -> k1));
            return jdhAngelMap;
        }
        return new HashMap<>();
    }
    /**
     *
     * @param list
     * @return
     */
    private Map<Long,JdOrder> getOrderMap(List<AngelSettlementDto> list){
        List<Long> orderIdList = list.stream().map(AngelSettlementDto::getOrderId).collect(Collectors.toList());
        List<JdOrder> ordersByList = jdOrderRepository.findOrdersByList(orderIdList);
        if(CollUtil.isNotEmpty(ordersByList)){
            Map<Long, JdOrder> jdOrderMap = ordersByList.stream().collect(Collectors.toMap(JdOrder::getOrderId, Function.identity(), (k1, k2) -> k1));
            return jdOrderMap;
        }
        return new HashMap<>();
    }

    /**
     *
     * @param list
     * @return
     */
    private Map<Long,JdhPromise> getPromiseMap(List<AngelSettlementDto> list){
        List<Long> promiseIdList = list.stream().map(AngelSettlementDto::getPromiseId).collect(Collectors.toList());
        List<JdhPromise> jdhPromises = jdhPromiseRepository.findList(PromiseRepQuery.builder().promiseIds(promiseIdList).build());
        if(CollUtil.isNotEmpty(jdhPromises)){
            Map<Long, JdhPromise> jdPromiseMap = jdhPromises.stream().collect(Collectors.toMap(JdhPromise::getPromiseId, Function.identity(), (k1, k2) -> k1));
            return jdPromiseMap;
        }
        return new HashMap<>();
    }

    /**
     *
     * @param list
     * @return
     */
    private Map<Long,String> getAngelWorkMap(List<AngelSettlementDto> list){
        List<Long> promiseIdList = list.stream().map(AngelSettlementDto::getPromiseId).collect(Collectors.toList());
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        //派单id
        angelWorkDBQuery.setPromiseIds(promiseIdList);
        Map<Long,String> angelWorkTimeMap = new HashMap();
        List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
        if(CollUtil.isNotEmpty(angelWorkList)){
            Map<Long, AngelWork> angelWorkMap = angelWorkList.stream().collect(Collectors.toMap(AngelWork::getWorkId, Function.identity(), (k1, k2) -> k1));
            List<Long> angelWorkIdList = angelWorkList.stream().map(AngelWork::getWorkId).collect(Collectors.toList());
            //查询工单历史
            AngelWorkHistoryDbQuery workHistoryDbQuery = AngelWorkHistoryDbQuery.builder().workIdList(angelWorkIdList).afterStatus(7).build();
            List<AngelWorkHistory> workHistoryList = angelWorkHistoryRepository.findList(workHistoryDbQuery);
            if(CollUtil.isNotEmpty(workHistoryList)){
                for(AngelWorkHistory angelWorkHistory : workHistoryList){
                    angelWorkTimeMap.put(angelWorkMap.get(angelWorkHistory.getWorkId()).getPromiseId(),
                            DateUtil.formatDate(angelWorkHistory.getCreateTime(),CommonConstant.YMDHMS));
                }
            }
        }
        return angelWorkTimeMap;
    }



    /**
     *
     * @param jdhAngelSettleAdjust
     */
    private void executeSaveAngelSettleAndDetail(JdhAngelSettleAdjust jdhAngelSettleAdjust) {
        log.info("JdServiceSettleApplicationImpl -> executeSaveAngelSettleAndDetail start orderId :{}", jdhAngelSettleAdjust);
        // 1.保存结算流水与明细
        List<AngelSettlement> angelSettlementList = new ArrayList<>();
        List<AngelSettlementDetail> angelSettlementDetailList = new ArrayList<>();
        Long settleId = generateIdFactory.getId();
        AngelSettlement angelSettlement = new AngelSettlement();
        angelSettlement.setAngelId(jdhAngelSettleAdjust.getAngelId());
        angelSettlement.setJobNature(jdhAngelSettleAdjust.getJobNature());
        //angelSettlement.setSettlementType(SettleTypeEnum.INCOME.getType());
        //angelSettlement.setItemType(jdhAngelSettleAdjust.getAdjustType());
        angelSettlement.setSettleAmount(jdhAngelSettleAdjust.getAdjustAmount());
        //2025-09-09 钱包优化，当调账金额低于0时，类型为调账扣除
        SettleItemTypeEnum settleItemTypeEnum = BigDecimal.ZERO.compareTo(jdhAngelSettleAdjust.getAdjustAmount()) > 0 ? SettleItemTypeEnum.DEDUCT_ADJUST : SettleItemTypeEnum.getSettleTypeEnumByType(jdhAngelSettleAdjust.getAdjustType());
        angelSettlement.setItemType(Objects.nonNull(settleItemTypeEnum) ? settleItemTypeEnum.getType() : jdhAngelSettleAdjust.getAdjustType());
        angelSettlement.setSettlementType(Objects.nonNull(settleItemTypeEnum) ? settleItemTypeEnum.getItemTypeGroupEnum().getSettlementType() : SettleTypeEnum.INCOME.getType());

        angelSettlement.setSettleTime(new Date());
        angelSettlement.setExpectSettleTime(jdhAngelSettleAdjust.getCashDate());
        angelSettlement.setSettleStatus(SettleStatusEnum.FREEZE.getType());
        angelSettlement.setSettleId(settleId);
        angelSettlementList.add(angelSettlement);
        AngelSettlement angelSettlement2 = new AngelSettlement();
        BeanUtil.copyProperties(angelSettlement,angelSettlement2);
        angelSettlement2.setSettleId(generateIdFactory.getId());
        angelSettlement2.setSettleStatus(SettleStatusEnum.INIT.getType());
        angelSettlementList.add(angelSettlement2);

        Long settleDetailId = generateIdFactory.getId();
        AngelSettlementDetail angelSettlementDetail = new AngelSettlementDetail();
        angelSettlementDetail.setSettleId(settleId);
        angelSettlementDetail.setSettleItemType(SettleItemTypeEnum.getSettleTypeEnumByType(jdhAngelSettleAdjust.getAdjustType()).getType());
        angelSettlementDetail.setFeeName(SettleItemTypeEnum.getSettleTypeEnumByType(jdhAngelSettleAdjust.getAdjustType()).getDesc());
        angelSettlementDetail.setSettleAmount(jdhAngelSettleAdjust.getAdjustAmount());
        angelSettlementDetail.setSettleDetailId(settleDetailId);
        angelSettlementDetailList.add(angelSettlementDetail);
        angelSettlementRepository.batchSaveAngelSettlementAndDetail(angelSettlementList,angelSettlementDetailList);
    }

}
