package com.jdh.o2oservice.application.settlement.service;

import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.settlement.bo.ServerSettleAmountBo;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.export.settlement.cmd.AngelInviteActivitySettlementCmd;
import com.jdh.o2oservice.export.trade.query.JdServiceSettleParam;

import java.util.List;
import java.util.Map;


/**
 * JdServiceSettleApplication 服务结算
 *
 * <AUTHOR>
 * @version 2024/5/8 23:15
 **/
public interface JdServiceSettleApplication {

    /**
     * 派单预估结算价
     * @param jdServiceSettleParam
     * @return
     */
    ServerSettleAmountBo getOrderSettleAmount(JdServiceSettleParam jdServiceSettleParam);

    /**
     * 护士完成单个服务开始结算
     * @param body
     */
    void angelServiceFinishSettleAndEbs(MedicalPromiseEventBody body);

    /**
     * 退款成功开始结算
     * @param context
     */
    void angelRefundSuccSettleAndEbs(AngelServiceFinishSettlementContext context);

    /**
     *
     */
    void orderFinishState(MedicalPromiseEventBody body);
    /**
     * 作废成功开始结算
     * @param context
     */
    void invalidVoucherSettleAndEbs(AngelServiceFinishSettlementContext context);

    /**
     *
     * @param promiseId
     * @return
     */
    String findAngelWorkSettleSnapshot(Long promiseId);

    /**
     * 护士邀请活动结算
     * @param cmd
     * @return
     */
    Boolean angelInviteActivitySettlement(AngelInviteActivitySettlementCmd cmd);

    /**
     * 清洗商品和项目结算价
     * @param ossKey
     * @return
     */
    Boolean cleanSkuItemSettlementPrice(String ossKey);

    /**
     * 清洗护士平台服务费
     * @param mappingOssKey
     * @return
     */
    Boolean cleanAngelPlatformDraw(String mappingOssKey, Boolean execute);

    /**
     * 派单预估结算价
     * @param jdServiceSettleParam
     * @return
     */
    Map<String, ServerSettleAmountBo> getOrderSettleAmountDiagram(JdServiceSettleParam jdServiceSettleParam);

    /**
     * 导出时间范围内护士结算差异数据（互医数据与到家数据不一致的数据）
     * @param queryMonth 月份，不传则匹配累计总金额
     * @return
     */
    Boolean exportAngelSettlementDiffData(String queryMonth);

    /**
     * 重新结算-根据工单ID
     * @param workIdList
     * @return
     */
    Boolean reAngelSettleByWorkId(List<Long> workIdList);
}
