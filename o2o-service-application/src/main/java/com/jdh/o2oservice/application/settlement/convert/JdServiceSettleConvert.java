package com.jdh.o2oservice.application.settlement.convert;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.medicine.base.common.util.DateUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettleAdjustQueryContext;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementUpdateContext;
import com.jdh.o2oservice.core.domain.settlement.enums.*;
import com.jdh.o2oservice.core.domain.settlement.model.*;
import com.jdh.o2oservice.core.domain.settlement.repository.query.JdhAngelSettleAreaFeeQuery;
import com.jdh.o2oservice.core.domain.settlement.vo.AngelCashOutVo;
import com.jdh.o2oservice.core.domain.settlement.vo.BankCardDetailVo;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawAccountVo;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawDetailVo;
import com.jdh.o2oservice.export.settlement.cmd.AngelCashOutCmd;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettleAdjustCmd;
import com.jdh.o2oservice.export.settlement.cmd.AngelSettlementConfigCmd;
import com.jdh.o2oservice.export.settlement.cmd.JdSettlementStatusCmd;
import com.jdh.o2oservice.export.settlement.dto.*;
import com.jdh.o2oservice.export.settlement.query.AngelSettleAdjustQuery;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 3:01 下午
 * @Description:
 */
@Mapper
public interface JdServiceSettleConvert {
    JdServiceSettleConvert ins = Mappers.getMapper(JdServiceSettleConvert.class);

    @Mapping(target = "totalPage", source = "pages")
    @Mapping(target = "pageNum", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "totalCount", source = "total")
    @Mapping(target = "list", source = "records")
    PageDto<AngelSettlementDto> entity2AngelSettlementDtoPage(Page<AngelSettlement> angelSettlementPage);

    @Mapping(target = "totalPage", source = "pages")
    @Mapping(target = "pageNum", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "totalCount", source = "total")
    @Mapping(target = "list", source = "records")
    PageDto<JdhAngelSettleAdjustDto> entity2AngelSettleJustDtoPage(Page<JdhAngelSettleAdjust> page);

    @Mapping(target = "cashDate", source = "cashDate", dateFormat = "yyyy-MM-dd")
    JdhAngelSettleAdjust cmd2JdhAngelSettleAdjust(AngelSettleAdjustCmd request);

    @Mapping(target = "cashDate", source = "cashDate", dateFormat = "yyyy-MM-dd")
    JdhAngelSettleAdjust import2JdhAngelSettleAdjust(ImportAngelSettleAdjust request);

    @Mapping(target = "cashDate", source = "cashDate", dateFormat = "yyyy-MM-dd")
    ImportAngelSettleAdjust import2ImportAngelSettleAdjust(JdhAngelSettleAdjust request);


    @Mapping(target = "itemTypeDesc", expression = "java(itemTypeConvertUpgrade(settlement))")
    @Mapping(target = "failSkipUrl", source = "failReasonCode")
    AngelSettlementDto entity2AngelSettlementDto(AngelSettlement settlement);


    @Named("itemTypeConvert")
    default String itemTypeConvert(Integer itemType, Long orderId) {
        SettleItemTypeEnum settleTypeEnum = SettleItemTypeEnum.getSettleTypeEnumByType(itemType);
        if (Objects.isNull(settleTypeEnum)) {
            return "";
        }
        if (SettleItemTypeEnum.isOrderRevenue(itemType) && Objects.nonNull(orderId)) {
            return String.format("%s--%s", settleTypeEnum.getUpgradeDesc(), orderId);
        }
        return settleTypeEnum.getUpgradeDesc();
    }

    @Named("itemTypeConvertUpgrade")
    default String itemTypeConvertUpgrade(AngelSettlement settlement) {
        SettleItemGroupTypeEnum itemTypeGroupEnum = settlement.getSettleItemTypeGroupEnum();
        if (Objects.isNull(itemTypeGroupEnum)) {
            return "";
        }
        if (SettleItemGroupTypeEnum.INCOME_ORDER.equals(itemTypeGroupEnum) && Objects.nonNull(settlement.getOrderId())) {
            return String.format("%s--%s", itemTypeGroupEnum.getDesc(), settlement.getOrderId());
        }
        return itemTypeGroupEnum.getDesc();
    }

    /**
     * 查询 AngelSettlementQueryContext 上下文信息
     * @param query AngelSettleQuery 查询参数
     * @return AngelSettlementQueryContext 上下文信息
     */
    AngelSettlementQueryContext query2Context(AngelSettleQuery query);

    AngelSettlementUpdateContext query2UpdateContext(JdSettlementStatusCmd jdSettlementStatusCmd);

    List<AngelSettlementDetailDto> entityAngelSettlementDetailDtos(List<AngelSettlementDetail> detailList);


    AngelBankDto vo2AngelBankDto(BankCardDetailVo detailVo);


    @Mapping(target = "totSettleAmount", source = "cumulativeAcquisitionAmount")
    @Mapping(target = "settleAmount", source = "accountWithdrawalAmount")
    @Mapping(target = "noSettleAmount", source = "accountFrozenAmount", defaultValue = "0")
    @Mapping(target = "toDaySettleAmount", source = "todayIncome", defaultValue = "0")
    AngelSettlementMoneyDto vo2AngelSettlementMoneyDto(WithdrawAccountVo accountVo);

    @AfterMapping
    default void setAccountBalance(@MappingTarget AngelSettlementMoneyDto target, WithdrawAccountVo source) {
        // 使用 Optional 和 orElse 来处理可能为 null 的值
        BigDecimal frozenAmount = Optional.ofNullable(source.getAccountFrozenAmount()).orElse(BigDecimal.ZERO);
        BigDecimal withdrawalAmount = Optional.ofNullable(source.getAccountWithdrawalAmount()).orElse(BigDecimal.ZERO);
        // 计算总余额
        BigDecimal accountBalance = frozenAmount.add(withdrawalAmount);
        target.setAccountBalance(accountBalance);
    }


    AngelCashOutVo cmd2AngelCashOutVo(AngelCashOutCmd cashOutCmd);

    @Named("packAngelSettlement")
    default AngelSettlement packAngelSettlement(AngelCashOutCmd cashOutCmd) {
        AngelSettlement angelSettlement = new AngelSettlement();
        angelSettlement.setAngelId(cashOutCmd.getAngelId());
        angelSettlement.setSettlementType(SettleTypeEnum.WITHDRAW.getType());
        angelSettlement.setSettleAmount(cashOutCmd.getSettleAmount());
        angelSettlement.setCashStatus(CashStatusEnum.NO_CASH.getType());
        angelSettlement.setCreateUser(cashOutCmd.getUserPin());
        angelSettlement.setUpdateUser(cashOutCmd.getUserPin());
        return angelSettlement;
    }

    @Named("getTotYearAngelSettlementQueryContext")
    default AngelSettlementQueryContext getTotAngelSettlementQueryContext(AngelSettleQuery query) {
        AngelSettlementQueryContext queryContext = new AngelSettlementQueryContext();
        queryContext.setAngelId(query.getAngelId());
        queryContext.setSettlementType(SettleTypeEnum.INCOME.getType());
        queryContext.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
        queryContext.setSettleTimeStart(Date.from(LocalDateTime.now().minusYears(1).toInstant(ZoneOffset.UTC)));
        queryContext.setSettleTimeEnd(new Date());
        return queryContext;
    }

    @Named("packWithdrawDetailVo")
    default void packWithdrawDetailVo(AngelSettlementDto settlementDto, WithdrawDetailVo detailVo) {
        if (Objects.nonNull(detailVo)) {
            if (Objects.nonNull(CashStatusEnum.getCashStatusEnumByHyType(detailVo.getWithdrawStatus()))) {
                settlementDto.setCashStatus(CashStatusEnum.getCashStatusEnumByHyType(detailVo.getWithdrawStatus()).getType());
            }
            settlementDto.setFailReason(detailVo.getWithdrawFailReason());
            settlementDto.setCashFailButton(detailVo.getWithdrawFailBut());
            settlementDto.setFailSkipUrl(detailVo.getWithdrawFailUrl());
            settlementDto.setReceivedTime(detailVo.getPayTime());

            //明细字段
            AngelSettlementDetailDto detailDto = new AngelSettlementDetailDto();
            detailDto.setSettleId(settlementDto.getSettleId());
            detailDto.setSettleAmount(detailVo.getAmount());
            detailDto.setBankName(detailVo.getBankOfDeposit());
            detailDto.setBankNo(detailVo.getBankCard());
            detailDto.setBankLogo(detailVo.getBankLogo());
            detailDto.setBankLogoBackground(detailVo.getBankLogoBackground());
            detailDto.setReceivedAmount(detailVo.getPayActualFee());
            detailDto.setTax(detailVo.getTaxFee());
            settlementDto.setDetailList(Arrays.asList(detailDto));
        }
    }

    @Mapping(target = "failReasonCode", source = "failSkipUrl")
    AngelSettlement dto2AngelSettlement(AngelSettlementDto settlementDto);

    List<AngelSettlementDetail> dto2AngelSettlementDetails(List<AngelSettlementDetailDto> detailDtos);

    @Mapping(target = "angelType", source = "settlementSubjectSubType")
    @Mapping(target = "angelTypeDesc", source = "settlementSubjectSubType", qualifiedByName = "dealJobNature")
    @Mapping(target = "operator", source = "updateUser")
    AngelSettlementConfigDto convert2AngelSettlementConfigDto(JdhSettlementAreaFeeConfig jdhSettlementAreaFeeConfig);

    JdhAngelSettleAreaFeeQuery convert2JdhAngelSettleAreaFeeQuery(AngelSettlementConfigCmd angelSettlementConfigCmd);

    JdhSettlementAreaFeeConfig convert2JdhSettlementAreaFeeConfig(AngelSettlementConfigCmd angelSettlementConfigCmd);

    @Named("dealJobNature")
    default String dealJobNature(String settlementSubjectSubType) {
        return AngelJobNatureEnum.getLabelByValue(settlementSubjectSubType);
    }

    @Named("packDetailMap")
    default void packDetailMap(AngelSettlementDto settlementDto) {
        if (Objects.isNull(settlementDto) || CollectionUtils.isEmpty(settlementDto.getDetailList())) {
            return;
        }
        List<AngelSettleDetailCommonDto> commonDtos = new ArrayList<>();
        if (Objects.equals(settlementDto.getSettlementType(), SettleTypeEnum.WITHDRAW.getType())) {
            commonDtos.add(AngelSettleDetailCommonDto.builder().key("提现金额").value(Objects.nonNull(settlementDto.getSettleAmount()) ? settlementDto.getSettleAmount().setScale(2, RoundingMode.HALF_UP).toPlainString() : null).build());
            if (CollectionUtils.isNotEmpty(settlementDto.getDetailList())) {
                AngelSettlementDetailDto angelSettlementDetailDto = settlementDto.getDetailList().get(0);
                /*
                 * 20250915 添加“新增合规优化点”
                 * 个税代缴 护士端文案变更：
                 * 到账时间20250727（含）之前提现：服务费
                 * 到账时间20250728（含）之后提现：税金
                 */
                Date date = TimeUtils.timeStrToDate("2025-07-28 00:00:00",TimeFormat.LONG_PATTERN_LINE);
                String desc = "服务费";
                if(settlementDto.getReceivedTime().getTime() >= date.getTime()){
                    desc = "税金";
                }
                commonDtos.add(AngelSettleDetailCommonDto.builder().key(desc).value(Objects.nonNull(angelSettlementDetailDto.getTax()) ? angelSettlementDetailDto.getTax().setScale(2, RoundingMode.HALF_UP).toPlainString() : null).build());
                commonDtos.add(AngelSettleDetailCommonDto.builder().key("到账金额").value(Objects.nonNull(angelSettlementDetailDto.getReceivedAmount()) ? angelSettlementDetailDto.getReceivedAmount().setScale(2, RoundingMode.HALF_UP).toPlainString() : null).build());
            }
            commonDtos.add(AngelSettleDetailCommonDto.builder().key("申请时间").value(DateUtil.formatDate(settlementDto.getCashTime(), CommonConstant.YMDHMS)).build());
            commonDtos.add(AngelSettleDetailCommonDto.builder().key("到账时间").value(DateUtil.formatDate(settlementDto.getReceivedTime(), CommonConstant.YMDHMS)).build());
            commonDtos.add(AngelSettleDetailCommonDto.builder().key("提现银行").value(CollectionUtils.isEmpty(settlementDto.getDetailList()) ? ""
                    : settlementDto.getDetailList().get(0).getBankName()).build());
            commonDtos.add(AngelSettleDetailCommonDto.builder().key("提现单号").value(settlementDto.getSettlementNo()).build());

        } else {
            commonDtos.add(AngelSettleDetailCommonDto.builder().key("状态").value(Objects.isNull(SettleStatusEnum.getEnumByType(settlementDto.getSettleStatus())) ? ""
                    : SettleStatusEnum.getEnumByType(settlementDto.getSettleStatus()).getDesc()).build());
            commonDtos.add(AngelSettleDetailCommonDto.builder().key("入账时间").value(DateUtil.formatDate(settlementDto.getCreateTime(), CommonConstant.YMDHMS)).build());
            if (SettleStatusEnum.SETTLED.getType().equals(settlementDto.getSettleStatus())) {
                commonDtos.add(AngelSettleDetailCommonDto.builder().key("结算时间").value(DateUtil.formatDate(settlementDto.getSettleTime(), CommonConstant.YMDHMS)).build());
            } else {
                commonDtos.add(AngelSettleDetailCommonDto.builder().key("预计结算时间").value(DateUtil.formatDate(settlementDto.getSettleTime(), CommonConstant.YMDHMS)).build());
            }
            /*if (CollectionUtils.isNotEmpty(settlementDto.getDetailList())) {
                settlementDto.getDetailList().forEach(e -> {
                    commonDtos.add(AngelSettleDetailCommonDto.builder().key(e.getFeeName()).value(e.getSettleAmount()).build());
                });
            }*/
            commonDtos.add(AngelSettleDetailCommonDto.builder().key("订单号").value(settlementDto.getOrderId()).build());
            commonDtos.add(AngelSettleDetailCommonDto.builder().key("履约单号").value(settlementDto.getPromiseId()).build());
            SettleItemTypeEnum settleItemTypeEnum = SettleItemTypeEnum.getSettleTypeEnumByType(settlementDto.getItemType());
            commonDtos.add(AngelSettleDetailCommonDto.builder().key(Objects.nonNull(settleItemTypeEnum) ? settleItemTypeEnum.getUpgradeDesc() : "收入").value(settlementDto.getSettleAmount().setScale(2, RoundingMode.HALF_UP).toPlainString()).isExpand(CollectionUtils.isNotEmpty(settlementDto.getDetailList())).build());

            // 护士邀请活动佣金
            if (AngelSettleItemTypeEnum.ACTIVITY.getType().equals(settlementDto.getItemType()) && settlementDto.getActivityConfigRule() != null){
                ActivityConfigAngelRecruitmentRuleDto activityConfigRule = settlementDto.getActivityConfigRule();
                String acceptAngelTwoDepartmentName = StringUtils.isBlank(activityConfigRule.getAcceptAngelTwoDepartmentName()) ? "" : "-" + activityConfigRule.getAcceptAngelTwoDepartmentName();
                commonDtos.add(AngelSettleDetailCommonDto.builder().key("受邀护士").value("*"+activityConfigRule.getAcceptAngelName().substring(1) + acceptAngelTwoDepartmentName).build());
                commonDtos.removeIf(angelSettleDetailCommonDto -> "订单号".equals(angelSettleDetailCommonDto.getKey()));
            }
        }
        settlementDto.setDetailCommonDtoList(commonDtos);
    }

    List<AngelSettlementDetailDto> entityAngelSettlementDtos(List<AngelSettlement> detailList);

    AngelSettleAdjustQueryContext vo2AngelSettleAdjustQueryContext(AngelSettleAdjustQuery angelSettleAdjustQuery);


    public static void main(String[] args) {
        Date from = Date.from(LocalDateTime.now().minusYears(1).toInstant(ZoneOffset.UTC));
        System.out.println(from);

    }
}
