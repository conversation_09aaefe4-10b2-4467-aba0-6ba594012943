package com.jdh.o2oservice.application.promise.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.common.web.LoginContext;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.PromiseExtApplication;
import com.jdh.o2oservice.application.promise.convert.PromiseApplicationConverter;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.support.service.DictApplication;
import com.jdh.o2oservice.application.support.util.BusinessModeUtil;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.application.via.common.ViaComponentDomainService;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderTypeEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.model.Birthday;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelModifyDateRuleDuccBo;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelWorkExtVo;
import com.jdh.o2oservice.core.domain.medpromise.context.DelMedicalPromiseContext;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.service.MedicalPromiseDomainService;
import com.jdh.o2oservice.core.domain.product.enums.ItemDangerLevelEnum;
import com.jdh.o2oservice.core.domain.promise.bo.AngelWorkBo;
import com.jdh.o2oservice.core.domain.promise.bo.AngelWorkHistoryBo;
import com.jdh.o2oservice.core.domain.promise.bo.MedPromiseBo;
import com.jdh.o2oservice.core.domain.promise.bo.query.ListServiceChildrenParam;
import com.jdh.o2oservice.core.domain.promise.context.*;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.event.PromiseCallbackEventBody;
import com.jdh.o2oservice.core.domain.promise.event.PromiseEventBaseBody;
import com.jdh.o2oservice.core.domain.promise.event.PromiseModifyEventBody;
import com.jdh.o2oservice.core.domain.promise.event.PromiseSubmitEventBody;
import com.jdh.o2oservice.core.domain.promise.ext.PromiseAutoSubmitExt;
import com.jdh.o2oservice.core.domain.promise.ext.dto.ExtAutoSubmitDraft;
import com.jdh.o2oservice.core.domain.promise.ext.dto.ExtPromiseStation;
import com.jdh.o2oservice.core.domain.promise.ext.dto.ExtPromiseTime;
import com.jdh.o2oservice.core.domain.promise.ext.dto.ExtPromiseUser;
import com.jdh.o2oservice.core.domain.promise.ext.param.PromiseAutoSubmitExtParam;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseAppointmentDraftRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseExtQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseHistoryRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.rpc.MedicalPromiseRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.MedicalPromiseBO;
import com.jdh.o2oservice.core.domain.promise.service.JdhPromiseDomainService;
import com.jdh.o2oservice.core.domain.promise.service.JdhPromiseQueryService;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhProcessDataTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import com.jdh.o2oservice.core.domain.support.businessMode.ServiceHomeTypeDomainService;
import com.jdh.o2oservice.core.domain.support.riskintercept.cqe.RiskInterceptQuery;
import com.jdh.o2oservice.core.domain.support.riskintercept.enums.RiskInterceptCodeEnum;
import com.jdh.o2oservice.core.domain.support.riskintercept.enums.RiskPassStatusEnum;
import com.jdh.o2oservice.core.domain.support.riskintercept.enums.RiskWorkTagEnum;
import com.jdh.o2oservice.core.domain.support.riskintercept.model.JdhPromiseRiskInterceptRecord;
import com.jdh.o2oservice.core.domain.support.riskintercept.repository.JdhPromiseRiskInterceptRecordRepository;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.enums.ModifyAppointmentTimeSceneEnum;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.PromiseServiceStartTimeModuleTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angelpromise.cmd.AngelWorkCancelShipCmd;
import com.jdh.o2oservice.export.angelpromise.cmd.ResetPromiseCmd;
import com.jdh.o2oservice.export.angelpromise.cmd.UpdatePromiseStatusCmd;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkDetailQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.angelpromise.query.GetDetailByPromiseIdQuery;
import com.jdh.o2oservice.export.dispatch.cmd.SubmitDispatchCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseBindSpecimenCodeCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseCreateCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.AgencyAppointDateDto;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.PromiseServiceStartTimeDescDTO;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.*;
import com.jdh.o2oservice.export.promise.cmd.*;
import com.jdh.o2oservice.export.promise.dto.*;
import com.jdh.o2oservice.export.promise.query.CompletePromiseRequest;
import com.jdh.o2oservice.export.promise.query.ListServiceChildrenRequest;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.PromiseListRequest;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.StoreInfoRequest;
import com.jdh.o2oservice.export.support.dto.DictInfoDto;
import com.jdh.o2oservice.export.support.query.DictRequest;
import com.jdh.o2oservice.export.trade.dto.AvaiableAppointmentTimeDTO;
import com.jdh.o2oservice.export.trade.dto.XfylAppointDateDTO;
import com.jdh.o2oservice.export.trade.dto.XfylAppointDateTimeGroupDTO;
import com.jdh.o2oservice.export.trade.query.AvaiableAppointmentTimeParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 履约单 应用服务
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Service
@Slf4j
public class PromiseApplicationImpl implements PromiseApplication, PromiseExtApplication {

    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository jdhPromiseRepository;
    @Resource
    private JdOrderRepository jdOrderRepository;
    /**
     * jdhPromiseRepository
     */
    @Resource
    private VoucherRepository voucherRepository;

    /**
     * 草稿仓储
     */
    @Resource
    private PromiseAppointmentDraftRepository promiseAppointmentDraftRepository;

    /**
     * jdhPromiseDomainService
     */
    @Resource
    private JdhPromiseDomainService jdhPromiseDomainService;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * 履约单历史记录仓储
     */
    @Resource
    private PromiseHistoryRepository promiseHistoryRepository;

    /** 自动预约 */
    @Autowired
    private List<PromiseAutoSubmitExt> autoSubmitExts;

    /**
     * dispatchApplication
     */
    @Autowired
    private DispatchApplication dispatchApplication;

    /**
     * medicalPromiseApplication
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;
    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;
    /**
     *
     */
    @Resource
    private MedicalPromiseRpc medicalPromiseRpc;
    /** */
    @Resource
    private JdhPromiseQueryService jdhPromiseQueryService;

    @Resource
    private AngelPromiseApplication angelPromiseApplication;

    @Resource
    private AngelApplication angelApplication;

    /**
     * promiseApplication
     */
    @Resource
    private PromiseApplication promiseApplication ;

    /**
     * ducc
     */
    @Resource
    private DuccConfig duccConfig;
    @Resource
    private ViaComponentDomainService viaComponentDomainService;


    /**
     * 护士工单
     */
    @Resource
    AngelWorkApplication angelWorkApplication;

    /**
     * 交易
     */
    @Resource
    private TradeApplication tradeApplication;

    /**
     * 健康地址rpc
     */
    @Resource
    private JdhAddressRpc jdhAddressRpc;

    @Resource
    private RedisLockUtil redisLockUtil;

    @Autowired
    private ProviderStoreApplication providerStoreApplication;



    /**
     * angelWorkRepository
     */
    @Resource
    AngelWorkRepository angelWorkRepository;

    /**
     * promiseRiskInterceptRecordRepository
     */
    @Resource
    private JdhPromiseRiskInterceptRecordRepository promiseRiskInterceptRecordRepository;

    /**
     * 缓存
     */
    @Resource
    RedisUtil redisUtil;

    /**
     * jimRedisService
     */
    @Resource
    private Cluster jimClient;

    /**
     * dictApplication
     */
    @Resource
    DictApplication dictApplication;

    @Autowired
    private JdOrderApplication jdOrderApplication;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private ProductApplication productApplication;

    @Resource
    private ServiceHomeTypeDomainService serviceHomeTypeDomainService;

    @Resource
    private VerticalBusinessRepository businessRepository;

    @Resource
    private MedicalPromiseDomainService medicalPromiseDomainService;

    /**
     * 基于草稿数据的自动预约
     * （1）获取实体，封装context
     * （2）调用领域服务，执行业务逻辑处理（状态机）
     * （3）数据存储，状态为预约中
     * （4）发送提交成功事件
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.autoSubmit")
    public Boolean autoSubmit(AutoSubmitCmd cmd) {
        JdhPromise snapshot = jdhPromiseRepository.find(new JdhPromiseIdentifier(cmd.getPromiseId()));
        log.info("PromiseApplicationImpl autoSubmit snapshot:{}",JSON.toJSONString(snapshot));
        ExtAutoSubmitDraft dto = null;
        PromiseSubmitAbilityContext context = null;

        if (StringUtil.equals("openTest",snapshot.getVerticalCode())){

            dto = new ExtAutoSubmitDraft();

            //预约人
            List<ExtPromiseUser> users = Lists.newArrayList(JsonUtil.parseObject(duccConfig.getOpenTestUser(), ExtPromiseUser.class));


            List<JdhPromiseExtend> promiseExtends = snapshot.getPromiseExtends();
            JdhPromiseExtend jdhPromiseExtend = promiseExtends.stream().filter(p -> StringUtil.equals(PromiseExtendKeyEnum.OPEN_TEST_INFO.getFiledKey(), p.getAttribute())).findFirst().orElse(null);
            JSONObject jsonObject = JSON.parseObject(jdhPromiseExtend.getValue());
            Date appointmentStartTime = jsonObject.getDate("appointmentStartTime");

            //预约时间
            ExtPromiseTime appointmentTime = new ExtPromiseTime();
            appointmentTime.setAppointmentEndTime(DateUtil.format(DateUtil.offsetHour(appointmentStartTime,CommonConstant.ONE),CommonConstant.YMDHM));
            appointmentTime.setDateType(CommonConstant.TWO);
            appointmentTime.setIsImmediately(Boolean.TRUE);
            appointmentTime.setAppointmentStartTime(DateUtil.format(appointmentStartTime,CommonConstant.YMDHM));


            //履约地址信息
            ExtPromiseStation station = new ExtPromiseStation();
            String stationId = jsonObject.getString("stationId");
            //查询实验室信息
            StoreInfoRequest storeInfoRequest = new StoreInfoRequest();
            storeInfoRequest.setStationId(stationId);
            StoreInfoDto storeInfoDto = providerStoreApplication.queryStationInfo(storeInfoRequest);
            station.setAddressDetail(storeInfoDto.getStationAddress());
            station.setAddressId(snapshot.getPromiseId());

            //预约人手机号
            String appointmentPhone = "18614271808";

            dto.setUsers(users);
            dto.setStation(station);
            dto.setAppointmentTime(appointmentTime);
            dto.setAppointmentPhone(appointmentPhone);

            context = PromiseApplicationConverter.INS.extDto2SubmitContext(dto, snapshot);


        }else {
            boolean hit = Boolean.FALSE;
            // 根据扩展点返回提交预约的数据 todo 逻辑调整到领域服务
            if (CollectionUtils.isNotEmpty(autoSubmitExts)){
                PromiseAutoSubmitExtParam param = new PromiseAutoSubmitExtParam();
                param.setServiceType(snapshot.getServiceType());
                param.setSourceVoucherId(snapshot.getSourceVoucherId());
                param.setVerticalCode(snapshot.getVerticalCode());
                for (PromiseAutoSubmitExt ext : autoSubmitExts) {
                    try {
                        if (ext.filter(param)){
                            dto = ext.findDraft(param);
                            log.info("PromiseApplicationImpl 扩展点返回草稿数据 dto:{}",JSON.toJSONString(dto));
                            hit = Boolean.TRUE;
                            break;
                        }
                    }catch (Exception e){
                        log.error("PromiseApplicationImpl autoSubmit PromiseSubmitExt execute error ", e);
                        throw new SystemException(SystemErrorCode.SYSTEM_EXT_EXECUTE_ERROR);
                    }
                }
            }
            // 命中扩展点
            if (hit){
                log.info("PromiseApplicationImpl->autoSubmit extAutoSubmitDraft={}", JSON.toJSONString(dto));
                context = PromiseApplicationConverter.INS.extDto2SubmitContext(dto, snapshot);
                // 未命中扩展点
            }else{
                PromiseAppointmentDraft draft = promiseAppointmentDraftRepository.find(new DraftIdentifier(cmd.getMedicalLocId()));
                log.info("PromiseApplicationImpl autoSubmit draft:{}",JSON.toJSONString(draft));
                context = PromiseApplicationConverter.INS.draft2SubmitContext(draft);
            }
        }



        // 初始化context
        context.setVerticalCode(snapshot.getVerticalCode());
        context.setServiceType(snapshot.getServiceType());
        log.info("PromiseApplicationImpl autoSubmit context:{}",JSON.toJSONString(context));
        context.init(snapshot, PromiseEventTypeEnum.PROMISE_AUTO_SUBMIT);
        log.info("PromiseApplicationImpl -> autoSubmit context:{}",JSON.toJSONString(context));

        // 执行业务逻辑
        jdhPromiseDomainService.submitPromise(context);
        // 存储
        jdhPromiseRepository.save(context.getPromise());
        // 发布事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(context.getPromise(), PromiseEventTypeEnum.INTERCEPT_PROMISE_AUTO_SUBMIT,
                new PromiseSubmitEventBody(snapshot, context.getPromise())));
        return Boolean.TRUE;
    }

    /**
     * 提交预约
     * （1）获取实体，封装context
     * （2）调用领域服务，执行业务逻辑处理（状态机）
     * （3）数据存储，状态为预约中
     * （4）发送提交成功事件
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.submit")
    public Boolean submit(SubmitPromiseCmd cmd) {
        AssertUtils.nonNull(cmd, "提交预约参数异常");
        AssertUtils.hasText(cmd.getPromiseId(), "提交预约预约单号不能为空");

        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.SUBMIT_PROMISE_LOCK_PREFIX, cmd.getPromiseId());
        boolean lock = redisLockUtil.tryLock(redisKey, "1",
                RedisKeyEnum.SUBMIT_PROMISE_LOCK_PREFIX.getExpireTime(),
                RedisKeyEnum.SUBMIT_PROMISE_LOCK_PREFIX.getExpireTimeUnit());
        if(!lock) {
            log.error("获取分布式锁失败!cmd={}",JSON.toJSONString(cmd));
            return true;
        }

        try{
            JdhPromise snapshot = jdhPromiseRepository.find(new JdhPromiseIdentifier(Long.parseLong(cmd.getPromiseId())));
            //存放扩展信息
            if (CollectionUtils.isNotEmpty(cmd.getMedicalCertificateFileIds())){
                List<JdhPromiseExtend> promiseExtends = null;
                if (CollectionUtils.isEmpty(snapshot.getPromiseExtends())){
                    promiseExtends = Lists.newArrayList();
                    JdhPromiseExtend jdhPromiseExtend = new JdhPromiseExtend();
                    jdhPromiseExtend.setPromiseId(Long.valueOf(cmd.getPromiseId()));
                    jdhPromiseExtend.setMedicalCertificateFileIds(JsonUtil.toJSONString(cmd.getMedicalCertificateFileIds()));
                    promiseExtends.add(jdhPromiseExtend);
                }else {
                    promiseExtends = snapshot.getPromiseExtends();
                    JdhPromiseExtend jdhPromiseExtend = promiseExtends.stream().filter(p -> StringUtils.equals(PromiseExtendKeyEnum.MEDICAL_CERTIFICATE_FILE_IDS.getFiledKey(), p.getAttribute())).findFirst().orElse(null);
                    if (jdhPromiseExtend == null){
                        JdhPromiseExtend jdhPromiseExtendFile = new JdhPromiseExtend();
                        jdhPromiseExtendFile.setPromiseId(Long.valueOf(cmd.getPromiseId()));
                        jdhPromiseExtendFile.setMedicalCertificateFileIds(JsonUtil.toJSONString(cmd.getMedicalCertificateFileIds()));
                        promiseExtends.add(jdhPromiseExtendFile);
                    }else {
                        jdhPromiseExtend.setValue(JsonUtil.toJSONString(cmd.getMedicalCertificateFileIds()));
                    }

                }

                snapshot.setPromiseExtends(promiseExtends);
            }

            log.info("PromiseApplicationImpl -> submit snapshot:{}",JSON.toJSONString(snapshot));
            JdhVoucher jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(snapshot.getVoucherId()).build());
            log.info("PromiseApplicationImpl -> submit jdhVoucher:{}",JSON.toJSONString(jdhVoucher));
            if (Objects.isNull(cmd.getIntendedNurse())){
                cmd.setIntendedNurse(new IntendedNurse());
            }
            PromiseSubmitAbilityContext context = PromiseApplicationConverter.INS.cmd2SubmitContext(cmd);
            context.setJdhVoucher(jdhVoucher);
            context.init(snapshot, PromiseEventTypeEnum.PROMISE_SUBMIT);
            fillGiftMainSku(jdhVoucher, context);
            log.info("PromiseApplicationImpl -> submit context:{}",JSON.toJSONString(context));

            jdhPromiseDomainService.submitPromise(context);
            log.info("PromiseApplicationImpl -> submit promise context:{}",JSON.toJSONString(context));
            jdhPromiseRepository.save(context.getPromise());

            eventCoordinator.publish(EventFactory.newDefaultEvent(context.getPromise(), PromiseEventTypeEnum.INTERCEPT_PROMISE_SUBMIT,
                    new PromiseSubmitEventBody(snapshot, context.getPromise())));
            return Boolean.TRUE;
        }finally {
            redisLockUtil.unLock(redisKey);
        }
    }

    /**
     * 修改预约
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.modify")
    public Boolean modify(ModifyAppointCmd cmd) {
        String lockKey = "";
        try{
            lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.MODIFY_PROMISE, cmd.getPromiseId());
            if (!redisLockUtil.tryLock(lockKey, "1",RedisKeyEnum.MODIFY_PROMISE.getExpireTime(),RedisKeyEnum.MODIFY_PROMISE.getExpireTimeUnit())) {
                throw new BusinessException(AngelPromiseBizErrorCode.MODIFY_PROMISE_ERROR);
            }
            if (cmd.getReasonType() != null) {
                cmd.setReasonContent(getReasonTypeDesc(cmd.getReasonType()) + (StringUtils.isNotBlank(cmd.getReasonContent()) ? "," + cmd.getReasonContent() : ""));
            }
            buildModifyParamFormScene(cmd);
            JdhPromise snapshot = jdhPromiseRepository.find(new JdhPromiseIdentifier(Long.parseLong(cmd.getPromiseId())));
            checkIsImmediately(cmd, snapshot);
            log.info("PromiseApplicationImpl -> modify snapshot:{}",JSON.toJSONString(snapshot));
            PromiseModifySubmitAbilityContext ctx = PromiseApplicationConverter.INS.cmd2ModifyContext(cmd);
            if (ctx.getVerticalCode() == null) {
                ctx.setVerticalCode(snapshot.getVerticalCode());
                ctx.setServiceType(snapshot.getServiceType());
            }
            ctx.init(snapshot, PromiseEventTypeEnum.PROMISE_USER_SUBMIT_MODIFY);
            log.info("PromiseApplicationImpl -> modify ctx:{}",JSON.toJSONString(ctx));
            snapshot.modify(ctx);

            log.info("PromiseApplicationImpl -> modify 状态流转后 ctx:{}",JSON.toJSONString(ctx));
            jdhPromiseRepository.save(ctx.getPromise());
            afterLogicFormScene(cmd, ctx);
            eventCoordinator.publish(EventFactory.newDefaultEvent(ctx.getPromise(), PromiseEventTypeEnum.PROMISE_USER_SUBMIT_MODIFY,
                    new PromiseModifyEventBody(snapshot, ctx.getPromise())));
        }finally {
            redisLockUtil.unLock(lockKey);
        }
        return Boolean.TRUE;
    }

    /**
     * 取消预约
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.cancel")
    public Boolean cancel(CancelAppointCmd cmd) {
        JdhPromise snapshot = jdhPromiseRepository.find(new JdhPromiseIdentifier(Long.parseLong(cmd.getPromiseId())));
        PromiseCancelSubmitAbilityContext ctx = PromiseApplicationConverter.INS.cmd2CancelContext(cmd);
        ctx.init(snapshot, PromiseEventTypeEnum.PROMISE_USER_SUBMIT_CANCEL);
        snapshot.cancel(ctx);
        jdhPromiseRepository.save(ctx.getPromise());
        eventCoordinator.publish(EventFactory.newDefaultEvent(ctx.getPromise(), PromiseEventTypeEnum.PROMISE_USER_SUBMIT_CANCEL,new PromiseEventBaseBody(snapshot, ctx.getPromise())));
        return Boolean.TRUE;
    }

    /**
     * 回调处理
     * @param cmd
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.callBack")
    public Boolean callBack(PromiseCallbackCmd cmd) {
        PromiseCallbackAbilityContext context = PromiseApplicationConverter.INS.cmd2CallbackCtx(cmd);
        log.info("PromiseApplicationImpl -> callBack context:{}",JSON.toJSONString(context));
        //1、查promise
        JdhPromise snapshot = jdhPromiseRepository.findPromise(PromiseRepQuery.builder().appointmentId(cmd.getAppointmentId()).promiseId(cmd.getPromiseId()).build());
        log.info("PromiseApplicationImpl -> callBack snapshot:{}",JSON.toJSONString(snapshot));

        //2、查history
        JdhPromiseHistory history = promiseHistoryRepository.findLastEvent(snapshot.getPromiseId(), snapshot.getPromiseStatus());
        log.info("PromiseApplicationImpl -> callBack history:{}",JSON.toJSONString(history));

        //3、初始化上下文
        PromiseEventTypeEnum triggerCmd = parseCallType(AppointCallBackEnum.convert(cmd.getCallbackCode()));
        context.init(snapshot,triggerCmd,history,null);

        //4、填充其他信息
        fillOtherInfo(context);
        log.info("PromiseApplicationImpl -> callBack after context:{}",JSON.toJSONString(context));

        jdhPromiseDomainService.callback(context);

        jdhPromiseRepository.save(context.getPromise());

        PromiseEventTypeEnum event = parsePublishEvent(context.getPromise());
        eventCoordinator.publish(EventFactory.newDefaultEvent(context.getPromise(), event, new PromiseCallbackEventBody(context)));
        return Boolean.TRUE;
    }

    /**
     * 补充其他单据信息
     *
     * @param context 上下文
     */
    private void fillOtherInfo(PromiseCallbackAbilityContext context){
        JdhPromise snapshot = context.getSnapshot();
        //到家业务处理
        if(ServiceTypeEnum.TEST.getServiceType().equals(snapshot.getServiceType())
                || ServiceTypeEnum.CARE.getServiceType().equals(snapshot.getServiceType())){
            try {
                //检测单集合
                List<MedicalPromiseDTO> medicalPromiseList = medicalPromiseApplication.queryMedicalPromiseList(MedicalPromiseListRequest.builder().promiseId(snapshot.getPromiseId()).build());
                log.info("PromiseApplicationImpl -> callBack medicalPromiseList:{}",JSON.toJSONString(medicalPromiseList));
                List<MedPromiseBo> medPromiseBos = medicalPromiseList.stream().map(ele -> MedPromiseBo.builder()
                                .medicalPromiseId(ele.getMedicalPromiseId())
                                .specimenCode(ele.getSpecimenCode())
                                .status(ele.getStatus())
                                .freeze(ele.getFreeze()).build())
                        .collect(Collectors.toList());

                context.setMedPromiseBoList(medPromiseBos);
            } catch (Exception e) {
                log.info("PromiseApplicationImpl -> fillOtherInfo queryMedicalPromiseList error",e);
            }

            try {
                //voucher
                JdhVoucher jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(snapshot.getVoucherId()).build());
                context.setJdhVoucher(jdhVoucher);
            } catch (Exception e) {
                log.info("PromiseApplicationImpl -> fillOtherInfo queryVoucher error",e);
            }

            try {
                //work
                AngelWorkDto angelWorkDto = angelWorkApplication.getAngelWorkByPromiseId(snapshot.getPromiseId());
                AngelWorkBo angelWorkBo = AngelWorkBo.builder()
                        .workId(angelWorkDto.getWorkId())
                        .workStatus(angelWorkDto.getStatus())
                        .build();
                List<AngelWorkHistoryDto> workHistoryList = angelWorkDto.getWorkHistoryList();
                if(CollUtil.isNotEmpty(workHistoryList)){
                    List<AngelWorkHistoryBo> workHistoryBoList = new ArrayList<>();
                    workHistoryList.forEach(ele->{
                        AngelWorkHistoryBo historyBo = AngelWorkHistoryBo.builder()
                                .workId(angelWorkBo.getWorkId())
                                .beforeStatus(ele.getBeforeStatus())
                                .afterStatus(ele.getAfterStatus())
                                .createTime(ele.getCreateTime())
                                .operateTime(ele.getOperateTime())
                                .build();
                        workHistoryBoList.add(historyBo);
                    });
                    angelWorkBo.setWorkHistories(workHistoryBoList);
                }
                context.setAngelWorkBo(angelWorkBo);
            } catch (Exception e) {
                log.info("PromiseApplicationImpl -> fillOtherInfo getAngelWorkByPromiseId error",e);
            }
        }
    }

    /**
     * 核销
     * @param cmd
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogAndAlarm
    public Boolean writeOff(PromiseWriteOffCmd cmd) {
        PromiseWriteOffContext context = new PromiseWriteOffContext();
        context.setCode(cmd.getCode());
        context.setCodePwd(cmd.getCodePwd());

        JdhPromise snapshot = jdhPromiseRepository.findAppointment(cmd.getAppointmentId());
        context.init(snapshot, PromiseEventTypeEnum.PROMISE_CALLBACK_NOTICE_WRITE_OFF);
        jdhPromiseDomainService.writeOff(context);

        jdhPromiseRepository.save(context.getPromise());
        eventCoordinator.publish(EventFactory.newDefaultEvent(context.getPromise(), PromiseEventTypeEnum.PROMISE_WRITE_OFF, new PromiseEventBaseBody(snapshot, context.getPromise())));
        return context.getConsumeRes();
    }

    /**
     * 用户核销，用户在页面操作的核销
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.userWriteOff")
    public Boolean userWriteOff(PromiseUserWriteOffCmd cmd) {
        PromiseWriteOffContext context = PromiseApplicationConverter.INS.cmd2WriteOffCtx(cmd);
        JdhPromise snapshot = jdhPromiseRepository.findAppointment(cmd.getUserPin(), cmd.getWriteOffPwd());
        AssertUtils.nonNull(snapshot, PromiseErrorCode.PROMISE_WRITE_OFF_CODE_ERROR);
        AssertUtils.nonEqualsObject(snapshot.getVerticalCode(),cmd.getVerticalCode(), PromiseErrorCode.PROMISE_WRITE_OFF_SKU_ERROR);
        AssertUtils.isEqualsObject(snapshot.getPromiseStatus(), JdhPromiseStatusEnum.COMPLETE.getStatus() ,PromiseErrorCode.PROMISE_WRITE_OFF_REPEAT_ERROR);

        context.setStoreId(cmd.getStoreId());
        context.setCode(snapshot.getCode());
        context.setCodePwd(snapshot.getCodePwd());
        context.init(snapshot, PromiseEventTypeEnum.PROMISE_WRITE_OFF);
        jdhPromiseDomainService.writeOff(context);

        jdhPromiseRepository.save(context.getPromise());
        eventCoordinator.publish(EventFactory.newDefaultEvent(context.getPromise(), PromiseEventTypeEnum.PROMISE_WRITE_OFF, new PromiseEventBaseBody(snapshot, context.getPromise())));
        return context.getConsumeRes();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.writeOffSync")
    public Boolean writeOffSync(PromiseWriteOffSyncCmd cmd) {
        JdhPromise snapshot = jdhPromiseRepository.findByCodeId(cmd.getCodeId(), cmd.getVerticalCode());
        if (Objects.isNull(snapshot)){
            return Boolean.TRUE;
        }

        JdhPromise promise = snapshot.copyInstance();
        if(promise.writeOffSync()){
            jdhPromiseRepository.save(promise);
            eventCoordinator.publish(EventFactory.newDefaultEvent(promise, PromiseEventTypeEnum.PROMISE_WRITE_OFF, new PromiseEventBaseBody(snapshot, promise)));
        }
        return Boolean.TRUE;
    }

    /**
     * 作废
     * 作废单据当前应该处于冻结状态才允许作废
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.invalid")
    public void invalid(PromiseInvalidCmd cmd) {
        Long promiseId = Long.valueOf(cmd.getPromiseId());
        JdhPromise snapshot = jdhPromiseRepository.find(new JdhPromiseIdentifier(promiseId));
        if (Objects.isNull(snapshot)){
           throw new NullPointerException("promise is null");
        }
        PromiseInvalidContext context = PromiseApplicationConverter.INS.convert2InvalidContext(cmd, snapshot);
        context.init(snapshot);
        jdhPromiseDomainService.invalid(context);
        jdhPromiseRepository.save(context.getPromise());
        eventCoordinator.publishDelay(EventFactory.newDelayEvent(context.getPromise(), PromiseEventTypeEnum.PROMISE_INVALID, new PromiseEventBaseBody(snapshot, context.getPromise(), cmd.getReason()),10L));
    }

    /**
     * 冻结履约单，依赖于服务单冻结。
     * 场景：订单退款时发起冻结。
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.freeze")
    public FreezeStateDto freeze(PromiseFreezeCmd cmd) {
        Long promiseId = Long.valueOf(cmd.getPromiseId());
        JdhPromise snapshot = jdhPromiseRepository.find(new JdhPromiseIdentifier(promiseId));
        if (Objects.isNull(snapshot)){
            return null;
        }
        PromiseFreezeContext context = PromiseApplicationConverter.INS.convert2FreezeContext(cmd, snapshot);
        context = context.init(snapshot);
        jdhPromiseDomainService.freeze(context);

        jdhPromiseRepository.save(context.getPromise());

        eventCoordinator.publish(EventFactory.newDefaultEvent(context.getPromise(), PromiseEventTypeEnum.PROMISE_FREEZE, new PromiseEventBaseBody(context.getPromise(), context.getPromise(), cmd.getReason())));
        return PromiseApplicationConverter.INS.freezeBo2StatusDto(context.getStatusBO());
    }

    /**
     * 解冻履约单
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.unFreeze")
    public Boolean unFreeze(PromiseUnFreezeCmd cmd) {
        PromiseRepQuery query = new PromiseRepQuery();
        query.setVoucherIds(Lists.newArrayList(cmd.getVoucherId()));
        List<JdhPromise> snapshots = jdhPromiseRepository.findList(query);
        if (CollectionUtil.isEmpty(snapshots)){
            return Boolean.TRUE;
        }
        if (!Objects.equals(cmd.getUnFreezeType(), JdhProcessDataTypeEnum.PROCESS_PROMISE.getType())){
            throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
        }

        for (JdhPromise snapshot : snapshots) {
            JdhPromise promise = snapshot.copyInstance();
            promise.unFreeze();
            jdhPromiseRepository.save(promise);
            eventCoordinator.publish(EventFactory.newDefaultEvent(promise, PromiseEventTypeEnum.PROMISE_UN_FREEZE, new PromiseEventBaseBody(snapshot, promise, cmd.getReason())));
        }
        return Boolean.TRUE;
    }

    /**
     * 延期
     *
     * @param cmd CMD
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.delay")
    public Boolean delay(PromiseDelayCmd cmd) {
        PromiseRepQuery query = new PromiseRepQuery();
        JdhVoucher jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(Long.valueOf(cmd.getVoucherId())).build());
        query.setVoucherIds(Lists.newArrayList(Long.valueOf(cmd.getVoucherId())));
        List<JdhPromise> snapshots = jdhPromiseRepository.findList(query);
        if (CollectionUtil.isEmpty(snapshots)){
            return Boolean.TRUE;
        }

        for (JdhPromise snapshot : snapshots) {
            JdhPromise promise = snapshot.copyInstance();
            PromiseDelayContext delayContext = PromiseDelayContext.builder().jdhVoucher(jdhVoucher).build();
            Boolean delay = promise.delay(delayContext);
            if(delay){
                jdhPromiseRepository.save(promise);
                eventCoordinator.publish(EventFactory.newDefaultEvent(promise, PromiseEventTypeEnum.PROMISE_UN_FREEZE, new PromiseEventBaseBody(snapshot, promise, "")));

            }
        }
        return Boolean.TRUE;
    }

    /**
     * 派单
     *          // 派单是在变成预约中状态之后的一个动作，变成预约中这次到家是在自动预约后，这里标记原始事件的话，应该放PROMISE_AUTO_SUBMIT，
     *         // 但是，后面变成预约中还有可能是先买后约，那原始事件就是PROMISE_SUBMIT
     *         // 所以无论哪个上游事件触发的派单，这里都统一放 派单事件，来标记是派单动作。至于这个dispatch是由消费哪个事件触发的，是可以追溯的。
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.dispatch")
    public Boolean dispatch(PromiseDispatchCmd cmd) {
        log.info("PromiseApplicationImpl -> dispatch cmd:{}",JSON.toJSONString(cmd));
        JdhPromise jdhPromise = jdhPromiseRepository.find(JdhPromiseIdentifier.builder().promiseId(cmd.getPromiseId()).build());
        PromiseDispatchContext context =  new PromiseDispatchContext();
        context.setPromise(jdhPromise);
        context.setSourceEvent(PromiseEventTypeEnum.PROMISE_DISPATCH);
        context.init(jdhPromise,PromiseEventTypeEnum.PROMISE_DISPATCH);
        log.info("PromiseApplicationImpl -> dispatch context:{}",JSON.toJSONString(context));
        Boolean requiredDispatch = jdhPromiseDomainService.requiredDispatch(context);
        log.info("PromiseApplicationImpl -> dispatch requiredDispatch:{}",requiredDispatch);
        if(requiredDispatch){
            //先查询检测单过滤掉失效的预约人信息
            MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
            medicalPromiseListQuery.setInvalid(false);
            medicalPromiseListQuery.setFreezeQuery(false);
            medicalPromiseListQuery.setPromiseId(cmd.getPromiseId());
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
            if(CollectionUtils.isEmpty(medicalPromises)) {
                log.error("PromiseApplicationImpl -> dispatch, 没有需要提交预约的预约人信息");
                return Boolean.TRUE;
            }
            Set<Long> validPromisePatientSet = medicalPromises.stream().map(MedicalPromise::getPromisePatientId).collect(Collectors.toSet());

            // 调用派单域，发起派单逻辑
            SubmitDispatchCmd submitDispatchCmd = PromiseApplicationConverter.INS.promise2SubmitDispatchCmd(jdhPromise, validPromisePatientSet);
            // 意向护士派单
            this.buildIntendedNurse(jdhPromise, submitDispatchCmd);
            log.info("PromiseApplicationImpl -> dispatch submitDispatchCmd:{}",JSON.toJSONString(submitDispatchCmd));
            Boolean submitDispatchResult = dispatchApplication.submitDispatch(submitDispatchCmd);
            if(Boolean.FALSE.equals(submitDispatchResult)){
                throw new SystemException(PromiseErrorCode.PROMISE_SUBMIT_DISPATCH_ERROR);
            }
        }
        //这里不在外抛事件
        return Boolean.TRUE;
    }

    /**
     * 修改预约发起派单
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean modifyServiceDateDispatch(PromiseDispatchCmd cmd) {
        Long promiseId = cmd.getPromiseId();
        log.info("PromiseApplicationImpl -> modifyPromiseDispatch cmd:{}",JSON.toJSONString(cmd));
        JdhPromise jdhPromise = jdhPromiseRepository.find(JdhPromiseIdentifier.builder().promiseId(promiseId).build());
        PromiseDispatchContext context =  new PromiseDispatchContext();
        context.setPromise(jdhPromise);
        context.setSourceEvent(PromiseEventTypeEnum.PROMISE_MODIFY_DISPATCH);
        context.init(jdhPromise,PromiseEventTypeEnum.PROMISE_MODIFY_DISPATCH);
        log.info("PromiseApplicationImpl -> modifyPromiseDispatch context:{}",JSON.toJSONString(context));
        //判断是否是中高风险评估阶段，此阶段无派单任务和护士工单。如果为此阶段，则直接修改成功
        RiskInterceptQuery riskInterceptQuery = new RiskInterceptQuery();
        riskInterceptQuery.setRiskCode(RiskInterceptCodeEnum.RISK_HIGH_ITEM_INTERCEPT.getRiskCode());
        riskInterceptQuery.setPromiseId(promiseId);
        List<JdhPromiseRiskInterceptRecord> interceptRecordList = promiseRiskInterceptRecordRepository.queryRiskInterceptList(riskInterceptQuery);
        if(CollectionUtils.isNotEmpty(interceptRecordList)) {
            Optional<JdhPromiseRiskInterceptRecord> any = interceptRecordList.stream().filter(riskInterceptRecord ->
                    RiskWorkTagEnum.needDo(riskInterceptRecord.getRiskWorkTag()) && !RiskPassStatusEnum.PASS.getStatus().equals(riskInterceptRecord.getRiskPassStatus())
            ).findAny();
            //被中高风险卡控
            if (any.isPresent()) {
                log.info("PromiseApplicationImpl -> modifyPromiseDispatch 被中高风险卡控, 直接返回修改时间成功 context:{}",JSON.toJSONString(context));
                //直接返回修改预约成功
                PromiseModifyEventBody body = new PromiseModifyEventBody();
                body.setPromiseId(jdhPromise.getPromiseId());
                body.setBeforeStatus(jdhPromise.getPromiseStatus());
                body.setAfterStatus(JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus());
                body.setBeforeAppointmentTime(cmd.getBeforeAppointmentTime());
                body.setAfterAppointmentTime(cmd.getAfterAppointmentTime());
                body.setOperatorRoleType(cmd.getOperatorRoleType());
                body.setReason(cmd.getReason());
                if (CollectionUtils.isNotEmpty(jdhPromise.getServices())) {
                    //查询商品项目信息
                    Set<Long> serviceIdList = jdhPromise.getServices().stream().map(PromiseService::getServiceId).collect(Collectors.toSet());
                    Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder().skuIdList(serviceIdList).querySkuCoreData(false).queryServiceItem(true).queryItemMaterial(false).queryItemSkill(false).build());
                    Set<String> itemNames = jdhSkuDtoMap.entrySet().stream().flatMap(entry -> entry.getValue().getServiceItemList().stream()).map(ServiceItemDto::getItemName).collect(Collectors.toSet());
                    String serviceName = CollectionUtils.isEmpty(itemNames) ? "" : itemNames.size() > 2 ? itemNames.stream().limit(2).collect(Collectors.joining("、")) + "..."  : String.join("、", itemNames);
                    body.setServiceName(serviceName);
                }
                eventCoordinator.publish(EventFactory.newDefaultEvent(jdhPromise, PromiseEventTypeEnum.PROMISE_AUTO_MODIFY_SUCCESS, body));
                return true;
            }
        }


        Boolean requiredDispatch = jdhPromiseDomainService.requiredDispatch(context);
        log.info("PromiseApplicationImpl -> modifyPromiseDispatch requiredDispatch:{}",requiredDispatch);
        if(requiredDispatch){
            //先查询检测单过滤掉失效的预约人信息
            MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
            medicalPromiseListQuery.setInvalid(false);
            medicalPromiseListQuery.setFreezeQuery(false);
            medicalPromiseListQuery.setPromiseId(cmd.getPromiseId());
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
            if(CollectionUtils.isEmpty(medicalPromises)) {
                log.error("PromiseApplicationImpl -> dispatch, 没有需要提交预约的预约人信息");
                return Boolean.TRUE;
            }
            Set<Long> validPromisePatientSet = medicalPromises.stream().map(MedicalPromise::getPromisePatientId).collect(Collectors.toSet());
            // 调用派单域，发起派单逻辑
            SubmitDispatchCmd submitDispatchCmd = PromiseApplicationConverter.INS.promise2SubmitDispatchCmd(jdhPromise, validPromisePatientSet);
            // 如果是客服、运营操作，写入erp信息，用于派单失败发送个具体的erp
            if (OperatorRoleTypeEnum.CUSTOMER_SERVICE.getType().equals(cmd.getOperatorRoleType()) || OperatorRoleTypeEnum.BIZ_OPERATION.getType().equals(cmd.getOperatorRoleType()) ) {
                // 后续流程有针对运营端来源不校验用户pin逻辑
                submitDispatchCmd.setOperateSource(CommonConstant.ONE);
                submitDispatchCmd.setOperateErp(cmd.getOperator());
            }
            if (OperatorRoleTypeEnum.DOCTOR.getType().equals(cmd.getOperatorRoleType())) {
                // 后续流程有针对运营端来源不校验用户pin逻辑
                submitDispatchCmd.setOperatePin(cmd.getOperator());
            }

            if (OperatorRoleTypeEnum.USER_SELF.getType().equals(cmd.getOperatorRoleType())) {
                // 用户自己改约
                submitDispatchCmd.setOperateSource(CommonConstant.TWO);
                submitDispatchCmd.setOperatePin(cmd.getOperator());
            }

            boolean verticalCodeBool = serviceHomeTypeDomainService.getServiceHomeVerticalCode("XFYL_HOME_SELF_TEST_TRANSPORT").equals(context.getVerticalBusiness().getVerticalCode());
            if(verticalCodeBool){
                // 后续流程有针对运营端来源不校验用户pin逻辑
                submitDispatchCmd.setOperateSource(CommonConstant.ONE);
            }
            log.info("PromiseApplicationImpl -> dispatch modifyPromiseDispatch:{}",JSON.toJSONString(submitDispatchCmd));
            submitDispatchCmd.setOperatorRoleType(cmd.getOperatorRoleType());
            submitDispatchCmd.setReason(cmd.getReason());
            Boolean modifyServiceDateDispatch = dispatchApplication.modifyServiceDateDispatch(submitDispatchCmd);
            if(Boolean.FALSE.equals(modifyServiceDateDispatch)){
                throw new SystemException(PromiseErrorCode.PROMISE_MODIFY_DATE_DISPATCH_ERROR);
            }
        }
        //这里不在外抛事件
        return Boolean.TRUE;
    }

    /**
     * 意向护士派单
     * @param jdhPromise
     * @param cmd
     */
    private void buildIntendedNurse(JdhPromise jdhPromise, SubmitDispatchCmd cmd) {
        try {
            List<String> serviceTypeList = JSON.parseArray(duccConfig.getIntendedNurseDispatchServiceTypeConfig(), String.class);
            if (serviceTypeList.contains(jdhPromise.getServiceType()) && CollectionUtils.isNotEmpty(jdhPromise.getPromiseExtends())){
                List<JdhPromiseExtend> promiseExtendList = jdhPromise.getPromiseExtends().stream().filter(e -> OrderExtTypeEnum.INTENDED_NURSE.getType()
                        .equals(e.getAttribute())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(promiseExtendList)){
                    return;
                }
                promiseExtendList.sort((order1,order2)->order2.getUpdateTime().compareTo(order1.getUpdateTime()));
                JdhPromiseExtend promiseExtend = promiseExtendList.get(0);
                log.info("PromiseApplicationImpl buildIntendedNurse promiseExtend={}", JSON.toJSONString(promiseExtend));
                PromiseIntendedNurse promiseIntendedNurse = JSON.parseObject(promiseExtend.getValue(), PromiseIntendedNurse.class);
                if (Integer.valueOf(2).equals(promiseIntendedNurse.getRecommendType()) && promiseIntendedNurse.getAngelId() != null){
                    cmd.setIntendedAngelIds(Collections.singletonList(promiseIntendedNurse.getAngelId()));
                }
            }
        } catch (Exception e) {
            log.error("PromiseApplicationImpl buildIntendedNurse error e", e);
        }
    }

    /**
     * 通过promiseId查找
     *
     * @param promiseIdRequest promiseIdRequest
     * @return {@link PromiseDto}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.findByPromiseId")
    public PromiseDto findByPromiseId(PromiseIdRequest promiseIdRequest) {
        JdhPromise jdhPromise = jdhPromiseRepository.find(JdhPromiseIdentifier.builder().promiseId(promiseIdRequest.getPromiseId()).build());
        log.info("PromiseApplicationImpl -> findByPromiseId jdhPromise:{}",JSON.toJSONString(jdhPromise));
        List<MedicalPromiseBO> medicalPromises = medicalPromiseRpc.findListByPromiseId(promiseIdRequest.getPromiseId());
        log.info("PromiseApplicationImpl -> findByPromiseId medicalPromiseBOS:{}",JSON.toJSONString(medicalPromises));
        return PromiseApplicationConverter.INS.entity2Dto(jdhPromise, medicalPromises);
    }

    /**
     * 通过promiseId查找
     *
     * @param promiseIdRequest promiseIdRequest
     * @return {@link PromiseDto}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.findPromiseByPromiseId")
    public PromiseDto findPromiseByPromiseId(PromiseIdRequest promiseIdRequest) {
        JdhPromise jdhPromise = jdhPromiseRepository.find(JdhPromiseIdentifier.builder().promiseId(promiseIdRequest.getPromiseId()).build());
        log.info("PromiseApplicationImpl -> findPromiseByPromiseId jdhPromise:{}",JSON.toJSONString(jdhPromise));
        return PromiseApplicationConverter.INS.entity2Dto(jdhPromise);
    }

    /**
     * 通过promiseId查找
     *
     * @param promiseId
     * @return {@link PromiseDto}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.jdhPromiseRepository")
    public String findSourceVoucherIdByPromiseId(Long promiseId) {
        JdhPromise jdhPromise = jdhPromiseRepository.find(JdhPromiseIdentifier.builder().promiseId(promiseId).build());
        log.info("PromiseApplicationImpl -> jdhPromiseRepository jdhPromise:{}",JSON.toJSONString(jdhPromise));
        if(Objects.nonNull(jdhPromise)){
            return jdhPromise.getSourceVoucherId();
        }
        return "";
    }

    /**
     * 创建实验室检测单
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.createMedPromise")
    public Boolean createMedPromise(PromiseCreateMedPromiseCmd cmd) {
        log.info("PromiseApplicationImpl -> createMedPromise cmd:{}",JSON.toJSONString(cmd));
        JdhPromise jdhPromise = jdhPromiseRepository.find(JdhPromiseIdentifier.builder().promiseId(cmd.getPromiseId()).build());
        log.info("PromiseApplicationImpl -> createMedPromise jdhPromise:{}",JSON.toJSONString(jdhPromise));
        PromiseCreateMedicalPromiseContext context =  new PromiseCreateMedicalPromiseContext();
        context.setPromise(jdhPromise);
        context.init(jdhPromise,PromiseEventTypeEnum.PROMISE_CREATED);
        log.info("PromiseApplicationImpl -> createMedPromise context:{}",JSON.toJSONString(context));
        Boolean requiredCreateMedicalPromise = jdhPromiseDomainService.requiredCreateMedicalPromise(context);
        log.info("PromiseApplicationImpl -> createMedPromise requiredCreateMedicalPromise:{}",requiredCreateMedicalPromise);
        if(requiredCreateMedicalPromise){
            MedicalPromiseCreateCmd createMedPromiseCmd = PromiseApplicationConverter.INS.promise2CreateMedPromiseCmd(jdhPromise);
            log.info("PromiseApplicationImpl -> createMedPromise createMedPromiseCmd:{}",JSON.toJSONString(createMedPromiseCmd));
            return medicalPromiseApplication.createMedicalPromise(createMedPromiseCmd);
        }
        //这里不在外抛事件
        return Boolean.TRUE;
    }

    @Override
    @LogAndAlarm
    public Boolean bindSpecimenCode(PromiseBindSpecimenCodeCmd cmd) {
        AssertUtils.isNotEmpty(cmd.getCodes(), "条码信息不能为空");
        JdhPromise promise = authorityPromiseCheck(cmd.getCodes().get(0).getPromiseId(), cmd.getUserPin());

        MedicalPromiseBindSpecimenCodeCmd bindSpecimenCodeCmd = new MedicalPromiseBindSpecimenCodeCmd();
        bindSpecimenCodeCmd.setSpecimenCodeList(cmd.getCodes());
        bindSpecimenCodeCmd.setVerticalCode(promise.getVerticalCode());
        bindSpecimenCodeCmd.setServiceType(promise.getServiceType());
        return medicalPromiseApplication.batchBindSpecimenCode(bindSpecimenCodeCmd);

    }

    /**
     * 验证条码
     * @param cmd
     * @return
     */
    @Override
    public List<PatientSpecimenCodeDto> verifySpecimenCode(PromiseVerifySpecimenCodeCmd cmd) {
        List<String> codes = cmd.getPatientSpecimenCodes().stream()
                .map(PatientSpecimenCodeDto::getSpecimenCodes)
                .flatMap(Collection::stream).collect(Collectors.toList());



         if(medicalPromiseRepository.existSpecimenCode(codes)){
             throw new BusinessException(PromiseErrorCode.SPECIMEN_CODE_REPEAT_FAIL);
         }

        cmd.getPatientSpecimenCodes().forEach(dto -> {
            SubmitUser user = dto.getUser();
            String headUrl = viaComponentDomainService.queryPatientHeadImage(user.getGender(), user.getAge());
            user.setPatientHeaderImage(headUrl);
            Integer age = Birthday.parseAge(user.getBirthday());
            user.setAge(age);
        });

         return cmd.getPatientSpecimenCodes();
    }

    /**
     * findByPromiseList
     *
     * @param request 请求
     * @return {@link List}<{@link PromiseDto}>
     */
    @Override
    @LogAndAlarm
    public List<PromiseDto> findByPromiseList(PromiseListRequest request) {
        List<JdhPromise> jdhPromises = jdhPromiseRepository.findList(PromiseRepQuery.builder().voucherIds(request.getVoucherIds()).sourceVoucherIdList(request.getSourceVoucherIdList()).build());
        return PromiseApplicationConverter.INS.entity2DtoList(jdhPromises);
    }

    /**
     * findByPromiseListByPromiseIds
     *
     * @param promiseIds 请求
     * @return {@link List}<{@link PromiseDto}>
     */
    @Override
    @LogAndAlarm
    public List<PromiseDto> findByPromiseListByPromiseIds(List<Long> promiseIds) {
        List<JdhPromise> jdhPromises = jdhPromiseRepository.findList(PromiseRepQuery.builder().promiseIds(promiseIds).build());
        List<PromiseDto> result = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(jdhPromises)){
            jdhPromises.forEach(jdhPromise -> {
                PromiseDto promiseDto = PromiseApplicationConverter.INS.entity2Dto(jdhPromise);
                result.add(promiseDto);
            });
        }
        return result;
    }

    /**
     * 查询患者列表
     * @param promisePatientId
     * @return
     */
    @Override
    @LogAndAlarm
    public List<PromisePatientDto> listPromisePatient(Set<Long> promisePatientId) {

        List<JdhPromisePatient>  patients = jdhPromiseRepository.listPatient(promisePatientId);
        return PromiseApplicationConverter.INS.convert2Patient(patients);
    }


    /**
     * 查询子套餐列表
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public List<PromiseServiceChildDto> listServiceChildren(ListServiceChildrenRequest request) {

        ListServiceChildrenParam param = new ListServiceChildrenParam();
        BeanUtils.copyProperties(request, param);
        return jdhPromiseQueryService.listServiceChildren(param);
    }

    /**
     * 履约单状态回调
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean providerPromiseCallback(PromiseCallbackCmd cmd) {
        return promiseApplication.callBack(cmd);
    }

    /**
     *
     * @param query
     * @return
     */
    @Override
    public List<PromiseDto> findJdhPromiseList(PromiseRepQuery query) {
        List<JdhPromise> jdhPromises = jdhPromiseRepository.findJdhPromiseList(query);
        return PromiseApplicationConverter.INS.entity2DtoList(jdhPromises);
    }

    /**
     * 解析回调类型
     *
     * @param callBackEnum 回调enum
     * @return {@link PromiseEventTypeEnum}
     */
    @SuppressWarnings("JdAvoidMethodReturnEnum")
    public PromiseEventTypeEnum parseCallType(AppointCallBackEnum callBackEnum){
        if (Objects.isNull(callBackEnum)){
            throw new NullPointerException("callBackEnum is null");
        }
        switch (callBackEnum){
            case APPOINTMENT_SUCC:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_APPOINTMENT_SUCCESS;
            case APPOINTMENT_FAIL:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_APPOINTMENT_FAIL;
            case MODIFY_SUCC:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_MODIFY_SUCCESS;
            case MODIFY_FAIL:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_MODIFY_FAIL;
            case CANCEL_SUCC:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_CANCEL_SUCCESS;
            case CANCEL_FAIL:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_CANCEL_FAIL;
            case CHECK_SUCCESS:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_NOTICE_COMPLETE;
            case DISPATCH_SUCCESS:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_DISPATCH_SUCCESS;
            case SERVICE_READY:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_SERVICE_READY;
            case SERVICING:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_SERVICING;
            case SERVICE_COMPLETE:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_SERVICE_COMPLETE;
            case PROMISE_COMPLETE:
                return PromiseEventTypeEnum.PROMISE_CALLBACK_COMPLETE;
            default:
                throw new UnsupportedOperationException("Unsupported CallBack");
        }
    }


    /**
     * 解析发布事件
     *
     * @param promise promise
     * @return {@link PromiseEventTypeEnum}
     */
    @SuppressWarnings("JdAvoidMethodReturnEnum")
    public PromiseEventTypeEnum parsePublishEvent(JdhPromise promise){
        JdhPromiseStatusEnum curStatus = JdhPromiseStatusEnum.convert(promise.getPromiseStatus());
        if (Objects.isNull(curStatus)){
            throw new NullPointerException("promise status convert error");
        }
        switch (curStatus){
            case APPOINTMENT_SUCCESS:
                return PromiseEventTypeEnum.PROMISE_APPOINTMENT_SUCCESS;
            case APPOINTMENT_FAIL:
                return PromiseEventTypeEnum.PROMISE_APPOINTMENT_FAIL;
            case MODIFY_SUCCESS:
                return PromiseEventTypeEnum.PROMISE_MODIFY_SUCCESS;
            case MODIFY_FAIL:
                return PromiseEventTypeEnum.PROMISE_MODIFY_FAIL;
            case CANCEL_SUCCESS:
                return PromiseEventTypeEnum.PROMISE_CANCEL_SUCCESS;
            case CANCEL_FAIL:
                return PromiseEventTypeEnum.PROMISE_CANCEL_FAIL;
            case SERVICE_READY:
                return PromiseEventTypeEnum.PROMISE_SERVICE_READY;
            case SERVICING:
                return PromiseEventTypeEnum.PROMISE_SERVICING;
            case SERVICE_COMPLETE:
                return PromiseEventTypeEnum.PROMISE_SERVICE_COMPLETE;
            case COMPLETE:
                return PromiseEventTypeEnum.PROMISE_COMPLETE;
            default:
                throw new UnsupportedOperationException("Unsupported Operate");
        }
    }

    /**
     * @param promiseId
     * @return
     */
    @Override
    public PromiseDto findVoucherIdByPromiseId(Long promiseId) {
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(promiseId);
        PromiseDto jdhPromise = this.findPromiseByPromiseId(promiseIdRequest);
        return jdhPromise;
    }

    /**
     *
     * @param sourceVoucherId
     * @return
     */
    @Override
    public List<PromiseDto> getPromiseByOrderItemId(String sourceVoucherId) {
        PromiseRepQuery query = new PromiseRepQuery();
        //根据订单号查询服务单列表，将服务单id放入入参
        query.setSourceVoucherId(sourceVoucherId);
        //查询预约单列表
        List<JdhPromise> list = jdhPromiseRepository.findList(query);
        if(CollUtil.isNotEmpty(list)){
            return PromiseApplicationConverter.INS.entity2DtoList(list);
        }
        return null;
    }

    /**
     * 数据越权校验
     * @return
     */
    private JdhPromise authorityPromiseCheck(Long promiseId, String userPin){
        AssertUtils.hasText(userPin, SystemErrorCode.LOGIN_FAILURE);
        PromiseRepQuery query = PromiseRepQuery.builder().userPin(userPin).promiseId(promiseId).build();
        JdhPromise promise = jdhPromiseRepository.findPromise(query);
        if (Objects.isNull(promise)){
            throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
        }
        return promise;
    }

    /**
     * 填充礼包的商品sku预约数据
     *
     * @param jdhVoucher jdhVoucher
     * @param context context
     */
    private void fillGiftMainSku(JdhVoucher jdhVoucher, PromiseSubmitAbilityContext context) {
        if (jdhVoucher == null || jdhVoucher.getExtend() == null || context == null) {
            return;
        }
        if (StringUtils.isNotBlank(jdhVoucher.getExtend().getMainSkuName())) {
            if (StringUtils.isNotBlank(context.getRemark())) {
                context.setRemark(context.getRemark() + "【主品信息】" + jdhVoucher.getExtend().getMainSkuName());
            } else {
                context.setRemark("【主品信息】" + jdhVoucher.getExtend().getMainSkuName());
            }
        }
    }


    @Override
    public CompletePromiseDto queryCompletePromise(CompletePromiseRequest request) {

        PromiseDto promiseDto = this.findByPromiseId(PromiseIdRequest.builder().promiseId(request.getPromiseId()).build());

        AngelWorkDetailDto workDetailDto = angelPromiseApplication.queryWorkListOrRecently(AngelWorkQuery.builder().promiseId(request.getPromiseId()).build());

        JdhAngelDto jdhAngelDto = null;
        if(Objects.nonNull(workDetailDto) && Objects.nonNull(workDetailDto.getAngelId())) {
            jdhAngelDto = angelApplication.queryByAngelInfo(AngelRequest.builder().angelId(Long.parseLong(workDetailDto.getAngelId())).build());
        }

        return CompletePromiseDto.builder().promiseDto(promiseDto).angelWorkDetailDto(workDetailDto).jdhAngelDto(jdhAngelDto).build();


    }

    @Override
    public List<CompletePromiseDto> queryCompletePromiseList(CompletePromiseRequest request) {
        List<PromiseDto> promiseDtoList = this.findJdhPromiseList(PromiseRepQuery.builder().promiseIds((request.getPromiseIds().stream().collect(Collectors.toList()))).build());
        if(CollectionUtils.isEmpty(promiseDtoList)){
           return Collections.emptyList();
        }
        List<CompletePromiseDto> completePromiseDtoList = new ArrayList<>();
        promiseDtoList.forEach(s -> {
            AngelWorkDetailDto workDetailDto = angelPromiseApplication.queryWorkListOrRecently(AngelWorkQuery.builder().promiseId(s.getPromiseId()).build());
            JdhAngelDto jdhAngelDto = null;
            if(Objects.nonNull(workDetailDto) && Objects.nonNull(workDetailDto.getAngelId())) {
                jdhAngelDto = angelApplication.queryByAngelInfo(AngelRequest.builder().angelId(Long.parseLong(workDetailDto.getAngelId())).build());
            }
            completePromiseDtoList.add(CompletePromiseDto.builder().promiseDto(s).angelWorkDetailDto(workDetailDto).jdhAngelDto(jdhAngelDto).build());
        });
        return completePromiseDtoList;
    }


    /**
     * 转换预约时间信息
     *
     * @param avaiableAppointmentTimeDTO dto
     * @return dto
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.changeAndFilterDate")
    public List<AgencyAppointDateDto> changeAndFilterDate(String parentDateId, AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO) {
        if (avaiableAppointmentTimeDTO == null || CollUtil.isEmpty(avaiableAppointmentTimeDTO.getCompatibleGroupDTO())) {
            return Collections.emptyList();
        }
        List<XfylAppointDateDTO> compatibleGroupDTO = avaiableAppointmentTimeDTO.getCompatibleGroupDTO();

        DateFormat dayFormat = new SimpleDateFormat("M月d日[E]");
        // 仅返回日数据
        if (StringUtils.isBlank(parentDateId)) {
            return compatibleGroupDTO.stream().map(s -> {
                AgencyAppointDateDto agencyAppointDateDto = new AgencyAppointDateDto();
                String dateStr = "";
                try {
                    Date dayDate = dayFormat.parse(s.getAppointDateDesc());
                    dateStr = TimeUtils.dateTimeToStr(dayDate, TimeFormat.DATE_PATTERN_MD_NONE);
                } catch (ParseException e) {
                    throw new BusinessException(SystemErrorCode.SYSTEM_ERROR);
                }
                agencyAppointDateDto.setId(dateStr);
                agencyAppointDateDto.setLabel(s.getAppointDateDesc());
                agencyAppointDateDto.setValue(dateStr);
                agencyAppointDateDto.setDisabled(!(s.getStatus() != null && s.getStatus() == 1));
                agencyAppointDateDto.setExistsChildren(true);
                return agencyAppointDateDto;
            }).collect(Collectors.toList());
        }

        Map<String, XfylAppointDateDTO> maps = compatibleGroupDTO.stream()
                .collect(Collectors.toMap(s -> {
                            String dateStr = "";
                            try {
                                Date dayDate = dayFormat.parse(s.getAppointDateDesc());
                                dateStr = TimeUtils.dateTimeToStr(dayDate, TimeFormat.DATE_PATTERN_MD_NONE);
                            } catch (ParseException e) {
                                throw new BusinessException(SystemErrorCode.SYSTEM_ERROR);
                            }
                            return dateStr;
                        }
                        ,
                        Function.identity(),
                        (existingValue, newValue) -> newValue
                ));
        if (!maps.containsKey(parentDateId)) {
            return Collections.emptyList();
        }
        XfylAppointDateDTO dto = maps.get(parentDateId);
        if (dto == null || CollUtil.isEmpty(dto.getAppointDateTimeGroupDTOList())) {
            return Collections.emptyList();
        }

        List<AgencyAppointDateDto> subList = new ArrayList<>();
        List<XfylAppointDateTimeGroupDTO> hoursTime = dto.getAppointDateTimeGroupDTOList();
        for (XfylAppointDateTimeGroupDTO hoursTimeDTO : hoursTime) {
            if (hoursTimeDTO == null || CollUtil.isEmpty(hoursTimeDTO.getDateTimeDTOList())) {
                continue;
            }
            subList.addAll(hoursTimeDTO.getDateTimeDTOList().stream().map(s -> {
                AgencyAppointDateDto agencyAppointDateDto = new AgencyAppointDateDto();
                agencyAppointDateDto.setId(parentDateId + "-" + s.getTimeDesc());
                agencyAppointDateDto.setLabel(s.getTimeDesc());
                agencyAppointDateDto.setValue(JSON.toJSONString(s));
                agencyAppointDateDto.setDisabled(!(s.getStatus() != null && s.getStatus() == 1));
                agencyAppointDateDto.setExistsChildren(false);
                return agencyAppointDateDto;
            }).collect(Collectors.toList()));
        }
        return subList;
    }

    /**
     * 查询预约时间
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.queryAvailableTime")
    public List<AgencyAppointDateDto> queryAvailableTime(AgencyQueryDateRequest request) {
        AvaiableAppointmentTimeParam param = new AvaiableAppointmentTimeParam();
        param.setUserPin(request.getUserPin());
        param.setSkuIds(request.getSkuIds());
        param.setShowTimeType(request.getShowTimeType());

        if (StringUtils.isNotBlank(request.getFullAddress())) {
            param.setAddressId(NumberUtils.generateAddressId(request.getFullAddress()));
            param.setFullAddress(request.getFullAddress());
        } else if (request.getAddressId() != null) {
            param.setAddressId(String.valueOf(request.getAddressId()));
            List<AddressDetailBO> addressDetailBOList = jdhAddressRpc.queryAddressList(request.getUserPin());
            AddressDetailBO addressDetailBO = addressDetailBOList.stream().filter(s -> request.getAddressId().equals(s.getAddressId())).findFirst().orElse(null);
            param.setFullAddress(addressDetailBO != null ? addressDetailBO.getFullAddress() : null);
        } else {
            PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(request.getPromiseId()).build());
            log.info("PromiseApplicationImpl queryAvailableTime promiseDto={}", JSON.toJSONString(promiseDto));
            if (promiseDto == null) {
                throw new BusinessException(PromiseErrorCode.PROMISE_NOT_EXISTS);
            }if (promiseDto.getStore() == null) {
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_MISS_ERROR);
            }
            if (StringUtils.isBlank(promiseDto.getStore().getStoreId())) {
                // 买约一体无storeId,底层需要addressId,用于缓存逻辑,不影响实际业务,默认给个-1
                promiseDto.getStore().setStoreId("-1");
            }
            param.setAddressId(promiseDto.getStore().getStoreId());
            param.setFullAddress(promiseDto.getStore().getStoreAddr());
            JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(promiseDto.getVerticalCode());
            if(Objects.nonNull(jdhVerticalBusiness)){
                param.setSkuServiceType(BusinessModeUtil.getSkuServiceType(BusinessModeEnum.getEnumByCode(jdhVerticalBusiness.getBusinessModeCode())));
            }
        }
        AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO = tradeApplication.queryAvaiableAppointmentTime(param);
        log.info("PromiseApplicationImpl queryAvailableTime queryAvaiableAppointmentTime param={}, avaiableAppointmentTimeDTO={}"
                , JSON.toJSONString(param), JSON.toJSONString(avaiableAppointmentTimeDTO));
        return changeAndFilterDate(request.getParentDateId(), avaiableAppointmentTimeDTO);
    }

    /**
     * 修改预约时间
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.PromiseApplicationImpl.modifyAppointmentTime")
    public Boolean modifyAppointmentTime(AgentModifyPromiseCmd cmd) {
        PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(cmd.getPromiseId()).build());
        log.info("PromiseApplicationImpl modifyAppointmentTime promiseDto={}", JSON.toJSONString(promiseDto));
        if (promiseDto == null) {
            throw new BusinessException(PromiseErrorCode.PROMISE_NOT_EXISTS);
        }

        ModifyAppointCmd modifyAppointCmd = new ModifyAppointCmd();
        modifyAppointCmd.setPromiseId(String.valueOf(promiseDto.getPromiseId()));
        AppointmentTime appointmentTime = new AppointmentTime();
        appointmentTime.setDateType(cmd.getDateType() == null ? 2 : cmd.getDateType());
        appointmentTime.setAppointmentStartTime(cmd.getAppointmentStartTime());
        appointmentTime.setAppointmentEndTime(cmd.getAppointmentEndTime());
        appointmentTime.setIsImmediately(cmd.getIsImmediately());
        modifyAppointCmd.setAppointmentTime(appointmentTime);
        modifyAppointCmd.setOperator(cmd.getOperator());
        modifyAppointCmd.setOperatorRoleType(cmd.getOperatorRoleType() == null ? OperatorRoleTypeEnum.CUSTOMER_SERVICE.getType() : cmd.getOperatorRoleType());
        return promiseApplication.modify(modifyAppointCmd);
    }

    /**
     * 检查履约单风险等级
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "PromiseApplicationImpl.checkPromiseDangerLevel")
    public Boolean checkPromiseDangerLevel(PromiseDangerLevelRequest request) {
        AssertUtils.nonNull(request, "请求参数不能为空");
        if(Objects.isNull(request.getOrderId()) && Objects.isNull(request.getPromiseId())) {
            log.error("PromiseApplicationImpl -> checkPromiseDangerLevel, 参数不正确.request={}", JSON.toJSONString(request));
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        if(CollectionUtils.isEmpty(request.getDangerLevelList())) {
            request.setDangerLevelList(Lists.newArrayList(ItemDangerLevelEnum.HIGH_DANGER_LEVEL.getLevel()));
        }
        PromiseRepQuery query = new PromiseRepQuery();
        query.setSourceVoucherId(Objects.nonNull(request.getOrderId()) ? String.valueOf(request.getOrderId()) : null);
        query.setPromiseId(request.getPromiseId());
        List<JdhPromise> promiseList = jdhPromiseRepository.findList(query);
        if(CollectionUtils.isEmpty(promiseList)) {
            log.info("PromiseApplicationImpl -> checkPromiseDangerLevel, 没有查询到履约单数据");
            return false;
        }
        JdhPromise jdhPromise = promiseList.get(0);
        JdhPromiseExtend extend = jdhPromise.findExtend(PromiseExtendKeyEnum.ITEM_DANGER_LEVEL.getFiledKey());
        if(Objects.isNull(extend) || StringUtils.isBlank(extend.getValue())) {
            log.info("PromiseApplicationImpl -> checkPromiseDangerLevel, 没有扩展信息");
            return false;
        }

        List<Integer> dangerLevelList = JSON.parseArray(extend.getValue(), Integer.class);
        List<Integer> noHitLevel = request.getDangerLevelList().stream()
                .filter(item -> !dangerLevelList.contains(item))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(noHitLevel);
    }

    /**
     * 查询履约单风险等级
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "PromiseApplicationImpl.queryPromiseDangerLevel")
    public List<Integer> queryPromiseDangerLevel(PromiseDangerLevelRequest request) {
        AssertUtils.nonNull(request, "请求参数不能为空");
        if(Objects.isNull(request.getOrderId()) && Objects.isNull(request.getPromiseId())) {
            log.error("PromiseApplicationImpl -> queryPromiseDangerLevel, 参数不正确.request={}", JSON.toJSONString(request));
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        PromiseRepQuery query = new PromiseRepQuery();
        query.setSourceVoucherId(Objects.nonNull(request.getOrderId()) ? String.valueOf(request.getOrderId()) : null);
        query.setPromiseId(request.getPromiseId());
        List<JdhPromise> promiseList = jdhPromiseRepository.findPromiseList(query);
        if(CollectionUtils.isEmpty(promiseList)) {
            log.info("PromiseApplicationImpl -> queryPromiseDangerLevel, 没有查询到履约单数据");
            if(Objects.nonNull(request.getSkuId())) {
                JdhServiceQuery jdhServiceQuery = new JdhServiceQuery();
                jdhServiceQuery.setSkuId(request.getSkuId());
                List<ServiceItemDto> serviceItemDtos = productApplication.querySkuAndItemList(jdhServiceQuery);
                log.info("VoucherApplicationImpl createVoucher serviceItemDtos={}", JSON.toJSONString(serviceItemDtos));
                if(CollectionUtils.isNotEmpty(serviceItemDtos)) {
                    List<ServiceItemDto> serviceItemDtoList = serviceItemDtos.stream().filter(item -> Objects.nonNull(item.getDangerLevel())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(serviceItemDtoList)) {
                        return Lists.newArrayList(serviceItemDtoList.stream().map(ServiceItemDto::getDangerLevel).collect(Collectors.toSet()));
                    }
                }
            }
            return Collections.emptyList();
        }
        JdhPromise jdhPromise = promiseList.get(0);
        PromiseExtQuery extQuery = new PromiseExtQuery();
        extQuery.setPromiseId(jdhPromise.getPromiseId());
        extQuery.setAttributeCode(Lists.newArrayList(PromiseExtendKeyEnum.ITEM_DANGER_LEVEL.getFiledKey()));
        List<JdhPromiseExtend> promiseExtList = jdhPromiseRepository.findPromiseExtList(extQuery);

        if(CollectionUtils.isEmpty(promiseExtList)) {
            return Collections.emptyList();
        }
        JdhPromiseExtend jdhPromiseExtend = promiseExtList.get(0);
        return JSON.parseArray(jdhPromiseExtend.getValue(), Integer.class);
    }


    /**
     * 非快检需求: 删除(假删)履约单
     * @param resetPromiseCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean delPromise(ResetPromiseCmd resetPromiseCmd) {

        //删除履约单
        DelPromiseContext delPromiseContext = JSON.parseObject(JSON.toJSONString(resetPromiseCmd),DelPromiseContext.class);
        jdhPromiseDomainService.delPromise(delPromiseContext);

        //删除检测单
        DelMedicalPromiseContext delMedicalPromiseContext = JSON.parseObject(JSON.toJSONString(resetPromiseCmd),DelMedicalPromiseContext.class);
        medicalPromiseDomainService.delMedicalPromise(delMedicalPromiseContext);

        GetDetailByPromiseIdQuery getDetailByPromiseIdQuery = new GetDetailByPromiseIdQuery();
        getDetailByPromiseIdQuery.setPromiseId(resetPromiseCmd.getPromiseId());
        AngelShipDto angelShipDto = angelWorkApplication.getDetailByPromiseId(getDetailByPromiseIdQuery);
        if(angelShipDto!=null&&
                Arrays.asList(AngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus()
                        ,AngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus()
                        ,AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus()
                        ,AngelShipStatusEnum.KNIGHT_REACH_STORE.getShipStatus()).contains(angelShipDto.getShipStatus())){
            AngelWorkCancelShipCmd angelWorkCancelShipCmd = AngelWorkCancelShipCmd.builder().build();
            angelWorkCancelShipCmd.setWorkId(angelShipDto.getAngelWorkId()+"");
            angelWorkCancelShipCmd.setShipId(angelShipDto.getShipId()+"");
            angelWorkCancelShipCmd.setOperateSource(CommonConstant.ONE);
            angelWorkCancelShipCmd.setStandCancelCode(AngelShipCancelCodeStatusEnum.OPERATE_CANCEL.getType());
            angelWorkCancelShipCmd.setOperator(LoginContext.getLoginContext().getPin());
            angelWorkApplication.cancelShip(angelWorkCancelShipCmd);
        }
        return true;
    }

    /**
     * 非快检需求专用
     * @param updatePromiseStatusCmd
     * @return
     */
    @LogAndAlarm
    @Override
    public Boolean updatePromiseStatus(UpdatePromiseStatusCmd updatePromiseStatusCmd) {
        //修改履约单状态
        UpdatePromiseStatusContext delPromiseContext = JSON.parseObject(JSON.toJSONString(updatePromiseStatusCmd),UpdatePromiseStatusContext.class);
        return jdhPromiseDomainService.updatePromiseStatus(delPromiseContext);
    }

    @Override
    public PromiseServiceStartTimeDescDTO queryPromiseServiceStartTimeDesc(PromiseServiceStartTimeDescRequest request) {
        // 1. 初始化必要参数并校验
        PromiseAppointmentTimeDto appointmentTime = request.getAppointmentTime();
        if (appointmentTime == null && request.getPromiseId() == null) {
            return null;
        }

        // 2. 补全预约时间信息
        if (appointmentTime == null) {
            PromiseDto promiseDto = getPromiseDto(request.getPromiseId());
            if (promiseDto == null || promiseDto.getAppointmentTime() == null) {
                return null;
            }
            appointmentTime = promiseDto.getAppointmentTime();
        }

        // 3. 验证必要字段
        if (appointmentTime.getAppointmentEndTime() == null || appointmentTime.getIsImmediately() == null) {
            return null;
        }
        Date promiseCreateTime = appointmentTime.getAppointmentStartTime();

        // 4. 获取配置模板
        JSONObject config = this.getServiceStartTimeConfig();
        String modeOneContent = config.getString(String.valueOf(CommonConstant.ONE)); // 服务开始模板
        String modeTwoContent = config.getString(String.valueOf(CommonConstant.TWO)); // 送达实验室模板
        String modeThreeContent = config.getString(String.valueOf(CommonConstant.THREE)); // 预约时间段服务开始模板
        String modeFourContent = config.getString(String.valueOf(CommonConstant.FOUR)); // 预约时间段送达实验室模板
        Integer notNurseCheckAddTime = config.getInteger("notNurseCheckTime"); // 非护士上门
        Integer nurseCheckAddTime = config.getInteger("nurseCheckTime"); // 护士上门显示时间


        // 5. 日期处理
        boolean isSameDay = this.isSameDay(new Date(), appointmentTime.getAppointmentStartTime());
        // 6. 根据功能模块处理
        String functionId = request.getFunctionId();
        if (this.isOrderOrScheduleFunction(functionId) || this.isWorkOrderDetailWithValidStatus(request)) {
            return this.buildTimeDescription(modeOneContent, appointmentTime, promiseCreateTime, isSameDay, notNurseCheckAddTime, Boolean.FALSE, modeThreeContent);
        }

        if (PromiseServiceStartTimeModuleTypeEnum.WORK_ORDER_DETAIL.getFunctionName().equals(functionId)) {
            return this.handleWorkOrderDetail(request, modeTwoContent, appointmentTime, promiseCreateTime, isSameDay, nurseCheckAddTime, modeFourContent);
        }

        return null;
    }

    // --- 获取promise详情 ---
    private PromiseDto getPromiseDto(Long promiseId) {
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(promiseId);
        return this.findByPromiseId(promiseIdRequest);
    }

    // 读取ducc配置
    private JSONObject getServiceStartTimeConfig() {
        String config = duccConfig.getPromiseServiceStartTimeDescConfig();
        return JSON.parseObject(config);
    }

    // 判断两个日期是否是同一天
    private boolean isSameDay(Date date1, Date date2) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date1).equals(sdf.format(date2));
    }

    // 判断是否是接单列表、日程列表、全部订单列表的方法调用
    private boolean isOrderOrScheduleFunction(String functionId) {
        return PromiseServiceStartTimeModuleTypeEnum.RECEIVING_ORDER_LIST.getFunctionName().equals(functionId)
                || PromiseServiceStartTimeModuleTypeEnum.SCHEDULE_LIST.getFunctionName().equals(functionId);
    }

    // 判断是工单详情页面，待服务，正在前往状态
    private boolean isWorkOrderDetailWithValidStatus(PromiseServiceStartTimeDescRequest request) {
        return PromiseServiceStartTimeModuleTypeEnum.WORK_ORDER_DETAIL.getFunctionName().equals(request.getFunctionId())
                && (AngelWorkStatusEnum.RECEIVED.getType().equals(request.getWorkStatus())
                || AngelWorkStatusEnum.WAIT_SERVICE.getType().equals(request.getWorkStatus()));
    }

    // 组装第一类描述文案
    private PromiseServiceStartTimeDescDTO buildTimeDescription(String template, PromiseAppointmentTimeDto appointmentTime, Date createTime,
                                                                boolean isSameDay, Integer addTime, Boolean nurseSelfSupplier, String template2) {
        // 立即预约处理
        if (Boolean.TRUE.equals(appointmentTime.getIsImmediately())) {
            if (createTime == null) {
                return null;
            }
            PromiseServiceStartTimeDescDTO promiseServiceStartTimeDescDTO = new PromiseServiceStartTimeDescDTO();
            createTime = TimeUtils.add(createTime, Calendar.MINUTE, addTime);

            promiseServiceStartTimeDescDTO.setScheduleListServiceTime(formatTemplate(template, createTime));
            promiseServiceStartTimeDescDTO.setServiceStartTimeDesc(formatTemplate(template, createTime));
            promiseServiceStartTimeDescDTO.setDeliverySampleTime(String.format(template, TimeUtils.dateTimeToStr(createTime, TimeFormat.LONG_PATTERN_DOT)));
            return promiseServiceStartTimeDescDTO;
        }

        Date appointmentEndTime = appointmentTime.getAppointmentEndTime();
        // 预约单处理
        // 护士上门检测业务身份下,且运单中包含自行配送的，需要把预约开始时间加上某个配置的值
        if (Boolean.TRUE.equals(nurseSelfSupplier)) {
            appointmentEndTime = TimeUtils.add(appointmentTime.getAppointmentStartTime(), Calendar.MINUTE, addTime);
        }
        // 如果模版2不为空，则覆盖模版1
        if (StringUtils.isNotBlank(template2)) {
            template = template2;
        }
        // 开始时间和结束时间日期格式化选择
        TimeFormat appointmentStartTimePattern = isSameDay ? TimeFormat.DATE_PATTERN_HM_SIMPLE : TimeFormat.LONG_PATTERN_DOT;
        TimeFormat appointmentEndTimePattern = TimeFormat.DATE_PATTERN_HM_SIMPLE;
        Date appointmentStartTime = appointmentTime.getAppointmentStartTime();
        String appointmentStartTimeStr = TimeUtils.dateTimeToStr(appointmentStartTime, appointmentStartTimePattern);
        String appointmentEndTimeStr = TimeUtils.dateTimeToStr(appointmentEndTime, appointmentEndTimePattern);
        String scheduleListAppointmentStartTimeStr = TimeUtils.dateTimeToStr(appointmentStartTime, appointmentEndTimePattern);

        String dateStr = appointmentStartTimeStr + "-" + appointmentEndTimeStr;
        String scheduleListDateStr = scheduleListAppointmentStartTimeStr + "-" + appointmentEndTimeStr;

        PromiseServiceStartTimeDescDTO promiseServiceStartTimeDescDTO = new PromiseServiceStartTimeDescDTO();

        promiseServiceStartTimeDescDTO.setScheduleListServiceTime(String.format(template, scheduleListDateStr));
        promiseServiceStartTimeDescDTO.setServiceStartTimeDesc(String.format(template, dateStr));
        promiseServiceStartTimeDescDTO.setDeliverySampleTime(String.format(template, TimeUtils.dateTimeToStr(appointmentStartTime, TimeFormat.LONG_PATTERN_DOT) + "-" + TimeUtils.dateTimeToStr(appointmentEndTime, TimeFormat.DATE_PATTERN_HM_SIMPLE)));

        return promiseServiceStartTimeDescDTO;
    }

    // 返回具体的文案
    private String formatTemplate(String template, Date date) {
        String dateStr = TimeUtils.dateTimeToStr(date, TimeFormat.DATE_PATTERN_HM_SIMPLE);
        return String.format(template, dateStr);
    }

    // 组装第二类描述文案
    private PromiseServiceStartTimeDescDTO handleWorkOrderDetail(PromiseServiceStartTimeDescRequest request, String template,
                                         PromiseAppointmentTimeDto appointmentTime, Date createTime,
                                          boolean isSameDay, Integer addTime, String template2) {
        // 非有效状态状态不展示
        List<Integer> validWorkStatusList = Lists.newArrayList(AngelWorkStatusEnum.ARRIVED.getType(), AngelWorkStatusEnum.SERVICING.getType(),
                AngelWorkStatusEnum.SERVICED.getType(), AngelWorkStatusEnum.DELIVERING.getType());
        if (!validWorkStatusList.contains(request.getWorkStatus())) {
            return null;
        }
        // 非护士业务直接返回
        if (!AngelWorkTypeEnum.NURSE.getType().equals(request.getWorkType())) {
            return null;
        }

        List<AngelWorkSpecimenDto> angelWorkSpecimens = request.getAngelWorkSpecimens();
        if (CollectionUtils.isEmpty(angelWorkSpecimens)) {
            return null;
        }

        // 检查自配送类型是否存在
        boolean hasSelfDelivery = angelWorkSpecimens.stream()
                .anyMatch(e -> DeliveryTypeEnum.SELF_DELIVERY.getType().equals(e.getShipType()));

        if (!hasSelfDelivery) {
            return null;
        }

        // 复用时间描述构建逻辑
        return buildTimeDescription(template, appointmentTime, createTime, isSameDay, addTime,  Boolean.TRUE, template2);
    }





    /**
     * 通过场景构建修改预约时间入参
     */
    private void buildModifyParamFormScene(ModifyAppointCmd cmd) {
        if(StringUtils.isBlank(cmd.getScene())) {
            return;
        }
        if (ModifyAppointmentTimeSceneEnum.ANGEL_MODIFY_DATE.getName().equalsIgnoreCase(cmd.getScene())) {
            AssertUtils.nonNull(cmd.getWorkId(), "工单id不能为空");
            Long workId = Long.parseLong(cmd.getWorkId());
            AngelWork aw = authorityCheck(workId, cmd.getUserPin());
            AngelWorkDetailQuery query = new AngelWorkDetailQuery();
            query.setWorkId(workId);
            query.setUserPin(cmd.getUserPin());
            AngelCallRecordsDTO angelCallRecordsDTO = angelPromiseApplication.queryCallRecords(query);
            if (angelCallRecordsDTO == null || Boolean.FALSE.equals(angelCallRecordsDTO.getExistCallSuccessRecords())) {
                throw new BusinessException(new DynamicErrorCode(AngelPromiseBizErrorCode.MODIFY_PROMISE_ERROR.getCode(),"请先联系客户协商一致后修改"));
            }
            cmd.setPromiseId(aw.getPromiseId().toString());
            JdhAngelWorkExtVo extVo = aw.getJdhAngelWorkExtVo();
            AngelModifyDateRuleDuccBo angelModifyDateRuleDuccBo = JSON.parseObject(duccConfig.getAngelModifyDateRule(), AngelModifyDateRuleDuccBo.class);
            if(extVo != null && extVo.getModifyTimes() != null && angelModifyDateRuleDuccBo != null) {
                if (angelModifyDateRuleDuccBo.getLimitAngelModifyWorkTimes() != null && extVo.getModifyTimes() >= angelModifyDateRuleDuccBo.getLimitAngelModifyWorkTimes()) {
                    throw new BusinessException(new DynamicErrorCode(AngelPromiseBizErrorCode.MODIFY_PROMISE_ERROR.getCode(), StringUtils.isNotBlank(angelModifyDateRuleDuccBo.getLimitAngelModifyWorkTimesTips()) ? angelModifyDateRuleDuccBo.getLimitAngelModifyWorkTimesTips() : "修改次数已达上限，请客户联系客服修改预约时间"));
                }
            }
            String modifyTimesByDayCache = jimClient.get(getModifyCacheKey(aw.getAngelPin()));
            int modifyTimesByDay = StringUtils.isNotBlank(modifyTimesByDayCache) ? Integer.parseInt(modifyTimesByDayCache) : 0;
            if (angelModifyDateRuleDuccBo != null && angelModifyDateRuleDuccBo.getLimitAngelModifyTimesByDay() != null && modifyTimesByDay >= angelModifyDateRuleDuccBo.getLimitAngelModifyTimesByDay()) {
                throw new BusinessException(new DynamicErrorCode(AngelPromiseBizErrorCode.MODIFY_PROMISE_ERROR.getCode(), StringUtils.isNotBlank(angelModifyDateRuleDuccBo.getLimitAngelModifyTimesByDayTips()) ? angelModifyDateRuleDuccBo.getLimitAngelModifyTimesByDayTips() : "修改次数已达上限，请客户联系客服修改预约时间"));
            }
            cmd.setOperatorRoleType(OperatorRoleTypeEnum.DOCTOR.getType());
        } else if (ModifyAppointmentTimeSceneEnum.USER_MODIFY_DATE.getName().equalsIgnoreCase(cmd.getScene())) {
            AssertUtils.nonNull(cmd.getPromiseId(), "履约单不能为空");
            String userPin = UserPinContext.get();
            JdhPromise promise = jdhPromiseRepository.findPromise(PromiseRepQuery.builder()
                    .promiseId(Long.valueOf(cmd.getPromiseId()))
                    .userPin(userPin)
                    .build());
            if (Objects.isNull(promise)){
                throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
            }
            cmd.setOperatorRoleType(OperatorRoleTypeEnum.USER_SELF.getType());
        }
    }

    /**
     * 数据越权校验
     * @param workId
     * @param angelPin
     * @return
     */
    private AngelWork authorityCheck(Long workId, String angelPin){
        AngelWork work = angelWorkRepository.authorityFind(new AngelWorkIdentifier(workId), angelPin);
        if (Objects.isNull(work)){
            throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
        }
        return work;
    }

    /**
     * 修改预约时间后置逻辑
     * @param cmd
     */
    private void afterLogicFormScene(ModifyAppointCmd cmd, PromiseModifySubmitAbilityContext ctx) {
        if(cmd == null || StringUtils.isBlank(cmd.getScene())) {
            return;
        }
        if (ModifyAppointmentTimeSceneEnum.ANGEL_MODIFY_DATE.getName().equalsIgnoreCase(cmd.getScene())) {
            AngelWork work = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(Long.parseLong(cmd.getWorkId())).build());

            redisUtil.incrExpireAt(getModifyCacheKey(work.getAngelPin()), DateUtil.endOfDay(new Date()));

            JdhAngelWorkExtVo extVo = work.getJdhAngelWorkExtVo();
            if (extVo == null) {
                extVo = new JdhAngelWorkExtVo();
            }
            if (extVo.getModifyTimes() == null) {
                extVo.setModifyTimes(1);
            }  else {
                extVo.setModifyTimes(extVo.getModifyTimes() + 1);
            }
            if (StringUtils.isNotBlank(cmd.getReasonContent())) {
                extVo.setModifyReason(cmd.getReasonContent());
            } else {
                if (cmd.getReasonType() != null) {
                    extVo.setModifyReason(getReasonTypeDesc(cmd.getReasonType()));
                }
            }
            extVo.setModifyReasonType(cmd.getReasonType());
            if (ctx != null && ctx.getSnapshot() != null && ctx.getSnapshot().getAppointmentTime() != null) {
                extVo.setBeforeAppointmentTime(TimeUtils.localDateTimeToDate(ctx.getSnapshot().getAppointmentTime().getAppointmentStartTime()));
            }
            work.setJdhAngelWorkExtVo(extVo);
            angelWorkRepository.save(work);
        }
    }

    /**
     * 缓存key
     * @param angelPin
     * @return
     */
    private String getModifyCacheKey(String angelPin) {
        return RedisKeyEnum.getRedisKey(RedisKeyEnum.ANGEL_MODIFY_DATE_LIMIT_TIMES_BY_DAY, angelPin);
    }

    /**
     * 查询原因内容
     * @param orderType
     * @return
     */
    private String getReasonTypeDesc(Integer orderType) {
        try {
            if (orderType == null) {
                return null;
            }
            DictRequest dictRequest = new DictRequest();
            String key = "angelModifyDateReason";
            dictRequest.setDictGroups(Sets.newHashSet(key));
            Map<String, List<DictInfoDto>> result = dictApplication.queryMultiDictList(dictRequest);
            if (CollUtil.isEmpty(result) || CollUtil.isEmpty(result.get(key))) {
                return null;
            }
            Map<String, DictInfoDto> maps = result.get(key).stream().collect(Collectors.toMap(value -> value.getValue().toString(), Function.identity(), (o, n) -> o));
            if (CollUtil.isEmpty(maps) || !maps.containsKey(orderType.toString())) {
                return null;
            }
            return maps.get(orderType.toString()).getLabel();
        } catch (Exception e) {
            log.error("PromiseApplicationImpl.getReasonTypeDesc 查询词典失败", e);
        }
        return null;
    }

    /**
     * 校验是否可以修改成即时单,如果曾经不是即时单不能修改为即时单
     */
    private void checkIsImmediately(ModifyAppointCmd cmd, JdhPromise snapshot) {
        //校验是否可以修改成即时单,如果曾经不是即时单不能修改为即时单
        if(Boolean.TRUE.equals(cmd.getIsImmediately()) || (cmd.getAppointmentTime() != null && Boolean.TRUE.equals(cmd.getAppointmentTime().getIsImmediately()))) {
            JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(snapshot.getVerticalCode());
            if(Objects.isNull(jdhVerticalBusiness)) {
                throw new BusinessException(PromiseErrorCode.ORDER_LACK_ERROR);
            }
            if(BusinessModeEnum.checkTestAndCare(jdhVerticalBusiness.getBusinessModeCode())) {
                JdOrder jdOrder = jdOrderApplication.queryJdOrderByOrderId(Long.valueOf(snapshot.getSourceVoucherId()));
                if(Objects.nonNull(jdOrder) && OrderTypeEnum.XFYL_VTP_VIRTUAL_ORDER_TYPE.getType().equals(jdOrder.getOrderType())) {
                    PromiseHistoryRepQuery promiseHistoryRepQuery = PromiseHistoryRepQuery.builder()
                            .promiseId(snapshot.getPromiseId())
                            .build();
                    List<JdhPromiseHistory> list = promiseHistoryRepository.findList(promiseHistoryRepQuery);
                    if(CollectionUtils.isNotEmpty(list)) {
                        List<JdhPromiseHistory> collect = list.stream().filter(item -> PromiseEventTypeEnum.PROMISE_USER_SUBMIT_MODIFY.getCode().equals(item.getEventCode())).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(collect)) {
                            JdhPromiseHistory jdhPromiseHistory = collect.stream().sorted(Comparator.comparing(JdhPromiseHistory::getCreateTime)).findFirst().get();
                            PromiseModifyEventBody promiseModifyEventBody = JSON.parseObject(jdhPromiseHistory.getExtend(), PromiseModifyEventBody.class);
                            if(Objects.isNull(promiseModifyEventBody.getBeforeTime().getIsImmediately()) || !promiseModifyEventBody.getBeforeTime().getIsImmediately()){
                                throw new BusinessException(PromiseErrorCode.IMMEDIATELY_ERROR);
                            }
                        }else {
                            if(Objects.isNull(snapshot.getAppointmentTime()) || !snapshot.getAppointmentTime().getIsImmediately()) {
                                throw new BusinessException(PromiseErrorCode.IMMEDIATELY_ERROR);
                            }
                        }
                    }else {
                        if(Objects.isNull(snapshot.getAppointmentTime()) || !snapshot.getAppointmentTime().getIsImmediately()) {
                            throw new BusinessException(PromiseErrorCode.IMMEDIATELY_ERROR);
                        }
                    }
                }else if(Objects.nonNull(jdOrder) && !OrderTypeEnum.XFYL_VTP_VIRTUAL_ORDER_TYPE.getType().equals(jdOrder.getOrderType())) {
                    JdOrderExt jdOrderExtDetail = jdOrderApplication.findJdOrderExtDetail(Long.valueOf(snapshot.getSourceVoucherId()), OrderExtTypeEnum.APPOINTMENT_INFO.getType());
                    if (Objects.isNull(jdOrderExtDetail) || StringUtils.isBlank(jdOrderExtDetail.getExtContext())) {
                        throw new BusinessException(PromiseErrorCode.ORDER_LACK_ERROR);
                    }
                    OrderAppointmentInfoValueObject orderAppointmentInfo = JsonUtil.parseObject(jdOrderExtDetail.getExtContext(), OrderAppointmentInfoValueObject.class);
                    if (Objects.isNull(orderAppointmentInfo.getAppointmentTime()) || Objects.isNull(orderAppointmentInfo.getAppointmentTime().getIsImmediately()) || !orderAppointmentInfo.getAppointmentTime().getIsImmediately()) {
                        throw new BusinessException(PromiseErrorCode.IMMEDIATELY_ERROR);
                    }
                }
            }
        }
    }
}
