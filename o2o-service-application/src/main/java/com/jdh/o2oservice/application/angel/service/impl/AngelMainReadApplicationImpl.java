package com.jdh.o2oservice.application.angel.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.fastjson.JSON;
import com.jdh.o2oservice.application.angel.service.AngelMainReadApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.AngelTypeEnum;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelCountRangeTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelCountTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelProfessionCodeEnum;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelEcologyValuationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhAngelEcologyGloryCountRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelEcologyValuationQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelEcologyGloryCountQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.export.angel.dto.*;
import com.jdh.o2oservice.export.angel.query.AngelMainRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName AngelMainReadApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/24 10:46
 */
@Service
@Slf4j
public class AngelMainReadApplicationImpl implements AngelMainReadApplication {

    @Resource
    private AngelRepository angelRepository;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private JdhAngelEcologyGloryCountRepository jdhAngelEcologyGloryCountRepository;

    @Resource
    private AngelEcologyValuationRepository angelEcologyValuationRepository;

    @Resource
    private DuccConfig duccConfig;

    private static final Integer PAGE_SIZE = 200;

    /**
     * 查询护士主数据
     *
     * @param angelMainRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelMainInfoDto queryAngelMain(AngelMainRequest angelMainRequest) {
        AssertUtils.nonNull(angelMainRequest, "查询护士主页参数不能为空");
        AssertUtils.hasText(angelMainRequest.getO2oAngelId(), "护士id不能为空");
        AssertUtils.nonNull(angelMainRequest.getPromiseId(), "履约单id不能为空");
        AssertUtils.nonNull(angelMainRequest.getOrderId(), "订单号不能为空");

        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(angelMainRequest.getPromiseId());
        angelWorkDBQuery.setAngelIds(Lists.newArrayList(angelMainRequest.getO2oAngelId()));

        AngelMainInfoDto angelMainInfoDto = new AngelMainInfoDto();
        //查询工单
        List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
        if(CollectionUtils.isEmpty(angelWorkList)) {
            log.error("AngelMainReadApplicationImpl -> queryAngelMain,没有查到工单信息.");
            return null;
        }
        AngelWork angelWork = angelWorkList.get(0);
        JdhAngel jdhAngel;
        AngelTypeEnum angelTypeEnum;
        if(AngelWorkTypeEnum.RIDER.getType().equals(angelWork.getWorkType())) {
            angelTypeEnum = AngelTypeEnum.DELIVERY;

            jdhAngel = new JdhAngel();
            jdhAngel.setAngelId(Long.valueOf(angelWork.getAngelId()));
            jdhAngel.setAngelName(angelWork.getAngelName());
            jdhAngel.setHeadImg(duccConfig.getRiderHeadImg());
        }else {
            angelTypeEnum = AngelTypeEnum.NURSE;
            //查询服务者主数据
            JdhAngelRepQuery query = new JdhAngelRepQuery();
            query.setAngelId(Long.valueOf(angelWork.getAngelId()));
            jdhAngel = angelRepository.queryAngelWithProfession(query);
            if(Objects.isNull(jdhAngel)) {
                log.error("AngelMainReadApplicationImpl -> queryAngelMain,没有查询到护士信息.");
                return null;
            }
        }

        //查询服务者的统计数据
        JdhAngelEcologyGloryCountQuery gloryCountQuery = new JdhAngelEcologyGloryCountQuery();
        gloryCountQuery.setAngelId(String.valueOf(angelMainRequest.getO2oAngelId()));
        gloryCountQuery.setCountType(Lists.newArrayList(AngelCountTypeEnum.FINISH_COUNT.getCountType(),
                AngelCountTypeEnum.GREAT_VALUATION_COUNT.getCountType(),
                AngelCountTypeEnum.ON_TIME_COUNT.getCountType(),
                AngelCountTypeEnum.GRAND_SERVICE_COUNT.getCountType()));
        gloryCountQuery.setGrandRangeType(Lists.newArrayList(AngelCountRangeTypeEnum.ALL_COUNT.getCountType()));
        List<JdhAngelEcologyGloryCount> jdhAngelEcologyGloryCounts = jdhAngelEcologyGloryCountRepository.queryAngelGlory(gloryCountQuery);

        //组装服务者基础数据
        AngelMainBaseFloorDto angelMainBaseFloorDto = swapAngelBaseMain(jdhAngel);
        angelMainBaseFloorDto.setAngelType(angelTypeEnum.getType());
        log.error("AngelMainReadApplicationImpl -> queryAngelMain, angelMainBaseFloorDto={}.", JSON.toJSONString(angelMainBaseFloorDto));
        //组装服务者统计数据
        AngelMainTotalFloorDto angelMainTotalFloorDto = swapAngelCountMain(angelWork, jdhAngelEcologyGloryCounts);
        log.error("AngelMainReadApplicationImpl -> queryAngelMain, angelMainTotalFloorDto={}.", JSON.toJSONString(angelMainTotalFloorDto));
        angelMainInfoDto.setAngelMainBaseFloorDto(angelMainBaseFloorDto);
        angelMainInfoDto.setAngelMainTotalFloorDto(angelMainTotalFloorDto);
        return angelMainInfoDto;
    }

    /**
     * 查询服务者评价分页数据
     *
     * @param angelMainRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelMainValuationPageDto getAngelValuationPage(AngelMainRequest angelMainRequest) {
        AssertUtils.nonNull(angelMainRequest, "参数不能空");
        AssertUtils.hasText(angelMainRequest.getO2oAngelId(), "服务者id不能为空");
        AssertUtils.nonNull(angelMainRequest.getPageNum(), "页码不能为空");
        AssertUtils.nonNull(angelMainRequest.getPageSize(), "页长不能为空");

        AngelMainValuationPageDto pageDto = new AngelMainValuationPageDto();
        //首页返回评价汇总数据
        if(angelMainRequest.getPageNum() == CommonConstant.ONE) {
            //计算评级的汇总数据
            getAngelValuationConclusionData(angelMainRequest.getO2oAngelId(), pageDto);
        }

        //查询评价数据
        AngelEcologyValuationQuery query = new AngelEcologyValuationQuery();
        query.setAngelId(angelMainRequest.getO2oAngelId());
        query.setMinRoundScore(BigDecimal.valueOf(CommonConstant.THREE));
        query.setPageNum(angelMainRequest.getPageNum());
        query.setPageSize(angelMainRequest.getPageSize());
        Page<JdhAngelEcologyValuation> pageList = angelEcologyValuationRepository.findPageList(query);
        if(Objects.isNull(pageList) || CollectionUtils.isEmpty(pageList.getRecords())) {
            log.error("AngelMainReadApplicationImpl -> getAngelValuationPage, 没有评价数据");
            return pageDto;
        }

        List<UserValuationDto> userValuationDtoList = Lists.newArrayList();
        pageDto.setUserValuationDtoList(userValuationDtoList);
        List<JdhAngelEcologyValuation> records = pageList.getRecords();
        for (JdhAngelEcologyValuation record : records) {
            List<String> valuationList = Lists.newArrayList();
            UserValuationDto userValuationDto = new UserValuationDto();
            userValuationDto.setValuationTag(valuationList);
            userValuationDto.setUserPin(DesensitizedUtil.idCardNum(record.getUserPin(), 1, 1));
            userValuationDto.setStarNum(record.getAroundScore());
            userValuationDto.setServiceTypeDesc(StringUtils.isNotBlank(record.getBusinessMode()) ? BusinessModeEnum.getEnumByCode(record.getBusinessMode()).getName() : null);
            userValuationDto.setValuationDay(TimeUtils.dateTimeToStr(record.getCreateTime(), TimeFormat.SHORT_PATTERN_LINE));
            if(record.getRespondTimeScore().compareTo(BigDecimal.valueOf(3)) >= 0 ) {
                valuationList.add("快速接单");
            }
            if(record.getHomeTimeScore().compareTo(BigDecimal.valueOf(3)) >= 0 ) {
                valuationList.add("上门准时");
            }
            if(record.getSkillScore().compareTo(BigDecimal.valueOf(3)) >= 0 ) {
                valuationList.add("服务专业");
            }
            if(record.getMannerScore().compareTo(BigDecimal.valueOf(3)) >= 0 ) {
                valuationList.add("态度友善");
            }
            userValuationDtoList.add(userValuationDto);
        }
        return pageDto;
    }

    /**
     * 查询服务者资质信息
     *
     * @param angelMainRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelAuthenticationDto getAngelAuthentication(AngelMainRequest angelMainRequest) {
        AssertUtils.nonNull(angelMainRequest, "查询护士主页参数不能为空");
        AssertUtils.hasText(angelMainRequest.getO2oAngelId(), "护士id不能为空");

        AngelAuthenticationDto angelAuthenticationDto = new AngelAuthenticationDto();
        //查询工单
        JdhAngelRepQuery query = new JdhAngelRepQuery();
        query.setAngelId(Long.valueOf(angelMainRequest.getO2oAngelId()));
        JdhAngel jdhAngel = angelRepository.queryAngelWithProfession(query);
        if(Objects.isNull(jdhAngel)) {
            log.error("AngelMainReadApplicationImpl -> getAngelAuthentication,没有查询到服务者信息");
            return angelAuthenticationDto;
        }

        //组装服务者基础数据
        AngelMainBaseFloorDto angelMainBaseFloorDto = swapAngelBaseMain(jdhAngel);
        log.error("AngelMainReadApplicationImpl -> getAngelAuthentication,angelMainBaseFloorDto={}.", JSON.toJSONString(angelMainBaseFloorDto));
        angelAuthenticationDto.setAngelMainBaseFloorDto(angelMainBaseFloorDto);

        //组装服务者资质信息
        AngelMainAuthenticationFloorDto authenticationFloorDto = swapAuthenticationMain(jdhAngel);
        log.error("AngelMainReadApplicationImpl -> getAngelAuthentication,authenticationFloorDto={}.", JSON.toJSONString(authenticationFloorDto));
        angelAuthenticationDto.setAngelMainAuthenticationFloorDto(authenticationFloorDto);

        return angelAuthenticationDto;
    }

    /**
     * 查询服务者资质信息
     *
     * @param jdhAngel
     * @return
     */
    private AngelMainAuthenticationFloorDto swapAuthenticationMain(JdhAngel jdhAngel) {
        if(jdhAngel == null) {
            log.error("AngelMainReadApplicationImpl -> swapAuthenticationMain, 护士信息不完整");
            return null;
        }
        AngelMainAuthenticationFloorDto dto = new AngelMainAuthenticationFloorDto();

        dto.setTechnicalTitle(jdhAngel.getTechnicalTitle());
        if(StringUtils.isNotBlank(jdhAngel.getCertificateNo())) {
            dto.setCertificateNo(DesensitizedUtil.idCardNum(jdhAngel.getCertificateNo(), 3, 2));
        }
        if(StringUtils.isNotBlank(jdhAngel.getCertificateIssuingAuthority())) {
            dto.setCertificateIssuingAuthority(DesensitizedUtil.idCardNum(jdhAngel.getCertificateIssuingAuthority(), 0, 3));
        }
        dto.setGrade(jdhAngel.getGrade());
        dto.setSpeciality(jdhAngel.getSpeciality());

        return dto;
    }

    /**
     * 计算服务者的评价汇总数据
     *
     * @param o2oAngelId
     * @param pageDto
     */
    private void getAngelValuationConclusionData(String o2oAngelId, AngelMainValuationPageDto pageDto) {
        AngelEcologyValuationQuery query = new AngelEcologyValuationQuery();
        query.setAngelId(o2oAngelId);
        query.setPageSize(PAGE_SIZE);

        int pageNo = CommonConstant.ONE;
        BigDecimal resTimeScore = BigDecimal.ZERO;
        BigDecimal homeTimeScore = BigDecimal.ZERO;
        BigDecimal skillScore = BigDecimal.ZERO;
        BigDecimal mannerScore = BigDecimal.ZERO;

        BigDecimal totalNum = BigDecimal.ZERO;
        do{
            query.setPageNum(pageNo);
            Page<JdhAngelEcologyValuation> pageList = angelEcologyValuationRepository.findPageList(query);
            if(Objects.isNull(pageList) || CollectionUtils.isEmpty(pageList.getRecords())) {
                break;
            }
            List<JdhAngelEcologyValuation> records = pageList.getRecords();

            //快速接单
            long resTimeCount = records.stream().filter(item -> Objects.nonNull(item.getRespondTimeScore()) && item.getRespondTimeScore().compareTo(BigDecimal.valueOf(3)) >= 0).count();
            resTimeScore = resTimeScore.add(BigDecimal.valueOf(resTimeCount));
            log.info("AngelMainReadApplicationImpl -> getAngelValuationConclusionData, resTimeScore={}", resTimeScore.doubleValue());

            //上门准时
            long homeTimeCount = records.stream().filter(item -> Objects.nonNull(item.getHomeTimeScore()) && item.getHomeTimeScore().compareTo(BigDecimal.valueOf(3)) >= 0).count();
            homeTimeScore = homeTimeScore.add(BigDecimal.valueOf(homeTimeCount));
            log.info("AngelMainReadApplicationImpl -> getAngelValuationConclusionData, homeTimeScore={}", homeTimeScore.doubleValue());

            //服务专业
            long skillCount = records.stream().filter(item -> Objects.nonNull(item.getSkillScore()) && item.getSkillScore().compareTo(BigDecimal.valueOf(3)) >= 0).count();
            skillScore = skillScore.add(BigDecimal.valueOf(skillCount));
            log.info("AngelMainReadApplicationImpl -> getAngelValuationConclusionData, skillScore={}", skillScore.doubleValue());

            //态度友善
            long mannerCount = records.stream().filter(item -> Objects.nonNull(item.getMannerScore()) && item.getSkillScore().compareTo(BigDecimal.valueOf(3)) >= 0).count();
            mannerScore = mannerScore.add(BigDecimal.valueOf(mannerCount));
            log.info("AngelMainReadApplicationImpl -> getAngelValuationConclusionData, mannerScore={}", mannerScore.doubleValue());

            //总条数
            totalNum = totalNum.add(BigDecimal.valueOf(records.size()));

            if(records.size() < PAGE_SIZE) {
                log.info("AngelMainReadApplicationImpl -> getAngelValuationConclusionData, 没有下一页了停止循环查询.size={}", records.size());
                break;
            }
            pageNo += 1;
        }while(pageNo <= CommonConstant.FIVE);

        if(totalNum.compareTo(BigDecimal.ZERO) == 0) {
            log.info("AngelMainReadApplicationImpl -> getAngelValuationConclusionData, 护士没有评价数据");
            pageDto.setRespondTimeRate(BigDecimal.valueOf(100));
            pageDto.setHomeTImeRate(BigDecimal.valueOf(100));
            pageDto.setSkillRate(BigDecimal.valueOf(100));
            pageDto.setMannerRate(BigDecimal.valueOf(100));
            pageDto.setGoodValuationRate(BigDecimal.valueOf(100));
            return;
        }

        pageDto.setRespondTimeRate(resTimeScore.divide(totalNum, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        pageDto.setHomeTImeRate(homeTimeScore.divide(totalNum, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        pageDto.setSkillRate(skillScore.divide(totalNum, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        pageDto.setMannerRate(mannerScore.divide(totalNum, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));

        JdhAngelEcologyGloryCountQuery glory = new JdhAngelEcologyGloryCountQuery();
        glory.setAngelId(o2oAngelId);
        glory.setGrandRangeType(Lists.newArrayList(AngelCountRangeTypeEnum.ALL_COUNT.getCountType()));
        glory.setCountType(Lists.newArrayList(AngelCountTypeEnum.GREAT_VALUATION_COUNT.getCountType()));
        List<JdhAngelEcologyGloryCount> jdhAngelEcologyGloryCounts = jdhAngelEcologyGloryCountRepository.queryAngelGlory(glory);
        if(CollectionUtils.isEmpty(jdhAngelEcologyGloryCounts)) {
            log.error("AngelMainReadApplicationImpl -> getAngelValuationConclusionData, 没有查询到好评率.glory={}", JSON.toJSONString(glory));
            pageDto.setGoodValuationRate(BigDecimal.valueOf(100));
        }else {
            BigDecimal goodRate = jdhAngelEcologyGloryCounts.get(0).getCountRate().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP);
            if(goodRate.compareTo(BigDecimal.valueOf(90)) >= 0) {
                pageDto.setGoodValuationRate(goodRate);
            }
        }
    }

    /**
     * 组装服务者统计信息
     *
     * @param angelWork
     * @param jdhAngelEcologyGloryCounts
     * @return
     */
    private AngelMainTotalFloorDto swapAngelCountMain(AngelWork angelWork, List<JdhAngelEcologyGloryCount> jdhAngelEcologyGloryCounts) {
        if(CollectionUtils.isEmpty(jdhAngelEcologyGloryCounts)) {
            log.error("AngelMainReadApplicationImpl -> swapAngelCountMain, 服务者没有统计数据");
            return null;
        }

        AngelMainTotalFloorDto countFloorDto = new AngelMainTotalFloorDto();

        //完单量
        List<JdhAngelEcologyGloryCount> finishCountList = jdhAngelEcologyGloryCounts.stream()
                .filter(item -> AngelCountTypeEnum.FINISH_COUNT.getCountType().equals(item.getCountType()))
                .sorted(Comparator.comparing(JdhAngelEcologyGloryCount::getCountDate).reversed())
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(finishCountList)) {
            JdhAngelEcologyGloryCount jdhAngelEcologyGloryCount = finishCountList.get(0);
            countFloorDto.setFinishTotal(getFinishNum(jdhAngelEcologyGloryCount.getCountDenominator().intValue()));
        }

        //好评率
        List<JdhAngelEcologyGloryCount> valuationCountList = jdhAngelEcologyGloryCounts.stream()
                .filter(item -> AngelCountTypeEnum.GREAT_VALUATION_COUNT.getCountType().equals(item.getCountType()))
                .sorted(Comparator.comparing(JdhAngelEcologyGloryCount::getCountDate).reversed())
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(valuationCountList)) {
            JdhAngelEcologyGloryCount jdhAngelEcologyGloryCount = valuationCountList.get(0);
            countFloorDto.setGoodValuationRate(getGreatValuationRate(jdhAngelEcologyGloryCount.getCountRate()));
        }

        //准时率
        List<JdhAngelEcologyGloryCount> onTimeCountList = jdhAngelEcologyGloryCounts.stream()
                .filter(item -> AngelCountTypeEnum.ON_TIME_COUNT.getCountType().equals(item.getCountType()))
                .sorted(Comparator.comparing(JdhAngelEcologyGloryCount::getCountDate).reversed())
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(onTimeCountList)) {
            JdhAngelEcologyGloryCount jdhAngelEcologyGloryCount = onTimeCountList.get(0);
            countFloorDto.setOnTimeRate(getOnTimeRate(jdhAngelEcologyGloryCount.getCountRate()));
        }

        //服务时长
        if(Objects.isNull(angelWork.getWorkType()) || angelWork.getWorkType().equals(AngelWorkTypeEnum.RIDER.getType())) {
            return countFloorDto;
        }
        List<JdhAngelEcologyGloryCount> DurationCountList = jdhAngelEcologyGloryCounts.stream()
                .filter(item -> AngelCountTypeEnum.GRAND_SERVICE_COUNT.getCountType().equals(item.getCountType()))
                .sorted(Comparator.comparing(JdhAngelEcologyGloryCount::getCountDate).reversed())
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(DurationCountList)) {
            JdhAngelEcologyGloryCount jdhAngelEcologyGloryCount = DurationCountList.get(0);
            countFloorDto.setTotalDuration(getServiceDuration(jdhAngelEcologyGloryCount.getCountDenominator()));
        }
        return countFloorDto;
    }

    /**
     * 处理服务时长
     *
     * @param countDenominator
     * @return
     */
    private String getServiceDuration(BigDecimal countDenominator) {
        if(Objects.isNull(countDenominator)) {
            return null;
        }
        BigDecimal roundedNumber = countDenominator.divide(BigDecimal.valueOf(60), 0, RoundingMode.HALF_UP);
        if(roundedNumber.compareTo(BigDecimal.valueOf(100)) < 0) {
            return null;
        }

        if(roundedNumber.compareTo(BigDecimal.valueOf(1000)) > 0) {
            return "999+";
        }
        return String.valueOf(roundedNumber.longValue());
    }

    /**
     * 处理准时率
     *
     * @param countRate
     * @return
     */
    private String getOnTimeRate(BigDecimal countRate) {
        if(Objects.isNull(countRate)) {
            return null;
        }
        BigDecimal roundedNumber = countRate.multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP);
        return String.valueOf(roundedNumber.longValue());
//        if(roundedNumber.compareTo(BigDecimal.valueOf(90)) >= 0) {
//        }
//        return null;
    }

    /**
     * 处理好评率
     *
     * @param countRate
     * @return
     */
    private String getGreatValuationRate(BigDecimal countRate) {
        if(Objects.isNull(countRate)) {
            return null;
        }
        BigDecimal roundedNumber = countRate.multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP);
        return String.valueOf(roundedNumber.longValue());
//        if(roundedNumber.compareTo(BigDecimal.valueOf(90)) >= 0) {
//        }
//        return null;
    }

    /**
     * 处理完单量
     *
     * @param finishNum
     * @return
     */
    private Integer getFinishNum(int finishNum) {
        if(finishNum < 10) {
            return null;
        }
        return finishNum;
    }

    /**
     * 组装服务者基础数据
     *
     * @param jdhAngel
     * @return
     */
    private AngelMainBaseFloorDto swapAngelBaseMain(JdhAngel jdhAngel) {
        AngelMainBaseFloorDto angelMainBaseFloorDto = new AngelMainBaseFloorDto();

        if(Objects.isNull(jdhAngel)) {
            log.error("AngelMainReadApplicationImpl -> swapAngelBaseMain, 没有查询到服务者信息");
            return angelMainBaseFloorDto;
        }

        if(StringUtils.isNotBlank(jdhAngel.getAngelName())) {
            String angelName = DesensitizedUtil.chineseName(jdhAngel.getAngelName());
            if(StringUtils.isNotBlank(angelName) && angelName.length() > 2) {
                angelName = angelName.substring(0, 2);
            }
            angelMainBaseFloorDto.setAngelName(angelName);
        }
        angelMainBaseFloorDto.setAngelGender(jdhAngel.getGender());
        angelMainBaseFloorDto.setAngelGenderDesc(GenderEnum.getDescOfType(jdhAngel.getGender()));
        if(Objects.nonNull(jdhAngel.getAngeExtBo())) {
            angelMainBaseFloorDto.setAngelNation(jdhAngel.getAngeExtBo().getNation());
            angelMainBaseFloorDto.setGreatDirection(jdhAngel.getAngeExtBo().getGreatDirection());
        }
        if(StringUtils.isNotBlank(jdhAngel.getIdCard())) {
            Pattern pattern = Pattern.compile("(\\d{6})(\\d{2})(\\d{2})(\\d{2})(\\d{2})\\d{2}(\\d{1})[X0-9]");
            Matcher matcher = pattern.matcher(jdhAngel.getIdCard());
            angelMainBaseFloorDto.setAngelCertificate(DesensitizedUtil.idCardNum(jdhAngel.getIdCard(), 3, 4));
            if (matcher.matches()) {
                String birthday = matcher.group(2) + matcher.group(3) + "年" + matcher.group(4) + "月"  + matcher.group(5) + "日";
                String birthdayStar = matcher.group(2) + "**年" + "*月";
                log.info("AngelMainReadApplicationImpl -> swapAngelBaseMain, birthday={}", birthday);
                angelMainBaseFloorDto.setBornDate(birthdayStar);
            } else {
                log.info("AngelMainReadApplicationImpl -> swapAngelBaseMain, 身份证号码格式不正确。");
            }
        }
        if(StringUtils.isNotBlank(jdhAngel.getCardIssuingAuthority())) {
            angelMainBaseFloorDto.setAngelIssuingAuthority(DesensitizedUtil.idCardNum(jdhAngel.getCardIssuingAuthority(), 0, 3));
        }
        angelMainBaseFloorDto.setAngelImg(jdhAngel.getHeadImg());
        if(CollectionUtils.isNotEmpty(jdhAngel.getJdhAngelProfessionRelList())) {
            JdhAngelProfessionRel jdhAngelProfessionRel = jdhAngel.getJdhAngelProfessionRelList().get(0);
            angelMainBaseFloorDto.setProfessionCode(jdhAngelProfessionRel.getProfessionCode());
            angelMainBaseFloorDto.setProfessionName(jdhAngelProfessionRel.getProfessionName());
            angelMainBaseFloorDto.setProfessionTitleCode(jdhAngelProfessionRel.getProfessionTitleCode());
            angelMainBaseFloorDto.setProfessionTitleName(jdhAngelProfessionRel.getProfessionTitleName());
        }
        angelMainBaseFloorDto.setOneDepartmentCode(jdhAngel.getOneDepartmentCode());
        angelMainBaseFloorDto.setOneDepartmentName(jdhAngel.getOneDepartmentName());
        angelMainBaseFloorDto.setTwoDepartmentCode(jdhAngel.getTwoDepartmentCode());
        angelMainBaseFloorDto.setTwoDepartmentName(jdhAngel.getTwoDepartmentName());
        angelMainBaseFloorDto.setAngelIntroduction(jdhAngel.getIntroduction());

        Set<Long> angelMainPageBlack = duccConfig.getAngelMainPageBlack();
        if(CollectionUtils.isNotEmpty(angelMainPageBlack) && angelMainPageBlack.contains(jdhAngel.getAngelId())) {
            angelMainBaseFloorDto.setAuthenticationTag(CommonConstant.ONE);
        }else if(angelAuthenticationTag(jdhAngel)) {
            angelMainBaseFloorDto.setAuthenticationTag(CommonConstant.ONE);
            angelMainBaseFloorDto.setAuthenticationUrl(MessageFormat.format(duccConfig.getAngelAuthenticationUrl(), String.valueOf(jdhAngel.getAngelId())));
        }else {
            angelMainBaseFloorDto.setAuthenticationTag(CommonConstant.TWO);
        }

        return angelMainBaseFloorDto;
    }

    /**
     * 服务者是否认证
     *
     * @param jdhAngel
     * @return
     */
    private boolean angelAuthenticationTag(JdhAngel jdhAngel) {
        if(CollectionUtils.isEmpty(jdhAngel.getJdhAngelProfessionRelList())) {
            return false;
        }

        JdhAngelProfessionRel jdhAngelProfessionRel = jdhAngel.getJdhAngelProfessionRelList().get(0);
        if(AngelProfessionCodeEnum.SMHS.getCode().equals(Integer.valueOf(jdhAngelProfessionRel.getProfessionCode()))) {
            return StringUtils.isNotBlank(jdhAngel.getCertificateIssuingAuthority())
                    && StringUtils.isNotBlank(jdhAngel.getCertificateNo());
        }else if(AngelProfessionCodeEnum.SMKFS.getCode().equals(Integer.valueOf(jdhAngelProfessionRel.getProfessionCode()))) {
            return StringUtils.isNotBlank(jdhAngel.getCertificateNo())
                    && StringUtils.isNotBlank(jdhAngel.getGrade())
                    && StringUtils.isNotBlank(jdhAngel.getSpeciality());
        }
        return false;
    }

    public static void main(String[] args) {
        String aa = "1234567898765432345";
        String s = DesensitizedUtil.idCardNum(aa, 3, 4);
        System.out.println(s);
    }
}
