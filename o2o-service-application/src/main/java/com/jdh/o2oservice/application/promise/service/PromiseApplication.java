package com.jdh.o2oservice.application.promise.service;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.export.angelpromise.cmd.ResetPromiseCmd;
import com.jdh.o2oservice.export.angelpromise.cmd.UpdatePromiseStatusCmd;
import com.jdh.o2oservice.export.product.dto.AgencyAppointDateDto;
import com.jdh.o2oservice.export.product.dto.PromiseServiceStartTimeDescDTO;
import com.jdh.o2oservice.export.product.query.AgencyQueryDateRequest;
import com.jdh.o2oservice.export.product.query.PromiseDangerLevelRequest;
import com.jdh.o2oservice.export.product.query.PromiseServiceStartTimeDescRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseServiceChildDto;
import com.jdh.o2oservice.export.promise.dto.*;
import com.jdh.o2oservice.export.promise.cmd.*;
import com.jdh.o2oservice.export.promise.query.CompletePromiseRequest;
import com.jdh.o2oservice.export.promise.query.ListServiceChildrenRequest;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.PromiseListRequest;

import com.jdh.o2oservice.export.trade.dto.AvaiableAppointmentTimeDTO;
import java.util.List;
import java.util.Set;

/**
 * JdhPromiseApplication
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
public interface PromiseApplication {


    /**
     * 自动预约
     * @param cmd
     * @return
     */
    Boolean autoSubmit(AutoSubmitCmd cmd);

    /**
     * 提交履约数据
     * @param cmd
     * @return
     */
    Boolean submit(SubmitPromiseCmd cmd);


    /**
     * 修改预约
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    Boolean modify(ModifyAppointCmd cmd);


    /**
     * 取消预约
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    Boolean cancel(CancelAppointCmd cmd);

    /**
     * 预约结果回调
     * @param cmd
     * @return
     */
    Boolean callBack(PromiseCallbackCmd cmd);

    /**
     * 核销
     * @param cmd
     * @return
     */
    Boolean writeOff(PromiseWriteOffCmd cmd);

    /**
     * 用户核销
     * @param cmd
     * @return
     */
    Boolean userWriteOff(PromiseUserWriteOffCmd cmd);

    /**
     * 核销结果同步（POP-LOC预约码的核销是通过Q异步通知的）
     * @param cmd
     * @return
     */
    Boolean writeOffSync(PromiseWriteOffSyncCmd cmd);

    /**
     * (强制)作废履约单
     * 场景：订单退款后发起作废。
     * @param cmd
     * @return
     */
    void invalid(PromiseInvalidCmd cmd);



    /**
     * 冻结履约单，依赖于服务单冻结。（强制冻结）
     * 场景：订单退款时发起冻结。
     * @param cmd
     * @return
     */
    FreezeStateDto freeze(PromiseFreezeCmd cmd);


    /**
     * 解冻履约单
     * @param cmd
     * @return
     */
    Boolean unFreeze(PromiseUnFreezeCmd cmd);

    /**
     * 延期
     *
     * @param cmd CMD
     * @return {@link Boolean}
     */
    Boolean delay(PromiseDelayCmd cmd);

    /**
     * 发起派单
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    Boolean dispatch(PromiseDispatchCmd cmd);

    /**
     * 修改预约发起派单
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    Boolean modifyServiceDateDispatch(PromiseDispatchCmd cmd);

    /**
     * 创建实验室检测单
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    Boolean createMedPromise(PromiseCreateMedPromiseCmd cmd);

    /**
     * 用户条码绑定
     * @param cmd
     * @return
     */
    Boolean bindSpecimenCode(PromiseBindSpecimenCodeCmd cmd);

    /**
     * 验证条码
     * @param cmd
     * @return
     */
    List<PatientSpecimenCodeDto> verifySpecimenCode(PromiseVerifySpecimenCodeCmd cmd);

    /**
     * 通过promiseId查找
     *
     * @param promiseIdRequest promiseIdRequest
     * @return {@link PromiseDto}
     */
    PromiseDto findByPromiseId(PromiseIdRequest promiseIdRequest);

    /**
     * 通过promiseId查找
     *
     * @param promiseIdRequest promiseIdRequest
     * @return {@link PromiseDto}
     */
    PromiseDto findPromiseByPromiseId(PromiseIdRequest promiseIdRequest);

    /**
     *
     * @param promiseId
     * @return
     */
    String findSourceVoucherIdByPromiseId(Long promiseId);

    /**
     * findByPromiseList
     *
     * @param request 请求
     * @return {@link List}<{@link PromiseDto}>
     */
    List<PromiseDto> findByPromiseList(PromiseListRequest request);

    /**
     * findByPromiseListByPromiseIds
     *
     * @param promiseIds 请求
     * @return {@link List}<{@link PromiseDto}>
     */
    List<PromiseDto> findByPromiseListByPromiseIds(List<Long> promiseIds);

    /**
     * 查询患者列表
     * @param promisePatientId
     * @return
     */
    List<PromisePatientDto> listPromisePatient(Set<Long> promisePatientId);

    /**
     * 查询子套餐列表
     * @param request
     * @return
     */
    List<PromiseServiceChildDto> listServiceChildren(ListServiceChildrenRequest request);

    /**
     *
     * @param query
     * @return
     */
    List<PromiseDto> findJdhPromiseList(PromiseRepQuery query);

    /**
     * 查询完整履约信息
     * @param request
     * @return
     */
    CompletePromiseDto queryCompletePromise(CompletePromiseRequest request);

    /**
     * 查询完整履约信息列表
     * @param request
     * @return
     */
    List<CompletePromiseDto> queryCompletePromiseList(CompletePromiseRequest request);

    /**
     * 转换预约时间信息
     * @param parentDateId
     * @param avaiableAppointmentTimeDTO
     * @return
     */
    List<AgencyAppointDateDto> changeAndFilterDate(String parentDateId, AvaiableAppointmentTimeDTO avaiableAppointmentTimeDTO);

    /**
     * 查询预约时间
     * @param request
     * @return
     */
    List<AgencyAppointDateDto> queryAvailableTime(AgencyQueryDateRequest request);

    /**
     * 修改预约时间
     * @param cmd
     * @return
     */
    Boolean modifyAppointmentTime(AgentModifyPromiseCmd cmd);

    /**
     * 检查履约单风险等级
     *
     * @param request
     * @return
     */
    Boolean checkPromiseDangerLevel(PromiseDangerLevelRequest request);

    /**
     * 查询履约单风险等级
     *
     * @param request
     * @return
     */
    List<Integer> queryPromiseDangerLevel(PromiseDangerLevelRequest request);

    /**
     * 查询promise服务开始时间描述
     * @param request
     * @return
     */
    PromiseServiceStartTimeDescDTO queryPromiseServiceStartTimeDesc(PromiseServiceStartTimeDescRequest request);

    /**
     * 将promise 设置yn=0 非快检使用
     * @return
     */
    Boolean delPromise(ResetPromiseCmd resetPromiseCmd);

    /**
     * 将履约单状态改成1 非快检使用
     * @return
     */
    Boolean updatePromiseStatus(UpdatePromiseStatusCmd updatePromiseStatusCmd);
}
