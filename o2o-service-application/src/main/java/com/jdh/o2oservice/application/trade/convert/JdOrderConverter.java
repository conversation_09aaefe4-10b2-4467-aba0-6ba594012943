package com.jdh.o2oservice.application.trade.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.jdh.o2oservice.core.domain.trade.context.AppointmentTimeContext;
import com.jdh.o2oservice.core.domain.trade.context.OrderInfoQueryContext;
import com.jdh.o2oservice.core.domain.trade.model.*;
import com.jdh.o2oservice.export.dispatch.dto.DispatchAppointmentTimeDto;
import com.jdh.o2oservice.export.trade.dto.*;
import com.jdh.o2oservice.export.trade.query.AppointmentTimeParam;
import com.jdh.o2oservice.export.trade.query.JdOrderSaveParam;
import com.jdh.o2oservice.export.trade.query.ReceiveOrderAndAppointmentParam;
import com.jdh.o2oservice.export.trade.query.SubmitOrderParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

/**
 * JdOrderConverter
 *
 * <AUTHOR>
 * @version 2024/4/23 23:28
 **/
@Mapper
public interface JdOrderConverter {

    JdOrderConverter INSTANCE = Mappers.getMapper(JdOrderConverter.class);

    @Mappings({
            @Mapping(source = "appointmentTimeParam", target = "appointmentTimeContext"),
            @Mapping(source = "patientParamList", target = "patients")
    })
    OrderInfoQueryContext convertToOrderInfoQueryContext(JdOrderSaveParam jdOrderSaveParam);

    @Mappings({
            @Mapping(source = "remarkParam.remark", target = "remark")
    })
    JdOrderSaveParam convertToJdOrderSaveParam(ReceiveOrderAndAppointmentParam param);

    AppointmentTimeContext convertToAppointmentTimeParam(AppointmentTimeParam appointmentTimeParam);

    default JdOrderSaveParam convertToJdOrderSaveParam(SubmitOrderParam submitOrderParam) {
        JdOrderSaveParam jdOrderSaveParam = new JdOrderSaveParam();
        jdOrderSaveParam.setAppointmentTimeParam(submitOrderParam.getAppointmentTimeParam());
        if (submitOrderParam.getRemarkParam() != null) {
            jdOrderSaveParam.setRemark(submitOrderParam.getRemarkParam().getRemark());
        }
        jdOrderSaveParam.setPartnerSourceOrderId(submitOrderParam.getPartnerSourceOrderId());
        jdOrderSaveParam.setPartnerSource(submitOrderParam.getPartnerSource());
        jdOrderSaveParam.setPatientIds(submitOrderParam.getPatientIds());
        jdOrderSaveParam.setUserPin(submitOrderParam.getUserPin());
        jdOrderSaveParam.setIntendedNurse(submitOrderParam.getIntendedNurse());
        jdOrderSaveParam.setPreSampleFlag(submitOrderParam.getPreSampleFlag());
        jdOrderSaveParam.setMedicalCertificateFileIds(submitOrderParam.getMedicalCertificateFileIds());
        jdOrderSaveParam.setSaleChannelId(submitOrderParam.getSaleChannelId());
        jdOrderSaveParam.setSaleChannelId(submitOrderParam.getSaleChannelId());
        jdOrderSaveParam.setEnvType(submitOrderParam.getEnvType());
        jdOrderSaveParam.setOuterPatientId(submitOrderParam.getOuterPatientId());
        jdOrderSaveParam.setEid(submitOrderParam.getEid());
        jdOrderSaveParam.setClientIp(submitOrderParam.getClientIp());
        jdOrderSaveParam.setAgent(submitOrderParam.getAgent());
        jdOrderSaveParam.setLocation(submitOrderParam.getLocation());
        return jdOrderSaveParam;
    }

    JdOrderDTO JdOrderToDTO(JdOrder orderDetail);

    List<JdOrderDTO> JdOrderToDTOList(List<JdOrder> jdOrderList);

    List<JdOrderMoneyDTO> JdOrderMoneyToDTOList(List<JdOrderMoney> jdOrderList);
    /**
     * 地址信息
     *
     * @param jdOrderExt
     */
    default AddressInfoDTO getAddressInfoDTO(JdOrderExt jdOrderExt) {
        if (jdOrderExt == null || StringUtils.isBlank(jdOrderExt.getExtContext())) {
            return null;
        }
        return JSON.parseObject(jdOrderExt.getExtContext(), CalcTradeServiceFeeDTO.class).getAddressInfo();
    }

    /**
     * 地址信息
     *
     * @param jdOrderExt
     */
    default DispatchAppointmentTimeDto getAppointmentTimeDto(JdOrderExt jdOrderExt) {
        if (jdOrderExt == null || StringUtils.isBlank(jdOrderExt.getExtContext())) {
            return null;
        }
        return JSON.parseObject(jdOrderExt.getExtContext(), CalcTradeServiceFeeDTO.class).getAppointmentTime();
    }

    /**
     * 费项信息
     *
     * @param jdOrderExt
     */
    default List<JdOrderServiceFeeInfoDTO> getJdOrderFeeDTO(JdOrderExt jdOrderExt) {
        if (jdOrderExt == null || StringUtils.isBlank(jdOrderExt.getExtContext())) {
            return null;
        }
        return JSONArray.parseArray(jdOrderExt.getExtContext(), JdOrderServiceFeeInfoDTO.class);
    }

    /**
     * 计算退款金额
     * @param jdOrderRefundTaskList
     * @return
     */
    default BigDecimal getRefundAmount(List<JdOrderRefundTask> jdOrderRefundTaskList) {
        if (CollectionUtils.isEmpty(jdOrderRefundTaskList)) {
            return BigDecimal.ZERO;
        }
        return jdOrderRefundTaskList.stream().map(JdOrderRefundTask::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
