package com.jdh.o2oservice.application.provider.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.jim.cli.Cluster;
import com.jd.jsf.gd.util.JsonUtils;
import com.jdh.o2oservice.application.provider.service.ProviderQueryApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.QuickCheckStatusMappingConfig;
import com.jdh.o2oservice.base.ducc.model.UsePromiseGoSwitch;
import com.jdh.o2oservice.base.enums.PrivacyNumberBindModelEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseFull;
import com.jdh.o2oservice.core.domain.medpromise.query.CompositeMedicalPromiseEsQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.es.JdMedicalPromiseEsRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.provider.enums.ProviderErrorCode;
import com.jdh.o2oservice.core.domain.provider.model.Provider;
import com.jdh.o2oservice.core.domain.provider.model.ProviderPromise;
import com.jdh.o2oservice.core.domain.provider.model.ProviderPromiseIdentifier;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderPromiseRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderRepository;
import com.jdh.o2oservice.core.domain.provider.repository.query.ProviderPromiseListQuery;
import com.jdh.o2oservice.core.domain.provider.rpc.VenderBasicRpc;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.UserName;
import com.jdh.o2oservice.core.domain.support.businessMode.ServiceHomeTypeDomainService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.reach.model.PrivacyNumber;
import com.jdh.o2oservice.core.domain.support.reach.repository.query.PrivacyNumberQuery;
import com.jdh.o2oservice.core.domain.support.reach.service.ReachDomainService;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.via.enums.ViaFloorEnum;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.export.provider.dto.ProviderPromisePrivacyNumberDto;
import com.jdh.o2oservice.export.provider.dto.ProviderPromiseUserDto;
import com.jdh.o2oservice.export.provider.dto.StationCompositeMedicalPromiseDTO;
import com.jdh.o2oservice.export.provider.query.PageCompositeMedicalPromiseRequest;
import com.jdh.o2oservice.export.provider.query.PageProviderPromiseRequest;
import com.jdh.o2oservice.export.provider.query.PromisePrivacyNumberRequest;
import com.jdh.o2oservice.export.provider.query.QueryPromiseUserRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: yangxiyu
 * @date: 2024/1/19 11:07 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class ProviderQueryApplicationImpl implements ProviderQueryApplication {

    /**
     *
     */
    @Resource
    private ProviderPromiseRepository providerPromiseRepository;
    /**
     *
     */
    @Resource
    private VenderBasicRpc venderBasicRpc;
    /**
     *
     */
    @Resource
    private ProviderRepository providerRepository;
    /**
     * voucherRepository
     */
    @Resource
    private VoucherRepository voucherRepository;


    /**
     * jdOrderItemRepository
     */
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;

    /**
     * reachDomainService
     */
    @Autowired
    @Lazy
    private ReachDomainService reachDomainService;
    @Resource
    private JdMedicalPromiseEsRepository jdMedicalPromiseEsRepository;
    @Resource
    private AngelShipRepository angelShipRepository;
    @Resource
    private PromiseGoRpcService promiseGoRpcService;
    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;
    @Resource
    private DuccConfig duccConfig;

    @Resource
    private ServiceHomeTypeDomainService serviceHomeTypeDomainService;

    /**
     * 缓存
     */
    @Resource
    private Cluster jimClient;


    /**
     * 根据查询状态匹配真实状态集合
     *
     * @param viaStatus
     * @param statusMapping
     * @return
     */
    private List<Integer> findSourceStatus(Integer viaStatus, List<ViaStatusMapping> statusMapping) {
        for (ViaStatusMapping promiseStatusMapping : statusMapping) {
            if (Objects.equals(viaStatus, promiseStatusMapping.getViaStatus())) {
                return promiseStatusMapping.getStatusList();
            }
        }
        return Collections.emptyList();
    }

    /**
     * 查询用户信息
     *
     * @param request
     * @return
     */
    @Override
    public Response<ProviderPromiseUserDto> queryUser(QueryPromiseUserRequest request) {
        Provider provider = providerRepository.findByUserPin(request.getUserPin(), request.getEnvType(), request.getRoleType());
        if (Objects.isNull(provider)) {
            throw new BusinessException(ProviderErrorCode.PROVIDER_ACCESS_DENIED);
        }
        ProviderPromise promise = providerPromiseRepository.findByAppointmentId(Long.parseLong(request.getAppointmentId()), provider.getChannelNo());
        log.info("ProviderQueryApplicationImpl->queryUser promise={}", JsonUtils.toJSONString(promise));
        ProviderPromiseUserDto dto = new ProviderPromiseUserDto();
        if (Objects.nonNull(promise.getUser()) && Objects.nonNull(promise.getUser().getUserName())) {
            dto.setUserName(promise.getUser().getUserName().encrypt());
        }
        if (Objects.nonNull(promise.getUser()) && Objects.nonNull(promise.getUser().getPhoneNumber())) {
            dto.setUserPhone(promise.getUser().getPhoneNumber().encrypt());
        }
        if (Objects.nonNull(promise.getUser()) && Objects.nonNull(promise.getUser().getCredentialNum())) {
            dto.setUserCredentialNo(promise.getUser().getCredentialNum().encrypt());
        }
        return ResponseUtil.buildSuccResponse(dto);
    }

    /**
     * 查询隐私号码
     *
     * @param request 请求
     * @return {@link ProviderPromisePrivacyNumberDto}
     */
    @Override
    public ProviderPromisePrivacyNumberDto queryPrivacyNumber(PromisePrivacyNumberRequest request) {
        // 目前仅AXE模式，这里做兜底处理，后续如果有其他模式可以在入参里传入
        if (request.getBindModel() == null) {
            request.setBindModel(PrivacyNumberBindModelEnum.AXE.getModelType());
        }
        //参数校验
        AssertUtils.nonNull(request.getPromiseId(), ProviderErrorCode.PRIVACY_NUMBER_PROMISE_ID_NULL_ERROR);
        ProviderPromise providerPromise = providerPromiseRepository.find(ProviderPromiseIdentifier.builder().promiseId(Long.parseLong(request.getPromiseId())).build());
        if (Objects.isNull(providerPromise)) {
            throw new BusinessException(ProviderErrorCode.PRIVACY_NUMBER_PROMISE_INFO_NOT_EXIT_ERROR);
        }
        if (Objects.isNull(providerPromise.getUser()) || Objects.isNull(providerPromise.getUser().getPhoneNumber())) {
            throw new BusinessException(ProviderErrorCode.PRIVACY_NUMBER_PROMISE_USER_PHONE_NOT_EXIT_ERROR);
        }

        //调用触达
        PrivacyNumberQuery query = PrivacyNumberQuery.builder()
                .userPin(request.getUserPin())
                .phoneNumber(providerPromise.getUser().getPhoneNumber().getPhone())
                .businessId(UUID.fastUUID().toString())
                .verticalCode(providerPromise.getVerticalCode())
                .serviceType(providerPromise.getServiceType())
                .bindModel(request.getBindModel())
                .build();
        PrivacyNumber privacyNumber = reachDomainService.getPrivacyNumber(query);

        return ProviderPromisePrivacyNumberDto.builder()
                .secretNo(privacyNumber.getSecretNo())
                .extension(privacyNumber.getExtension())
                .build();
    }


    /**
     * 构建查询query
     *
     * @param request
     * @param viaConfig
     * @param provider
     * @return
     */
    @Override
    public ProviderPromiseListQuery buildQuery(PageProviderPromiseRequest request, ViaConfig viaConfig, Provider provider) {
        List<ViaFloorInfo> floorList = viaConfig.getFloorList();
        // 筛选条件处理
        ViaFloorInfo condition = floorList.stream().filter(f -> Objects.equals(f.getFloorCode(), ViaFloorEnum.JM_APPOINT_PAGE_CONDITION.getFloorCode())).findFirst().get();
        ViaFloorConfig floorConfig = condition.getFloorConfigList().get(0);
        ViaFormItem item = floorConfig.getFormItemList().stream().filter(e -> StringUtils.equals(e.getParamField(), "promiseStatus")).findFirst().get();

        ProviderPromiseListQuery query = new ProviderPromiseListQuery();
        query.setPageNum(request.getPageNum());
        query.setPageSize(request.getPageSize());
        query.setChannelNo(provider.getChannelNo());
        query.setOrderByCreateTimeDesc(Boolean.TRUE);
        if (StringUtils.isNotBlank(request.getAppointmentId())) {
            query.setAppointmentId(Long.valueOf(request.getAppointmentId()));
        }
        query.setServiceType(viaConfig.getServiceType());
        if (Objects.nonNull(request.getPromiseStatus())) {
            query.setInPromiseStatus(findSourceStatus(request.getPromiseStatus(), item.getStatusMapping()));
        }

        if (StringUtils.isNotBlank(request.getStartTime())) {
            query.setStartTime(TimeUtils.strToDate(request.getStartTime()));
        }
        if (StringUtils.isNotBlank(request.getEndTime())) {
            LocalDate endDay = LocalDate.parse(request.getEndTime(), TimeFormat.SHORT_PATTERN_LINE.formatter);
            LocalDateTime endTime = LocalDateTime.of(endDay, LocalTime.of(23, 59, 59));
            query.setEndTime(TimeUtils.localDateTimeToDate(endTime));
        }

        if (StringUtils.isNotBlank(request.getStoreId())) {
            query.setStoreId(Long.valueOf(request.getStoreId()));
        }

        if (StringUtils.isNotBlank(request.getOrderId())) {
            List<Long> voucherIds = parseVoucherIds(Long.parseLong(request.getOrderId()), request.getVerticalCode());
            query.setVoucherIds(voucherIds);
        }

        if (CollectionUtils.isNotEmpty(request.getHaveStoreIds())) {
            query.setHaveStoreIds(request.getHaveStoreIds());
        }
        //资源库检索
        log.info("ProviderQueryApplicationImpl->buildQuery query={}", JSON.toJSONString(query));
        return query;
    }

    @Override
    public PageDto<StationCompositeMedicalPromiseDTO> pageCompositeMedicalPromise(PageCompositeMedicalPromiseRequest request) {
        CompositeMedicalPromiseEsQuery query = new CompositeMedicalPromiseEsQuery();
        BeanUtils.copyProperties(request, query);
        convertStatus(request, query);

        PageDto<MedicalPromiseFull> pageDto = jdMedicalPromiseEsRepository.pageCompositeMedicalPromise(query);
        if (PageInfoUtil.isEmpty(pageDto)) {
            return PageDto.getEmptyPage();
        }

        // 根据workId拿最新的ship数据
        Map<Long, AngelShip> shipMap = Maps.newHashMap();

        List<String> verticalCodeList = Lists.newArrayList();
        Set<Long> workIds = pageDto.getList().stream().peek(e -> verticalCodeList.add(e.getVerticalCode())).filter(e -> Objects.nonNull(e.getWorkId()))
                .map(e -> Long.valueOf(e.getWorkId())).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(workIds)) {
            AngelShipDBQuery shipDBQuery = new AngelShipDBQuery();
            shipDBQuery.setWorkIds(workIds);
            log.info("ProviderQueryApplicationImpl->pageCompositeMedicalPromise workIds={}", JSON.toJSONString(workIds));
            List<AngelShip> ships = angelShipRepository.findList(shipDBQuery);
            shipMap = ships.stream()
                    .collect(Collectors.toMap(AngelShip::getWorkId, Function.identity(),
                            (o, n) -> o.getCreateTime().after(n.getCreateTime()) ? o : n));
        }


        // 送达实验室之前，查询eta预估时间
        Map<Long, LabPromisegoBo> etaMap = findEtaTime(pageDto.getList());


        Map<Long, AngelShip> finalShipMap = shipMap;
        return PageInfoUtil.convert(pageDto, (t) -> {

            StationCompositeMedicalPromiseDTO dto = new StationCompositeMedicalPromiseDTO();
            dto.setMedicalPromiseId(t.getMedicalPromiseId());
            dto.setSerialNum(t.getSerialNum());
            dto.setServiceItemName(t.getServiceItemName());
            dto.setSpecimenCode(t.getSpecimenCode());
            dto.setCollectionTime(EntityUtil.getFiledDefaultNull(t.getCollectionTime(), TimeUtils::dateTimeToStr));

            // 聚合状态
            CompositeStatus compositeStatus = transferCompositeStatus(t);
            if (Objects.nonNull(compositeStatus)) {
                dto.setCompositeStatus(compositeStatus.getStatus());
                dto.setCompositeStatusLabel(compositeStatus.getDesc());
            }

            // 送达时间和预计送达时间
            String deliveryStoreTime = EntityUtil.getFiledDefaultNull(t.getDeliveryStoreTime(), TimeUtils::dateTimeToStr);
            if (StringUtils.isBlank(deliveryStoreTime) && Objects.nonNull(etaMap)) {
                LabPromisegoBo etaBo = etaMap.get(Long.valueOf(t.getMedicalPromiseId()));
                if (Objects.nonNull(etaBo)) {
                    if (Objects.nonNull(etaBo.getTermScript())) {
                        deliveryStoreTime = etaBo.getTermScript().getScriptContent();
                    }
                    boolean verticalCodeBool = serviceHomeTypeDomainService.getServiceHomeVerticalCode("XFYL_HOME_SELF_TEST_TRANSPORT").equalsIgnoreCase(t.getVerticalCode());
                    if (verticalCodeBool && Objects.nonNull(etaBo.getCurrScript()) && StringUtils.isNotBlank(etaBo.getCurrScript().getScriptContent())) {
                        dto.setCompositeStatusLabel(dto.getCompositeStatusLabel() == null ? "" : dto.getCompositeStatusLabel() + "(" + etaBo.getCurrScript().getScriptContent() + ")");
                    }
                }
            }
            dto.setDeliveryStoreTime(deliveryStoreTime);

            dto.setCheckTime(EntityUtil.getFiledDefaultNull(t.getCheckTime(), TimeUtils::dateTimeToStr));
            dto.setReportTime(EntityUtil.getFiledDefaultNull(t.getReportTime(), TimeUtils::dateTimeToStr));
            // 收样超时和检测超时
            buildTestTimeOut(t, dto, compositeStatus);

            // 如果workId不为空，则获取最新创建的ship，并填充骑手姓名，电话数据
            if (StringUtils.isNotBlank(t.getWorkId())) {
                AngelShip ship = finalShipMap.get(Long.valueOf(t.getWorkId()));
                if (Objects.nonNull(ship)) {
                    DeliveryTypeEnum deliveryTypeEnum = DeliveryTypeEnum.getEnumByType(ship.getType());
                    if (Objects.nonNull(deliveryTypeEnum)) {
                        dto.setDeliveryType(deliveryTypeEnum.getDesc());
                    }
                    dto.setTransferNameEncrypt(UserName.encrypt(ship.getTransferName()));
                    dto.setTransferPhoneEncrypt(PhoneNumber.encrypt(ship.getTransferPhone()));
                    dto.setFinishCode(ship.getFinishCode());
                }
            }
            return dto;
        });
    }


    /**
     * 根据orderId查询服务单，再根据服务单查询履约单
     *
     * @param orderId
     * @return
     */
    private List<Long> parseVoucherIds(Long orderId, String verticalCode) {
        List<JdOrderItem> items = jdOrderItemRepository.listByOrderId(orderId);
        log.info("ProviderQueryApplicationImpl->parseVoucherIds items={}", JSON.toJSONString(items));
        if (CollectionUtil.isEmpty(items)) {
            return Lists.newArrayList(0L);
        }
        List<Long> itemIds = items.stream().map(JdOrderItem::getOrderItemId).collect(Collectors.toList());
        List<String> sourceVoucherId = Lists.newArrayList();
        for (Long itemId : itemIds) {
            sourceVoucherId.add(String.valueOf(itemId));
        }
        List<JdhVoucher> vouchers = voucherRepository.listBySourceVoucherId(sourceVoucherId, verticalCode);
        log.info("ProviderQueryApplicationImpl->parseVoucherIds vouchers={}", JSON.toJSONString(vouchers));
        if (CollectionUtil.isEmpty(vouchers)) {
            return Lists.newArrayList(0L);
        }

        return vouchers.stream().map(JdhVoucher::getVoucherId).collect(Collectors.toList());
    }


    private void convertStatus(PageCompositeMedicalPromiseRequest request, CompositeMedicalPromiseEsQuery query) {
        AssertUtils.hasText(request.getCompositeCode(), "compositeCode is require");
        String compositeCode = request.getCompositeCode();
        Integer compositeStatus = request.getCompositeStatus();
        List<Integer> workStatus = null;
        List<Integer> shipStatus = null;
        List<Integer> medicalPromiseStatus = null;
        if (StringUtils.equals(compositeCode, COMPOSITE_ALL)) {
            // 待骑手接单
            if (Objects.equals(compositeStatus, CompositeStatus.WAITING_RECEIVE_ORDER.getStatus())) {
                workStatus = Lists.newArrayList(AngelWorkStatusEnum.INIT.getType(),
                        AngelWorkStatusEnum.WAIT_RECEIVE.getType());

                query.setMatchNullWorkStatus(Boolean.TRUE);
                // 待骑手取货
            } else if (Objects.equals(compositeStatus, CompositeStatus.WAITING_RECEIVE_GOODS.getStatus())) {
                workStatus = Lists.newArrayList(AngelWorkStatusEnum.RECEIVED.getType(), AngelWorkStatusEnum.WAIT_SERVICE.getType()
                        , AngelWorkStatusEnum.SERVICING.getType(), AngelWorkStatusEnum.SERVICED.getType());

                // 配送中
            } else if (Objects.equals(compositeStatus, CompositeStatus.DELIVERY_IN_PROGRESS.getStatus())) {
                shipStatus = Lists.newArrayList(AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus());
                // 已送达、待收样
            } else if (Objects.equals(compositeStatus, CompositeStatus.DELIVERED.getStatus())) {
                shipStatus = Lists.newArrayList(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus());
                medicalPromiseStatus = Lists.newArrayList(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus()
                        , MedicalPromiseStatusEnum.COLLECTED.getStatus());

                // 检测中
            } else if (Objects.equals(compositeStatus, CompositeStatus.CHECK_ING.getStatus())) {
                medicalPromiseStatus = Lists.newArrayList(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus());
                // 已出报告
            } else if (Objects.equals(compositeStatus, CompositeStatus.COMPLETED.getStatus())) {
                medicalPromiseStatus = Lists.newArrayList(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                //作废、退款
            } else if (Objects.equals(compositeStatus, CompositeStatus.INVALID.getStatus())) {
                medicalPromiseStatus = Lists.newArrayList(MedicalPromiseStatusEnum.INVALID.getStatus());
            }

            // 待收样
        } else if (StringUtils.equals(compositeCode, WAITING_RECEIVE)) {
            shipStatus = Lists.newArrayList(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus());
            medicalPromiseStatus = Lists.newArrayList(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus()
                    , MedicalPromiseStatusEnum.COLLECTED.getStatus());
            // 已检测未出报告
        } else if (StringUtils.equals(compositeCode, WAITING_REPORT)) {
            medicalPromiseStatus = Lists.newArrayList(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus());
        }

        query.setWorkStatus(workStatus);
        query.setShipStatus(shipStatus);
        query.setMedicalPromiseStatus(medicalPromiseStatus);

    }

    /**
     * 构建收样超时和检测超时信息
     *
     * @param t
     * @param dto
     * @param compositeStatus
     */
    private void buildTestTimeOut(MedicalPromiseFull t, StationCompositeMedicalPromiseDTO dto, CompositeStatus compositeStatus) {
        if (Objects.isNull(compositeStatus)){
            return;
        }

        if (Objects.nonNull(t.getWaitingTestTimeOutStatus())) {
            // 收样超时
            if (Objects.equals(t.getWaitingTestTimeOutStatus(), YnStatusEnum.YES.getCode())) {
                dto.setWaitingTestTimeOutStatusLabel("收样超时");
                // 收样未超时，判断状态；已收样：未超时、已送达待收样：计算倒计时、未送达：未超时
            } else {
                if (compositeStatus == CompositeStatus.DELIVERED) {
                    dto.setWaitingTestTimeOutStatusLabel(null);
                } else {
                    dto.setWaitingTestTimeOutStatusLabel("未超时");
                }
            }
        }

        // 检测超时
        if (Objects.nonNull(t.getTestingTimeOutStatus())) {
            if (Objects.equals(t.getTestingTimeOutStatus(), YnStatusEnum.YES.getCode())) {
                dto.setTestingTimeOutStatusLabel("检测超时");
            } else {
                if (compositeStatus == CompositeStatus.CHECK_ING) {
                    dto.setTestingTimeOutStatusLabel(null);
                } else {
                    dto.setTestingTimeOutStatusLabel("未超时");
                }
                dto.setTestingTimeOutStatusLabel("未超时");
            }
        }
        dto.setTestingTimeOutDate(EntityUtil.getFiledDefaultNull(t.getTestingTimeOutDate(), TimeUtils::dateTimeToStr));
        dto.setWaitingTestTimeOutDate(EntityUtil.getFiledDefaultNull(t.getWaitingTestTimeOutDate(), TimeUtils::dateTimeToStr));
    }

    /**
     * 查询ETA预估时间
     *
     * @param list
     * @return
     */
    public Map<Long, LabPromisegoBo> findEtaTime(List<MedicalPromiseFull> list) {

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        // 查询eta列表
        List<MedicalPromiseFull> queryEtaList = new ArrayList<>();
        // 查询ship表列表
        List<MedicalPromiseFull> queryAngelShipList = new ArrayList<>();
        list.forEach(s -> {
            // 快递检测暂无eta数据直接使用ship中物流同步的信息
            boolean verticalCodeBool = serviceHomeTypeDomainService.getServiceHomeVerticalCode("XFYL_HOME_SELF_TEST_TRANSPORT").equalsIgnoreCase(s.getVerticalCode());
            if (verticalCodeBool) {
                queryAngelShipList.add(s);
            } else {
                queryEtaList.add(s);
            }
        });

        List<String> verticalCode = Lists.newArrayList();
        // 样本送达实验室之前才需要展示ETA预测的数据，运单为空或者运单送达之前
        List<MedicalPromiseFull> queryEtaShipStatusList = queryEtaList.stream().filter(e ->
                        Objects.isNull(e.getShipStatus()) || AngelShipStatusEnum.finishBeforeStatus().contains(e.getShipStatus())
                )
                .peek(e -> verticalCode.add(e.getVerticalCode())).collect(Collectors.toList());
        List<MedicalPromiseFull> queryAngelShipStatusList = queryAngelShipList.stream().filter(e -> !Objects.equals(CommonConstant.ONE, e.getReportStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(queryEtaShipStatusList) && CollectionUtils.isEmpty(queryAngelShipStatusList)) {
            return Collections.emptyMap();
        }

        Map<Long, LabPromisegoBo> retMap = new HashMap<>(1);
        List<LabPromisegoRequestBo> params = Lists.newArrayList();
        for (MedicalPromiseFull medicalPromiseFull : queryEtaShipStatusList) {
            JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(medicalPromiseFull.getVerticalCode());

            // 实验室端的ETA开关
            UsePromiseGoSwitch usePromiseGoSwitch = duccConfig.getUsePromiseGoSwitch();
            if (Objects.isNull(usePromiseGoSwitch)) {
                continue;
            }
            // 如果不支持当前模式则终止
            if (!usePromiseGoSwitch.supportLabUse(verticalBusiness.getBusinessModeCode())) {
                continue;
            }
            String aggregateStatus = parseStatus(medicalPromiseFull, verticalBusiness);
            if (Objects.isNull(aggregateStatus)) {
                continue;
            }
            LabPromisegoRequestBo labPromisegoRequestBo = LabPromisegoRequestBo.builder()
                    .aggregateStatus(aggregateStatus)
                    .promiseId(Long.valueOf(medicalPromiseFull.getPromiseId()))
                    .medicalPromiseId(Long.valueOf(medicalPromiseFull.getMedicalPromiseId()))
                    .businessMode(verticalBusiness.getBusinessModeCode())
                    .queryTermScript(Boolean.TRUE)
                    .build();

            Date startTime = medicalPromiseFull.getAppointmentStartTime();
            if (Objects.nonNull(startTime)) {
                PromisegoRequestAppointmentTime appointmentTime = new PromisegoRequestAppointmentTime();
                appointmentTime.setDateType(Integer.valueOf(medicalPromiseFull.getDateType()));
                Boolean immediately = Boolean.valueOf(medicalPromiseFull.getIsImmediately());
                appointmentTime.setImmediately(immediately);
                appointmentTime.setAppointmentStartTime(TimeUtils.dateToLocalDateTime(medicalPromiseFull.getAppointmentStartTime()));
                appointmentTime.setAppointmentEndTime(TimeUtils.dateToLocalDateTime(medicalPromiseFull.getAppointmentEndTime()));
                labPromisegoRequestBo.setAppointmentTime(appointmentTime);
            }

            if (StringUtils.isNotBlank(medicalPromiseFull.getStoreAddr())) {
                PromisegoRequestAddress address = PromisegoRequestAddress.builder()
                        .provinceId(medicalPromiseFull.getProvinceCode())
                        .cityId(medicalPromiseFull.getCityCode())
                        .countyId(medicalPromiseFull.getDistrictCode())
                        .townId(medicalPromiseFull.getTownCode())
                        .provinceName(medicalPromiseFull.getProvinceName())
                        .cityName(medicalPromiseFull.getCityName())
                        .countyName(medicalPromiseFull.getDistrictName())
                        .townName(medicalPromiseFull.getTownName())
                        .fullAddress(medicalPromiseFull.getStoreAddr())
                        .build();
                labPromisegoRequestBo.setAppointmentAddress(address);
            }

            params.add(labPromisegoRequestBo);
        }

        List<String> angelShipParam = new ArrayList<>();
        for (MedicalPromiseFull medicalPromiseFull : queryAngelShipStatusList) {
            angelShipParam.add(medicalPromiseFull.getPromiseId());
        }

        if (CollectionUtils.isEmpty(params) && CollUtil.isEmpty(angelShipParam)) {
            return Collections.emptyMap();
        }

        try {
            if (CollUtil.isNotEmpty(params)) {
                Map<Long, LabPromisegoBo> etaRetMap = promiseGoRpcService.listLabPromisego(params);
                if (CollUtil.isNotEmpty(etaRetMap)) {
                    retMap.putAll(etaRetMap);
                }
            }
            buildShipMessage(angelShipParam, retMap);
            return retMap;
        } catch (Exception e) {
            log.warn("findEtaTime error msg={}", e.getMessage());
            return Collections.emptyMap();
        }
    }

    /**
     * 构建运单信息
     * @param angelShipParam
     * @param retMap
     */
    private void buildShipMessage(List<String> angelShipParam, Map<Long, LabPromisegoBo> retMap) {
        try {
            if (CollUtil.isNotEmpty(angelShipParam)) {
                List<String> cacheValues = jimClient.mGet(angelShipParam.stream().map(s -> RedisKeyEnum.getRedisKey(RedisKeyEnum.STORE_QUERY_MEDICAL_LIST_SHIP_INFO, s)).toArray(String[]::new));
                int index = 0;
                Map<Long, LabPromisegoBo> shipMessageMap = new HashMap<>();
                for (String s : angelShipParam) {
                    String shipMessage = cacheValues.get(index);
                    LabPromisegoBo labPromisegoBo = new LabPromisegoBo();
                    ScriptBo scriptBo = new ScriptBo();
                    scriptBo.setScriptContent(shipMessage);
                    labPromisegoBo.setCurrScript(scriptBo);
                    shipMessageMap.put(Long.parseLong(s), labPromisegoBo);
                    index++;
                }
                if (CollectionUtil.isNotEmpty(shipMessageMap)) {
                    retMap.putAll(shipMessageMap);
                }
            }
        } catch (Exception e) {
            log.error("ProviderQueryApplicationImpl buildShipMessage exception", e);
        }
    }

    private String parseStatus(MedicalPromiseFull promiseFull, JdhVerticalBusiness verticalBusiness) {
        Map<String, Object> expParam = new HashMap<>();
        if (Objects.nonNull(promiseFull.getWorkStatus())) {
            expParam.put("angelWorkStatus", Integer.valueOf(promiseFull.getWorkStatus()));
        }
        if (Objects.nonNull(promiseFull.getShipStatus())) {
            expParam.put("angelShipStatus", promiseFull.getShipStatus());
        }
        expParam.put("medicalPromiseStatus", Integer.valueOf(promiseFull.getMedicalPromiseStatus()));
        expParam.put("freeze", promiseFull.getMedPromiseFreeze());
        expParam.put("businessModeCode", verticalBusiness.getBusinessModeCode());

        for (QuickCheckStatusMappingConfig statusMappingConfig : duccConfig.getQuickCheckStatusMappingConfig()) {
            if ((Boolean) AviatorEvaluator.compile(statusMappingConfig.getStatusExpression(), Boolean.TRUE).execute(expParam)) {
                return statusMappingConfig.getPromiseGoAggregateStatus();
            }
        }
        log.warn("ProviderQueryApplicationImpl->parseStatus 未匹配到状态 promiseFull={}, verticalBusiness={}", JSON.toJSONString(promiseFull), verticalBusiness);

        return null;
    }


    /**
     * 骑手检测：骑手接单后到骑手点击配送中，都属于待取货
     * 护士检测：待取货状态包含护士接单、上门、服务、呼叫二段运力等节点
     * @param promiseFull
     * @return
     */
    public CompositeStatus transferCompositeStatus(MedicalPromiseFull promiseFull) {
        Integer workStatus = Objects.isNull(promiseFull.getWorkStatus()) ? null : Integer.valueOf(promiseFull.getWorkStatus());
        if (Objects.equals(promiseFull.getMedicalPromiseStatus(), MedicalPromiseStatusEnum.COMPLETED.getStatusStr())) {
            return CompositeStatus.COMPLETED;
        } else if (Objects.equals(promiseFull.getMedicalPromiseStatus(), MedicalPromiseStatusEnum.INVALID.getStatusStr())) {
            return CompositeStatus.INVALID;
        } else if (Objects.equals(promiseFull.getMedicalPromiseStatus(), MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatusStr())) {
            return CompositeStatus.CHECK_ING;

        } else if ((Objects.equals(promiseFull.getMedicalPromiseStatus(), MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatusStr())
                || Objects.equals(promiseFull.getMedicalPromiseStatus(), MedicalPromiseStatusEnum.COLLECTED.getStatusStr()))
                && Objects.equals(promiseFull.getShipStatus(), AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus())) {
            return CompositeStatus.DELIVERED;

        } else if (Objects.equals(promiseFull.getShipStatus(), AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus())) {
            return CompositeStatus.DELIVERY_IN_PROGRESS;

            // 待取货
        } else if (Objects.equals(workStatus, AngelWorkStatusEnum.RECEIVED.getType())
                || Objects.equals(workStatus, AngelWorkStatusEnum.WAIT_SERVICE.getType())
                || Objects.equals(workStatus, AngelWorkStatusEnum.SERVICING.getType())
                || Objects.equals(workStatus, AngelWorkStatusEnum.SERVICED.getType())) {
            return CompositeStatus.WAITING_RECEIVE_GOODS;

        } else if (Objects.isNull(workStatus) || Objects.equals(workStatus, AngelWorkStatusEnum.INIT.getType())
                || Objects.equals(workStatus, AngelWorkStatusEnum.WAIT_RECEIVE.getType())) {
            return CompositeStatus.WAITING_RECEIVE_ORDER;
        }
        return null;
    }

    public enum CompositeStatus {

        /**
         * 待骑手接单
         * 骑手检测场景对应AngelShipStatusEnum#SHIP_ORDER_INIT，AngelShipStatusEnum#WAITING_RECEIVE_ORDER
         * 护士上门检测场景，对应AngelShipStatusEnum#SHIP_ORDER_INIT，AngelShipStatusEnum#WAITING_RECEIVE_ORDER 和 null(即没有ship产生时，也是待接单)
         */
        WAITING_RECEIVE_ORDER(2, "待接单"),
        /**
         * 待骑手取货
         */
        WAITING_RECEIVE_GOODS(5, "待取货"),
        /**
         * 配送中
         */
        DELIVERY_IN_PROGRESS(8, "配送中"),
        /**
         * 已送达（待收样）
         */
        DELIVERED(11, "已送达，待收样"),
        /**
         * 检测中
         */
        CHECK_ING(14, "检测中"),
        /**
         * 已出报告
         */
        COMPLETED(17, "报告已出"),
        /**
         * 无效的作废的（退款）
         */
        INVALID(20, "已退款"),
        ;
        private final Integer status;
        private final String desc;

        CompositeStatus(Integer status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public Integer getStatus() {
            return status;
        }

        public String getDesc() {
            return desc;
        }


    }

}
