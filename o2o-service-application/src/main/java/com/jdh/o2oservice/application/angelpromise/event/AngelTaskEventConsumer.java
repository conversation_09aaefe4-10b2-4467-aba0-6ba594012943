package com.jdh.o2oservice.application.angelpromise.event;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.EventConsumerRetryTemplate;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelTaskStateBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelTaskStatusEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelTaskDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelWorkDomainService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:AngelTaskEventConsumer
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/19 20:40
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AngelTaskEventConsumer {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelWorkDomainService angelWorkDomainService;

    @Resource
    private AngelTaskDomainService angelTaskDomainService;

    @PostConstruct
    public void registerEventConsumer(){
        eventConsumerRegister.register(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_IN_SERVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "workTaskInServed", this::handleWorkInServed, Boolean.FALSE, Boolean.FALSE));

        eventConsumerRegister.register(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_DONE_SERVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "workTaskDoneServed", this::handleWorkDoneServed, Boolean.FALSE, Boolean.FALSE,
                        EventConsumerRetryTemplate.exponentialRetryInstance(4, 1000, 2.0, 30000)));

        eventConsumerRegister.register(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_DELIVERY,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "workTaskDelivery", this::handleWorkDelivery, Boolean.FALSE, Boolean.FALSE,
                        EventConsumerRetryTemplate.exponentialRetryInstance(4, 1000, 2.0, 30000)));

        eventConsumerRegister.register(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_FINISH_SERVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "workTaskFinish", this::handleWorkFinish, Boolean.FALSE, Boolean.TRUE));

        eventConsumerRegister.register(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_REFUND_SERVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "workTaskRefund", this::workTaskRefund, Boolean.TRUE, Boolean.FALSE));
    }

    /**
     * 上门结束
     *
     * @param event
     */
    private void handleWorkDoneServed(Event event) {
        angelWorkDomainService.handleWorkStatus(event, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_DONE_SERVED, AngelWorkStatusEnum.SERVICED);
        AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(Long.valueOf(event.getAggregateId())));
        if(Objects.isNull(angelWork)){
            return;
        }
        AngelTaskStatusEventBody angelTaskStatusEventBody = JSON.parseObject(event.getBody(), AngelTaskStatusEventBody.class);
        if(Objects.equals(AngelWorkTypeEnum.CARE.getType(), angelWork.getWorkType())){
            AngelTaskStatusContext angelTaskStatusContext = AngelTaskStatusContext.builder().build();
            angelTaskStatusContext.setWorkId(angelTaskStatusEventBody.getWorkId());

            List<AngelTaskStateBo> angelTaskStateBoList = Lists.newArrayList();
            AngelTaskStateBo angelTaskStateBo = AngelTaskStateBo.builder()
                    .taskId(angelTaskStatusEventBody.getTaskId())
                    .taskStatus(AngelTaskStatusEnum.COMPLETED.getType())
                    .build();
            angelTaskStateBoList.add(angelTaskStateBo);

            angelTaskStatusContext.setAngelTaskStateBoList(angelTaskStateBoList);
            angelTaskStatusContext.setAngelTaskEventTypeEnum(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_FINISH_SERVED);
            log.info("[AngelTaskEventConsumer.handleWorkDoneServed],执行任务单状态到完成!angelTaskStatusContext={}", JSON.toJSONString(angelTaskStatusContext));
            angelTaskDomainService.executeTask(angelTaskStatusContext);
        }else if(Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())) {
            AngelTaskStatusContext angelTaskStatusContext = AngelTaskStatusContext.builder().build();
            angelTaskStatusContext.setWorkId(angelTaskStatusEventBody.getWorkId());

            List<AngelTaskStateBo> angelTaskStateBoList = Lists.newArrayList();
            AngelTaskStateBo angelTaskStateBo = AngelTaskStateBo.builder()
                    .taskId(angelTaskStatusEventBody.getTaskId())
                    .taskStatus(AngelTaskStatusEnum.CONFIRMED.getType())
                    .build();
            angelTaskStateBoList.add(angelTaskStateBo);

            angelTaskStatusContext.setAngelTaskStateBoList(angelTaskStateBoList);
            angelTaskStatusContext.setAngelTaskEventTypeEnum(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_DELIVERY);
            log.info("[AngelTaskEventConsumer.handleWorkDoneServed],执行任务单状态到送检中!angelTaskStatusContext={}", JSON.toJSONString(angelTaskStatusContext));
            angelTaskDomainService.executeTask(angelTaskStatusContext);
        }
    }

    /**
     * 任务单退款
     *
     * @param event
     */
    private void workTaskRefund(Event event) {
        angelWorkDomainService.handleWorkStatus(event, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_REFUND_SERVED, AngelWorkStatusEnum.REFUNDED);
    }

    /**
     * 处理工单服务中
     *
     * @param event
     */
    private void handleWorkInServed(Event event) {
        angelWorkDomainService.handleWorkStatus(event, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_IN_SERVED, AngelWorkStatusEnum.SERVICING);
    }

    /**
     * 处理工单完成
     * @param event
     */
    private void handleWorkFinish(Event event) {
        angelWorkDomainService.handleWorkStatus(event, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_FINISH_SERVED, AngelWorkStatusEnum.COMPLETED);
    }

    /**
     * 处理工单送检中
     * @param event
     */
    private void handleWorkDelivery(Event event) {
        angelWorkDomainService.handleWorkStatus(event, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_DELIVERING, AngelWorkStatusEnum.DELIVERING);
    }
}
