package com.jdh.o2oservice.application.settlement.listener;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.NumberUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleErrorCode;
import com.jdh.o2oservice.core.domain.settlement.enums.SettlementAdjustTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettlementFileBizTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.ImportAngelSettleAdjust;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 护士调账导入
 *
 * <AUTHOR>
 * @date 2024-08-22 11:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class ImportAngelSettleAdjustListener extends AnalysisEventListener<ImportAngelSettleAdjust> {
    /**
     * 导入数据总量
     */
    private int total = 0;
    /**
     * 导入数据成功总量
     */
    private int successTotal = 0;
    /**
     * 导入数据百分比
     */
    private Integer percentage = 0;
    /**
     * 失败文件地址
     */
    private String failUrl;
    /**
     * 解析是否有失败
     */
    private Boolean isFail = false;
    /**
     * 失败列表
     */
    private List<ImportAngelSettleAdjust> failList = new ArrayList<>();
    /**
     * 成功列表
     */
    private List<ImportAngelSettleAdjust> succList = new ArrayList<>();
    /**
     * userPin
     */
    private String userPin;
    /**
     * filePreSignedUrlDto
     */
    private FilePreSignedUrlDto filePreSignedUrlDto;

    /**
     * 有效行数
     */
    private int actualValidRowCount = 0;

    /**
     * 构造函数
     */
    public ImportAngelSettleAdjustListener(String userPin) {
        this.userPin = userPin;
    }

    /**
     *
     * @param importAngelSettleAdjust importAngelSettleAdjust
     * @param analysisContext     analysisContext
     */
    @Override
    public void invoke(ImportAngelSettleAdjust importAngelSettleAdjust, AnalysisContext analysisContext) {
        // 步骤1：空行检测
        if (isRowEmpty(importAngelSettleAdjust)) {
            log.info("ImportAngelSettleAdjustListener invoke 跳过空行，行号: {}", analysisContext.readRowHolder().getRowIndex());
            return; // 直接跳过空行处理
        }
        actualValidRowCount++; // 只统计非空行
        log.info("ImportAngelSettleAdjustListener invoke 处理有效行 {}，当前总有效行数: {}", analysisContext.readRowHolder().getRowIndex(), actualValidRowCount);
        String failReason = "";
        try {
            Integer rowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
            log.info("ImportAngelSettleAdjustListener#invoke importAngelSettleAdjust={} rowNumber={}", JSON.toJSONString(importAngelSettleAdjust), rowNumber);
            if (actualValidRowCount > 99) {
                throw new BusinessException(SettleErrorCode.ANGEL_ASJUST_DATA_OUT);
            }
            StringBuilder stringBuilder = new StringBuilder();
            if(StringUtil.isEmpty(importAngelSettleAdjust.getAngelId())){
                stringBuilder.append("护士ID为空,");
            }else{
                JdhAngelDto angelDto = getAngelDto(Long.parseLong(importAngelSettleAdjust.getAngelId()));
                if(Objects.isNull(angelDto)){
                    stringBuilder.append("查不到护士信息或姓名不一致,");
                }else{
                    if(StringUtil.isNotBlank(importAngelSettleAdjust.getAngelName())){
                        if(!importAngelSettleAdjust.getAngelName().equals(angelDto.getAngelName())){
                            stringBuilder.append("护士信息与姓名不一致,");
                        }
                    }
                    importAngelSettleAdjust.setJobNature(angelDto.getJobNature());
                    importAngelSettleAdjust.setNethpDocId(angelDto.getNethpDocId());
                }
            }
            if(StringUtil.isEmpty(importAngelSettleAdjust.getAngelName())){
                stringBuilder.append("护士姓名为空,");
            }
            String adjustType = importAngelSettleAdjust.getAdjustTypeDesc();
            if(StringUtil.isEmpty(adjustType)){
                stringBuilder.append("调账项为空,");
            }else{
                SettlementAdjustTypeEnum adjustTypeEnum = SettlementAdjustTypeEnum.getSettleTypeEnumByDesc(adjustType);
                if(Objects.isNull(adjustTypeEnum)){
                    stringBuilder.append("调账项不符合,");
                }else {
                    importAngelSettleAdjust.setAdjustType(adjustTypeEnum.getType());
                }
            }

            String adjustAmount = importAngelSettleAdjust.getAdjustAmount();
            if(StringUtil.isEmpty(adjustAmount)){
                stringBuilder.append("调账金额为空,");
            }else{
                if(!NumberUtil.isNumber(adjustAmount)){
                    stringBuilder.append("调账金额非法,");
                }
                String pattern = "^-?(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,2})?$";
                if(!adjustAmount.matches(pattern)){
                    stringBuilder.append("调账金额小数点后最多两位");
                }else{
                    BigDecimal amount = new BigDecimal(adjustAmount);
                    if(amount.compareTo(new BigDecimal("99999.99")) > 0 || amount.compareTo(new BigDecimal("-99999.99")) < 0 ){
                        stringBuilder.append("调账金额需要在-99999.99至99999.99之间");
                    }
                }
            }
            if(StringUtil.isBlank(importAngelSettleAdjust.getCashDate())){
                stringBuilder.append("可提现日期为空,");
            }else{
                Date cashDate = DateUtil.parseDateWithPatterns(importAngelSettleAdjust.getCashDate(),
                        CommonConstant.YMD,CommonConstant.YMD3,CommonConstant.YMDHMS,CommonConstant.YMDHMS3);
                if(Objects.isNull(cashDate)){
                    stringBuilder.append("可提现日期格式不符合,");
                }else{
                    String cashDateStr = DateUtil.formatDate(cashDate, CommonConstant.YMD);
                    String twoDays = LocalDate.now().plusDays(CommonConstant.TWO).toString();
                    if(twoDays.compareTo(cashDateStr) > 0){
                        stringBuilder.append("可提现日期早于T+2,");
                    }
                    importAngelSettleAdjust.setCashDate(cashDateStr);
                }
            }
            String remark = importAngelSettleAdjust.getRemark();
            if(StringUtil.isNotBlank(remark) && remark.length() > 100){
                stringBuilder.append("备注字数超过100个,");
            }
            if(StringUtil.isNotBlank(stringBuilder.toString())){
                importAngelSettleAdjust.setFailReason(stringBuilder.toString());
                isFail = true;
            }
            failList.add(importAngelSettleAdjust);
        } catch (Exception e) {
            log.error("ImportAngelSettleAdjustListener exception", e);
            failReason = e.getMessage();
            importAngelSettleAdjust.setFailReason(failReason);
            isFail = true;
            failList.add(importAngelSettleAdjust);
        }
        succList.add(importAngelSettleAdjust);
    }

    /**
     * 校验行数据是否为空
     * @param row
     * @return
     */
    private boolean isRowEmpty(ImportAngelSettleAdjust row) {
        return StringUtil.isEmpty(row.getAngelId()) &&
                StringUtil.isEmpty(row.getAngelName()) &&
                StringUtil.isEmpty(row.getAdjustTypeDesc()) &&
                StringUtil.isEmpty(row.getAdjustAmount()) &&
                StringUtil.isEmpty(row.getCashDate());
    }

    private JdhAngelDto getAngelDto(Long angelId){
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelId(angelId);
        AngelApplication angelApplication = SpringUtil.getBean(AngelApplication.class);
        return angelApplication.queryByAngelInfo(angelRequest);
    }

    /**
     *
     * @param analysisContext analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (isFail) {
            log.info("ImportAngelSettleAdjustListener doAfterAllAnalysed failList={}", JSON.toJSONString(failList));
            FileManageApplication fileManageApplication = SpringUtil.getBean(FileManageApplication.class);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcelFactory.write(outputStream, ImportAngelSettleAdjust.class).sheet("失败记录").doWrite(failList);
            String fileName = "错误文件.xlsx";
            GeneratePutUrlCommand data = new GeneratePutUrlCommand();
            data.setUserPin(userPin);
            data.setDomainCode(DomainEnum.SETTLE_MENT.getCode());
            data.setFileBizType(SettlementFileBizTypeEnum.ANGEL_SETTLE_ADJUST.getBizType());
            filePreSignedUrlDto = fileManageApplication.upload(fileName, new ByteArrayInputStream(outputStream.toByteArray()), LocalDateTime.now().plusHours(24), data);
        }
    }
}
