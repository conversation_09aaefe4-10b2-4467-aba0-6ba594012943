package com.jdh.o2oservice.application.medicalpromise.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jd.jsf.gd.util.JsonUtils;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.trade2.base.export.common.util.CollectionUtils;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseStatusUtil;
import com.jdh.o2oservice.application.medicalpromise.convert.MedicalPromiseConvert;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderEquipmentApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.support.OperationLogApplication;
import com.jdh.o2oservice.application.trade.TradeExtApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.SubStatusAlarmConfig;
import com.jdh.o2oservice.base.ducc.model.dispatch.DispatchReceiveReStationDisConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.event.*;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TextUtil;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelIdentifier;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.model.JdhStationIdentifier;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.event.DispatchCallbackEventBody;
import com.jdh.o2oservice.core.domain.dispatch.event.DispatchWaitEventBody;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.medpromise.bo.MedicalPromiseExceptionBO;
import com.jdh.o2oservice.core.domain.medpromise.context.MedicalPromiseSubmitContext;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedPromiseSettlementEventBody;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseReportEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseSummary;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.QueryTimeSummaryQuery;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.provider.rpc.QuickCheckThirdExportServiceRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.FlowCodeBO;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.FlowCodeListBO;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.FlowCodeQueryBO;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.FreeFlowCodeBO;
import com.jdh.o2oservice.core.domain.report.bo.ReportAuditQueryBO;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicator;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicatorQueryPageBo;
import com.jdh.o2oservice.core.domain.report.model.ReportAudit;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportDataRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportIndicatorRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.ReportAuditRepository;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.businessMode.ServiceHomeTypeDomainService;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.businessMode.ServiceHomeTypeDomainService;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.enums.TradeEventTypeEnum;
import com.jdh.o2oservice.core.domain.trade.event.OrderSplitEventBody;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.export.medicalpromise.cmd.*;
import com.jdh.o2oservice.export.medicalpromise.dto.MergePdfDTO;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.PromiseStationDto;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.PromiseListRequest;
import com.jdh.o2oservice.export.promise.query.VoucherPageRequest;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.ProviderEquipmentDto;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import com.jdh.o2oservice.export.provider.query.StoreInfoRequest;
import com.jdh.o2oservice.export.report.AbnormalTypeEnum;
import com.jdh.o2oservice.export.report.dto.MedicalPromiseMqDTO;
import com.jdh.o2oservice.export.report.dto.MedicalReportDTO;
import com.jdh.o2oservice.export.report.enums.SampleAnomalyOperateReasonTypeEnum;
import com.jdh.o2oservice.export.report.enums.SampleAnomalyOperateTypeEnum;
import com.jdh.o2oservice.export.report.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.report.query.MedicalReportRequest;
import com.jdh.o2oservice.export.support.command.OperationLogCmd;
import com.jdh.o2oservice.export.support.enums.OperationLogOperateTypeEnum;
import com.jdh.o2oservice.export.trade.dto.AddressInfoDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePatientPoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName MedicalPromiseEventSubscriber
 * @Description 服务商履约域，事件订阅处理
 * <AUTHOR>
 * @Date 2024/4/22 18:22
 **/
@Slf4j
@Component
public class MedicalPromiseEventSubscriber {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * medicalPromiseApplication
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * promiseApplication
     */
    @Resource
    private PromiseApplication promiseApplication;

    /**
     * promiseApplication
     */
    @Resource
    private PromiseRepository promiseRepository;

    /**
     * 检测单仓储层
     */
    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     *
     */
    @Autowired
    private VerticalBusinessRepository businessRepository;

    /**
     * commonDuccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * dongDongRobotRpc
     */
    @Resource
    private DongDongRobotRpc dongDongRobotRpc;

    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Autowired
    private MedicalReportApplication medicalReportApplication;

    @Value("${topics.event.medicalPromiseTopic}")
    private String medicalPromiseTopic;

    /**
     * dispatchRepository
     */
    @Resource
    private DispatchRepository dispatchRepository;
    /**
     * angelWorkRepository
     */
    @Resource
    private AngelWorkRepository angelWorkRepository;

    /**
     * 注入的护士信息仓库，用于获取护士的相关信息。
     */
    @Autowired
    private AngelRepository angelRepository;

    /**
     * JdhStationRepository对象的引用，用于获取护士对应服务站对应实验室信息。
     */
    @Resource
    private JdhStationRepository jdhStationRepository;
    /**
     * providerStoreApplication
     */
    @Autowired
    private ProviderStoreApplication providerStoreApplication;

    /**
     * voucherApplication
     */
    @Resource
    private VoucherApplication voucherApplication;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;

    @Resource
    private TradeExtApplication tradeExtApplication;

    @Resource
    private ServiceHomeTypeDomainService serviceHomeTypeDomainService;

    /**
     * jimRedisService
     */
    @Resource
    private Cluster cluster;

    @Autowired
    private ProviderEquipmentApplication providerEquipmentApplication;

    @Autowired
    private ReportAuditRepository reportAuditRepository;

    @Autowired
    private MedicalReportDataRepository medicalReportDataRepository;

    @Autowired
    private AngelShipRepository angelShipRepository;

    /**
     * operationLogJsfExport
     */
    @Autowired
    private OperationLogApplication operationLogJsfExport;

    @Autowired
    private QuickCheckThirdExportServiceRpc quickCheckThirdExportServiceRpc;

    @Resource
    private MedicalReportIndicatorRepository medicalReportIndicatorRepository;


    @Resource
    private JdhPromisePatientPoMapper jdhPromisePatientPoMapper;

    @Resource
    private FileManageService fileManageService;

    @Value("${topics.report.merge}")
    private String mergeTopic;


    /**
     * 注册事件使用者
     */
    @PostConstruct
    public void registerEventConsumer() {

        //=======>>>>>> 绑定条码事件 监听消息 -> 触发提交信息同步服务商
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_BIND_SPECIMEN_CODE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medicalPromiseBindSpecimenCodeCall", this::bindSpecimenCodeCall, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        //=======>>>>>> 绑定主动提交事件 监听消息 -> 触发提交信息同步服务商
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SUBJECTIVE_SUBMIT, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medicalPromiseSubjectiveSubmitCall", this::subjectiveSubmitCall, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        //=======>>>>>> 绑定推送报告事件 监听消息 -> 触发保存报告
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_PUSH_REPORT, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medPromisePushReport", this::pushReport, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(30,1000,2.0,30000)));

        //=======>>>>>> 状态回传 收样 监听消息 -> 触发状态回传
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SUBJECTIVE_STATION_RECEIVE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medPromiseSubjectiveStationReceive", this::medicalPromiseCheck, Boolean.TRUE, Boolean.TRUE));
//
//        //=======>>>>>> 结算事件 -> 触发结算
//        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SETTLEMENT, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
//                "medPromiseSettlement", this::medicalPromiseSettlement, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 判断报告是否全部已出
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_JUDGE_ALL_GENERATE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medPromiseJudgeAllGenerate", this::medPromiseJudgeAllGenerate, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        //=======>>>>>> 预约单完成事件->触发实验室结算
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SKU_PATIENT_ALL_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medPromiseStationSettlement", this::medicalPromiseSettlement, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        //=======>>>>>> 预约单人纬度完成事件->触发实验室结算
//        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_PATIENT_ALL_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
//                "medPromiseStationPatientSettlement", this::medicalPromisePatientSettlement, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        //=======>>>>>> 实验室天算结算完成->触发实验室ebs结算
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SETTLEMENT_COMPLETE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medPromiseStationEbsSettlement", this::medicalPromiseEbsSettlement, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        //=======>>>>>> 护士上门检测类型的检测单，在开始发起派单后进行分派实验室。PRD：https://joyspace.jd.com/pages/NWSij1s0slMVyN19NMa8
        //2024-10-10 逻辑变更，从创建履约单分派实验室变更为发起派单分派实验室
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_CREATE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medPromiseStoreDisPatch", this::medPromiseStoreDisPatch, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        //=======>>>>>> 实验室检测单收样延迟消息监控
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseStationReceiveMonitor", this::medPromiseStationReceiveMonitor, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 实验室检测单收样，报警
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE_ALARM_DELAY, WrapperEventConsumer.newDelayInstance(DomainEnum.MED_PROMISE,
                "saveMedPromiseStationReceiveAlarmDelay", this::saveMedPromiseStationReceiveAlarmDelay));

        //=======>>>>>> 检测单绑码
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_BIND_SPECIMEN_CODE, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medMedicalPromiseBindSpecimenCodeMq", this::medicalPromiseMq, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 检测单出报告
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medMedicalPromiseGenerateMq", this::medicalPromiseMq, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>> 检测单出报告,进行AI体检报告解读
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medMedicalPromiseAiRead", this::medMedicalPromiseAiRead, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.exponentialRetryInstance(3,2000,3.0,30000)));

        //=======>>>>>> 检测单出报告
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "checkOrderAllReportIsDone", this::checkOrderAllReportIsDone, Boolean.TRUE, Boolean.TRUE));

        //非快检模式-用户绑码后触发派实验室逻辑
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_CREATED, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "dispatchLab", this::dispatchLab, Boolean.TRUE, Boolean.TRUE));

        //https://joyspace.jd.com/pages/yw8Wlb7OPJdORwpCuwQj，上海战役变更，如果是上海自营护士，护士接单后，检查是否是对应服务站的实验室，如果是，则不处理，如果不是，则指定实验室
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_RECEIVED, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "dispatchReceivedStationDis", this::dispatchReceivedStationDis, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        //发送是否合并pdf文件流
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SMS_PART,
                WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE, "sendMergePdfMq", this::sendMergePdfMq, Boolean.TRUE));

        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_CALL_BACK, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medPromiseCallBackDeal", this::medicalPromiseCallBack, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.defaultInstance()));


//        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_FINISH, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
//                "shipFinishModifyMedicalPromise", this::reachStation, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        //发送是否合并pdf文件流
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SMS_ALL,
                WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE, "sendMergePdfMq", this::sendMergePdfMq, Boolean.TRUE));

        //检查超时和阳性率
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SMS_PART,
                WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE, "checkPositiveAndTimeoutRate", this::checkPositiveAndTimeoutRate, Boolean.TRUE));

        //检查超时和阳性率
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SMS_ALL,
                WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE, "checkPositiveAndTimeoutRate", this::checkPositiveAndTimeoutRate, Boolean.TRUE));
    }

    /**
     * 检查阳性率和超时率,
     * @param event
     */
    private void checkPositiveAndTimeoutRate(Event event){

        Long medicalPromiseId = Long.valueOf(event.getAggregateId());

        //NOTE 检查当前当前检测单是否履约超时
        this.checkTimeOut(medicalPromiseId);

    }

    /**
     * 检查当前当前检测单是否履约超时,发送告警
     * @param medicalPromiseId
     */
    private void checkTimeOut(Long medicalPromiseId){
        QueryTimeSummaryQuery queryTimeSummaryQuery = new QueryTimeSummaryQuery();
        queryTimeSummaryQuery.setMedicalPromiseId(medicalPromiseId);
        List<MedicalPromiseSummary> medicalPromiseSummaries = medicalPromiseRepository.queryTimeSummary(queryTimeSummaryQuery);
        if (CollectionUtils.isEmpty(medicalPromiseSummaries)){
            log.info("MedicalPromiseEventSubscriber medicalPromiseSummaries为空,逻辑终止!!!");
        }
        MedicalPromiseSummary medicalPromiseSummary = medicalPromiseSummaries.get(0);
        if(CommonConstant.ONE == medicalPromiseSummary.getTestTimeOut()){

            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("报告履约超时");

            dongDongRobotRpc.sendDongDongRobotMessage(String.format("【报告履约超时】检测单号:%s,实验室:%s,检测项目:%s,配置检测时长:%s分,实际检测耗时:%s",
                    medicalPromiseSummary.getMedicalPromiseId(),
                    medicalPromiseSummary.getStationName(),
                    medicalPromiseSummary.getServiceItemName(),
                    medicalPromiseSummary.getTestDuration(),
                    (medicalPromiseSummary.getReportTime().getTime()-medicalPromiseSummary.getDeliveryFinishTime().getTime())/1000/60
                    ), jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
        }
    }


    /**
     * 样本送达实验室
     * @param event
     */
    private void reachStation(Event event){

        log.info("MedicalPromiseEventSubscriber -> reachStation event:{}", JSON.toJSONString(event));
        AngelShip angelShip = angelShipRepository.find(AngelShipIdentifier.builder().shipId(Long.valueOf(event.getAggregateId())).build());
        log.info("MedicalPromiseEventSubscriber -> reachStation angelShip:{}", JSON.toJSONString(angelShip));
        //查运单
        if (Objects.isNull(angelShip)){
            return;
        }
        //查工单
        AngelWork angelWork = angelWorkRepository.findAngelWork(AngelWorkDBQuery.builder().workIds(Lists.newArrayList(angelShip.getWorkId())).build());
        log.info("MedicalPromiseEventSubscriber -> reachStation angelWork:{}", JSON.toJSONString(angelWork));
        if (Objects.isNull(angelWork)){
            return;
        }
        //查检测单

        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(angelWork.getPromiseId()).build());
        log.info("MedicalPromiseEventSubscriber -> reachStation medicalPromises:{}", JSON.toJSONString(medicalPromises));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(medicalPromises)){
            return;
        }

        List<MedicalPromise> receiveMedicalPromiseList = medicalPromises.stream().filter(p -> StringUtils.equals(angelShip.getReceiverId(), p.getStationId())).collect(Collectors.toList());
        log.info("MedicalPromiseEventSubscriber -> reachStation receiveMedicalPromiseList:{}", JSON.toJSONString(receiveMedicalPromiseList));

        for (MedicalPromise medicalPromise : receiveMedicalPromiseList) {

            if (medicalPromise.getStatus()>3){
                continue;
            }
            medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_CHECK.getSubStatus());
            medicalPromiseRepository.save(medicalPromise);
        }

    }



    /**
     * 某个人报告全部出完 则发送合并pdf文件mq
     */
    public void sendMergePdfMq(Event event){
        log.info("MedicalPromiseEventSubscriber -> sendMergePdfMq event:{}",JSON.toJSONString(event));
        MedicalPromiseEventBody medicalPromiseEventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
        medicalPromiseListQuery.setPromiseId(medicalPromiseEventBody.getPromiseId());

        //全部检测单
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);

        //当前检测单
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(medicalPromiseEventBody.getMedicalPromiseId()).build());

        //当前检测单对应的报告
        MedicalPromiseRequest medicalPromiseRequest = new MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        MedicalReportDTO medicalReportDTO = medicalReportApplication.getByMedicalPromiseId(medicalPromiseRequest);

        //过滤出当前检测人的全部结构化检测单
        List<MedicalPromise> curPatientMedicalPromises = medicalPromises.stream().filter(t->t.getPromisePatientId().equals(medicalPromise.getPromisePatientId())&&ReportShowTypeEnum.STRUCT.getType().equals(t.getReportShowType())).collect(Collectors.toList());
        //过滤出当前检测人的全部已出报告的检测单
        List<MedicalPromise> curPatientExceptionMedicalPromises = curPatientMedicalPromises.stream().filter(t->MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(t.getStatus())).collect(Collectors.toList());

        if(curPatientMedicalPromises.size()!=curPatientExceptionMedicalPromises.size()){
            log.info("用户报告还未出完,不发送合并pdf报告的mq");
            return;
        }

        //查询当前人的全部报告
        MedicalReportRequest medicalReportRequest = new MedicalReportRequest();
        medicalReportRequest.setPromiseId(medicalPromiseEventBody.getPromiseId());
        medicalReportRequest.setPatientId(medicalReportDTO.getPatientId());
        List<MedicalReportDTO> medicalReportDTOS =  medicalReportApplication.queryMedicalReportList(medicalReportRequest);

        List<MedicalReportDTO> medicalReportPdfs = medicalReportDTOS.stream().filter(t->StringUtils.isNotEmpty(t.getReportOss())&&StringUtils.isNotBlank(t.getStructReportOss())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(medicalReportPdfs)){
            log.info("用户没有pdf报告,不发送合并pdf报告的mq");
            return;
        }

        List<MedicalReportDTO> abnormalMedicalReportPdfList = new ArrayList<>();
        List<MedicalReportDTO> noAbnormalMedicalReportPdfList = new ArrayList<>();

        medicalReportPdfs.forEach(t->{
            MedicalReportIndicatorQueryPageBo medicalReportIndicatorQueryPageBo = new MedicalReportIndicatorQueryPageBo();
            medicalReportIndicatorQueryPageBo.setReportId(t.getId()+"");
            PageDto<MedicalReportIndicator> pageDto = medicalReportIndicatorRepository.pageMedicalReportIndicators(medicalReportIndicatorQueryPageBo);
            if (pageDto != null && CollUtil.isNotEmpty(pageDto.getList())) {
                if(!AbnormalTypeEnum.NORMAL.getType().equals(pageDto.getList().get(0).getAbnormalMarkType())){
                    abnormalMedicalReportPdfList.add(t);
                }else{
                    noAbnormalMedicalReportPdfList.add(t);
                }
            }else{
                noAbnormalMedicalReportPdfList.add(t);
            }
        });

        log.info("合并数据之前 abnormalMedicalReportPdfList={},noAbnormalMedicalReportPdfList={}",JSON.toJSONString(abnormalMedicalReportPdfList),JSON.toJSONString(noAbnormalMedicalReportPdfList));
        abnormalMedicalReportPdfList.addAll(noAbnormalMedicalReportPdfList);
        log.info("合并数据之后 abnormalMedicalReportPdfList={},noAbnormalMedicalReportPdfList={}",JSON.toJSONString(abnormalMedicalReportPdfList),JSON.toJSONString(noAbnormalMedicalReportPdfList));

        List<String> urls = new ArrayList<>();
        abnormalMedicalReportPdfList.forEach(report->{
            urls.add(report.getReportOss());
        });

        MergePdfDTO mergePdfDTO = new MergePdfDTO();
        mergePdfDTO.setUrls(urls);
        mergePdfDTO.setPromiseId(medicalPromiseEventBody.getPromiseId());
        mergePdfDTO.setPromisePatientId(medicalPromise.getPromisePatientId());

        log.info("MedicalPromiseEventSubscriber -> sendMessage cmd:{}", JSON.toJSONString(urls));
        Message message = new Message(mergeTopic, JSON.toJSONString(mergePdfDTO), "88884444");
        log.info("MedicalPromiseEventSubscriber-> sendMessage message={}", JSON.toJSONString(message));
        try {
            reachStoreProducer.send(message);
        } catch (Exception e) {
            log.info("合并pdf mq 发送异常",e);
        }
    }
    /**
     * 非快检模式-派实验室
     * @param event
     */
    private void dispatchLab(Event event){
        log.info("MedicalPromiseEventSubscriber -> dispatchLab event:{}", JSON.toJSONString(event));
        Long promiseId = Long.parseLong(event.getAggregateId());

        JdhPromise jdhPromise = promiseRepository.find(new JdhPromiseIdentifier(promiseId));

        boolean verticalCodeBool = serviceHomeTypeDomainService.getServiceHomeVerticalCode("XFYL_HOME_SELF_TEST_TRANSPORT").equals(jdhPromise.getVerticalCode());
        if(!verticalCodeBool){
            log.info("MedicalPromiseEventSubscriber -> dispatchLab 非快检模式不执行");
            return;
        }


        JdOrderDTO jdOrderDTO = tradeExtApplication.queryOrderFullDetail(Long.parseLong(jdhPromise.getSourceVoucherId()));

        MedicalPromiseDispatchCmd medicalPromiseDispatchCmd = new MedicalPromiseDispatchCmd();
        medicalPromiseDispatchCmd.setPromiseId(Long.parseLong(event.getAggregateId()));
        jdOrderDTO.getJdOrderExtList().stream()
                .filter(t->Objects.nonNull(t) && "orderAddress".equals(t.getExtType()))
                .findFirst()
                .ifPresent(address->{
                    AddressInfoDTO addressInfo = JSON.parseObject(address.getExtContext(),AddressInfoDTO.class);
                    medicalPromiseDispatchCmd.setAddressId(Objects.toString(addressInfo.getId()));
                    medicalPromiseDispatchCmd.setStartAddress(addressInfo.getFullAddress());
                });
        medicalPromiseDispatchCmd.setOrderId(jdOrderDTO.getOrderId());
        medicalPromiseDispatchCmd.setVerticalCode(jdhPromise.getVerticalCode());
        medicalPromiseDispatchCmd.setServiceType(jdhPromise.getServiceType());
        medicalPromiseDispatchCmd.setPromisePatientIdList(jdhPromise.getPatients().stream().map(JdhPromisePatient::getPromisePatientId).collect(Collectors.toList()));

        medicalPromiseApplication.storeDisPatch(medicalPromiseDispatchCmd);

        List<MedicalPromise> list = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(promiseId).build());
        list.forEach(medicalPromise -> {
            eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_BIND_SPECIMEN_CODE,
                    MedicalPromiseEventBody.builder()
                            .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                            .status(medicalPromise.getStatus())
                            .specimenCode(medicalPromise.getSpecimenCode())
                            .verticalCode(medicalPromise.getVerticalCode())
                            .serviceType(medicalPromise.getServiceType())
                            .promiseId(medicalPromise.getPromiseId())
                            .build()));
            log.info("MedicalPromiseEventSubscriber -> 非快检派实验室成功,发送绑码事件同步实验室检测单 medicalPromiseId:{} code:{}", medicalPromise.getMedicalPromiseId(), medicalPromise.getSpecimenCode());
        });
    }

    /**
     * 派单任务创建成功
     *
     * @param event 事件
     */
    private void bindSpecimenCodeCall(Event event) {
        log.info("MedicalPromiseEventSubscriber -> bindSpecimenCodeCall event:{}", JSON.toJSONString(event));
        MedicalPromiseEventBody eventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        //如果是骑手上门场景，调用服务商同步采样信息
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(eventBody.getMedicalPromiseId()).build());
        if (Objects.isNull(medicalPromise)) {
            log.info("MedicalPromiseEventSubscriber -> bindSpecimenCodeCall medicalPromise null");
            return;
        }
        MedicalPromiseSubmitContext context = new MedicalPromiseSubmitContext();
        context.init(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_BIND_SPECIMEN_CODE);
        log.info("MedicalPromiseEventSubscriber -> bindSpecimenCodeCall context:{}", JSON.toJSONString(context));
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode()) || Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.SELF_TEST_TRANSPORT.getCode())) {
            medicalPromiseApplication.submitMedicalPromiseToStation(MedicalPromiseSubmitCmd.builder().medicalPromiseId(eventBody.getMedicalPromiseId()).build());
        }
    }

    /**
     * 触发提交信息同步服务商
     *
     * @param event 事件
     */
    private void subjectiveSubmitCall(Event event) {
        log.info("MedicalPromiseEventSubscriber -> subjectiveSubmitCall event:{}", JSON.toJSONString(event));
        MedicalPromiseEventBody eventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        medicalPromiseApplication.submitMedicalPromiseToStation(MedicalPromiseSubmitCmd.builder().medicalPromiseId(eventBody.getMedicalPromiseId()).build());
    }

    /**
     * 推送报告事件
     * @param event
     */
    private void pushReport(Event event){
        log.info("MedicalPromiseEventSubscriber -> pushReport event:{}", JSON.toJSONString(event));
        MedicalPromiseReportEventBody medicalPromiseReportEventBody = JSON.parseObject(event.getBody(), MedicalPromiseReportEventBody.class);
        MedicalPromiseReportCmd medicalPromiseReportCmd = MedicalPromiseConvert.INSTANCE.convert(medicalPromiseReportEventBody);
        medicalPromiseApplication.pushMedicalPromiseReportInfo(medicalPromiseReportCmd);

    }

    /**
     * 推送报告回传事件
     * @param event
     */
    private void medicalPromiseCheck(Event event){
        log.info("MedicalPromiseEventSubscriber -> medicalPromiseCheck event:{}", JSON.toJSONString(event));
        MedicalPromiseEventBody medicalPromiseEventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        MedPromiseCallbackStatusCmd medPromiseCallbackStatusCmd = new MedPromiseCallbackStatusCmd();
        medPromiseCallbackStatusCmd.setMedicalPromiseId(medicalPromiseEventBody.getMedicalPromiseId());
        medPromiseCallbackStatusCmd.setOuterId(medicalPromiseEventBody.getOuterId());
//        MedicalPromiseReportCmd medicalPromiseReportCmd = MedicalPromiseConvert.INSTANCE.convert(medicalPromiseReportEventBody);
        try {
            medicalPromiseApplication.medicalPromiseCheck(medPromiseCallbackStatusCmd);
        }catch (Exception e){
            log.error("MedicalPromiseEventSubscriber->medicalPromiseCheck,");
        }

    }

    /**
     * 结算事件
     * @param event
     */
    private void medicalPromiseSettlement(Event event){
        log.info("MedicalPromiseEventSubscriber -> medicalPromiseSettlement event:{}", JSON.toJSONString(event));
        MedicalPromiseEventBody body = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        MedPromiseSettlementCmd cmd = new MedPromiseSettlementCmd();
        cmd.setMedicalPromiseId(body.getMedicalPromiseId());
        cmd.setPromisePatientId(body.getPromisePatientId());
        cmd.setPromiseId(body.getPromiseId());
        cmd.setAppointmentId(body.getAppointmentId());
        cmd.setServiceId(body.getServiceId());
        cmd.setCompleteTime(new Date());
        medicalPromiseApplication.sendSettlementMessage(cmd);
    }

    /**
     * 预约单人纬度完成事件->触发实验室结算
     *
     * @param event
     */
    private void medicalPromisePatientSettlement(Event event) {
        log.info("MedicalPromiseEventSubscriber -> medicalPromisePatientSettlement event:{}", JSON.toJSONString(event));
        MedicalPromiseEventBody body = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        MedPromiseSettlementCmd cmd = new MedPromiseSettlementCmd();
        cmd.setMedicalPromiseId(body.getMedicalPromiseId());
        cmd.setPromisePatientId(body.getPromisePatientId());
        cmd.setPromiseId(body.getPromiseId());
        cmd.setAppointmentId(body.getAppointmentId());
        cmd.setServiceId(body.getServiceId());
        cmd.setCompleteTime(new Date());
        medicalPromiseApplication.sendPatientSettlementMessage(cmd);
    }

    /**
     * 结算事件
     * @param event
     */
    private void medPromiseJudgeAllGenerate(Event event){
        log.info("MedicalPromiseEventSubscriber -> medPromiseJudgeAllGenerate event:{}", JSON.toJSONString(event));
        MedicalPromiseEventBody body = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        MedicalPromiseAllGenerateCmd cmd = new MedicalPromiseAllGenerateCmd();
        cmd.setPromiseId(body.getPromiseId());
        medicalPromiseApplication.medicalPromiseAllGenerate(cmd);
    }

    /**
     * 结算事件
     * @param event
     */
    private void medicalPromiseEbsSettlement(Event event){
        log.info("MedicalPromiseEventSubscriber -> medicalPromiseEbsSettlement event:{}", JSON.toJSONString(event));
        MedPromiseSettlementEventBody body = JSON.parseObject(event.getBody(), MedPromiseSettlementEventBody.class);
        MedPromiseToEbsCmd medPromiseToEbsCmd = new MedPromiseToEbsCmd();

        medPromiseToEbsCmd.setMedicalPromiseId(body.getMedicalPromiseId());
        medPromiseToEbsCmd.setPromiseId(body.getPromiseId());
        medPromiseToEbsCmd.setPromisePatientId(body.getPromisePatientId());
        medPromiseToEbsCmd.setSettlementPrice(body.getSettlementPrice());
        medPromiseToEbsCmd.setCompleteTime(body.getCompleteTime());
        medPromiseToEbsCmd.setServiceId(body.getServiceId());

        medicalPromiseApplication.settlementToEbs(medPromiseToEbsCmd);
    }

    /**
     * 护士上门检测类型的检测单，在创建后进行分派实验室
     * PRD：https://joyspace.jd.com/pages/NWSij1s0slMVyN19NMa8
     * @param event
     */
    private void medPromiseStoreDisPatch(Event event){
        //护士上门检测派发实验室新旧流程开关
        Boolean dispatchSwitch = duccConfig.getAngelHomeTestStoreDispatchSwitch();
        if(dispatchSwitch){
            log.info("MedicalPromiseEventSubscriber -> medPromiseStoreDisPatch angelHomeTestStoreDispatchSwitch:{}", dispatchSwitch);
            return;
        }
        log.info("MedicalPromiseEventSubscriber -> medPromiseStoreDisPatch event:{}", JSON.toJSONString(event));
        DispatchWaitEventBody eventBody = JSON.parseObject(event.getBody(), DispatchWaitEventBody.class);
        //根据PromiseId查询履约单，然后进行实验室派单
        PromiseDto promise = promiseApplication.findPromiseByPromiseId(PromiseIdRequest.builder().promiseId(eventBody.getPromiseId()).build());
        if(Objects.isNull(promise)) {
            log.info("MedicalPromiseEventSubscriber -> medPromiseStoreDisPatch promise is null, promiseId:{}", event.getAggregateId());
            return;
        }

        VerticalBusinessRepository businessRepository = SpringUtil.getBean(VerticalBusinessRepository.class);
        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(promise.getVerticalCode());
        //只处理护士上门检测类型的检测单
        ArrayList<String> businessModeCodeList = Lists.newArrayList(BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode());
        if (Objects.nonNull(jdhVerticalBusiness) && businessModeCodeList.contains(jdhVerticalBusiness.getBusinessModeCode())) {
            log.info("MedicalPromiseEventSubscriber -> medPromiseStoreDisPatch 护士上门检测，进行分派实验室={}", JsonUtil.toJSONString(promise));
            //检测人ID
            List<Long> patientIds = promise.getPatients().stream().map(PromisePatientDto::getPromisePatientId).collect(Collectors.toList());
            //组装分派实验室参数
            MedicalPromiseDispatchCmd medicalPromiseDispatchCmd = new MedicalPromiseDispatchCmd();
            medicalPromiseDispatchCmd.setPromiseId(promise.getPromiseId());
            medicalPromiseDispatchCmd.setVerticalCode(promise.getVerticalCode());
            medicalPromiseDispatchCmd.setServiceType(promise.getServiceType());
            medicalPromiseDispatchCmd.setStartAddress(promise.getStore().getStoreAddr());
            medicalPromiseDispatchCmd.setPromisePatientIdList(patientIds);
            medicalPromiseApplication.storeDisPatch(medicalPromiseDispatchCmd);
        }

    }

    /**
     * 实验室收样监控超时报警
     * PRD：https://joyspace.jd.com/pages/Gj8ZIdppDsPRA0q9KbVS
     * @param event
     */
    private void medPromiseStationReceiveMonitor(Event event) {
        log.info("MedicalPromiseEventSubscriber -> medPromiseStationReceiveMonitor,event={}", JSON.toJSONString(event));

    }

    /**
     *
     */
    private void saveMedPromiseStationReceiveAlarmDelay(Event event) {
        log.info("MedicalPromiseEventSubscriber -> saveMedPromiseStationReceiveAlarmDelay,event={}", JSON.toJSONString(event));
        MedicalPromiseEventBody medicalPromiseEventBody = JsonUtils.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        MedicalPromiseRepQuery medicalPromiseRepQuery = new MedicalPromiseRepQuery();
        medicalPromiseRepQuery.setMedicalPromiseId(medicalPromiseEventBody.getMedicalPromiseId());
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(medicalPromiseRepQuery);

        //超时未出报告 报警
        if (Objects.nonNull(medicalPromise)) {
            JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(medicalPromise.getVerticalCode());
            if (Objects.isNull(jdhVerticalBusiness) || Objects.equals(BusinessModeEnum.ANGEL_CARE.getCode(), jdhVerticalBusiness.getBusinessModeCode())) {
                log.info("MedicalPromiseEventSubscriber -> alarm disable 护士上门护理不发送报警 medicalPromise={}", JsonUtil.toJSONString(medicalPromise));
                return;
            }
            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("超时未出报告");
            if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
                log.info("MedicalPromiseEventSubscriber -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
                return;
            }
            if (Objects.equals(medicalPromise.getReportStatus(), 1)) {
                log.info("MedicalPromiseEventSubscriber -> alarm disable 已出报告不发送报警 medicalPromise={}", JsonUtil.toJSONString(medicalPromise));
                return;
            }
            String stationReceiveTime = "";
            if (Objects.nonNull(medicalPromiseEventBody.getStationReceiveTime())) {
                stationReceiveTime = DateUtil.formatDateTime(medicalPromiseEventBody.getStationReceiveTime());
            }
            boolean verticalCodeBool = serviceHomeTypeDomainService.getServiceHomeVerticalCode("XFYL_HOME_SELF_TEST_TRANSPORT").equalsIgnoreCase(medicalPromise.getVerticalCode());
            if (verticalCodeBool) {
                JSONObject transportJsonObject = robotAlarmMap.get("非快检超时未出报告");
                if (!Objects.isNull(transportJsonObject) && StringUtil.isNotBlank(jsonObject.getString("groupId"))) {
                    jsonObject = transportJsonObject;
                }
                List<MedicalPromise> list = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(medicalPromise.getPromiseId()).build());
                //发送咚咚报警
                Long time = duccConfig.getHomeSelfTestAlarm().getLong("transportTestReportTimeInterval") / 60;
                dongDongRobotRpc.sendDongDongRobotMessage(String.format("【超时未出报告】样本已于%s小时前送达实验室，但仍未出具报告，请及时关注！实验室ID：%s，实验室名称：%s，样本编码：%s；",
                        time, medicalPromise.getStationId(),
                        medicalPromise.getStationName(),
                        CollUtil.isEmpty(list) ? medicalPromise.getSpecimenCode() : Joiner.on("、").join(list.stream().map(MedicalPromise::getSpecimenCode).filter(StringUtils::isNotBlank).collect(Collectors.toList()))), jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            } else {
                //发送咚咚报警

                //查询配置的检测时间
                JdhStationServiceItemRelDto jdhStationServiceItemRelDto = providerStoreApplication.queryStationServiceItemRel(JdhStationServiceItemRelRequest.builder().stationId(medicalPromise.getStationId()).serviceItemId(Long.valueOf(medicalPromise.getServiceItemId())).build());
                Long time = duccConfig.getDelayTime() / 60;
                if (Objects.nonNull(jdhStationServiceItemRelDto) && Objects.nonNull(jdhStationServiceItemRelDto.getTestDuration())){
                    time = Long.valueOf(jdhStationServiceItemRelDto.getTestDuration());
                }

//                Long time = duccConfig.getDelayTime() / 60;
                dongDongRobotRpc.sendDongDongRobotMessage(String.format("【超时未出报告】样本已于%s分钟前送达实验室，但仍未出具报告，请尽快排查解决（实验室名称：%s，已送达时间：%s，服务单号：%s，样本编码：%s，检测项目名称：%s）",
                        time, medicalPromise.getStationName(), stationReceiveTime, medicalPromiseEventBody.getMedicalPromiseId(), medicalPromise.getSpecimenCode(), medicalPromise.getServiceItemName()), jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            }
        }
    }

    /**
     * 发送履约状态消息
     * @param event
     */
    private void medicalPromiseMq(Event event) {

        MedicalPromiseMqDTO medicalPromiseMqDTO = new MedicalPromiseMqDTO();
        try {

            MedicalPromiseEventBody eventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
            MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(eventBody.getMedicalPromiseId()).build());
            PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(medicalPromise.getPromiseId()).build());

            //userPin
            medicalPromiseMqDTO.setUserPin(promiseDto.getUserPin());
            //订单id
            medicalPromiseMqDTO.setOrderId(Long.parseLong(promiseDto.getSourceVoucherId()));
            //服务id
            medicalPromiseMqDTO.setVoucherId(promiseDto.getVoucherId());
            //履约id
            medicalPromiseMqDTO.setPromiseId(promiseDto.getPromiseId());
            //设置状态
            medicalPromiseMqDTO.setMedicalPromiseStatus(medicalPromise.getStatus());


            /**
             * 报告生成条件，status = 6, ReportCenterId存在；
             */
            if(MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(medicalPromise.getStatus())) {

                MedicalPromiseRequest medicalPromiseRequest = new MedicalPromiseRequest();
                medicalPromiseRequest.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
                MedicalReportDTO medicalReportDTO = medicalReportApplication.getByMedicalPromiseId(medicalPromiseRequest);

                if(StringUtils.isBlank(medicalReportDTO.getReportCenterId())) {
                    throw new Exception("reportCenterId is null");
                }

                medicalPromiseMqDTO.setReportCenterId(medicalReportDTO.getReportCenterId());

            }

            log.info("履约消息发送B2C成功，订单id:{},发送 mq 内容:{}", medicalPromiseMqDTO.getOrderId(), JSON.toJSONString(medicalPromiseMqDTO));
            Message message = new Message(medicalPromiseTopic, JSON.toJSONString(medicalPromiseMqDTO), promiseDto.getSourceVoucherId());
            reachStoreProducer.send(message);

        } catch (JMQException e) {
            log.error("履约消息发送B2C errer，订单id:{},发送 mq 内容:{}", medicalPromiseMqDTO.getOrderId(), JSON.toJSONString(medicalPromiseMqDTO), e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("履约消息发送B2C medicalPromiseMq error.", e);
            throw new RuntimeException(e);
        }

    }

    /**
     * 接单，重派实验室
     * @param event
     */
    @LogAndAlarm
    private void dispatchReceivedStationDis(Event event){
        log.info("dispatchReceivedStationDis event={}", event);
        //1.查开关
        DispatchReceiveReStationDisConfig config = duccConfig.getDispatchReceiveReStationDisConfig();
        //总开关关闭
        if(Objects.isNull(config) || !config.getReSwitch()){
            return;
        }

        //2.查履约单
        //查派单
        String angelWorkIdStr = event.getAggregateId();
//        JdhDispatch dispatch = dispatchRepository.findDispatch(DispatchRepQuery.builder().dispatchId(Long.valueOf(dispatchIdStr)).build());
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(Long.valueOf(angelWorkIdStr)).build());

        // 非快检忽略
        boolean verticalCodeBool = serviceHomeTypeDomainService.getServiceHomeVerticalCode("XFYL_HOME_SELF_TEST_TRANSPORT").equalsIgnoreCase(angelWork.getVerticalCode());
        if (verticalCodeBool) {
            return;
        }

        //查履约单
        PromiseDto promise = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(angelWork.getPromiseId()).build());

        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(
                MedicalPromiseListQuery.builder().promiseId(promise.getPromiseId()).invalid(Boolean.FALSE).freezeQuery(Boolean.FALSE).allQuery(Boolean.FALSE).build()
        );
        if (CollectionUtils.isEmpty(medicalPromises)){
            return;
        }
        medicalPromises.removeIf(p->StringUtils.isBlank(p.getStationId()));
        if (CollectionUtils.isEmpty(medicalPromises)){
            return;
        }
        Set<String> station = medicalPromises.stream().map(MedicalPromise::getStationId).collect(Collectors.toSet());
        if (station.size()>1){
            return;
        }

        String reExpression = config.getReExpression();
        Map<String,Object> paramMap = new HashMap<>();

        //预约地点
        PromiseStationDto store = promise.getStore();

        //业务code
        paramMap.put("verticalCode",promise.getVerticalCode());
        //四级地址
        paramMap.put("provinceCode",store.getProvinceCode());
        paramMap.put("cityCode",store.getCityCode());
        paramMap.put("districtCode",store.getDistrictCode());
        paramMap.put("townCode",store.getTownCode());
        //护士身份
        AngelWork lastAngelWork = angelWorkRepository.findLastAngelWork(promise.getPromiseId());
        JdhAngel jdhAngel = angelRepository.find(JdhAngelIdentifier.builder().angelId(Long.valueOf(lastAngelWork.getAngelId())).build());
        if (Objects.isNull(jdhAngel)){
            return;
        }

        paramMap.put("jobNature",jdhAngel.getJobNature());
        //3.判断履约单是否符合要求
        if (!(Boolean) AviatorEvaluator.compile(reExpression,Boolean.TRUE).execute(paramMap)){
            return;
        }


        //4.查护士对应服务站对应实验室是否和检测单一致
        JdhStation jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(jdhAngel.getStationId()).build());
        String stationId = jdhStation.getStationId();
        if (StringUtils.isEmpty(stationId)){
            return;
        }
        if (StringUtils.equals(station.stream().findFirst().orElse(null),stationId)){
            return;
        }

        StoreInfoRequest storeInfoRequest = new StoreInfoRequest();
        storeInfoRequest.setStationId(stationId);
        StoreInfoDto storeInfoDto = providerStoreApplication.queryStationInfo(storeInfoRequest);

        List<Long> serviceItemIds = medicalPromises.stream().map(MedicalPromise::getServiceItemId).map(Long::valueOf).collect(Collectors.toList());

        Set<Long> canServiceItems = new HashSet<>();

        //查询实验室关联项目
        List<JdhStationServiceItemRelDto> jdhStationServiceItemRelDtos = providerStoreApplication.queryStationServiceItemRelList(
                JdhStationServiceItemRelRequest.builder().stationId(stationId).serviceItemIds(serviceItemIds).build()
        );

        if (CollectionUtils.isNotEmpty(jdhStationServiceItemRelDtos)){
            canServiceItems = jdhStationServiceItemRelDtos.stream().map(JdhStationServiceItemRelDto::getServiceItemId).collect(Collectors.toSet());
        }


        //5.重派实验室
        for (MedicalPromise mp : medicalPromises) {
            //如果没有服务站信息
            if (mp.getStatus() >= 4 || mp.getReportStatus() == 1 || !canServiceItems.contains(Long.valueOf(mp.getServiceItemId()))){
                continue;
            }
            updateMp(mp, storeInfoDto, jdhStation);
        }

    }

    /**
     * 更新检测单信息
     * @param mp 原始的医疗承诺对象。
     * @param storeInfoDto 存储信息DTO。
     * @param jdhStation Jdh站点信息。
     */
    private void updateMp(MedicalPromise mp, StoreInfoDto storeInfoDto, JdhStation jdhStation) {
        MedicalPromise medicalPromise = new MedicalPromise();
        medicalPromise.setId(mp.getId());
        medicalPromise.setMedicalPromiseId(mp.getMedicalPromiseId());
        medicalPromise.setProviderId(storeInfoDto.getProviderId());
        medicalPromise.setStationId(storeInfoDto.getStationId());
        medicalPromise.setStationName(storeInfoDto.getStoreName());
        medicalPromise.setStationAddress(storeInfoDto.getStationAddress());
        medicalPromise.setStationPhone(storeInfoDto.getStationPhone());
        medicalPromise.setAngelStationId(String.valueOf(jdhStation.getAngelStationId()));
        medicalPromise.setVersion(mp.getVersion());
        medicalPromiseRepository.save(medicalPromise);
    }

    /**
     * 检查
     */
    private void checkOrderAllReportIsDone(Event event) {
        MedicalPromiseEventBody eventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(eventBody.getMedicalPromiseId()).build());
        PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(medicalPromise.getPromiseId()).build());
        VoucherPageRequest request = VoucherPageRequest.builder()
                .sourceVoucherId(promiseDto.getSourceVoucherId()).build();
        List<VoucherDto> voucherList = voucherApplication.queryVoucherList(request);
        if (CollUtil.isEmpty(voucherList)) {
            log.info("checkOrderAllReportIsDone voucherList is empty, medicalPromiseId={}", eventBody.getMedicalPromiseId());
            return;
        }
        int totalVoucher = voucherList.stream().map(VoucherDto::getPromiseNum).filter(Objects::nonNull).reduce(0, Integer::sum);
        if (totalVoucher == 0) {
            log.info("checkOrderAllReportIsDone totalVoucher is 0, medicalPromiseId={}", eventBody.getMedicalPromiseId());
            return;
        }
        List<PromiseDto> orderPromiseList = promiseApplication.findByPromiseList(PromiseListRequest.builder().voucherIds(voucherList.stream().map(VoucherDto::getVoucherId).collect(Collectors.toList())).build());
        if (CollUtil.isEmpty(orderPromiseList)) {
            log.info("checkOrderAllReportIsDone orderPromiseList is empty, medicalPromiseId={}", eventBody.getMedicalPromiseId());
            return;
        }
        // 查询订单下报告已出检测单
        List<MedicalPromise> medicalPromiseReportedList = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseIdList(orderPromiseList.stream().map(PromiseDto::getPromiseId).collect(Collectors.toList())).reportStatus(CommonConstant.ONE).build());
        if (CollUtil.isEmpty(medicalPromiseReportedList)) {
            log.info("checkOrderAllReportIsDone medicalPromiseReportedList is empty, medicalPromiseId={} orderId={}", eventBody.getMedicalPromiseId(), promiseDto.getSourceVoucherId());
            return;
        }
        if (totalVoucher == medicalPromiseReportedList.size()) {
            log.info("checkOrderAllReportIsDone 订单下报告已全部出具, medicalPromiseId={} orderId={}", eventBody.getMedicalPromiseId(), promiseDto.getSourceVoucherId());
            JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(Long.parseLong(promiseDto.getSourceVoucherId())));
            //推送事件
            eventCoordinator.publish(EventFactory.newDefaultEvent(jdOrder, TradeEventTypeEnum.ORDER_ALL_REPORTED, new OrderSplitEventBody(jdOrder)));
        } else {
            log.info("checkOrderAllReportIsDone 订单下报告未全部出具, medicalPromiseId={} orderId={}", eventBody.getMedicalPromiseId(), promiseDto.getSourceVoucherId());
        }
    }

    public void medicalPromiseCallBack(Event event){
        log.info("medicalPromiseCallBack->event={}", JsonUtil.toJSONString(event));
        MedicalPromiseEventBody eventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(eventBody.getMedicalPromiseId()).build());

        Integer snapShotStatus = medicalPromise.getStatus();
        Integer snapShotSubStatus = medicalPromise.getSubStatus();

        log.info("medicalPromiseCallBack->medicalPromise={}", JsonUtil.toJSONString(medicalPromise));

        //判断是新老逻辑，如果没有主状态是老逻辑，有主状态是新逻辑

        if (Objects.nonNull(eventBody.getResultType()) && Objects.isNull(eventBody.getCoreStatus())){
            //老逻辑，签收
            eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SUBJECTIVE_STATION_RECEIVE,
                    MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).status(medicalPromise.getStatus()).outerId(eventBody.getAppointmentId()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).build()));
            return;
        }


        //其他回调都是新逻辑
        //修改状态

        //先查是几个节点，如果是3个 并且子状态是402 签收就是上机
        ProviderEquipmentDto providerEquipmentDto = providerEquipmentApplication.queryEquipmentDtoByStationIdAndItemId(medicalPromise.getStationId(), Long.valueOf(medicalPromise.getServiceItemId()));

        Integer detectingNodesNum  = Objects.nonNull(providerEquipmentDto) ? providerEquipmentDto.getDetectingNodesNum() : CommonConstant.THREE;

        //日志
        OperationLogCmd operationLogCmd = getOperationLogCmd(medicalPromise);

        //日志详情
        MedicalPromiseLogCmd medicalPromiseLogCmd = getMedicalPromiseLogCmd(medicalPromise, eventBody);

        //日志action
        BizSceneActionKeyEnum bizSceneActionKeyEnum = null;

        //子状态
        MedicalPromiseSubStatusEnum enumBySubStatus = MedicalPromiseSubStatusEnum.getEnumBySubStatus(eventBody.getSubStatus());
        if (Objects.isNull(enumBySubStatus)){
            return;
        }
        medicalPromise.setStatus(enumBySubStatus.getStatus());
        medicalPromise.setSubStatus(enumBySubStatus.getSubStatus());
        log.info("medicalPromiseCallBack->enumBySubStatus={}", JsonUtil.toJSONString(enumBySubStatus));

        //检测中状态流转
        if ( Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),enumBySubStatus.getStatus())) {

            //如果为3个节点，并且不是未上机异常状态，回调则认为是上机检测中
            if (Objects.equals(CommonConstant.THREE,detectingNodesNum) &&
                    !MedicalPromiseSubStatusEnum.getUnCollectSubStatus().contains(eventBody.getSubStatus())
                    && CollectionUtil.isEmpty(eventBody.getSampleAnomalyOperateReasonType())
            ){

                //非异常 不允许重复上报
                if (snapShotStatus >= 4 ){
                    return;
                }
                //补充收样状态、检测状态
                if (Objects.isNull(medicalPromise.getCheckTime())){
                    medicalPromise.setCheckStatus(CommonConstant.ONE);
                    medicalPromise.setCheckTime(new Date());
                }
                if (Objects.isNull(medicalPromise.getTestTime())){
                    medicalPromise.setTestTime(new Date());
                    medicalPromise.setTestStatus(CommonConstant.ONE);
                }

                medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_TEST.getSubStatus());
                //修改状态
                if (StringUtils.isEmpty(medicalPromise.getSerialNum())){
                    //补充顺序
                    String serialNum = generalSerialNum(medicalPromise);
                    log.info("registerEventConsumer->medicalPromiseCallBack,serialNum={}",serialNum);
                    medicalPromise.setSerialNum(serialNum);
                    medicalPromiseRepository.updateSerialNum(medicalPromise);
                }

                bizSceneActionKeyEnum = BizSceneActionKeyEnum.RECEIVE_SAMPLE;

            }else if (Objects.equals(CommonConstant.FIVE,detectingNodesNum)){

                //如果是5个节点
                //收样-》样本处理中
                if (Objects.equals(MedicalPromiseSubStatusEnum.ARRIVE_STATION_WAIT_COLLECT.getSubStatus(),eventBody.getSubStatus())){
                    if (snapShotStatus >= 4){
                        return;
                    }
                    if (Objects.isNull(medicalPromise.getCheckTime())){
                        medicalPromise.setCheckStatus(CommonConstant.ONE);
                        medicalPromise.setCheckTime(new Date());
                    }
                    if (StringUtils.isEmpty(medicalPromise.getSerialNum())){
                        //补充顺序
                        String serialNum = generalSerialNum(medicalPromise);
                        log.info("registerEventConsumer->medicalPromiseCallBack,serialNum={}",serialNum);
                        medicalPromise.setSerialNum(serialNum);
                        medicalPromiseRepository.updateSerialNum(medicalPromise);
                    }
                    //正常收样->状态变为样本处理中
                    if (CollectionUtils.isEmpty(eventBody.getSampleAnomalyOperateReasonType())){
                        medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getSubStatus());
                    }
                    bizSceneActionKeyEnum = BizSceneActionKeyEnum.RECEIVE_SAMPLE;

                }else if (Objects.equals(MedicalPromiseSubStatusEnum.SAMPLE_TEST.getSubStatus(),eventBody.getSubStatus())){
                    //上机
                    if (Objects.isNull(medicalPromise.getCheckTime())){
                        medicalPromise.setCheckStatus(CommonConstant.ONE);
                        medicalPromise.setCheckTime(new Date());
                    }
                    if (Objects.isNull(medicalPromise.getTestTime())){
                        medicalPromise.setTestTime(new Date());
                        medicalPromise.setTestStatus(CommonConstant.ONE);
                    }
                    bizSceneActionKeyEnum = BizSceneActionKeyEnum.TEST_SAMPLE;

                }else if (Objects.equals(MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getSubStatus(),eventBody.getSubStatus())){
                    //样本处理中
                    if (Objects.isNull(medicalPromise.getCheckTime())){
                        medicalPromise.setCheckStatus(CommonConstant.ONE);
                        medicalPromise.setCheckTime(new Date());
                    }
                    medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getSubStatus());
                    bizSceneActionKeyEnum = BizSceneActionKeyEnum.RECEIVE_SAMPLE;

                }
            }

            //如果需要流转码，则查询流转码
            getFlowCode(bizSceneActionKeyEnum, providerEquipmentDto, medicalPromise);
        }

        //异常情况,根据不同异常修改状态
        if (CollectionUtils.isNotEmpty(eventBody.getSampleAnomalyOperateReasonType())) {
            bizSceneActionKeyEnum = getBizSceneActionKeyEnum(eventBody, medicalPromise, medicalPromiseLogCmd);
        }


        //状态判断
        if (!medicalPromiseStatusStage(snapShotStatus,medicalPromise.getStatus(),snapShotSubStatus,medicalPromise.getSubStatus())){
            log.info("medicalPromiseCallBack->状态错误，snapShotStatus={},medicalPromiseStatus={},snapShotSubStatus={},subStatus{}",snapShotStatus,medicalPromise.getStatus(),snapShotSubStatus,medicalPromise.getSubStatus());
            return;
        }


        //更新
        int save = medicalPromiseRepository.save(medicalPromise);
        //如果更新失败，重试
        if (save < 1){
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATUS_CHANGE_ERROR);
        }

        // 如果是样本重测异常，则发送样本重测事件，promisego计算
        if (Objects.equals(SampleAnomalyOperateTypeEnum.RETEST.getType(), eventBody.getSampleAnomalyOperateType())) {
            eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SAMPLE_RETESTING,
                    MedicalPromiseEventBody.builder()
                            .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                            .beforeStatus(snapShotStatus)
                            .status(medicalPromise.getStatus())
                            .subStatus(medicalPromise.getSubStatus())
                            .specimenCode(medicalPromise.getSpecimenCode())
                            .verticalCode(medicalPromise.getVerticalCode())
                            .serviceType(medicalPromise.getServiceType())
                            .promiseId(medicalPromise.getPromiseId())
                            .build()));
        }



        //如果有异常，告警
        try {
            alarm(eventBody, medicalPromise);
        }catch (Exception e){
            log.info("medicalPromiseCallBack->alarm error", e);
        }

        //如果是报告驳回，则需要判读报告审批单
        if (Objects.equals(MedicalPromiseSubStatusEnum.REPORT_CHECK_ERROR.getSubStatus(),eventBody.getSubStatus())){
            //报告审核驳回
            bizSceneActionKeyEnum = getBizSceneActionKeyEnum(medicalPromise, eventBody);
        }

        //日志action兜底
        if (Objects.isNull(bizSceneActionKeyEnum)){
            bizSceneActionKeyEnum = enumBySubStatus.getBizSceneActionKey();
        }

        log.info("medicalPromiseCallBack-> subStatus{}, flowCode={}", medicalPromise.getSubStatus(), medicalPromise.getFlowCode());
        if (Objects.equals(MedicalPromiseSubStatusEnum.SAMPLE_TEST_ERROR_RE_TEST.getSubStatus(),medicalPromise.getSubStatus()) && StringUtil.isNotBlank(medicalPromise.getFlowCode())){
            //样本检测异常，重新检测,调用释放流转码
            freeFlowCode(medicalPromise);
        }


        //判断是否同步实验室状态
        //如果是实验室端调用的，则需要同步三方系统对方，如果是三方系统调过来的，则不需要同步
        eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_CALL_BACK_SYNC_STATION,
                MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).status(medicalPromise.getStatus()).outerId(eventBody.getAppointmentId()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).build()));

        //记录日志
        if (Objects.nonNull(bizSceneActionKeyEnum)){
            medicalPromiseLogCmd.setOperateType(bizSceneActionKeyEnum.getBizSceneActionKey());
            operationLogCmd.setBizSceneActionKey(bizSceneActionKeyEnum.getBizSceneActionKey());
            operationLogCmd.setBizSceneActionDesc(bizSceneActionKeyEnum.getBizSceneActionDesc());
            operationLogCmd.setParam(JsonUtil.toJSONString(medicalPromiseLogCmd));
            operationLogJsfExport.batchInsertAsyncToLocalDB(Lists.newArrayList(operationLogCmd));

        }

        //如果5个节点&& 样本处理中  或者 3个节点 && 检测中，兼容旧逻辑则发送收样事件，promisego计算
        if ((Objects.equals(CommonConstant.FIVE,detectingNodesNum) && Objects.equals(medicalPromise.getSubStatus(),MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getSubStatus()))
                || (Objects.equals(CommonConstant.THREE,detectingNodesNum) && Objects.equals(medicalPromise.getSubStatus(),MedicalPromiseSubStatusEnum.SAMPLE_TEST.getSubStatus()))){
            eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE,
                    MedicalPromiseEventBody.builder()
                            .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                            .beforeStatus(snapShotStatus)
                            .status(medicalPromise.getStatus())
                            .beforeSubStatus(snapShotSubStatus)
                            .subStatus(medicalPromise.getSubStatus())
                            .specimenCode(medicalPromise.getSpecimenCode())
                            .verticalCode(medicalPromise.getVerticalCode())
                            .serviceType(medicalPromise.getServiceType())
                            .promiseId(medicalPromise.getPromiseId())
                            .build()));

        }


    }

    private  OperationLogCmd getOperationLogCmd(MedicalPromise medicalPromise) {
        OperationLogCmd operationLogCmd = new OperationLogCmd();
        operationLogCmd.setBizUnionId(String.valueOf(medicalPromise.getMedicalPromiseId()));
        operationLogCmd.setOperateTime(new Date());
        operationLogCmd.setOperateType(OperationLogOperateTypeEnum.UPDATE.getType());
        operationLogCmd.setBizSceneKey(BizSceneKeyEnum.MEDICAL_PROMISE_STATION_INTERACTION.getBizSceneKey());
        String append = MedicalPromiseStatusUtil.getDesc(medicalPromise);
        operationLogCmd.setBizSceneDesc(BizSceneKeyEnum.MEDICAL_PROMISE_STATION_INTERACTION.getBizSceneDesc() + (StringUtils.isNotBlank(append) ? "-" + append : ""));
        return operationLogCmd;
    }

    private void getFlowCode(BizSceneActionKeyEnum bizSceneActionKeyEnum, ProviderEquipmentDto providerEquipmentDto, MedicalPromise medicalPromise) {
        if (Objects.nonNull(bizSceneActionKeyEnum)
                && Objects.nonNull(providerEquipmentDto)
                && bizSceneActionKeyEnum.getBizSceneActionKey().equals(BizSceneActionKeyEnum.RECEIVE_SAMPLE.getBizSceneActionKey())
                && Objects.equals(CommonConstant.ONE, providerEquipmentDto.getStreamTranscoding())
        ){
            FlowCodeBO flowCodeBO = quickCheckThirdExportServiceRpc.queryFlowCode(FlowCodeQueryBO.builder().medicalPromiseId(String.valueOf(medicalPromise.getMedicalPromiseId())).specimenCode(medicalPromise.getSpecimenCode()).stationId(medicalPromise.getStationId()).specimenCodeList(Lists.newArrayList(medicalPromise.getSpecimenCode())).build());
            if (Objects.nonNull(flowCodeBO) && CollUtil.isNotEmpty(flowCodeBO.getTranCodeVoList())){
                Map<String, FlowCodeListBO> transCodeMap = flowCodeBO.getTranCodeVoList().stream().collect(Collectors.toMap(
                                FlowCodeListBO::getSampleBarcode,
                                codeDto -> codeDto,
                                (oldValue, newValue) -> oldValue // 保留第一个
                        ));
                if (CollUtil.isNotEmpty(transCodeMap) && transCodeMap.containsKey(medicalPromise.getSpecimenCode())) {
                    FlowCodeListBO flowCodeListBO = transCodeMap.get(medicalPromise.getSpecimenCode());
                    medicalPromise.setFlowCode(flowCodeListBO.getTransCode());
                }
            }
        }
    }

    private  BizSceneActionKeyEnum getBizSceneActionKeyEnum(MedicalPromiseEventBody eventBody, MedicalPromise medicalPromise, MedicalPromiseLogCmd medicalPromiseLogCmd) {
        BizSceneActionKeyEnum bizSceneActionKeyEnum;
        bizSceneActionKeyEnum = BizSceneActionKeyEnum.EXCEPTION_SUBMIT;
        //样本检查中
        if (Objects.equals(MedicalPromiseSubStatusEnum.SAMPLE_CHECK.getSubStatus(), eventBody.getSubStatus())){
            //样本检查-重采样->251
            if (Objects.equals(SampleAnomalyOperateTypeEnum.RESAMPLE.getType(), eventBody.getSampleAnomalyOperateType())){
                medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_RE_COLLECT_CONNECT.getSubStatus());
            }else if (Objects.equals(SampleAnomalyOperateTypeEnum.CONCESSION_TEST.getType(), eventBody.getSampleAnomalyOperateType())){
                //样本检查-让步检测->254
                medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_CONNECT.getSubStatus());
            }
        }else if (Objects.equals(MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getSubStatus(), eventBody.getSubStatus())){
            //样本处理中
            //样本处理-重采样->251
            if (Objects.equals(SampleAnomalyOperateTypeEnum.RESAMPLE.getType(), eventBody.getSampleAnomalyOperateType())){
                medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_DEAL_ERROR_RE_COLLECT_CONNECT.getSubStatus());
            }
        } else if (Objects.equals(MedicalPromiseSubStatusEnum.SAMPLE_TEST.getSubStatus(), eventBody.getSubStatus())){
            //样本检测->重检测
            // 部分API对接厂商无法获取样本检测、样本处理节点，只有上机操作节点
            if (Objects.equals(SampleAnomalyOperateTypeEnum.RETEST.getType(), eventBody.getSampleAnomalyOperateType())){
                medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_TEST_ERROR_RE_TEST.getSubStatus());
            } else if (Objects.equals(SampleAnomalyOperateTypeEnum.RESAMPLE.getType(), eventBody.getSampleAnomalyOperateType())){
                medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_RE_COLLECT_CONNECT.getSubStatus());
            }else if (Objects.equals(SampleAnomalyOperateTypeEnum.CONCESSION_TEST.getType(), eventBody.getSampleAnomalyOperateType())){
                //样本检查-让步检测->254
                medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_CONNECT.getSubStatus());
            }
        }

        MedicalPromiseExceptionBO exceptionBO = new MedicalPromiseExceptionBO();
        exceptionBO.setSampleAnomalyOperateType(eventBody.getSampleAnomalyOperateType());
        exceptionBO.setSampleAnomalyOperateReason(eventBody.getSampleAnomalyOperateReason());
        exceptionBO.setSampleAnomalyOperateReasonType(eventBody.getSampleAnomalyOperateReasonType());
        medicalPromise.setExceptionRecord(JsonUtil.toJSONString(exceptionBO));

        medicalPromiseLogCmd.setExceptionMsg(exceptionBO.getSampleAnomalyOperateReason());
        medicalPromiseLogCmd.setDealType(exceptionBO.getSampleAnomalyOperateType());
        medicalPromiseLogCmd.setDealTypeDesc(SampleAnomalyOperateTypeEnum.getDescByType(exceptionBO.getSampleAnomalyOperateType()));
        return bizSceneActionKeyEnum;
    }

    private MedicalPromiseLogCmd getMedicalPromiseLogCmd(MedicalPromise medicalPromise, MedicalPromiseEventBody eventBody) {
        MedicalPromiseLogCmd medicalPromiseLogCmd = new MedicalPromiseLogCmd();
        medicalPromiseLogCmd.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        medicalPromiseLogCmd.setParam(eventBody.getParam());
        if (StringUtils.isNotBlank(eventBody.getFlowCode())){
            medicalPromiseLogCmd.setFlowCode(eventBody.getFlowCode());
        }else {
            medicalPromiseLogCmd.setFlowCode(medicalPromise.getFlowCode());
        }
        medicalPromiseLogCmd.setSpecimenCode(medicalPromise.getSpecimenCode());
        medicalPromiseLogCmd.setStationId(medicalPromise.getStationId());
        medicalPromiseLogCmd.setOperateTime(new Date());
        medicalPromiseLogCmd.setOperatePin(eventBody.getOperator());
        return medicalPromiseLogCmd;
    }

    private void freeFlowCode(MedicalPromise medicalPromise) {
        FlowCodeListBO flowCodeListBO = new FlowCodeListBO();
        flowCodeListBO.setTransCode(medicalPromise.getFlowCode());
        flowCodeListBO.setSampleBarcode(medicalPromise.getSpecimenCode());
        quickCheckThirdExportServiceRpc.freeFlowCode(FreeFlowCodeBO.builder().medicalPromiseId(String.valueOf(medicalPromise.getMedicalPromiseId())).flowCodeList(Lists.newArrayList(flowCodeListBO)).stationId(medicalPromise.getStationId()).build());
    }

    private  BizSceneActionKeyEnum getBizSceneActionKeyEnum(MedicalPromise medicalPromise, MedicalPromiseEventBody eventBody) {
        BizSceneActionKeyEnum bizSceneActionKeyEnum;
        List<ReportAudit> reportAudits = reportAuditRepository.queryReportAuditList(ReportAuditQueryBO.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).build());
        if (CollectionUtil.isNotEmpty(reportAudits)){
            List<ReportAudit> sort = reportAudits.stream().sorted(Comparator.comparing(ReportAudit::getCreateTime).reversed()).collect(Collectors.toList());
            ReportAudit reportAudit = sort.get(0);
            reportAudit.setAuditStatus(CommonConstant.TWO);
            reportAudit.setAuditTime(new Date());
            reportAudit.setAuditUser(eventBody.getOperator());
            reportAuditRepository.updateReportAuditStatus(Lists.newArrayList(reportAudit));
            medicalReportDataRepository.updateByExperimentalId(reportAudit.getExperimentalId());
        }

        bizSceneActionKeyEnum = BizSceneActionKeyEnum.AUDIT_REFUSE;
        return bizSceneActionKeyEnum;
    }

    /**
     * 发送告警消息给机器人
     * @param eventBody 事件体对象，包含样本异常操作原因类型和子状态等信息
     * @param medicalPromise 医疗承诺对象，包含订单号、样本编码、检测项目、实验室等信息
     */
    private void alarm(MedicalPromiseEventBody eventBody, MedicalPromise medicalPromise) {
        if (CollectionUtils.isNotEmpty(eventBody.getSampleAnomalyOperateReasonType())){
            //判断是否需要告警
            List<SubStatusAlarmConfig> subStatusAlarmConfigs = duccConfig.getSubStatusAlarmConfig();
            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();

            SubStatusAlarmConfig subStatusAlarmConfig = subStatusAlarmConfigs.stream().filter(p -> Objects.equals(p.getSubStatus(), eventBody.getSubStatus())).findFirst().orElse(null);
            JSONObject jsonObject = null;
            String alarmTemple = "";
            if (Objects.nonNull(subStatusAlarmConfig)){
                //告警
                jsonObject = robotAlarmMap.get(subStatusAlarmConfig.getAlarmConfig());
                alarmTemple = subStatusAlarmConfig.getAlarmTemple();
            }else {
                //走默认告警
                jsonObject = robotAlarmMap.get("心智之战通知");
                alarmTemple = "实验室在{stageDesc}阶段反馈由于{reason}，需联系用户是否{dealDesc}，订单号：{orderId},履约单号:{promiseId},样本编码:{specimenCode}，检测项目:{serviceItemName}，实验室:{stationName}";
            }

            if (Objects.equals(SampleAnomalyOperateTypeEnum.RETEST.getType(),eventBody.getSampleAnomalyOperateType())){
                alarmTemple = "实验室在{stageDesc}阶段反馈由于{reason}，需要{dealDesc}，订单号：{orderId},履约单号{promiseId},样本编码{specimenCode}，检测项目{serviceItemName}，实验室{stationName}";
            }

            if (Objects.nonNull(jsonObject)){
                String stageDesc = MedPromiseMainStatusSyncEnum.getCallBackMainStatusDesc(eventBody.getCoreStatus());
                String reason = SampleAnomalyOperateReasonTypeEnum.packReasonDesc(eventBody.getSampleAnomalyOperateReasonType());
                if (CollectionUtils.isNotEmpty(eventBody.getSampleAnomalyOperateReasonType()) && eventBody.getSampleAnomalyOperateReasonType().contains(999)){
                    reason = reason+ eventBody.getSampleAnomalyOperateReason();
                }
                String dealDesc = SampleAnomalyOperateTypeEnum.getDescByType(eventBody.getSampleAnomalyOperateType());
                //订单号
                JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(medicalPromise.getPromiseId()).build());
                //样本编码
                //检测项目
                //实验室
                Map<String,String> replaceMap = Maps.newHashMap();
                replaceMap.put("stageDesc",stageDesc);
                replaceMap.put("reason",reason);
                replaceMap.put("dealDesc",dealDesc);
                replaceMap.put("orderId",promise.getSourceVoucherId());
                replaceMap.put("specimenCode", medicalPromise.getSpecimenCode());
                replaceMap.put("serviceItemName", medicalPromise.getServiceItemName());
                replaceMap.put("stationName", medicalPromise.getStationName());
                replaceMap.put("promiseId", String.valueOf(medicalPromise.getPromiseId()));
                replaceMap.put("medicalPromiseId", String.valueOf(medicalPromise.getMedicalPromiseId()));
                String message = TextUtil.replacePlaceholders(alarmTemple,replaceMap);

                dongDongRobotRpc.sendDongDongRobotMessage(message,jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            }
        }
    }

    /**
     * AI体检报告解读
     * @param event
     */
    @LogAndAlarm
    public void medMedicalPromiseAiRead(Event event){
        medicalPromiseApplication.aiRead(Long.parseLong(event.getAggregateId()));
    }
    /**
     * 获取序号
     * @param medicalPromise
     * @return
     */
    private String generalSerialNum(MedicalPromise medicalPromise){
        if (StringUtils.isNotBlank(medicalPromise.getSerialNum())){
            return medicalPromise.getSerialNum();
        }

        String checkDate = DateUtil.format(medicalPromise.getCheckTime(), CommonConstant.YMD);
        String stationSerialNumKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.MEDICAL_PROMISE_SERIAL_KEY,medicalPromise.getStationId(),checkDate);
        log.info("MedicalPromiseEventSubscriber->generalSerialNum,stationSerialNumKey={}",stationSerialNumKey);
        Long incr = cluster.incr(stationSerialNumKey);
        cluster.expireAt(stationSerialNumKey,DateUtil.endOfDay(medicalPromise.getCheckTime()));
        return String.valueOf(incr);

    }

    /**
     * 判断医疗承诺状态是否可以从当前状态转换到目标状态。
     * @param status 当前主状态。
     * @param afterStatus 目标主状态。
     * @param subStatus 当前子状态。
     * @param afterSubStatus 目标子状态。
     * @return 如果可以转换，则返回 true；否则返回 false。
     */
    private Boolean medicalPromiseStatusStage(Integer status,Integer afterStatus,Integer subStatus,Integer afterSubStatus){
        String medicalPromiseCallbackStatusStage = duccConfig.getMedicalPromiseCallbackStatusStage();

        Map<String,Object> map = Maps.newHashMap();
        map.put("status",status);
        map.put("afterStatus",afterStatus);
        map.put("subStatus",subStatus);
        map.put("afterSubStatus",afterSubStatus);

        return (Boolean) AviatorEvaluator.compile(medicalPromiseCallbackStatusStage,Boolean.TRUE).execute(map);

    }


}