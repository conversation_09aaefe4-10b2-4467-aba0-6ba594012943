package com.jdh.o2oservice.application.medicalpromise;

import com.jdh.o2oservice.common.enums.MedPromiseMainStatusSyncEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseSubStatusEnum;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import org.apache.commons.lang3.StringUtils;

public class MedicalPromiseStatusUtil {


    public static String getDesc(MedicalPromise medicalPromise) {
        try {
            if (medicalPromise == null) {
                return null;
            }
            String bizDesc = null;
            if (medicalPromise.getStatus() != null) {
                String coreDesc = MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus());
                if (StringUtils.isNotBlank(coreDesc)) {
                    bizDesc = "【主状态】" + coreDesc;
                }
            }
            if (medicalPromise.getSubStatus() != null) {
                MedicalPromiseSubStatusEnum medicalPromiseSubStatusEnum = MedicalPromiseSubStatusEnum.getEnumBySubStatus(medicalPromise.getSubStatus());
                if (medicalPromiseSubStatusEnum != null) {
                    if (StringUtils.isNotBlank(bizDesc)) {
                        bizDesc += ",【子状态】" + medicalPromiseSubStatusEnum.getDesc();
                    } else {
                        bizDesc = "【子状态】" + medicalPromiseSubStatusEnum.getDesc();
                    }
                }
            }
            return bizDesc;
        } catch (Exception e) {
            return null;
        }
    }

    public static String getDesc(MedicalPromiseDTO medicalPromiseDTO) {
        try {
            if (medicalPromiseDTO == null) {
                return null;
            }
            String bizDesc = null;
            if (medicalPromiseDTO.getStatus() != null) {
                String coreDesc = MedicalPromiseStatusEnum.getDescByStatus(medicalPromiseDTO.getStatus());
                if (StringUtils.isNotBlank(coreDesc)) {
                    bizDesc = "【主状态】" + coreDesc;
                }
            }
            if (medicalPromiseDTO.getSubStatus() != null) {
                MedicalPromiseSubStatusEnum medicalPromiseSubStatusEnum = MedicalPromiseSubStatusEnum.getEnumBySubStatus(medicalPromiseDTO.getSubStatus());
                if (medicalPromiseSubStatusEnum != null) {
                    if (StringUtils.isNotBlank(bizDesc)) {
                        bizDesc += ",【子状态】" + medicalPromiseSubStatusEnum.getDesc();
                    } else {
                        bizDesc = "【子状态】" + medicalPromiseSubStatusEnum.getDesc();
                    }
                }
            }
            return bizDesc;
        } catch (Exception e) {
            return null;
        }
    }


    public static Integer getCoreStatus(MedicalPromiseDTO medicalPromiseDTO) {
        try {
            if (medicalPromiseDTO == null) {
                return null;
            }
            if (medicalPromiseDTO.getCoreStatus() != null) {
                return medicalPromiseDTO.getCoreStatus();
            }
            if (MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseDTO.getStatus())) {
                return medicalPromiseDTO.getStatus();
            }
            if (MedicalPromiseStatusEnum.WAIT_COLLECTED.getStatus().equals(medicalPromiseDTO.getStatus())) {
                return MedPromiseMainStatusSyncEnum.WAITING_COLLECT.getCallBackMainStatus();
            }
            if (medicalPromiseDTO.getSubStatus() != null) {
                MedicalPromiseSubStatusEnum medicalPromiseSubStatusEnum = MedicalPromiseSubStatusEnum.getEnumBySubStatus(medicalPromiseDTO.getSubStatus());
                if (medicalPromiseSubStatusEnum != null) {
                    return medicalPromiseSubStatusEnum.getCallBackMainStatus();
                }
            }
        } catch (Exception e) {
            return medicalPromiseDTO.getStatus();
        }
        return medicalPromiseDTO.getStatus();
    }

}
