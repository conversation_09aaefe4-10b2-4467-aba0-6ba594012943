package com.jdh.o2oservice.application.via.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.*;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.PopLocStoreInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.StoreRpcBO;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.ViaBtnCodeEnum;
import com.jdh.o2oservice.core.domain.support.via.enums.ViaFloorEnum;
import com.jdh.o2oservice.core.domain.support.via.enums.ViaPageEnum;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.export.via.dto.ViaDetailUserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * POP_LOC预约详情页面
 *
 * <AUTHOR>
 * @date 2024/01/02
 */
@Slf4j
@Service
public class PopLocDetailPageHandler extends AbstractViaDataFillHandler implements MapAutowiredKey{


    /**
     * promiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;
    /**
     *
     */
    @Resource
    private PromiseHistoryRepository promiseHistoryRepository;

    /**
     * popLocStoreInfoRpc
     */
    @Resource
    private PopLocStoreInfoRpc popLocStoreInfoRpc;

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return ViaPageEnum.APPOINT_DETAIL.getScene() + "_" + BusinessModeEnum.POP_LOC.getCode();
    }

    /**
     * checkParam
     *
     * @param ctx ctx
     */
    private void checkParam(FillViaConfigDataContext ctx){
        //场景
        AssertUtils.hasText(ctx.getScene(), SupportErrorCode.VIA_CONFIG_NOT_EXIT);
        AssertUtils.hasText(ctx.getPromiseId(), SupportErrorCode.VIA_CONFIG_NOT_EXIT);
    }

    /**
     * 填充数据
     *
     * @param ctx ctx
     */
    @Override
    public void handle(FillViaConfigDataContext ctx) {
        log.info("PopVaccineDetailPageHandler handle ctx:{}",JSON.toJSONString(ctx));
        // 基本参数校验
        checkParam(ctx);
        ViaConfig viaConfig = ctx.getViaConfig();
        String promiseId = ctx.getPromiseId();

        List<ViaFloorInfo> floorList = viaConfig.getFloorList();
        Map<String, ViaFloorInfo> floorInfoMap = floorList.stream().collect(Collectors.toMap(ViaFloorInfo::getFloorCode, obj -> obj, (key1, key2) -> key1));
        log.info("PopVaccineDetailPageHandler handle floorInfoMap:{}",JSON.toJSONString(floorInfoMap));

        JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(Long.parseLong(promiseId)).build());
        if(Objects.isNull(jdhPromise)){
            throw new BusinessException(SupportErrorCode.VIA_PROMISE_INFO_NOT_EXIT);
        }
        //越权校验
        if(!ctx.getUserPin().equals(jdhPromise.getUserPin())){
            throw new BusinessException(SupportErrorCode.VIA_PROMISE_INFO_NOT_EXIT);
        }

        log.info("PopVaccineDetailPageHandler handle jdhPromise:{}",JSON.toJSONString(jdhPromise));

        Integer promiseStatus = jdhPromise.getPromiseStatus();
        String serviceId = jdhPromise.getServices().get(0).getServiceId().toString();
        ViaStatusMapping statusMapping = null;
        for (ViaStatusMapping promiseStatusMapping : viaConfig.getStatusMapping()) {
            if (promiseStatusMapping.getStatusList().contains(promiseStatus)) {
                statusMapping = promiseStatusMapping;
                break;
            }
        }
        log.info("PopVaccineDetailPageHandler handle statusMapping:{}",JSON.toJSONString(statusMapping));
        if(Objects.isNull(statusMapping)){
            throw new BusinessException(SupportErrorCode.DETAIL_PROMISE_STATUS_MAPPING_CONFIG_NOT_EXIT);
        }

        Map<String, Object> actionCommonParams = new HashMap<>(NumConstant.NUM_4);
        actionCommonParams.put("verticalCode",viaConfig.getVerticalCode());
        actionCommonParams.put("serviceId",serviceId);
        actionCommonParams.put("serviceType",viaConfig.getServiceType());
        log.info("PopVaccineDetailPageHandler handle actionCommonParams:{}",JSON.toJSONString(actionCommonParams));

        //=========>>>>> 1、 状态楼层
        if (!statusMapping.getHiddenFloorCode().contains(ViaFloorEnum.DETAIL_APPOINT_STATUS_INFO.getFloorCode())) {
            ViaFloorInfo statusInfoFloor = floorInfoMap.get(ViaFloorEnum.DETAIL_APPOINT_STATUS_INFO.getFloorCode());
            ViaFloorConfig statusFloorConfig = statusInfoFloor.getFloorConfigList().get(0);
            statusFloorConfig.setMainIcon(statusMapping.getMainIcon());
            statusFloorConfig.setStatusDesc(statusMapping.getStatusDesc());
            String tip = statusMapping.getNoticeTip();
            if (StringUtils.isNotBlank(tip) && tip.contains("{0}")){
                JdhPromiseHistory history = promiseHistoryRepository.findLastEvent(jdhPromise.getPromiseId(), jdhPromise.getPromiseStatus());
                tip = MessageFormat.format(tip, history.getReason());
            }
            statusFloorConfig.setNoticeTip(tip);
        }else{
            floorInfoMap.remove(ViaFloorEnum.DETAIL_APPOINT_STATUS_INFO.getFloorCode());
        }


        //=========>>>>> 2、 消费码楼层
        if (!statusMapping.getHiddenFloorCode().contains(ViaFloorEnum.DETAIL_PROMISE_CODE_INFO.getFloorCode())) {
            ViaFloorInfo promiseCodeFloor = floorInfoMap.get(ViaFloorEnum.DETAIL_PROMISE_CODE_INFO.getFloorCode());
            ViaFloorConfig promiseCodeFloorConfig = promiseCodeFloor.getFloorConfigList().get(0);
            promiseCodeFloorConfig.setPromiseCode(jdhPromise.getCode());
            promiseCodeFloorConfig.setPromiseCodePwd(jdhPromise.getCodePwd());
            promiseCodeFloorConfig.setPromiseQrCode(jdhPromise.getCodePwd());
            promiseCodeFloorConfig.setPromiseCodeMark(statusMapping.getPromiseCodeMark());
            promiseCodeFloorConfig.setPromiseCodeSaveBtn(statusMapping.getPromiseCodeSaveBtn());
            promiseCodeFloorConfig.setPromiseCodeMarkIcon(statusMapping.getPromiseCodeMarkIcon());
        }else{
            floorList.removeIf(next -> next.getFloorCode().equals(ViaFloorEnum.DETAIL_PROMISE_CODE_INFO.getFloorCode()));
        }


        //=========>>>>> 3、 预约信息楼层
        if(!statusMapping.getHiddenFloorCode().contains(ViaFloorEnum.DETAIL_APPOINT_INFO.getFloorCode())){
            ViaFloorInfo appointInfoFloor = floorInfoMap.get(ViaFloorEnum.DETAIL_APPOINT_INFO.getFloorCode());
            ViaFloorConfig appointInfoFloorConfig = appointInfoFloor.getFloorConfigList().get(0);
            Map<String,Object> appointInfoFloorConfigValue = new HashMap<>(NumConstant.NUM_4);
            if(Objects.nonNull(jdhPromise.getStore()) && StrUtil.isNotEmpty(jdhPromise.getStore().getStoreId())){
                StoreRpcBO storeRpcBO = popLocStoreInfoRpc.queryById(Long.parseLong(jdhPromise.getStore().getStoreId()));
                appointInfoFloorConfigValue.put("store",JSON.toJSONString(storeRpcBO));
            }
            if(Objects.nonNull(jdhPromise.findOnlyPatient())){
                appointInfoFloorConfigValue.put("user",JSON.toJSONString(buildUserDto(jdhPromise)));
            }
            if(Objects.nonNull(jdhPromise.getAppointmentTime())){
                appointInfoFloorConfigValue.put("appointmentTime",JSON.toJSONString(jdhPromise.getAppointmentTime().buildAppointmentTimeFieldMap()));
            }

            appointInfoFloorConfig.setValue(JSON.toJSONString(appointInfoFloorConfigValue));
        }else{
            floorList.removeIf(next -> next.getFloorCode().equals(ViaFloorEnum.DETAIL_APPOINT_INFO.getFloorCode()));
        }

        //=========>>>>> 4、 温馨楼层

        //=========>>>>> 5、 底部按钮楼层
        if(!statusMapping.getHiddenFloorCode().contains(ViaFloorEnum.DETAIL_FOOTER_BUTTONS.getFloorCode())){
            ViaFloorInfo footerButtonsFloor = floorInfoMap.get(ViaFloorEnum.DETAIL_FOOTER_BUTTONS.getFloorCode());
            List<ViaBtnInfo> btnList = footerButtonsFloor.getFloorConfigList().get(0).getBtnList();
            Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
            while (btnInfoIterator.hasNext()){
                ViaBtnInfo btnInfo = btnInfoIterator.next();
                if (statusMapping.getFooterButtonCodeList().contains(btnInfo.getCode())) {
                    ViaActionInfo action = btnInfo.getAction();
                    //重新预约，跳转链接需要重拼 https://laputa-yf.jd.com/xfyl-appointment/buyAppointment/gether?scene=popYmGether&pageType=modifyAppoint&promiseId={0}
                    if(ViaBtnCodeEnum.RE_APPOINT_BTN.getCode().equals(btnInfo.getCode())){
                        String url = action.getUrl();
                        String realUrl = MessageFormat.format(url,promiseId);
                        action.setUrl(realUrl);
                    }
                    //重新预约，跳转链接需要重拼 https://laputa-yf.jd.com/xfyl-appointment/buyAppointment/gether?scene=popYmGether&pageType=reAppoint&promiseId={0}
                    if(ViaBtnCodeEnum.MODIFY_APPOINT_BTN.getCode().equals(btnInfo.getCode())){
                        String url = action.getUrl();
                        String realUrl = MessageFormat.format(url,promiseId);
                        action.setUrl(realUrl);
                    }
                    //取消预约
                    if(ViaBtnCodeEnum.CANCEL_APPOINT_BTN.getCode().equals(btnInfo.getCode())){
                        Map<String, Object> params = new HashMap<>(actionCommonParams);
                        params.put("promiseId",jdhPromise.getPromiseId().toString());
                        action.setParams(params);

                        ViaActionInfo nextAction = action.getNextAction();
                        String url = nextAction.getUrl();
                        String realUrl = MessageFormat.format(url, promiseId);
                        nextAction.setUrl(realUrl);
                    }
                }else{
                    btnInfoIterator.remove();
                }
            }
        }else{
            floorList.removeIf(next -> next.getFloorCode().equals(ViaFloorEnum.DETAIL_FOOTER_BUTTONS.getFloorCode()));
        }

    }



    /**
     * buildUserDto
     *
     * @param jdhPromise jdhPromise
     * @return {@link ViaDetailUserDto}
     */
    private ViaDetailUserDto buildUserDto(JdhPromise jdhPromise){
        User user = jdhPromise.findOnlyPatient();
        CredentialNumber credentialNum = user.getCredentialNum();
        UserName userName = user.getUserName();
        PhoneNumber phoneNumber = user.getPhoneNumber();
        Birthday birthday = user.getBirthday();

        ViaDetailUserDto viaDetailUserDto = new ViaDetailUserDto();

        if(Objects.nonNull(userName)){
            viaDetailUserDto.setName(userName.encrypt());
            viaDetailUserDto.setNameMask(userName.getName());
        }

        if(Objects.nonNull(phoneNumber)) {
            viaDetailUserDto.setPhone(phoneNumber.encrypt());
            viaDetailUserDto.setPhoneMask(phoneNumber.mask());
        }

        if(Objects.nonNull(credentialNum)) {
            viaDetailUserDto.setCredentialType(credentialNum.getCredentialType());
            viaDetailUserDto.setCredentialNo(credentialNum.encrypt());
            viaDetailUserDto.setCredentialNoMask(credentialNum.mask());
        }

        //性别
        viaDetailUserDto.setGender(jdhPromise.findOnlyPatient().getGender());
        viaDetailUserDto.setGenderDesc(GenderEnum.getDescOfType(jdhPromise.findOnlyPatient().getGender()));

        if(Objects.nonNull(birthday)){
            viaDetailUserDto.setBirthday(birthday.getBirth());
            //年龄
            viaDetailUserDto.setRealTimeAge(user.getBirthday().getAge());
        }

        if(jdhPromise.getStore()!=null){
            //预约门店
            viaDetailUserDto.setStoreName(jdhPromise.getStore().getStoreName());
        }

        if (Objects.nonNull(jdhPromise.getAppointmentTime())){
            //预约时间
            viaDetailUserDto.setAppointmentTimeDesc(jdhPromise.getAppointmentTime().formatDesc());
        }

        viaDetailUserDto.setGender(user.getGender());
        viaDetailUserDto.setMarriage(user.getMarriage());
        viaDetailUserDto.setRelativesType(user.getRelativesType());

        return viaDetailUserDto;
    }
}
