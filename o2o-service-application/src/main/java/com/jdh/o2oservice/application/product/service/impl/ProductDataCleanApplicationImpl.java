package com.jdh.o2oservice.application.product.service.impl;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.gms.component.labrador.api.domain.*;
import com.jd.health.medical.examination.export.dto.SkuInfoDTO;
import com.jd.health.xfyl.merchant.export.enums.VenderTypeEnum;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.product.convert.ProductBizItemConvertor;
import com.jdh.o2oservice.application.product.convert.ProductProgramConvertor;
import com.jdh.o2oservice.application.product.convert.ProductServiceItemConvert;
import com.jdh.o2oservice.application.product.service.ProductDataCleanApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.common.enums.ProgramSaleShowMethodTypeEnum;
import com.jdh.o2oservice.base.enums.SuitableEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.EasyExcelUtil;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.enums.BizCategorySceneEnum;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.product.bo.*;
import com.jdh.o2oservice.core.domain.product.context.*;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.enums.StandardItemApplySceneEnum;
import com.jdh.o2oservice.core.domain.product.model.*;
import com.jdh.o2oservice.core.domain.product.repository.db.*;
import com.jdh.o2oservice.core.domain.product.repository.query.*;
import com.jdh.o2oservice.core.domain.provider.model.Provider;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderRepository;
import com.jdh.o2oservice.base.exception.errorcode.BaseDomainErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.category.model.JdhBizCategory;
import com.jdh.o2oservice.core.domain.support.category.repository.JdhBizCategoryRepository;
import com.jdh.o2oservice.core.domain.support.category.repository.query.BizCategoryRepQuery;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.export.angel.dto.ExaminationSkuBinLakeBody;
import com.jdh.o2oservice.export.product.cmd.SaveBizItemCmd;
import com.jdh.o2oservice.export.product.cmd.SaveProductBizItemCmd;
import com.jdh.o2oservice.export.product.cmd.StandardIndicatorCmd;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.product.enums.ProductSaleChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName ProductDataCleanApplicationImpl
 * @Description 商品域数据清洗
 * <AUTHOR>
 * @Date 2024/7/15 17:08
 **/
@Service
@Slf4j
public class ProductDataCleanApplicationImpl implements ProductDataCleanApplication {

    /**
     *
     */
    private static final ThreadLocal<Map<String, Set<Long>>> SCENE_INDICATOR_THREAD_LOCAL = new ThreadLocal<>();

    /**
     *
     */
    private static final ThreadLocal<Set<Long>> BIZ_ITEM_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * productServiceItemApplication
     */
    @Resource
    private ProductServiceItemApplication productServiceItemApplication;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * fileManageApplication
     */
    @Autowired
    private FileManageApplication fileManageApplication;

    /**
     * fileManageService
     */
    @Resource
    private FileManageService fileManageService;

    /**
     * 指标分类
     */
    @Resource
    private JdhBizCategoryRepository jdhBizCategoryRepository;

    /**
     * 标准指标
     */
    @Resource
    private JdhStandardIndicatorRepository jdhStandardIndicatorRepository;

    /**
     * 标准指标（老）
     */
    @Resource
    private JdhServiceIndicatorRepository serviceIndicatorRepository;

    /**
     * 标准指标分类（老）
     */
    @Resource
    private JdhIndicatorCategoryRepository indicatorCategoryRepository;

    /**
     * 标准项目
     */
    @Resource
    private JdhStandardItemRepository jdhStandardItemRepository;

    /**
     * 业务项目（老）
     */
    @Resource
    private JdhServiceItemRepository serviceItemRepository;

    /**
     * 业务项目关联的指标（老）
     */
    @Resource
    private JdhServiceItemIndicatorRepository serviceItemIndicatorRepository;

    /**
     * 业务项目（新）
     */
    @Resource
    private JdhBizItemRepository jdhBusinessItemRepository;

    /**
     * 京东服务与业务项目关联关系
     */
    @Resource
    private JdhServiceItemRelRepository serviceItemRelRepository;

    /**
     * 京东服务/套餐（老）
     */
    @Resource
    private JdhServiceRepository serviceRepository;

    /**
     * 京东组合服务（老）
     */
    @Resource
    private JdhServiceGroupRepository serviceGroupRepository;

    /**
     * 京东服务（新）
     */
    @Resource
    private JdhProgramRepository jdhProgramRepository;

    /**
     * 供应商
     */
    @Resource
    private ProviderRepository providerRepository;

    /**
     * skuInfoRpc
     */
    @Resource
    private SkuInfoRpc skuInfoRpc;

    /**
     * 导入标准指标数据
     * @param ossKey
     * @return
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean importStandardIndicator(String ossKey) {
        //获取文件流
        InputStream inputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);

        ImportParams params = new ImportParams();
        params.setHeadRows(1);
        params.setReadSingleCell(true);
        //params.setNeedVerify(true);
        //params.getLastOfInvalidRow();
        try {
            log.info("ProductDataCleanApplicationImpl -> 解析excel start：params={}", JSON.toJSONString(params));
            List<StandardIndicatorExcelModel> importResult = EasyExcelUtil.read(inputStream, 1, StandardIndicatorExcelModel.class);
            log.info("ProductDataCleanApplicationImpl -> 解析excel集合：importResult={}", JSON.toJSONString(importResult));
            //excel工具在合并单元格处理上会缺少数据，补充缺失分类的数据
            attachBizCategory(importResult);
            log.info("ProductDataCleanApplicationImpl -> 处理集合：importResult={}", JSON.toJSONString(importResult));

            //1.提取要保存的指标分类（包括分类数据、分类层级之间关系），保存分类
            Map<String, JdhBizCategory> name2FirstCategory = new HashMap<>();
            Map<String, JdhBizCategory> name2SecondCategory = new HashMap<>();
            Map<String, JdhBizCategory> name2ThirdCategory = new HashMap<>();
            Map<String, String> categoryName2ParentName = new HashMap<>();
            for (StandardIndicatorExcelModel indicatorExcelModel : importResult) {
                String levelFirstName = "1-" + indicatorExcelModel.getFirstBizCategoryName();
                if (!name2FirstCategory.containsKey(indicatorExcelModel.getFirstBizCategoryName())) {
                    JdhBizCategory build = JdhBizCategory.builder().bizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode()).categoryLevel(1).categoryName(indicatorExcelModel.getFirstBizCategoryName()).build();
                    name2FirstCategory.put(indicatorExcelModel.getFirstBizCategoryName(), build);
                }
                String levelSecondName = "2-" + indicatorExcelModel.getSecondBizCategoryName();
                if (!name2SecondCategory.containsKey(indicatorExcelModel.getSecondBizCategoryName())) {
                    JdhBizCategory build = JdhBizCategory.builder().bizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode()).categoryLevel(2).categoryName(indicatorExcelModel.getSecondBizCategoryName()).build();
                    name2SecondCategory.put(indicatorExcelModel.getSecondBizCategoryName(), build);
                    categoryName2ParentName.put(levelSecondName, levelFirstName);
                }
                String levelThirdName = "3-" + indicatorExcelModel.getThirdBizCategoryName();
                if (!name2ThirdCategory.containsKey(indicatorExcelModel.getThirdBizCategoryName())) {
                    JdhBizCategory build = JdhBizCategory.builder().bizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode()).categoryLevel(3).categoryName(indicatorExcelModel.getThirdBizCategoryName()).build();
                    name2ThirdCategory.put(indicatorExcelModel.getThirdBizCategoryName(), build);
                    categoryName2ParentName.put(levelThirdName, levelSecondName);
                }
            }
            //查数据库已存在的一级分类
            List<JdhBizCategory> bizFirstCategoryList = jdhBizCategoryRepository.queryJdhBizCategoryList(BizCategoryRepQuery.builder().bizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode()).categoryLevel(1).exactQueryIndicatorName(Boolean.TRUE).categoryNameList(name2FirstCategory.keySet()).build());
            for (JdhBizCategory jdhBizCategory : bizFirstCategoryList) {
                if (name2FirstCategory.containsKey(jdhBizCategory.getCategoryName())){
                    name2FirstCategory.get(jdhBizCategory.getCategoryName()).setCategoryId(jdhBizCategory.getCategoryId());
                    name2FirstCategory.get(jdhBizCategory.getCategoryName()).setVersion(jdhBizCategory.getVersion());
                }
            }
            //查数据库已存在的二级分类
            List<JdhBizCategory> bizSecondCategoryList = jdhBizCategoryRepository.queryJdhBizCategoryList(BizCategoryRepQuery.builder().bizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode()).categoryLevel(2).exactQueryIndicatorName(Boolean.TRUE).categoryNameList(name2SecondCategory.keySet()).build());
            for (JdhBizCategory jdhBizCategory : bizSecondCategoryList) {
                if (name2SecondCategory.containsKey(jdhBizCategory.getCategoryName())){
                    name2SecondCategory.get(jdhBizCategory.getCategoryName()).setCategoryId(jdhBizCategory.getCategoryId());
                    name2SecondCategory.get(jdhBizCategory.getCategoryName()).setVersion(jdhBizCategory.getVersion());
                }
            }
            //查数据库已存在的三级分类
            List<JdhBizCategory> bizThirdCategoryList = jdhBizCategoryRepository.queryJdhBizCategoryList(BizCategoryRepQuery.builder().bizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode()).categoryLevel(3).exactQueryIndicatorName(Boolean.TRUE).categoryNameList(name2ThirdCategory.keySet()).build());
            for (JdhBizCategory jdhBizCategory : bizThirdCategoryList) {
                if (name2ThirdCategory.containsKey(jdhBizCategory.getCategoryName())){
                    name2ThirdCategory.get(jdhBizCategory.getCategoryName()).setCategoryId(jdhBizCategory.getCategoryId());
                    name2ThirdCategory.get(jdhBizCategory.getCategoryName()).setVersion(jdhBizCategory.getVersion());
                }
            }
            //分类基础数据保存
            List<JdhBizCategory> saveBizCategoryList = Lists.newArrayList(name2FirstCategory.values());
            saveBizCategoryList.addAll(name2SecondCategory.values());
            saveBizCategoryList.addAll(name2ThirdCategory.values());
            for (JdhBizCategory jdhBizCategory : saveBizCategoryList) {
                jdhBizCategoryRepository.save(jdhBizCategory);
            }
            log.info("ProductDataCleanApplicationImpl -> 分类基础数据保存, firstCategoryList={}", JSON.toJSONString(name2FirstCategory.values()));
            log.info("ProductDataCleanApplicationImpl -> 分类基础数据保存, secondCategoryList={}", JSON.toJSONString(name2SecondCategory.values()));
            log.info("ProductDataCleanApplicationImpl -> 分类基础数据保存, thirdCategoryList={}", JSON.toJSONString(name2ThirdCategory.values()));
            log.info("ProductDataCleanApplicationImpl -> 分类基础数据保存, saveBizCategoryList={}", JSON.toJSONString(saveBizCategoryList));
            //处理分类关系保存
            List<JdhBizCategory> updateBizCategoryList = Lists.newArrayList();
            log.info("ProductDataCleanApplicationImpl -> 分类名称与父级分类名称映射, categoryName2ParentName={}",JSON.toJSONString(categoryName2ParentName));
            //二级分类
            for (JdhBizCategory jdhBizCategory : name2SecondCategory.values()) {
                String levelName = jdhBizCategory.getCategoryLevel() + "-" + jdhBizCategory.getCategoryName();
                if (categoryName2ParentName.containsKey(levelName)) {
                    String levelFirstName = categoryName2ParentName.get(levelName);
                    levelFirstName = levelFirstName.replace("1-","");
                    JdhBizCategory jdhBizFirstCategory = name2FirstCategory.get(levelFirstName);
                    jdhBizCategory.setParentCategoryId(Objects.isNull(jdhBizFirstCategory) ? null : jdhBizFirstCategory.getCategoryId());
                    updateBizCategoryList.add(jdhBizCategory);
                }
            }
            //三级分类
            for (JdhBizCategory jdhBizCategory : name2ThirdCategory.values()) {
                String levelName = jdhBizCategory.getCategoryLevel() + "-" + jdhBizCategory.getCategoryName();
                if (categoryName2ParentName.containsKey(levelName)) {
                    String levelSecondName = categoryName2ParentName.get(levelName);
                    levelSecondName = levelSecondName.replace("2-","");
                    JdhBizCategory jdhBizSecondCategory = name2SecondCategory.get(levelSecondName);
                    jdhBizCategory.setParentCategoryId(Objects.isNull(jdhBizSecondCategory) ? null : jdhBizSecondCategory.getCategoryId());
                    updateBizCategoryList.add(jdhBizCategory);
                }
            }
            //保存
            log.info("ProductDataCleanApplicationImpl -> 处理分类关系保存, updateBizCategoryList={}", JSON.toJSONString(updateBizCategoryList));
            for (JdhBizCategory jdhBizCategory : updateBizCategoryList) {
                jdhBizCategoryRepository.save(jdhBizCategory);
            }

            //2.将已保存的指标分类ID、标准项目ID放入指标数据中，保存指标
            List<JdhStandardIndicator> jdhStandardIndicators = new ArrayList<>();
            Map<String, JdhStandardIndicator> code2Indicator = new HashMap<>();
            for (StandardIndicatorExcelModel indicatorExcelModel : importResult) {
                if (StringUtils.isBlank(indicatorExcelModel.getHealthIndicatorCode())) {
                    log.info("ProductDataCleanApplicationImpl -> 指标没有 检后指标ID，不处理 indicatorExcelModel={}", JSON.toJSONString(indicatorExcelModel));
                    continue;
                }
                String code = indicatorExcelModel.getHealthIndicatorCode();
                if (code2Indicator.containsKey(code)) {
                    log.info("ProductDataCleanApplicationImpl -> 存在重复的指标，标准指标数据只插入一条，不同标准项目通过关联关系表维护相同的标准指标 indicatorExcelModel={}", JSON.toJSONString(indicatorExcelModel));
                    continue;
                }
                JdhStandardIndicator build = JdhStandardIndicator.builder()
                        .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                        .firstBizCategory(name2FirstCategory.containsKey(indicatorExcelModel.getFirstBizCategoryName()) ? name2FirstCategory.get(indicatorExcelModel.getFirstBizCategoryName()).getCategoryId() : null)
                        .secondBizCategory(name2SecondCategory.containsKey(indicatorExcelModel.getSecondBizCategoryName()) ? name2SecondCategory.get(indicatorExcelModel.getSecondBizCategoryName()).getCategoryId() : null)
                        .thirdBizCategory(name2ThirdCategory.containsKey(indicatorExcelModel.getThirdBizCategoryName()) ? name2ThirdCategory.get(indicatorExcelModel.getThirdBizCategoryName()).getCategoryId() : null)
                        .indicatorId(SpringUtil.getBean(GenerateIdFactory.class).getId())
                        .indicatorName(indicatorExcelModel.getIndicatorName())
                        .indicatorMean(StringUtil.isNotBlank(indicatorExcelModel.getIndicatorMean()) ? indicatorExcelModel.getIndicatorMean() : "")
                        //适用人群 1-男 2-女未婚 3-女已婚 存储数据位集合对象[1,2,3]
                        //.suitable()
                        .healthIndicatorCode(indicatorExcelModel.getHealthIndicatorCode())
                        .build();
                code2Indicator.put(code, build);
                jdhStandardIndicators.add(build);
            }
            //根据code查询数据库已存在的标准指标
            List<JdhStandardIndicator> standardIndicatorList = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().healthIndicatorCodeList(code2Indicator.keySet()).build());
            for (JdhStandardIndicator jdhStandardIndicator : standardIndicatorList) {
                if (code2Indicator.containsKey(jdhStandardIndicator.getHealthIndicatorCode())) {
                    JdhStandardIndicator indicator = code2Indicator.get(jdhStandardIndicator.getHealthIndicatorCode());
                    indicator.setVersion(jdhStandardIndicator.getVersion());
                    indicator.setIndicatorId(jdhStandardIndicator.getIndicatorId());
                    indicator.setId(jdhStandardIndicator.getId());
                }
            }
            log.info("ProductDataCleanApplicationImpl -> 标准指标数据列表, jdhStandardIndicators={}", JSON.toJSONString(jdhStandardIndicators));
            //保存
            for (JdhStandardIndicator jdhStandardIndicator : jdhStandardIndicators) {
                jdhStandardIndicatorRepository.save(jdhStandardIndicator);
            }

            //3.提取要保存的标准项目列表（将第1步指标分类id放入项目数据中），保存标准项目、以及关联的指标
            List<JdhStandardItem> jdhStandardItems = new ArrayList<>();
            Map<String, JdhStandardItem> name2Item = new HashMap<>();
            Map<String, Set<Long>> name2IndicatorList = new HashMap<>();
            for (StandardIndicatorExcelModel indicatorExcelModel : importResult) {
                //过滤没有项目名称的标准项目
                if (StringUtils.isBlank(indicatorExcelModel.getItemName())) {
                    log.info("ProductDataCleanApplicationImpl -> 过滤没有项目名称的标准项目, indicatorExcelModel={}", JSON.toJSONString(indicatorExcelModel));
                    continue;
                }
                if (!name2Item.containsKey(indicatorExcelModel.getItemName())) {
                    StandardItemApplySceneEnum applySceneEnum = StandardItemApplySceneEnum.getEnumByDesc(indicatorExcelModel.getApplySceneDesc());
                    JdhStandardItem build = JdhStandardItem.builder()
                            .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                            .applyScene(Objects.nonNull(applySceneEnum) ? Lists.newArrayList(applySceneEnum.getScene()) : "BC端场景".equals(indicatorExcelModel.getApplySceneDesc()) ? StandardItemApplySceneEnum.getAllApplyScene() : null)
                            .firstBizCategory(name2FirstCategory.containsKey(indicatorExcelModel.getFirstBizCategoryName()) ? name2FirstCategory.get(indicatorExcelModel.getFirstBizCategoryName()).getCategoryId() : null)
                            .secondBizCategory(name2SecondCategory.containsKey(indicatorExcelModel.getSecondBizCategoryName()) ? name2SecondCategory.get(indicatorExcelModel.getSecondBizCategoryName()).getCategoryId() : null)
                            .thirdBizCategory(name2ThirdCategory.containsKey(indicatorExcelModel.getThirdBizCategoryName()) ? name2ThirdCategory.get(indicatorExcelModel.getThirdBizCategoryName()).getCategoryId() : null)
                            .itemId(SpringUtil.getBean(GenerateIdFactory.class).getId())
                            //.itemIdList()
                            .itemName(indicatorExcelModel.getItemName())
                            .itemMean(StringUtil.isNotBlank(indicatorExcelModel.getItemMean()) ? indicatorExcelModel.getItemMean() : "")
                            .yn(YnStatusEnum.YES.getCode())
                            //.desc()
                            //.indicatorList()
                            .indicatorNum(StringUtils.isNotBlank(indicatorExcelModel.getIndicatorNum()) ? Integer.valueOf(indicatorExcelModel.getIndicatorNum()) : null)
                            //.remark()
                            .build();
                    jdhStandardItems.add(build);
                    name2Item.put(indicatorExcelModel.getItemName(), build);
                }
                //设置关联的指标列表
                Set<Long> indicatorSet = name2IndicatorList.get(indicatorExcelModel.getItemName());
                if (CollectionUtils.isEmpty(indicatorSet)) {
                    indicatorSet = Sets.newHashSet();
                    name2IndicatorList.put(indicatorExcelModel.getItemName(), indicatorSet);
                }
                if (code2Indicator.containsKey(indicatorExcelModel.getHealthIndicatorCode())) {
                    indicatorSet.add(code2Indicator.get(indicatorExcelModel.getHealthIndicatorCode()).getIndicatorId());
                } else {
                    log.info("ProductDataCleanApplicationImpl -> 此标准项目关联的指标，没有被创建, indicatorExcelModel={}", JSON.toJSONString(indicatorExcelModel));
                }
            }
            //设置标准项目关联的标准指标
            for (JdhStandardItem standardItem : jdhStandardItems) {
                standardItem.setIndicatorList(CollectionUtils.isEmpty(name2IndicatorList.get(standardItem.getItemName())) ? Lists.newArrayList() : Lists.newArrayList(name2IndicatorList.get(standardItem.getItemName())));
            }
            //查询数据库已有的标准指标项目
            List<JdhStandardItem> standardItemExistList = jdhStandardItemRepository.queryList(JdhStandardItemRepQuery.builder().itemNameList(name2Item.keySet()).build());
            for (JdhStandardItem standardItem : standardItemExistList) {
                if (name2Item.containsKey(standardItem.getItemName())) {
                    JdhStandardItem item = name2Item.get(standardItem.getItemName());
                    item.setVersion(standardItem.getVersion());
                    item.setItemId(standardItem.getItemId());
                    item.setId(standardItem.getId());
                }
            }
            log.info("ProductDataCleanApplicationImpl -> 标准项目列表, jdhStandardItems={}", JSON.toJSONString(jdhStandardItems));
            //保存
            for (JdhStandardItem jdhStandardItem : jdhStandardItems) {
                jdhStandardItemRepository.save(jdhStandardItem);
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> 指标信息导入excel信息异常", e);
            return Boolean.FALSE;
        }
    }

    /**
     * 导出新旧指标映射
     * @return
     */
    @Override
    public Boolean exportMappingIndicator(String mappingOssKey) {
        //导出excel
        //1、构建上下文
        Map<String, Object> ctx = new HashMap<>();
        ctx.put("scene", FileExportTypeEnum.PRODUCT_OLD_STANDARD_INDICATOR_MAPPING_EXPORT.getType() + "_" + ServiceTypeEnum.PHYSICAL.getServiceType());
        ctx.put("userPin","system");
        ctx.put("operationType", FileExportTypeEnum.PRODUCT_OLD_STANDARD_INDICATOR_MAPPING_EXPORT.getType());
        ctx.put("mappingOssKey", mappingOssKey);
        //2、调用通用文件导入能力
        fileManageApplication.export(ctx);
        return Boolean.TRUE;
    }

    /**
     * 导出套餐数据（供业务、商家校准）
     * @return
     */
    @Override
    public Boolean exportProgramItemData(String mappingOssKey, Integer type, Integer partNum) {
        //导出excel
        //1、构建上下文
        if (Objects.equals(type, 1)) {
            Map<String, Object> ctx = new HashMap<>();
            ctx.put("scene", FileExportTypeEnum.SERVICE_OLD_ITEM_MAPPING_NEW_ITEM_EXPORT.getType() + "_" + ServiceTypeEnum.PHYSICAL.getServiceType());
            ctx.put("userPin","system");
            ctx.put("operationType", FileExportTypeEnum.SERVICE_OLD_ITEM_MAPPING_NEW_ITEM_EXPORT.getType());
            ctx.put("mappingOssKey", mappingOssKey);
            ctx.put("partNum", partNum);
            //2、调用通用文件导入能力
            fileManageApplication.export(ctx);
        } else if (Objects.equals(type, 2)) {
            Map<String, Object> ctx = new HashMap<>();
            ctx.put("scene", FileExportTypeEnum.PRODUCT_OLD_ITEM_MAPPING_NEW_ITEM_EXPORT.getType() + "_" + ServiceTypeEnum.PHYSICAL.getServiceType());
            ctx.put("userPin","system");
            ctx.put("operationType", FileExportTypeEnum.PRODUCT_OLD_ITEM_MAPPING_NEW_ITEM_EXPORT.getType());
            ctx.put("mappingOssKey", mappingOssKey);
            ctx.put("partNum", partNum);
            //2、调用通用文件导入能力
            fileManageApplication.export(ctx);
        } else if (Objects.equals(type, 3)) {
            Map<String, Object> ctx = new HashMap<>();
            ctx.put("scene", FileExportTypeEnum.YKWD_OLD_ITEM_INDICATOR_MAPPING_EXPORT.getType() + "_" + ServiceTypeEnum.PHYSICAL.getServiceType());
            ctx.put("userPin","system");
            ctx.put("operationType", FileExportTypeEnum.YKWD_OLD_ITEM_INDICATOR_MAPPING_EXPORT.getType());
            ctx.put("mappingOssKey", mappingOssKey);
            ctx.put("partNum", partNum);
            //2、调用通用文件导入能力
            fileManageApplication.export(ctx);
        }
        return Boolean.TRUE;
    }

    /**
     * 新旧指标映射
     * @return
     */
    @Override
    public List<OldStandardIndicatorMappingDTO> mappingIndicator(String mappingOssKey) {
        //查询所有老指标
        List<Indicator> oldIndicators = serviceIndicatorRepository.queryIndicatorList(ServiceIndicatorQueryContext.builder().firstIndicatorCategory(Sets.newHashSet(906596746242L, 923776581122L, 940956728834L, 949546484226L, 661004250626L, 629554156546L, 124202627586L, 565856855042L, 923776581123L)).build());
        log.info("ProductDataCleanApplicationImpl -> mappingIndicator={}", JSON.toJSONString(oldIndicators));
        //查询老指标分类
        List<IndicatorCategory> indicatorCategories = indicatorCategoryRepository.queryIndicatorCategoryList(IndicatorCategoryQueryContext.builder().build());
        Map<Long, IndicatorCategory> id2CategoryId = indicatorCategories.stream().collect(Collectors.toMap(IndicatorCategory::getCategoryId, indicatorCategory -> indicatorCategory, (o1, o2) -> o2));

        //查询所有新指标
        List<JdhStandardIndicator> standardIndicators = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
        log.info("ProductDataCleanApplicationImpl -> standardIndicators={}", JSON.toJSONString(standardIndicators));
        //查询新指标分类
        List<JdhBizCategory> newCategoryList = jdhBizCategoryRepository.queryJdhBizCategoryList(BizCategoryRepQuery.builder().bizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode()).build());
        Map<Long, JdhBizCategory> id2BizCategory = newCategoryList.stream().collect(Collectors.toMap(JdhBizCategory::getCategoryId, jdhBizCategory -> jdhBizCategory, (o1, o2) -> o2));

        //老指标遍历，找出30%匹配度的对比名称（匹配度 = 相同的字 / 老指标基准对比名称总字数）
        List<OldStandardIndicatorMappingDTO> result = new ArrayList<>();
        for (Indicator indicator : oldIndicators) {
            //String targetIndicatorName = id2CategoryId.containsKey(indicator.getSecondIndicatorCategory()) ? id2CategoryId.get(indicator.getSecondIndicatorCategory()).getCategoryName() + indicator.getIndicatorName() : indicator.getIndicatorName();
            String targetIndicatorName = indicator.getIndicatorName();
            log.info("ProductDataCleanApplicationImpl -> mapping targetIndicatorName={}", targetIndicatorName);
            List<String> mappingIndicators = new ArrayList<>();
            for (JdhStandardIndicator standardIndicator : standardIndicators) {
                //String standardIndicatorName = id2BizCategory.containsKey(standardIndicator.getThirdBizCategory()) ? id2BizCategory.get(standardIndicator.getThirdBizCategory()).getCategoryName() + standardIndicator.getIndicatorName() : standardIndicator.getIndicatorName();
                String standardIndicatorName = standardIndicator.getIndicatorName();
                double matchPercentage = calculateMatchPercentage(targetIndicatorName, standardIndicatorName);
                log.info("ProductDataCleanApplicationImpl -> mapping targetIndicatorName={}, standardIndicatorName={}, matchPercentage={}", targetIndicatorName, standardIndicatorName, matchPercentage);
                if (matchPercentage >= 0.4) {
                    mappingIndicators.add(standardIndicator.getHealthIndicatorCode() + "-" + standardIndicator.getIndicatorName());
                }
            }
            result.add(OldStandardIndicatorMappingDTO.builder()
                    .indicatorId(indicator.getIndicatorId())
                    .indicatorName(indicator.getIndicatorName())
                    .indicatorMean(indicator.getIndicatorMean())
                    .mappingIndicators(Joiner.on(",").join(mappingIndicators))
                    .build());
        }
        return result;
    }

    /**
     *
     * @return
     */
    @Override
    public List<ServiceItemMappingNewItemDTO> mappingServiceItem(String mappingOssKey) {
        try {
            List<ServiceItemMappingNewItemDTO> result = new ArrayList<>();
            //查询所有老指标
            List<Indicator> oldIndicators = serviceIndicatorRepository.queryIndicatorList(ServiceIndicatorQueryContext.builder().firstIndicatorCategory(Sets.newHashSet(906596746242L, 923776581122L, 940956728834L, 949546484226L, 661004250626L, 629554156546L, 124202627586L, 565856855042L, 923776581123L)).build());
            log.info("ProductDataCleanApplicationImpl -> mappingServiceItem mappingIndicator={}", JSON.toJSONString(oldIndicators));
            Map<Long, Indicator> id2OldIndicator = oldIndicators.stream().collect(Collectors.toMap(Indicator::getIndicatorId, indicator -> indicator, (o1, o2) -> o2));

            //查询所有新指标
            List<JdhStandardIndicator> standardIndicators = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
            log.info("ProductDataCleanApplicationImpl -> mappingServiceItem standardIndicators={}", JSON.toJSONString(standardIndicators));
            Map<String, JdhStandardIndicator> code2NewIndicator = standardIndicators.stream().collect(Collectors.toMap(JdhStandardIndicator::getHealthIndicatorCode, indicator -> indicator, (o1, o2) -> o2));

            //查询新老指标映射关系
            InputStream inputStream = fileManageService.get(mappingOssKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
            List<OldStandardIndicatorMappingBO> mappingIndicatorModelList = EasyExcelUtil.read(inputStream, 1, OldStandardIndicatorMappingBO.class);
            log.info("ProductDataCleanApplicationImpl -> mappingServiceItem解析excel集合：importResult size={}", mappingIndicatorModelList.size());
            //设置成老指标ID -> 新指标编码list 格式map，方便处理映射
            Map<String, List<String>> resultMap = new HashMap<>();
            for (OldStandardIndicatorMappingBO oldStandardIndicatorMappingBO : mappingIndicatorModelList) {
                log.info("ProductDataCleanApplicationImpl -> mappingServiceItem解析excel oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                List<String> healthIndicatorCodeList = resultMap.get(oldStandardIndicatorMappingBO.getOldIndicatorId());
                if (CollectionUtils.isEmpty(healthIndicatorCodeList)) {
                    healthIndicatorCodeList = new ArrayList<>();
                    resultMap.put(oldStandardIndicatorMappingBO.getOldIndicatorId(), healthIndicatorCodeList);
                }
                if (StringUtil.isNotBlank(oldStandardIndicatorMappingBO.getHealthIndicatorCode()) && oldStandardIndicatorMappingBO.getHealthIndicatorCode().contains("SC")) {
                    healthIndicatorCodeList.add(oldStandardIndicatorMappingBO.getHealthIndicatorCode());
                } else {
                    log.info("ProductDataCleanApplicationImpl -> mappingServiceItem解析excel 包含非法检后指标ID oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                }
            }

            //所有有效的service 和 业务项目的关联关系
            List<JdhServiceItemRel> jdhServiceItemRelList = serviceItemRelRepository.queryJdhServiceItemRelList(JdhServiceItemRelContext.builder().build());
            //按照serviceId分组
            Map<Long, List<JdhServiceItemRel>> serviceId2List = jdhServiceItemRelList.stream().collect(Collectors.groupingBy(JdhServiceItemRel::getServiceId));
            log.info("ProductDataCleanApplicationImpl -> mappingServiceItem 清洗京东服务数据 serviceId2List size={}", serviceId2List.size());
            Map<Long, JdhProgram> oldServiceId2Map = new HashMap<>();
            //遍历map，依次处理每个service和对应业务项目绑定关系
            for (Map.Entry<Long, List<JdhServiceItemRel>> entry : serviceId2List.entrySet()) {
                Long serviceId = entry.getKey();
                //查询老京东服务表，获取服务数据
                JdhService jdhService = serviceRepository.find(JdhServiceIdentifier.builder().serviceId(serviceId).build());
                //查询新表京东服务
                JdhProgram existProgram = jdhProgramRepository.find(JdhProgramIdentifier.builder().programId(serviceId).build());
                oldServiceId2Map.put(serviceId, existProgram);
                //处理服务与业务项目关联关系
                List<JdhServiceItemRel> itemRels = entry.getValue();
                Map<Long, Integer> itemIdMap = itemRels.stream().collect(Collectors.toMap(JdhServiceItemRel::getItemId, jdhServiceItemRel -> Optional.ofNullable(jdhServiceItemRel.getImportant()).orElse(0), (o1, o2) -> o2));
                // 老业务项目列表
                List<ServiceItem> serviceItems = serviceItemRepository.queryServiceItemList(JdhItemListQueryContext.builder().itemIdList(itemIdMap.keySet()).build());
                Map<Long, ServiceItem> id2OldItem = serviceItems.stream().collect(Collectors.toMap(ServiceItem::getItemId, serviceItem -> serviceItem, (o1, o2) -> o2));
                // 新业务项目列表
                List<JdhBizItem> existBizItemList = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().oldItemIdList(itemIdMap.keySet()).needIndicatorIdList(true).build());
                log.info("ProductDataCleanApplicationImpl -> mappingServiceItem, existBizItemList, serviceId={}, existBizItemList={}", serviceId, JSON.toJSONString(existBizItemList));
                Map<Long, List<JdhBizItem>> item2BizList = existBizItemList.stream().filter(jdhBizItem -> Objects.nonNull(jdhBizItem.getExtJson()) && Objects.nonNull(jdhBizItem.getExtJson().getOldItemId()))
                        .collect(Collectors.groupingBy(jdhBizItem -> jdhBizItem.getExtJson().getOldItemId()));

                //老业务项目关联的老指标数据
                List<ServiceItemIndicatorRel> serviceItemIndicatorRelList = serviceItemIndicatorRepository.queryItemIndicatorList(ServiceItemQueryContext.builder().itemIds(itemIdMap.keySet()).build());
                Map<Long, List<Long>> itemId2IndicatorMap = CollectionUtils.isEmpty(serviceItemIndicatorRelList) ? new HashMap<>() : serviceItemIndicatorRelList.stream().collect(Collectors.groupingBy(ServiceItemIndicatorRel::getItemId, Collectors.mapping(ServiceItemIndicatorRel::getIndicatorId, Collectors.toList())));

                List<JdhProgramBizItemRel> saveServiceItemRelList = new ArrayList<>();
                for (Map.Entry<Long, Integer> itemEntry : itemIdMap.entrySet()) {
                    Long oldItemId = itemEntry.getKey();
                    //老项目数据
                    ServiceItem serviceItem = id2OldItem.get(oldItemId);
                    //老指标数据
                    List<Long> oldIndicatorIds = itemId2IndicatorMap.get(oldItemId);
                    List<Indicator> oldIndicatorList = CollectionUtils.isEmpty(oldIndicatorIds)? Collections.emptyList() :oldIndicatorIds.stream().map(id2OldIndicator::get).filter(Objects::nonNull).collect(Collectors.toList());

                    //新业务项目数据
                    List<JdhBizItem> jdhBizItems = item2BizList.get(oldItemId);
                    //Map<Long, JdhBizItem> id2BizItem = jdhBizItems.stream().collect(Collectors.toMap(JdhBizItem::getBizItemId, jdhBizItem -> jdhBizItem, (o1, o2) -> o2));

                    //新业务项目与新指标映射
                    Map<Long,JdhBizItem> indicator2BizItemId = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(jdhBizItems)) {
                        for (JdhBizItem jdhBizItem : jdhBizItems) {
                            List<Long> indicatorList = jdhBizItem.getIndicatorList();
                            if (CollectionUtils.isEmpty(indicatorList)) {
                                continue;
                            }
                            for (Long indicatorId : indicatorList) {
                                indicator2BizItemId.put(indicatorId, jdhBizItem);
                            }
                        }
                    }
                    if (CollectionUtils.isEmpty(oldIndicatorList)) {
                        ServiceItemMappingNewItemDTO build = ServiceItemMappingNewItemDTO.builder()
                                .serviceId(serviceId)
                                .serviceName(Objects.nonNull(jdhService) ? jdhService.getServiceName() : "")
                                .oldItemId(oldItemId)
                                .oldItemName(Objects.nonNull(serviceItem) ? serviceItem.getItemName() : "")
                                .build();
                        result.add(build);
                        continue;
                    }
                    //遍历老指标，组装excel数据
                    for (Indicator oldIndicator : oldIndicatorList) {

                        List<String> healthIndicatorCodeList = resultMap.get(String.valueOf(oldIndicator.getIndicatorId()));
                        if (CollectionUtils.isEmpty(healthIndicatorCodeList)) {
                            ServiceItemMappingNewItemDTO build = ServiceItemMappingNewItemDTO.builder()
                                    .serviceId(serviceId)
                                    .serviceName(Objects.nonNull(jdhService) ? jdhService.getServiceName() : "")
                                    .oldItemId(oldItemId)
                                    .oldItemName(Objects.nonNull(serviceItem) ? serviceItem.getItemName() : "")
                                    .oldIndicatorId(oldIndicator.getIndicatorId())
                                    .oldIndicatorName(oldIndicator.getIndicatorName())
                                    .build();
                            result.add(build);
                            continue;
                        }
                        for (String healthIndicatorCode : healthIndicatorCodeList) {
                            JdhStandardIndicator jdhStandardIndicator = code2NewIndicator.get(healthIndicatorCode);
                            JdhBizItem jdhBizItem = indicator2BizItemId.get(Objects.nonNull(jdhStandardIndicator) ? jdhStandardIndicator.getIndicatorId() : "");

                            ServiceItemMappingNewItemDTO build = ServiceItemMappingNewItemDTO.builder()
                                    .serviceId(serviceId)
                                    .serviceName(Objects.nonNull(jdhService) ? jdhService.getServiceName() : "")
                                    .oldItemId(oldItemId)
                                    .oldItemName(Objects.nonNull(serviceItem) ? serviceItem.getItemName() : "")
                                    .oldIndicatorId(oldIndicator.getIndicatorId())
                                    .oldIndicatorName(oldIndicator.getIndicatorName())
                                    .newItemName(Objects.nonNull(jdhBizItem) ? jdhBizItem.getBizItemName() : "")
                                    .newItemSuitable(Objects.nonNull(jdhBizItem) ? jdhBizItem.getSuitable() : Collections.emptyList())
                                    .newIndicatorName(Objects.nonNull(jdhStandardIndicator) && Objects.nonNull(jdhBizItem) ? jdhStandardIndicator.getIndicatorName() : "")
                                    .healthIndicatorCode(Objects.nonNull(jdhStandardIndicator) && Objects.nonNull(jdhBizItem)? jdhStandardIndicator.getHealthIndicatorCode() : "")
                                    .build();
                            result.add(build);
                        }
                    }

                }

            }
            return result;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> mappingServiceItem处理信息异常Exception", e);
            return Collections.emptyList();
        } catch (Throwable e) {
            log.error("ProductDataCleanApplicationImpl -> mappingServiceItem处理信息异常Throwable", e);
            return Collections.emptyList();
        }
    }

    /**
     *
     * @return
     */
    @Override
    public List<ProductItemMappingNewItemDTO> mappingProductItem(String mappingOssKey) {
        List<ProductItemMappingNewItemDTO> result = new ArrayList<>();
        //获取文件流
        InputStream inputStream = fileManageService.get(mappingOssKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        try {

            List<PopProductExcelBO> importResult = EasyExcelUtil.read(inputStream, 1, PopProductExcelBO.class);
            log.info("ProductDataCleanApplicationImpl -> mappingProductItem解析excel集合：importResult size={}",importResult.size());
            //Product 过滤
            HashBasedTable<String, String, PopProductExcelBO> basedTable = HashBasedTable.create();
            for (PopProductExcelBO popProductExcelBO : importResult) {
                if (basedTable.contains(popProductExcelBO.getProductId(), popProductExcelBO.getSkuId())) {
                    continue;
                }
                basedTable.put(popProductExcelBO.getProductId(), popProductExcelBO.getSkuId(), popProductExcelBO);
            }
            log.info("ProductDataCleanApplicationImpl -> mappingProductItem处理集合：basedTable size={}", basedTable.size());
            log.info("ProductDataCleanApplicationImpl -> mappingProductItem处理集合：stringMapMap={}", JSON.toJSONString(basedTable.rowMap()));

            //查询所有老指标
            List<Indicator> oldIndicators = serviceIndicatorRepository.queryIndicatorList(ServiceIndicatorQueryContext.builder().firstIndicatorCategory(Sets.newHashSet(906596746242L, 923776581122L, 940956728834L, 949546484226L, 661004250626L, 629554156546L, 124202627586L, 565856855042L, 923776581123L)).build());
            log.info("ProductDataCleanApplicationImpl -> mappingProductItem mappingIndicator={}", JSON.toJSONString(oldIndicators));
            Map<Long, Indicator> id2OldIndicator = oldIndicators.stream().collect(Collectors.toMap(Indicator::getIndicatorId, indicator -> indicator, (o1, o2) -> o2));

            //查询所有新指标
            List<JdhStandardIndicator> standardIndicators = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
            log.info("ProductDataCleanApplicationImpl -> mappingProductItem standardIndicators={}", JSON.toJSONString(standardIndicators));
            Map<String, JdhStandardIndicator> code2NewIndicator = standardIndicators.stream().collect(Collectors.toMap(JdhStandardIndicator::getHealthIndicatorCode, indicator -> indicator, (o1, o2) -> o2));

            //查询新老指标映射关系
            InputStream mappingInputStream = fileManageService.get("整理版-映射以此为准0902.xlsx", FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
            List<OldStandardIndicatorMappingBO> mappingIndicatorModelList = EasyExcelUtil.read(mappingInputStream, 1, OldStandardIndicatorMappingBO.class);
            log.info("ProductDataCleanApplicationImpl -> mappingProductItem解析excel集合：importResult size={}", mappingIndicatorModelList.size());
            //设置成老指标ID -> 新指标编码list 格式map，方便处理映射
            Map<String, List<String>> resultMap = new HashMap<>();
            for (OldStandardIndicatorMappingBO oldStandardIndicatorMappingBO : mappingIndicatorModelList) {
                log.info("ProductDataCleanApplicationImpl -> mappingProductItem解析excel oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                List<String> healthIndicatorCodeList = resultMap.get(oldStandardIndicatorMappingBO.getOldIndicatorId());
                if (CollectionUtils.isEmpty(healthIndicatorCodeList)) {
                    healthIndicatorCodeList = new ArrayList<>();
                    resultMap.put(oldStandardIndicatorMappingBO.getOldIndicatorId(), healthIndicatorCodeList);
                }
                if (StringUtil.isNotBlank(oldStandardIndicatorMappingBO.getHealthIndicatorCode()) && oldStandardIndicatorMappingBO.getHealthIndicatorCode().contains("SC")) {
                    healthIndicatorCodeList.add(oldStandardIndicatorMappingBO.getHealthIndicatorCode());
                } else {
                    log.info("ProductDataCleanApplicationImpl -> mappingProductItem解析excel 包含非法检后指标ID oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                }
            }


            //1.依次查询商品主数据，拿取商品配置的结构化项目数据，存入数据库
            for (Map.Entry<String, Map<String, PopProductExcelBO>> cell : basedTable.rowMap().entrySet()) {
                log.info("ProductDataCleanApplicationImpl -> mappingProductItem 处理cell={}", JSON.toJSONString(cell));
                // 先查商品现有类目属性数据 https://joyspace.jd.com/page/4FTGMPrW7KEHqCKzYWOe
                if (cell.getValue().size() > 1) {
                    log.info("ProductDataCleanApplicationImpl -> mappingProductItem 该商品有多个sku，productId={}，sku={}", cell.getKey(), cell.getValue().keySet());
                }

                String productId = cell.getKey();
                List<String> skuIdList = new ArrayList<>(cell.getValue().keySet());
                PopProductExcelBO productExcelBO = cell.getValue().values().iterator().next();
                Product product = skuInfoRpc.getProductById(productExcelBO.getVenderId(), Long.valueOf(productId), Sets.newHashSet("customProps", "multiCategoryId", "categoryId", "productName")
                        , Sets.newHashSet(CommonConstant.SAME_CITY_ATTRIBUTE));
                if (Objects.isNull(product)) {
                    log.warn("ProductDataCleanApplicationImpl ->mappingProductItem处理集合：product is null, productId={}", productId);
                    continue;
                }
                // 获取商品自定义属性
                Set<CustomProp> productCustomProps = Optional.ofNullable(product.getCustomProps()).orElse(new HashSet<>());
                Map<String, CustomProp> customPropMap = productCustomProps.stream().collect(Collectors.toMap(CustomProp::getAttrId, customProp -> customProp, (t, t2) -> t2));

                //获取存在商品数据中的结构化x项目数据属性ID
                String attrId = null;
                Map<String, String> skuCategoryAttrIdMap = duccConfig.getSkuCategoryAttrIdMap();
                if (Objects.nonNull(product.getCategoryId())) {
                    attrId = skuCategoryAttrIdMap.get(product.getCategoryId().toString());
                }
                if (Objects.nonNull(product.getMultiCategoryId())) {
                    attrId = skuCategoryAttrIdMap.get(product.getMultiCategoryId().toString());
                }
                if (StringUtil.isBlank(attrId)) {
                    throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("attrId"));
                }

                CustomProp customProp = customPropMap.get(attrId);
                log.info("ProductDataCleanApplicationImpl -> mappingProductItem clean customProp={}", JsonUtil.toJSONString(customProp));
                //如果无，不做处理
                if (Objects.isNull(customProp)) {
                    log.warn("ProductDataCleanApplicationImpl -> mappingProductItem 商品没有结构化项目属性：productCustomProps={}", JSON.toJSONString(productCustomProps));
                    continue;
                }

                // 保存program和programSku数据
                //老业务项目集合
                List<ServiceItem> oldBizItemCmdList = new ArrayList<>();
                //新业务项目集合
                List<SaveBizItemCmd> bizItemCmdList = new ArrayList<>();
                //新业务项目 to 是否重点项目
                Map<Long, Boolean> bizItemImportantMap = new HashMap<>();

                boolean cleanResult = cleanNewCustomAttrValue(customProp.getCustomAttrValues(), oldBizItemCmdList, bizItemCmdList, bizItemImportantMap);
                log.info("ProductDataCleanApplicationImpl -> mappingProductItem clean 老业务项目集合 oldBizItemCmdList={}", JsonUtil.toJSONString(oldBizItemCmdList));
                log.info("ProductDataCleanApplicationImpl -> mappingProductItem clean 新业务项目集合 bizItemCmdList={}", JsonUtil.toJSONString(bizItemCmdList));
                if (!cleanResult) {
                    log.info("ProductDataCleanApplicationImpl -> mappingProductItem cleanNewCustomAttrValue 未正确处理 customProp={}", JsonUtil.toJSONString(customProp));
                    if (CollectionUtils.isEmpty(oldBizItemCmdList)) {
                        ProductItemMappingNewItemDTO build = ProductItemMappingNewItemDTO.builder()
                                .productId(productExcelBO.getProductId())
                                .productName(productExcelBO.getProductName())
                                //.skuId(skuId)
                                .venderId(productExcelBO.getVenderId())
                                .onSale(productExcelBO.getOnSaleStatusDesc())
                                //.oldItemId()
                                //.oldItemName()
                                //.oldIndicatorId()
                                //.oldIndicatorName()
                                //.newItemName()
                                //.newItemSuitable()
                                //.newIndicatorName()
                                //.healthIndicatorCode()
                                .build();
                        result.add(build);
                        continue;
                    } else {
                        oldBizItemCmdList.forEach(serviceItem -> {
                            ProductItemMappingNewItemDTO build = ProductItemMappingNewItemDTO.builder()
                                    .productId(productExcelBO.getProductId())
                                    .productName(productExcelBO.getProductName())
                                    //.skuId(skuId)
                                    .venderId(productExcelBO.getVenderId())
                                    .onSale(productExcelBO.getOnSaleStatusDesc())
                                    .oldItemId(serviceItem.getItemId())
                                    .oldItemName(serviceItem.getItemName())
                                    .price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().toPlainString() : "")
                                    //.oldIndicatorId()
                                    //.oldIndicatorName()
                                    //.newItemName()
                                    //.newItemSuitable()
                                    //.newIndicatorName()
                                    //.healthIndicatorCode()
                                    .build();
                            result.add(build);
                        });
                        continue;
                    }
                }

                //老业务项目关联的老指标数据
                Map<Long, List<Long>> itemId2IndicatorMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(oldBizItemCmdList)) {
                    List<ServiceItemIndicatorRel> serviceItemIndicatorRelList = serviceItemIndicatorRepository.queryItemIndicatorList(ServiceItemQueryContext.builder().itemIds(oldBizItemCmdList.stream().map(ServiceItem::getItemId).collect(Collectors.toSet())).build());
                    itemId2IndicatorMap = CollectionUtils.isEmpty(serviceItemIndicatorRelList) ? new HashMap<>() : serviceItemIndicatorRelList.stream().collect(Collectors.groupingBy(ServiceItemIndicatorRel::getItemId, Collectors.mapping(ServiceItemIndicatorRel::getIndicatorId, Collectors.toList())));
                }

                //新业务项目数据
                Map<Long, List<JdhBizItem>> item2BizList = new HashMap<>();
                if (CollectionUtils.isNotEmpty(oldBizItemCmdList)) {
                    List<JdhBizItem> existBizItemList = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().oldItemIdList(oldBizItemCmdList.stream().map(ServiceItem::getItemId).collect(Collectors.toSet())).needIndicatorIdList(true).build());
                    log.info("ProductDataCleanApplicationImpl -> mappingProductItem, existBizItemList, productId={}, existBizItemList={}", productId, JSON.toJSONString(existBizItemList));
                    item2BizList  = CollectionUtils.isEmpty(existBizItemList) ? new HashMap<>() :existBizItemList.stream().filter(jdhBizItem -> Objects.nonNull(jdhBizItem.getExtJson()) && Objects.nonNull(jdhBizItem.getExtJson().getOldItemId()))
                            .collect(Collectors.groupingBy(jdhBizItem -> jdhBizItem.getExtJson().getOldItemId()));
                }


                if (CollectionUtils.isEmpty(oldBizItemCmdList)) {
                    ProductItemMappingNewItemDTO build = ProductItemMappingNewItemDTO.builder()
                            .productId(productExcelBO.getProductId())
                            .productName(productExcelBO.getProductName())
                            .venderId(productExcelBO.getVenderId())
                            .onSale(productExcelBO.getOnSaleStatusDesc())
                            //.oldItemId()
                            //.oldItemName()
                            //.oldIndicatorId()
                            //.oldIndicatorName()
                            //.newItemName()
                            //.newItemSuitable()
                            //.newIndicatorName()
                            //.healthIndicatorCode()
                            .build();
                    result.add(build);
                    continue;
                }
                for (ServiceItem serviceItem : oldBizItemCmdList) {
                    //老指标数据
                    List<Long> oldIndicatorIds = itemId2IndicatorMap.get(serviceItem.getItemId());
                    List<Indicator> oldIndicatorList = CollectionUtils.isEmpty(oldIndicatorIds)? Collections.emptyList() :oldIndicatorIds.stream().map(id2OldIndicator::get).filter(Objects::nonNull).collect(Collectors.toList());

                    //新业务项目数据
                    List<JdhBizItem> jdhBizItems = item2BizList.get(serviceItem.getItemId());
                    //Map<Long, JdhBizItem> id2BizItem = jdhBizItems.stream().collect(Collectors.toMap(JdhBizItem::getBizItemId, jdhBizItem -> jdhBizItem, (o1, o2) -> o2));

                    //新业务项目与新指标映射
                    Map<Long,JdhBizItem> indicator2BizItemId = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(jdhBizItems)) {
                        for (JdhBizItem jdhBizItem : jdhBizItems) {
                            List<Long> indicatorList = jdhBizItem.getIndicatorList();
                            if (CollectionUtils.isEmpty(indicatorList)) {
                                continue;
                            }
                            for (Long indicatorId : indicatorList) {
                                indicator2BizItemId.put(indicatorId, jdhBizItem);
                            }
                        }
                    }
                    if (CollectionUtils.isEmpty(oldIndicatorList)) {
                        ProductItemMappingNewItemDTO build = ProductItemMappingNewItemDTO.builder()
                                .productId(productExcelBO.getProductId())
                                .productName(productExcelBO.getProductName())
                                .venderId(productExcelBO.getVenderId())
                                .onSale(productExcelBO.getOnSaleStatusDesc())
                                .oldItemId(serviceItem.getItemId())
                                .oldItemName(serviceItem.getItemName())
                                .price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().toPlainString() : "")
                                //.oldIndicatorId()
                                //.oldIndicatorName()
                                //.newItemName()
                                //.newItemSuitable()
                                //.newIndicatorName()
                                //.healthIndicatorCode()
                                .build();
                        result.add(build);
                        continue;
                    }
                    //遍历老指标，组装excel数据
                    for (Indicator oldIndicator : oldIndicatorList) {

                        List<String> healthIndicatorCodeList = resultMap.get(String.valueOf(oldIndicator.getIndicatorId()));
                        if (CollectionUtils.isEmpty(healthIndicatorCodeList)) {
                            ProductItemMappingNewItemDTO build = ProductItemMappingNewItemDTO.builder()
                                    .productId(productExcelBO.getProductId())
                                    .productName(productExcelBO.getProductName())
                                    .venderId(productExcelBO.getVenderId())
                                    .onSale(productExcelBO.getOnSaleStatusDesc())
                                    .oldItemId(serviceItem.getItemId())
                                    .oldItemName(serviceItem.getItemName())
                                    .oldIndicatorId(oldIndicator.getIndicatorId())
                                    .oldIndicatorName(oldIndicator.getIndicatorName())
                                    .price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().toPlainString() : "")
                                    //.newItemName()
                                    //.newItemSuitable()
                                    //.newIndicatorName()
                                    //.healthIndicatorCode()
                                    .build();
                            result.add(build);
                            continue;
                        }
                        for (String healthIndicatorCode : healthIndicatorCodeList) {
                            JdhStandardIndicator jdhStandardIndicator = code2NewIndicator.get(healthIndicatorCode);
                            JdhBizItem jdhBizItem = indicator2BizItemId.get(Objects.nonNull(jdhStandardIndicator) ? jdhStandardIndicator.getIndicatorId() : "");

                            ProductItemMappingNewItemDTO build = ProductItemMappingNewItemDTO.builder()
                                    .productId(productExcelBO.getProductId())
                                    .productName(productExcelBO.getProductName())
                                    .venderId(productExcelBO.getVenderId())
                                    .oldItemId(serviceItem.getItemId())
                                    .oldItemName(serviceItem.getItemName())
                                    .onSale(productExcelBO.getOnSaleStatusDesc())
                                    .oldIndicatorId(oldIndicator.getIndicatorId())
                                    .oldIndicatorName(oldIndicator.getIndicatorName())
                                    .price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().toPlainString() : "")
                                    .newItemName(Objects.nonNull(jdhBizItem) ? jdhBizItem.getBizItemName() : "")
                                    .newItemSuitable(Objects.nonNull(jdhBizItem) ? jdhBizItem.getSuitable() : Collections.emptyList())
                                    .newIndicatorName(Objects.nonNull(jdhStandardIndicator) && Objects.nonNull(jdhBizItem) ? jdhStandardIndicator.getIndicatorName() : "")
                                    .healthIndicatorCode(Objects.nonNull(jdhStandardIndicator) && Objects.nonNull(jdhBizItem)? jdhStandardIndicator.getHealthIndicatorCode() : "")
                                    .build();
                            result.add(build);
                        }
                    }


                }

            }
            return result;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> mappingProductItem信息异常", e);
            throw e;
        }
    }

    /**
     * 清洗业务项目数据（老 -> 新）
     * @param mappingOssKey
     * @return
     */
    @Override
    //@Transactional(rollbackFor = RuntimeException.class)
    public Boolean cleanBizServiceItem(String mappingOssKey) {
        try {
            //查询所有业务项目数据（自营体检、pop体检）
            List<ServiceItem> serviceItems = serviceItemRepository.queryServiceItemList(JdhItemListQueryContext.builder().firstIndicatorCategory(Sets.newHashSet(906596746242L, 923776581122L, 940956728834L, 949546484226L, 661004250626L, 629554156546L, 124202627586L, 565856855042L, 923776581123L)).build());
            log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem 所有体检业务项目数据 size={}", serviceItems.size());
            Set<Long> itemIdSet = serviceItems.stream().map(ServiceItem::getItemId).collect(Collectors.toSet());
            //查询业务项目关联的指标数据
            List<ServiceItemIndicatorRel> serviceItemIndicatorRelList = serviceItemIndicatorRepository.queryItemIndicatorList(ServiceItemQueryContext.builder().itemIds(itemIdSet).build());
            Map<Long, List<Long>> itemId2IndicatorMap = serviceItemIndicatorRelList.stream().collect(Collectors.groupingBy(ServiceItemIndicatorRel::getItemId, Collectors.mapping(ServiceItemIndicatorRel::getIndicatorId, Collectors.toList())));
            log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem 所有关联了指标的体检业务项目数据 itemId2IndicatorMap size={}", itemId2IndicatorMap.size());

            //查询所有老指标数据（按一级分类查，写死的，一级分类除了10000外都是体检的，一共9个ID）
            List<Indicator> oldIndicators = serviceIndicatorRepository.queryIndicatorList(ServiceIndicatorQueryContext.builder().firstIndicatorCategory(Sets.newHashSet(906596746242L, 923776581122L, 940956728834L, 949546484226L, 661004250626L, 629554156546L, 124202627586L, 565856855042L, 923776581123L)).build());
            log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem 老指标数量 oldIndicators size={}", oldIndicators.size());

            //查询所有新指标数据
            List<JdhStandardIndicator> standardIndicatorList = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
            log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem 新指标数量 standardIndicatorList size={}", standardIndicatorList.size());
            Map<String, JdhStandardIndicator> code2StandardIndicatorMap = standardIndicatorList.stream().collect(Collectors.toMap(JdhStandardIndicator::getHealthIndicatorCode, standardIndicator -> standardIndicator, (t, t2) -> t2));
            log.info("ProductDataCleanApplicationImpl -> 新指标按检后ID去重后数量 code2StandardIndicatorMap size={}", code2StandardIndicatorMap.size());
            log.info("ProductDataCleanApplicationImpl -> code2StandardIndicatorMap={}", JSON.toJSONString(code2StandardIndicatorMap));

            //查询所有标准项目数据
            List<JdhStandardItem> standardItemList = jdhStandardItemRepository.queryList(JdhStandardItemRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
            log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem 所有标准项目数据 standardItemList size={}", standardItemList.size());
            Map<Long, JdhStandardItem> id2StandardItemMap = standardItemList.stream().collect(Collectors.toMap(JdhStandardItem::getItemId, standardItem -> standardItem, (t, t2) -> t2));
            //指标对应的标准项目集合
            Map<Long, Set<Long>> indicatorId2ItemIdMap = new HashMap<>();
            standardItemList.forEach(standardItem -> {
                log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem 指标对应的标准项目集合 standardItem={}", JSON.toJSONString(standardItem));
                List<Long> indicatorList = standardItem.getIndicatorList();
                if (CollectionUtils.isNotEmpty(indicatorList)) {
                    for (Long indicatorId : indicatorList) {
                        Set<Long> list = indicatorId2ItemIdMap.get(indicatorId);
                        if (CollectionUtils.isEmpty(list)) {
                            list = new HashSet<>();
                            indicatorId2ItemIdMap.put(indicatorId, list);
                        }
                        list.add(standardItem.getItemId());
                    }
                }
            });
            log.info("ProductDataCleanApplicationImpl -> 指标对应的标准项目集合 indicatorId2ItemIdMap size={}", indicatorId2ItemIdMap.size());
            log.info("ProductDataCleanApplicationImpl -> 指标对应的标准项目集合 indicatorId2ItemIdMap={}", JSON.toJSONString(indicatorId2ItemIdMap));

            //查询新老指标映射关系
            //获取文件流
            InputStream inputStream = fileManageService.get(mappingOssKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
            List<OldStandardIndicatorMappingBO> mappingIndicatorModelList = EasyExcelUtil.read(inputStream, 1, OldStandardIndicatorMappingBO.class);
            log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem解析excel集合：importResult size={}", mappingIndicatorModelList.size());
            //设置成老指标ID -> 新指标编码list 格式map，方便处理映射
            Map<String, List<String>> resultMap = new HashMap<>();
            for (OldStandardIndicatorMappingBO oldStandardIndicatorMappingBO : mappingIndicatorModelList) {
                log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem解析excel oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                List<String> healthIndicatorCodeList = resultMap.get(oldStandardIndicatorMappingBO.getOldIndicatorId());
                if (CollectionUtils.isEmpty(healthIndicatorCodeList)) {
                    healthIndicatorCodeList = new ArrayList<>();
                    resultMap.put(oldStandardIndicatorMappingBO.getOldIndicatorId(), healthIndicatorCodeList);
                }
                /*List<String> list = Stream.of(oldStandardIndicatorMappingBO.getHealthIndicatorCode(), oldStandardIndicatorMappingBO.getHealthIndicatorCode2(),
                        oldStandardIndicatorMappingBO.getHealthIndicatorCode3(), oldStandardIndicatorMappingBO.getHealthIndicatorCode4(),
                        oldStandardIndicatorMappingBO.getHealthIndicatorCode5()).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
                log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem解析excel oldStandardIndicatorMappingBO list={}", JSON.toJSONString(list));
                List<String> result = list.stream().filter(s -> s.contains("SC")).collect(Collectors.toList());
                if (list.size() != result.size()) {
                    log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem解析excel 包含非法检后指标ID oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                    continue;
                }
                healthIndicatorCodeList.addAll(result);*/
                if (StringUtil.isNotBlank(oldStandardIndicatorMappingBO.getHealthIndicatorCode()) && oldStandardIndicatorMappingBO.getHealthIndicatorCode().contains("SC")) {
                    healthIndicatorCodeList.add(oldStandardIndicatorMappingBO.getHealthIndicatorCode());
                } else {
                    log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem解析excel 包含非法检后指标ID oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                }
            }
            log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem解析excel resultMap size={}", resultMap.size());
            log.info("ProductDataCleanApplicationImpl -> cleanBizServiceItem解析excel resultMap={}", JSON.toJSONString(resultMap));

            //遍历清洗业务项目数据和关联的指标数据
            Map<Long, List<JdhBizItem>> saveServiceItemMap = new HashMap<>();
            Map<Long, List<String>> suitablePopMap = new HashMap<Long, List<String>>(){{
                put(661004250626L, Lists.newArrayList(SuitableEnum.MAN.getCode()));//一级分类为男性专项，适用人群处理为男
                put(124202627586L, Lists.newArrayList(SuitableEnum.UNMARRY_WOMEN.getCode()));//一级分类为女性专项(不含妇科)，适用人群处理为女未婚
                put(565856855042L, Lists.newArrayList(SuitableEnum.MARRIED_WOMEN.getCode()));//一级分类为女性专项(含妇科)，适用人群处理为女已婚
                put(629554156546L, Lists.newArrayList(SuitableEnum.UNMARRY_WOMEN.getCode(), SuitableEnum.MARRIED_WOMEN.getCode()));//一级分类为女性专项，适用人群处理为女未婚、女已婚
            }};//其余一级分类，适用人群处理为全部人群适用
            itemLoop : for (ServiceItem serviceItem : serviceItems) {
                if (!itemId2IndicatorMap.containsKey(serviceItem.getItemId())) {
                    log.error("ProductDataCleanApplicationImpl -> 该业务项目未绑定指标={}", JsonUtil.toJSONString(serviceItem));
                    continue;
                } else {
                    List<Long> oldIndicatorIdList = itemId2IndicatorMap.get(serviceItem.getItemId());
                    //1.根据老指标ID映射新指标
                    Set<Long> mappingStandardIndicatorList = new HashSet<>();
                    for (Long oldIndicatorId : oldIndicatorIdList) {
                        if (!resultMap.containsKey(String.valueOf(oldIndicatorId))) {
                            log.error("ProductDataCleanApplicationImpl -> 老指标没有和新指标的映射关系oldItemId={}, oldIndicatorId={}", serviceItem.getItemId(), oldIndicatorId);
                            continue;
                        }
                        Set<Long> list = resultMap.get(String.valueOf(oldIndicatorId)).stream().map(code2StandardIndicatorMap::get).filter(Objects::nonNull).map(JdhStandardIndicator::getIndicatorId).collect(Collectors.toSet());
                        if (list.size() != resultMap.get(String.valueOf(oldIndicatorId)).size()) {
                            log.error("ProductDataCleanApplicationImpl -> 老指标和新指标的映射关系与数据库存储的新指标不符oldItemId={}, oldIndicatorId={}, mappingStandardIndicatorIdList={}, list={}", serviceItem.getItemId(), oldIndicatorId, JsonUtil.toJSONString(resultMap.get(String.valueOf(oldIndicatorId))), JsonUtil.toJSONString(list));
                            //continue itemLoop;
                        }
                        mappingStandardIndicatorList.addAll(list);
                    }
                    //2.映射标准项目列表
                    //2.1 POP业务项目，需要限制匹配的标准项目，应用场景都是to C的
                    boolean filterApplyScene = Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType());
                    Map<Long, Set<Long>> standardItemId2IndicatorMap = dispatchStandardItemList(mappingStandardIndicatorList, indicatorId2ItemIdMap, id2StandardItemMap, filterApplyScene ? StandardItemApplySceneEnum.TO_C.getScene() : null);
                    if (standardItemId2IndicatorMap.isEmpty()) {
                        log.warn("ProductDataCleanApplicationImpl -> 老项目映射标准项目报错 serviceItem={}", JsonUtil.toJSONString(serviceItem));
                        continue itemLoop;
                    }
                    //处理venderId和venderName
                    String venderId = null;
                    String venderName = "";
                    if (Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType())) {
                        Provider provider = providerRepository.findByVender(serviceItem.getChannelNo());
                        venderId = Objects.nonNull(provider) && Objects.nonNull(provider.getVenderId())? provider.getVenderId().toString() : null;
                        venderName = Objects.nonNull(provider)  && StringUtils.isNotBlank(provider.getVenderName()) ? provider.getVenderName() : null;
                    }
                    List<String> suitable = Lists.newArrayList(SuitableEnum.MAN.getCode(),SuitableEnum.UNMARRY_WOMEN.getCode(),SuitableEnum.MARRIED_WOMEN.getCode());
                    if (Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType())) {
                        //pop的项目适用人群，按照业务项目的一级分类做处理
                        //男性专项(661004250626)→男
                        //女性专项(124202627586)（不含妇科）→女未婚
                        //女性专项(565856855042)（含妇科）→女已婚
                        //女性专项(629554156546)→女已婚、女未婚
                        //其他→男、女已婚、女未婚
                        if (Objects.isNull(serviceItem.getFirstIndicatorCategory())) {
                            log.error("ProductDataCleanApplicationImpl -> pop业务项目没有指标一级分类 serviceItem={}", JSON.toJSONString(serviceItem));
                            continue itemLoop;
                        };
                        if (suitablePopMap.containsKey(serviceItem.getFirstIndicatorCategory())) {
                            suitable = suitablePopMap.get(serviceItem.getFirstIndicatorCategory());
                        }
                    } else {
                        //自营一卡万店（两种情况）
                        //情况1：项目已存适用人群
                        if (StringUtils.isNotBlank(serviceItem.getItemSuitable())) {
                            suitable = Arrays.stream(serviceItem.getItemSuitable().split(""))
                                    .sorted()
                                    .collect(Collectors.toList());
                        }
                        //情况2：项目未存适用人群，默认全部
                    }
                    Integer applyScene = null;
                    //pop的项目应用场景均为to c
                    if (Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType())) {
                        applyScene = Integer.valueOf(StandardItemApplySceneEnum.TO_C.getScene());
                    }
                    List<JdhBizItem> tempItemList = Lists.newArrayList();
                    for(Map.Entry<Long, Set<Long>> entry :  standardItemId2IndicatorMap.entrySet()){
                        JdhStandardItem jdhStandardItem = id2StandardItemMap.get(entry.getKey());
                        Integer tempApplyScene = applyScene;
                        if (Objects.isNull(applyScene)) {
                            //一卡万店的应用场景，默认取标准项目的应用场景，如果标准项目没有，则默认是to B应用场景
                            tempApplyScene = CollectionUtils.isNotEmpty(jdhStandardItem.getApplyScene()) ? Integer.valueOf(jdhStandardItem.getApplyScene().get(0)) : Integer.valueOf(StandardItemApplySceneEnum.TO_B.getScene());
                        }
                        JdhBizItem build = JdhBizItem.builder()
                                //.id()
                                .venderId(venderId)
                                .venderName(venderName)
                                .businessModeCode(Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType()) ? BusinessModeEnum.POP_LOC.getCode() : BusinessModeEnum.YK.getCode())
                                .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                                .applyScene(tempApplyScene)
                                .firstBizCategory(jdhStandardItem.getFirstBizCategory())
                                .secondBizCategory(jdhStandardItem.getSecondBizCategory())
                                .thirdBizCategory(jdhStandardItem.getThirdBizCategory())
                                //.bizItemId(SpringUtil.getBean(GenerateIdFactory.class).getId())
                                .bizItemName(jdhStandardItem.getItemName())
                                .standardItemId(entry.getKey())
                                .indicatorList(Lists.newArrayList(entry.getValue()))
                                .price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().divide(new BigDecimal("100"), 2, RoundingMode.DOWN) : null)
                                //.price(standardItemId2IndicatorMap.entrySet().size() == 1 ? serviceItem.getItemPrice() : null)
                                .mean(serviceItem.getItemMean())
                                .suitable(suitable)
                                //.gender()
                                .extJson(JdhBizItemExtJsonBO.builder().oldItemId(serviceItem.getItemId()).build())//记录老业务项目的ID，方便后续映射排查
                                .remark(serviceItem.getRemark())
                                .build();

                        tempItemList.add(build);
                    }
                    log.info("ProductDataCleanApplicationImpl -> 老项目数据 serviceItem={}，映射的新项目数据tempItemList={}", JSON.toJSONString(serviceItem), JSON.toJSONString(tempItemList));
                    saveServiceItemMap.put(serviceItem.getItemId(), tempItemList);
                }
            }
            Map<String, Set<Long>> stringSetMap = SCENE_INDICATOR_THREAD_LOCAL.get();
            log.info("ProductDataCleanApplicationImpl -> 未在相应场景的标准项目映射的指标数据 size map={}", Objects.nonNull(stringSetMap) ? stringSetMap.size() : 0);
            log.info("ProductDataCleanApplicationImpl -> 未在相应场景的标准项目映射的指标数据 map={}", JSON.toJSONString(stringSetMap));
            log.info("ProductDataCleanApplicationImpl -> 保存清洗的项目数据 saveServiceItemMap size={}", saveServiceItemMap.size());
            log.info("ProductDataCleanApplicationImpl -> 保存清洗的项目数据 saveServiceItemMap={}", JSON.toJSONString(saveServiceItemMap));

            // 查询数据库已存在的新业务项目列表
            List<JdhBizItem> existBizItemList = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().needIndicatorIdList(true).build());
            log.info("ProductDataCleanApplicationImpl -> 查询数据库已存在的新业务项目列表 existBizItemList size={}", existBizItemList.size());
            Map<Long, List<JdhBizItem>> oldItemId2BizItemMap = existBizItemList.stream().filter(jdhBizItem -> Objects.nonNull(jdhBizItem.getExtJson()) && Objects.nonNull(jdhBizItem.getExtJson().getOldItemId())).collect(Collectors.groupingBy(jdhBizItem -> jdhBizItem.getExtJson().getOldItemId()));
            log.info("ProductDataCleanApplicationImpl -> oldItemId2BizItemMap size={}", oldItemId2BizItemMap.size());
            log.info("ProductDataCleanApplicationImpl -> oldItemId2BizItemMap={}", JSON.toJSONString(oldItemId2BizItemMap));
            for (Map.Entry<Long, List<JdhBizItem>> entry : saveServiceItemMap.entrySet()) {
                //需要保存的业务项目列表
                List<JdhBizItem> jdhBizItems = entry.getValue();

                //与现有数据库中存的数据进行处理
                if (oldItemId2BizItemMap.containsKey(entry.getKey())) {
                    List<JdhBizItem> oldJdhBizItems = oldItemId2BizItemMap.get(entry.getKey());
                    Map<Long, JdhBizItem> standardIdMap = oldJdhBizItems.stream().collect(Collectors.toMap(JdhBizItem::getStandardItemId, oldItem -> oldItem, (t, t2) -> t2));
                    for (JdhBizItem item : jdhBizItems) {
                        JdhBizItem oldJdhBizItem = standardIdMap.get(item.getStandardItemId());
                        if (Objects.nonNull(oldJdhBizItem)) {
                            item.setBizItemId(oldJdhBizItem.getBizItemId());
                            standardIdMap.remove(oldJdhBizItem.getStandardItemId());
                        }
                    }
                    if (!standardIdMap.isEmpty()) {
                        standardIdMap.forEach((key, value) -> jdhBusinessItemRepository.deleteBizItemById(value.getBizItemId(), null));
                    }
                }
                //新增新数据
                jdhBizItems.forEach(jdhBizItem -> jdhBusinessItemRepository.save(jdhBizItem));
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> cleanBizServiceItem处理信息异常", e);
            SCENE_INDICATOR_THREAD_LOCAL.remove();
            return Boolean.FALSE;
        } catch (Throwable e) {
            log.error("ProductDataCleanApplicationImpl -> cleanBizServiceItem处理信息异常Throwable", e);
            SCENE_INDICATOR_THREAD_LOCAL.remove();
            return Boolean.FALSE;
        }
    }

    /**
     * 清洗京东服务数据
     * @param mappingOssKey
     * @return
     */
    @Override
    //@Transactional(rollbackFor = RuntimeException.class)
    public Boolean cleanService(String mappingOssKey) {
        try {
            //所有有效的service 和 业务项目的关联关系
            List<JdhServiceItemRel> jdhServiceItemRelList = serviceItemRelRepository.queryJdhServiceItemRelList(JdhServiceItemRelContext.builder().build());
            //按照serviceId分组
            Map<Long, List<JdhServiceItemRel>> serviceId2List = jdhServiceItemRelList.stream().collect(Collectors.groupingBy(JdhServiceItemRel::getServiceId));
            log.info("ProductDataCleanApplicationImpl -> 清洗京东服务数据 serviceId2List size={}", serviceId2List.size());
            Map<Long, JdhProgram> oldServiceId2Map = new HashMap<>();
            //遍历map，依次处理每个service和对应业务项目绑定关系
            for (Map.Entry<Long, List<JdhServiceItemRel>> entry : serviceId2List.entrySet()) {
                Long serviceId = entry.getKey();
                //查询老京东服务表，获取服务数据
                JdhService jdhService = serviceRepository.find(JdhServiceIdentifier.builder().serviceId(serviceId).build());
                //组装京东服务
                JdhProgram program = buildProgram(jdhService, serviceId);
                //查询新表京东服务
                JdhProgram existProgram = jdhProgramRepository.find(JdhProgramIdentifier.builder().programId(serviceId).build());
                if (Objects.nonNull(existProgram)) {
                    program.setId(existProgram.getId());
                    program.setVersion(existProgram.getVersion());
                }
                oldServiceId2Map.put(serviceId, program);
                //处理服务与业务项目关联关系
                List<JdhServiceItemRel> itemRels = entry.getValue();
                Map<Long, Integer> itemIdMap = itemRels.stream().collect(Collectors.toMap(JdhServiceItemRel::getItemId, jdhServiceItemRel -> Optional.ofNullable(jdhServiceItemRel.getImportant()).orElse(0), (o1, o2) -> o2));
                // 查询业务项目列表
                List<JdhBizItem> existBizItemList = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().oldItemIdList(itemIdMap.keySet()).build());
                log.info("ProductDataCleanApplicationImpl -> cleanService, existBizItemList, serviceId={}, existBizItemList={}", serviceId, JSON.toJSONString(existBizItemList));
                Map<Long, List<JdhBizItem>> item2BizList = existBizItemList.stream().filter(jdhBizItem -> Objects.nonNull(jdhBizItem.getExtJson()) && Objects.nonNull(jdhBizItem.getExtJson().getOldItemId()))
                        .collect(Collectors.groupingBy(jdhBizItem -> jdhBizItem.getExtJson().getOldItemId()));
                if (itemIdMap.size() != item2BizList.size()) {
                    Set<Long> differenceSet1 = new HashSet<>(itemIdMap.keySet());
                    differenceSet1.removeAll(item2BizList.keySet());
                    Set<Long> differenceSet2 = new HashSet<>(item2BizList.keySet());
                    differenceSet2.removeAll(itemIdMap.keySet());
                    differenceSet1.addAll(differenceSet2);
                    Set<Long> bizItemList = BIZ_ITEM_THREAD_LOCAL.get();
                    if (CollectionUtils.isEmpty(bizItemList)) {
                        bizItemList = new HashSet<>();
                        BIZ_ITEM_THREAD_LOCAL.set(bizItemList);
                    }
                    bizItemList.addAll(differenceSet1);
                    log.warn("ProductDataCleanApplicationImpl -> cleanService, 服务绑定的项目与数据库业务项目数量不一致,serviceId={}, itemIdMap={}, item2BizList={}, differenceSet1={}", serviceId, JSON.toJSONString(itemIdMap), JSON.toJSONString(item2BizList), JSON.toJSONString(differenceSet1));
                    // continue;
                }
                List<JdhProgramBizItemRel> saveServiceItemRelList = new ArrayList<>();
                for (Map.Entry<Long, Integer> itemEntry : itemIdMap.entrySet()) {
                    List<JdhBizItem> jdhBizItems = item2BizList.get(itemEntry.getKey());
                    if (CollectionUtils.isEmpty(jdhBizItems)) {
                        log.warn("ProductDataCleanApplicationImpl -> cleanService, itemEntry={}", JSON.toJSONString(itemEntry));
                        continue;
                    }
                    for (JdhBizItem jdhBizItem : jdhBizItems) {
                        saveServiceItemRelList.add(JdhProgramBizItemRel.builder().bizItemId(jdhBizItem.getBizItemId()).programId(program.getProgramId()).isImportant(itemEntry.getValue()).build());
                    }
                }
                program.setProgramBizItemRelList(saveServiceItemRelList);
                log.info("ProductDataCleanApplicationImpl -> cleanService, 保存 program={}", JSON.toJSONString(program));
                jdhProgramRepository.save(program);
                //jdhProgramRepository.batchSaveProgramItemRel(saveServiceItemRelList);
            }
            log.info("ProductDataCleanApplicationImpl -> 找不到业务项目的数据总数, bizItemList={}", BIZ_ITEM_THREAD_LOCAL.get());
            log.info("ProductDataCleanApplicationImpl -> cleanService, oldServiceId2Map={}", JSON.toJSONString(oldServiceId2Map));
            //处理组合套餐
            List<JdhServiceGroup> jdhServiceGroups = serviceGroupRepository.queryJdhServiceGroupList(JdhServiceGroupContext.builder().build());
            Map<Long, List<JdhServiceGroup>> groupId2Map = jdhServiceGroups.stream().collect(Collectors.groupingBy(JdhServiceGroup::getGroupId));
            log.info("ProductDataCleanApplicationImpl -> cleanService, 处理组合套餐 groupId2Map size={}", groupId2Map.size());
            log.info("ProductDataCleanApplicationImpl -> cleanService, 处理组合套餐 groupId2Map={}", JSON.toJSONString(groupId2Map));
            serviceGroupLoop:
            for (Map.Entry<Long, List<JdhServiceGroup>> entry : groupId2Map.entrySet()) {
                Long groupId = entry.getKey();
                //查询是否存在组合京东服务，新表如果存在，则不做处理。新表不存在，保存service
                JdhProgram program = jdhProgramRepository.find(JdhProgramIdentifier.builder().programId(groupId).build());
                log.info("ProductDataCleanApplicationImpl -> cleanService, 处理组合套餐 program={}", JSON.toJSONString(program));
                if (Objects.isNull(program) && CollectionUtils.isNotEmpty(entry.getValue())) {
                    //获取组合服务数据
                    JdhServiceGroup jdhServiceGroup = entry.getValue().get(0);
                    //新表保存京东服务
                    program = JdhProgram.builder()
                            .programId(groupId)
                            .programName(jdhServiceGroup.getGroupName())
                            .saleShowMethod(ProgramSaleShowMethodTypeEnum.COMPOSE.getTypeNo())
                            .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                            .businessModeCode(BusinessModeEnum.YK.getCode())
                            .suitable(ProductProgramConvertor.ins.suitableProgramConvert(jdhServiceGroup.getGroupSuitable()))
                            .build();
                    jdhProgramRepository.save(program);
                } else {
                    program.setSaleShowMethod(ProgramSaleShowMethodTypeEnum.COMPOSE.getTypeNo());
                    program.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
                    program.setBusinessModeCode(BusinessModeEnum.YK.getCode());
                    jdhProgramRepository.save(program);
                }
                log.info("ProductDataCleanApplicationImpl -> cleanService, group program={}", JSON.toJSONString(program));
                //保存组合套餐配置的和基础套餐关系
                List<JdhProgramRel> jdhProgramRelList = new ArrayList<>();
                for (JdhServiceGroup jdhServiceGroup : entry.getValue()) {
                    if (!oldServiceId2Map.containsKey(jdhServiceGroup.getServiceId())) {
                        log.warn("ProductDataCleanApplicationImpl -> cleanService, 组合套餐绑定的基础套餐不存在, jdhServiceGroup={}", JSON.toJSONString(jdhServiceGroup));
                        continue serviceGroupLoop;
                    }
                    jdhProgramRelList.add(JdhProgramRel.builder()
                            .sourceProgramId(program.getProgramId())
                            .targetProgramId(oldServiceId2Map.get(jdhServiceGroup.getServiceId()).getProgramId())
                            .relevanceType(3)
                            .build());
                }
                log.info("ProductDataCleanApplicationImpl -> cleanService, group program={}, rel={}", JSON.toJSONString(program), JSON.toJSONString(jdhProgramRelList));
                jdhProgramRepository.batchSaveProgramRel(jdhProgramRelList);
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> cleanBizServiceItem处理信息异常Exception", e);
            BIZ_ITEM_THREAD_LOCAL.remove();
            return Boolean.FALSE;
        } catch (Throwable e) {
            log.error("ProductDataCleanApplicationImpl -> cleanBizServiceItem处理信息异常Throwable", e);
            BIZ_ITEM_THREAD_LOCAL.remove();
            return Boolean.FALSE;
        }
    }

    /**
     * 清洗自营商品套餐和项目关系
     * @param mappingOssKey
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Exception.class)
    public Boolean cleanSelfServiceRel(String mappingOssKey) {
        //所有有效的service 和 业务项目的关联关系
        List<JdhServiceItemRel> jdhServiceItemRelList = serviceItemRelRepository.queryJdhServiceItemRelList(JdhServiceItemRelContext.builder().build());
        //按照serviceId分组
        Map<Long, List<JdhServiceItemRel>> serviceId2List = jdhServiceItemRelList.stream().collect(Collectors.groupingBy(JdhServiceItemRel::getServiceId));
        log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceRel 清洗京东服务数据 serviceId2List size={}", serviceId2List.size());
        //查询所有新业务项目
        List<JdhBizItem> bizItems = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().businessModeCode(BusinessModeEnum.YK.getCode()).build());
        Map<Long, JdhBizItem> id2ItemMap = bizItems.stream().collect(Collectors.toMap(JdhBizItem::getBizItemId, jdhBizItem -> jdhBizItem, (t, t2) -> t2));

        List<JdhProgramQuery> list = serviceId2List.keySet().stream().map(serviceId -> JdhProgramQuery.builder().programId(serviceId).build()).collect(Collectors.toList());
        List<JdhProgram> programList = jdhProgramRepository.queryList(list);
        Map<Long, JdhProgram> id2Program = programList.stream().collect(Collectors.toMap(JdhProgram::getProgramId, jdhProgram -> jdhProgram, (t, t2) -> t2));
        List<JdhProgram> saveList = new ArrayList<>();
        Set<Long> loseItemIdSet = new HashSet<>();
        for (Map.Entry<Long, List<JdhServiceItemRel>> serviceEntry : serviceId2List.entrySet()) {
            JdhProgram jdhProgram = id2Program.get(serviceEntry.getKey());
            if (Objects.isNull(jdhProgram)) {
                log.error("ProductDataCleanApplicationImpl -> cleanSelfServiceRel jdhProgram为空, serviceEntry={}", JSON.toJSONString(serviceEntry));
                continue;
            }

            List<JdhServiceItemRel> serviceItemRelList = serviceEntry.getValue();
            if (CollectionUtils.isEmpty(serviceItemRelList)) {
                log.error("ProductDataCleanApplicationImpl -> cleanSelfServiceRel jdhProgram serviceItemRelList为空, serviceEntry={}", JSON.toJSONString(serviceEntry));
                continue;
            }
            List<JdhProgramBizItemRel> saveRelList = serviceItemRelList.stream().map(serviceItemRel -> {
                //判断项目是否存在
                JdhBizItem jdhBizItem = id2ItemMap.get(serviceItemRel.getItemId());
                if (Objects.isNull(jdhBizItem)) {
                    log.error("ProductDataCleanApplicationImpl -> cleanSelfServiceRel 项目不存在, serviceItemRel={}", JSON.toJSONString(serviceItemRel));
                    loseItemIdSet.add(serviceItemRel.getItemId());
                    return null;
                }
                return JdhProgramBizItemRel.builder()
                        //.id()
                        .bizItemId(jdhBizItem.getBizItemId())
                        //.remark()
                        .isImportant(serviceItemRel.getImportant())
                        .programId(jdhProgram.getProgramId())
                        //.version()
                        .yn(1)
                        .createUser(serviceItemRel.getCreateUser())
                        .createTime(serviceItemRel.getCreateTime())
                        .build();
            }).filter(Objects::nonNull).collect(Collectors.toList());

            //保存套餐的业务项目
            jdhProgram.setProgramBizItemRelList(saveRelList);
            saveList.add(jdhProgram);
        }
        log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceRel saveList={}", JSON.toJSONString(saveList));
        log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceRel loseItemIdSet={}", JSON.toJSONString(loseItemIdSet));
        for (JdhProgram jdhProgram : saveList) {
            jdhProgramRepository.save(jdhProgram);
        }
        return Boolean.TRUE;
    }

    /**
     * 清洗自营商品项目
     * @param ossKey 问题老项目excel
     * @param mappingOssKey 指标映射表
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cleanSelfServiceItem(String ossKey, String mappingOssKey) {
        //TODO 删除老项目创建的历史新项目，extJson.oldItemId相同
        try {

            //查询所有业务项目数据（自营体检、pop体检）
            List<ServiceItem> serviceItems = serviceItemRepository.queryServiceItemList(JdhItemListQueryContext.builder().firstIndicatorCategory(Sets.newHashSet(906596746242L, 923776581122L, 940956728834L, 949546484226L, 661004250626L, 629554156546L, 124202627586L, 565856855042L, 923776581123L)).vendorType(VenderTypeEnum.SELF.getType()).build());
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 所有体检业务项目数据 size={}", serviceItems.size());
            Set<Long> itemIdSet = serviceItems.stream().map(ServiceItem::getItemId).collect(Collectors.toSet());
            //查询业务项目关联的指标数据
            List<ServiceItemIndicatorRel> serviceItemIndicatorRelList = serviceItemIndicatorRepository.queryItemIndicatorList(ServiceItemQueryContext.builder().itemIds(itemIdSet).build());
            Map<Long, List<Long>> itemId2IndicatorMap = serviceItemIndicatorRelList.stream().collect(Collectors.groupingBy(ServiceItemIndicatorRel::getItemId, Collectors.mapping(ServiceItemIndicatorRel::getIndicatorId, Collectors.toList())));
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 所有关联了指标的体检业务项目数据 itemId2IndicatorMap size={}", itemId2IndicatorMap.size());

            //查询所有老指标数据（按一级分类查，写死的，一级分类除了10000外都是体检的，一共9个ID）
            List<Indicator> oldIndicators = serviceIndicatorRepository.queryIndicatorList(ServiceIndicatorQueryContext.builder().firstIndicatorCategory(Sets.newHashSet(906596746242L, 923776581122L, 940956728834L, 949546484226L, 661004250626L, 629554156546L, 124202627586L, 565856855042L, 923776581123L)).build());
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 老指标数量 oldIndicators size={}", oldIndicators.size());
            Map<Long, Indicator> oldId2IndicatorMap = oldIndicators.stream().collect(Collectors.toMap(Indicator::getIndicatorId, indicator -> indicator, (t, t2) -> t2));

            //查询所有新指标数据
            List<JdhStandardIndicator> standardIndicatorList = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 新指标数量 standardIndicatorList size={}", standardIndicatorList.size());
            Map<String, JdhStandardIndicator> code2StandardIndicatorMap = standardIndicatorList.stream().collect(Collectors.toMap(JdhStandardIndicator::getHealthIndicatorCode, standardIndicator -> standardIndicator, (t, t2) -> t2));
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 新指标按检后ID去重后数量 code2StandardIndicatorMap size={}", code2StandardIndicatorMap.size());
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem code2StandardIndicatorMap={}", JSON.toJSONString(code2StandardIndicatorMap));

            //查询所有标准项目数据
            List<JdhStandardItem> standardItemList = jdhStandardItemRepository.queryList(JdhStandardItemRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 所有标准项目数据 standardItemList size={}", standardItemList.size());
            Map<Long, JdhStandardItem> id2StandardItemMap = standardItemList.stream().collect(Collectors.toMap(JdhStandardItem::getItemId, standardItem -> standardItem, (t, t2) -> t2));
            Map<String, JdhStandardItem> name2StandardItemMap = standardItemList.stream().collect(Collectors.toMap(JdhStandardItem::getItemName, standardItem -> standardItem, (t, t2) -> t2));

            //指标对应的标准项目集合
            Map<Long, Set<Long>> indicatorId2ItemIdMap = new HashMap<>();
            standardItemList.forEach(standardItem -> {
                log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 指标对应的标准项目集合 standardItem={}", JSON.toJSONString(standardItem));
                List<Long> indicatorList = standardItem.getIndicatorList();
                if (CollectionUtils.isNotEmpty(indicatorList)) {
                    for (Long indicatorId : indicatorList) {
                        Set<Long> list = indicatorId2ItemIdMap.get(indicatorId);
                        if (CollectionUtils.isEmpty(list)) {
                            list = new HashSet<>();
                            indicatorId2ItemIdMap.put(indicatorId, list);
                        }
                        list.add(standardItem.getItemId());
                    }
                }
            });
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 指标对应的标准项目集合 indicatorId2ItemIdMap size={}", indicatorId2ItemIdMap.size());
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 指标对应的标准项目集合 indicatorId2ItemIdMap={}", JSON.toJSONString(indicatorId2ItemIdMap));

            //查询新老指标映射关系
            //获取文件流
            InputStream inputStream = fileManageService.get(mappingOssKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
            List<OldStandardIndicatorMappingBO> mappingIndicatorModelList = EasyExcelUtil.read(inputStream, 1, OldStandardIndicatorMappingBO.class);
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 解析excel集合：importResult size={}", mappingIndicatorModelList.size());

            //问题老项目excel
            //获取文件流
            InputStream problemItemInputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
            List<ServiceIndicatorExportBO> exportlist = EasyExcelUtil.read(problemItemInputStream, 1, ServiceIndicatorExportBO.class);
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 问题老项目excel：exportlist size={}", exportlist.size());
            //id -> 修改的内容
            Map<Long, ServiceIndicatorExportBO> id2ExportDto = new HashMap<>();
            //id -> 新指标id
            Map<Long, Set<Long>> id2IndicatorIdMap = new HashMap<>();
            for (ServiceIndicatorExportBO exportDto : exportlist) {
                if (!id2ExportDto.containsKey(exportDto.getItemId())) {
                    id2ExportDto.put(exportDto.getItemId(), exportDto);
                }
                if (StringUtils.isBlank(exportDto.getHealthIndicatorCode())) {
                    log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 老项目没有指标：exportDto={}", JSON.toJSONString(exportDto));
                    continue;
                }
                JdhStandardIndicator jdhStandardIndicator = code2StandardIndicatorMap.get(exportDto.getHealthIndicatorCode());
                if (Objects.isNull(jdhStandardIndicator)) {
                    log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem jdhStandardIndicator null healthIndicatorCode={}", exportDto.getHealthIndicatorCode());
                    throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                }

                Set<Long> indicatorIds = id2IndicatorIdMap.get(exportDto.getItemId());
                if (CollectionUtils.isEmpty(indicatorIds)) {
                    indicatorIds = new HashSet<>();
                    id2IndicatorIdMap.put(exportDto.getItemId(), indicatorIds);
                }
                indicatorIds.add(jdhStandardIndicator.getIndicatorId());
            }
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem id2ExportDto={}", JSON.toJSONString(id2ExportDto));
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem id2IndicatorIdMap={}", JSON.toJSONString(id2IndicatorIdMap));

            //设置成老指标ID -> 新指标编码list 格式map，方便处理映射
            Map<String, Set<String>> resultMap = new HashMap<>();
            for (OldStandardIndicatorMappingBO oldStandardIndicatorMappingBO : mappingIndicatorModelList) {
                log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 解析excel oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                Set<String> healthIndicatorCodeList = resultMap.get(oldStandardIndicatorMappingBO.getOldIndicatorId());
                if (CollectionUtils.isEmpty(healthIndicatorCodeList)) {
                    healthIndicatorCodeList = new HashSet<>();
                    resultMap.put(oldStandardIndicatorMappingBO.getOldIndicatorId(), healthIndicatorCodeList);
                }
                if (StringUtil.isNotBlank(oldStandardIndicatorMappingBO.getHealthIndicatorCode()) && oldStandardIndicatorMappingBO.getHealthIndicatorCode().contains("SC")) {
                    healthIndicatorCodeList.add(oldStandardIndicatorMappingBO.getHealthIndicatorCode());
                } else {
                    log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 解析excel 包含非法检后指标ID oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                }
            }
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 解析excel resultMap size={}", resultMap.size());
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 解析excel resultMap={}", JSON.toJSONString(resultMap));
            List<JdhBizItem> saveItemList = new ArrayList<>();
            //遍历清洗业务项目数据和关联的指标数据
            for (ServiceItem serviceItem : serviceItems) {
                //特殊需要修正的项目数据
                if (id2ExportDto.containsKey(serviceItem.getItemId())) {
                    ServiceIndicatorExportBO exportDto = id2ExportDto.get(serviceItem.getItemId());
                    //处理venderId和venderName
                    String venderId = null;
                    String venderName = "";
                    JdhStandardItem jdhStandardItem = name2StandardItemMap.get(exportDto.getStandardItemName());
                    if (Objects.isNull(jdhStandardItem)) {
                        log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem jdhStandardItem null StandardItemName={}", exportDto.getStandardItemName());
                        throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                    }
                    List<String> suitable = Arrays.stream(exportDto.getItemSuitable().split(""))
                            .sorted()
                            .collect(Collectors.toList());
                    Integer tempApplyScene = CollectionUtils.isNotEmpty(jdhStandardItem.getApplyScene()) ? Integer.valueOf(jdhStandardItem.getApplyScene().get(0)) : Integer.valueOf(StandardItemApplySceneEnum.TO_B.getScene());

                    Set<Long> newIndicatorIdList = id2IndicatorIdMap.get(serviceItem.getItemId());
                    //项目没有指标，创建一个空指标项目
                    if (CollectionUtils.isEmpty(newIndicatorIdList)) {
                        JdhBizItem build = JdhBizItem.builder()
                                //.id()
                                .venderId(venderId)
                                .venderName(venderName)
                                .businessModeCode(BusinessModeEnum.YK.getCode())
                                .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                                .applyScene(tempApplyScene)
                                .firstBizCategory(jdhStandardItem.getFirstBizCategory())
                                .secondBizCategory(jdhStandardItem.getSecondBizCategory())
                                .thirdBizCategory(jdhStandardItem.getThirdBizCategory())
                                .bizItemId(serviceItem.getItemId())
                                .bizItemName(serviceItem.getItemName())
                                .standardItemId(jdhStandardItem.getItemId())
                                //.indicatorList(Lists.newArrayList(entry.getValue()))
                                .price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().divide(new BigDecimal("100"), 2, RoundingMode.DOWN) : null)
                                .mean(exportDto.getItemMean())
                                .suitable(suitable)
                                //.gender()
                                //.extJson(JdhBizItemExtJsonBO.builder().oldItemId(serviceItem.getItemId()).build())//记录老业务项目的ID，方便后续映射排查
                                .remark(serviceItem.getRemark())
                                .build();
                        saveItemList.add(build);
                        continue;
                    }
                    //1.根据老指标ID映射新指标
                    Set<Long> mappingStandardIndicatorList = newIndicatorIdList;
                    /*for (Long oldIndicatorId : oldIndicatorIdList) {
                        if (!resultMap.containsKey(String.valueOf(oldIndicatorId))) {
                            log.error("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 老指标没有和新指标的映射关系oldItemId={}, oldIndicatorId={}", serviceItem.getItemId(), oldIndicatorId);
                            throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                        }
                        Set<Long> list = resultMap.get(String.valueOf(oldIndicatorId)).stream().map(code2StandardIndicatorMap::get).filter(Objects::nonNull).map(JdhStandardIndicator::getIndicatorId).collect(Collectors.toSet());
                        if (list.size() != resultMap.get(String.valueOf(oldIndicatorId)).size()) {
                            log.error("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 老指标和新指标的映射关系与数据库存储的新指标不符oldItemId={}, oldIndicatorId={}, mappingStandardIndicatorIdList={}, list={}", serviceItem.getItemId(), oldIndicatorId, JsonUtil.toJSONString(resultMap.get(String.valueOf(oldIndicatorId))), JsonUtil.toJSONString(list));
                            throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                        }
                        mappingStandardIndicatorList.addAll(list);
                    }*/

                    Map<Long, Set<Long>> standardItemId2IndicatorMap = dispatchStandardItemList(mappingStandardIndicatorList, indicatorId2ItemIdMap, id2StandardItemMap, null);
                    if (standardItemId2IndicatorMap.isEmpty()) {
                        log.warn("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 老项目映射标准项目报错 serviceItem={}", JsonUtil.toJSONString(serviceItem));
                        throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                    }
                    if (standardItemId2IndicatorMap.size() > 1) {
                        log.warn("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 老项目映射标准项目报错 serviceItem={}", JsonUtil.toJSONString(serviceItem));
                        throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                    }
                    for (Map.Entry<Long, Set<Long>> entry : standardItemId2IndicatorMap.entrySet()) {
                        JdhBizItem build = JdhBizItem.builder()
                                //.id()
                                .venderId(venderId)
                                .venderName(venderName)
                                .businessModeCode(BusinessModeEnum.YK.getCode())
                                .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                                .applyScene(tempApplyScene)
                                .firstBizCategory(jdhStandardItem.getFirstBizCategory())
                                .secondBizCategory(jdhStandardItem.getSecondBizCategory())
                                .thirdBizCategory(jdhStandardItem.getThirdBizCategory())
                                .bizItemId(serviceItem.getItemId())
                                .bizItemName(serviceItem.getItemName())
                                .standardItemId(jdhStandardItem.getItemId())
                                .indicatorList(Lists.newArrayList(entry.getValue()))
                                .price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().divide(new BigDecimal("100"), 2, RoundingMode.DOWN) : null)
                                .mean(exportDto.getItemMean())
                                .suitable(suitable)
                                //.gender()
                                //.extJson(JdhBizItemExtJsonBO.builder().oldItemId(serviceItem.getItemId()).build())//记录老业务项目的ID，方便后续映射排查
                                .remark(serviceItem.getRemark())
                                .build();
                        saveItemList.add(build);
                    }
                    continue;
                }

                if (!itemId2IndicatorMap.containsKey(serviceItem.getItemId())) {
                    log.error("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 该业务项目未绑定指标={}", JsonUtil.toJSONString(serviceItem));
                    continue;
                } else {
                    List<Long> oldIndicatorIdList = itemId2IndicatorMap.get(serviceItem.getItemId());
                    //1.根据老指标ID映射新指标
                    Set<Long> mappingStandardIndicatorList = new HashSet<>();
                    for (Long oldIndicatorId : oldIndicatorIdList) {
                        if (!resultMap.containsKey(String.valueOf(oldIndicatorId))) {
                            log.error("ProductDataCleanApplicationImpl -> 老指标没有和新指标的映射关系oldItemId={}, oldIndicatorId={}", serviceItem.getItemId(), oldIndicatorId);
                            throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                        }
                        Set<Long> list = resultMap.get(String.valueOf(oldIndicatorId)).stream().map(code2StandardIndicatorMap::get).filter(Objects::nonNull).map(JdhStandardIndicator::getIndicatorId).collect(Collectors.toSet());
                        if (list.size() != resultMap.get(String.valueOf(oldIndicatorId)).size()) {
                            log.error("ProductDataCleanApplicationImpl -> 老指标和新指标的映射关系与数据库存储的新指标不符oldItemId={}, oldIndicatorId={}, mappingStandardIndicatorIdList={}, list={}", serviceItem.getItemId(), oldIndicatorId, JsonUtil.toJSONString(resultMap.get(String.valueOf(oldIndicatorId))), JsonUtil.toJSONString(list));
                            throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                        }
                        mappingStandardIndicatorList.addAll(list);
                    }
                    //2.映射标准项目列表
                    //2.1 POP业务项目，需要限制匹配的标准项目，应用场景都是to C的
                    boolean filterApplyScene = Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType());
                    Map<Long, Set<Long>> standardItemId2IndicatorMap = dispatchStandardItemList(mappingStandardIndicatorList, indicatorId2ItemIdMap, id2StandardItemMap, filterApplyScene ? StandardItemApplySceneEnum.TO_C.getScene() : null);
                    if (standardItemId2IndicatorMap.isEmpty()) {
                        log.warn("ProductDataCleanApplicationImpl -> 老项目映射标准项目报错 serviceItem={}", JsonUtil.toJSONString(serviceItem));
                        throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                    }
                    if (standardItemId2IndicatorMap.size() > 1) {
                        log.warn("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 老项目映射标准项目报错 serviceItem={}", JsonUtil.toJSONString(serviceItem));
                        throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                    }
                    //处理venderId和venderName
                    String venderId = null;
                    String venderName = "";
                    List<String> suitable = Arrays.stream(serviceItem.getItemSuitable().split(""))
                            .sorted()
                            .collect(Collectors.toList());
                    for(Map.Entry<Long, Set<Long>> entry :  standardItemId2IndicatorMap.entrySet()){
                        JdhStandardItem jdhStandardItem = id2StandardItemMap.get(entry.getKey());
                        Integer tempApplyScene = CollectionUtils.isNotEmpty(jdhStandardItem.getApplyScene()) ? Integer.valueOf(jdhStandardItem.getApplyScene().get(0)) : Integer.valueOf(StandardItemApplySceneEnum.TO_B.getScene());

                        JdhBizItem build = JdhBizItem.builder()
                                //.id()
                                .venderId(venderId)
                                .venderName(venderName)
                                .businessModeCode(BusinessModeEnum.YK.getCode())
                                .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                                .applyScene(tempApplyScene)
                                .firstBizCategory(jdhStandardItem.getFirstBizCategory())
                                .secondBizCategory(jdhStandardItem.getSecondBizCategory())
                                .thirdBizCategory(jdhStandardItem.getThirdBizCategory())
                                .bizItemId(serviceItem.getItemId())
                                .bizItemName(serviceItem.getItemName())
                                .standardItemId(entry.getKey())
                                .indicatorList(Lists.newArrayList(entry.getValue()))
                                .price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().divide(new BigDecimal("100"), 2, RoundingMode.DOWN) : null)
                                //.price(standardItemId2IndicatorMap.entrySet().size() == 1 ? serviceItem.getItemPrice() : null)
                                .mean(serviceItem.getItemMean())
                                .suitable(suitable)
                                //.gender()
                                //.extJson(JdhBizItemExtJsonBO.builder().oldItemId(serviceItem.getItemId()).build())//记录老业务项目的ID，方便后续映射排查
                                .remark(serviceItem.getRemark())
                                .build();

                        saveItemList.add(build);
                        log.info("ProductDataCleanApplicationImpl -> 老项目数据 serviceItem={}，映射的新项目数据build={}", JSON.toJSONString(serviceItem), JSON.toJSONString(build));
                    }

                }
            }
            log.info("ProductDataCleanApplicationImpl -> cleanSelfServiceItem saveItemList={}", JSON.toJSONString(saveItemList));
            //查已清洗的数据
            //TODO 确认无误放开
            saveItemList.forEach(jdhBizItem -> {
                //历史的删掉，插入新的
                jdhBusinessItemRepository.deleteBizItemById(jdhBizItem.getBizItemId(), "system");
                jdhBusinessItemRepository.createBizItem(jdhBizItem);
            });
            return true;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 处理信息异常", e);
            throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
        } catch (Throwable e) {
            log.error("ProductDataCleanApplicationImpl -> cleanSelfServiceItem 处理信息异常Throwable", e);
            throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 新增自营商品项目
     * @param ossKey 问题老项目excel
     * @return
     */
    @Override
    public Boolean addSelfServiceItem(String ossKey) {
        try {
            //查询所有标准项目数据
            List<JdhStandardItem> standardItemList = jdhStandardItemRepository.queryList(JdhStandardItemRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
            log.info("ProductDataCleanApplicationImpl -> addSelfServiceItem 所有标准项目数据 standardItemList size={}", standardItemList.size());
            Map<String, JdhStandardItem> name2StandardItemMap = standardItemList.stream().collect(Collectors.toMap(JdhStandardItem::getItemName, standardItem -> standardItem, (t, t2) -> t2));

            //问题老项目excel
            //获取文件流
            InputStream problemItemInputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
            List<ServiceIndicatorExportBO> exportlist = EasyExcelUtil.read(problemItemInputStream, 1, ServiceIndicatorExportBO.class);
            log.info("ProductDataCleanApplicationImpl -> addSelfServiceItem 问题老项目excel：exportlist size={}", exportlist.size());
            List<JdhBizItem> saveItemList = new ArrayList<>();
            for (ServiceIndicatorExportBO exportDto : exportlist) {
                //处理venderId和venderName
                String venderId = null;
                String venderName = "";
                JdhStandardItem jdhStandardItem = name2StandardItemMap.get(exportDto.getStandardItemName());
                if (Objects.isNull(jdhStandardItem)) {
                    log.info("ProductDataCleanApplicationImpl -> addSelfServiceItem jdhStandardItem null StandardItemName={}", exportDto.getStandardItemName());
                    throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                }
                List<String> suitable = Arrays.stream(exportDto.getItemSuitable().split(""))
                        .sorted()
                        .collect(Collectors.toList());
                Integer tempApplyScene = CollectionUtils.isNotEmpty(jdhStandardItem.getApplyScene()) ? Integer.valueOf(jdhStandardItem.getApplyScene().get(0)) : Integer.valueOf(StandardItemApplySceneEnum.TO_B.getScene());

                //特殊需要修正的项目数据
                JdhBizItem build = JdhBizItem.builder()
                        //.id()
                        .venderId(venderId)
                        .venderName(venderName)
                        .businessModeCode(BusinessModeEnum.YK.getCode())
                        .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                        .applyScene(tempApplyScene)
                        .firstBizCategory(jdhStandardItem.getFirstBizCategory())
                        .secondBizCategory(jdhStandardItem.getSecondBizCategory())
                        .thirdBizCategory(jdhStandardItem.getThirdBizCategory())
                        .bizItemId(exportDto.getItemId())
                        .bizItemName(exportDto.getItemName())
                        .standardItemId(jdhStandardItem.getItemId())
                        //.indicatorList(Lists.newArrayList(entry.getValue()))
                        .price(null)
                        .mean(exportDto.getItemMean())
                        .suitable(suitable)
                        //.gender()
                        //.extJson(JdhBizItemExtJsonBO.builder().oldItemId(serviceItem.getItemId()).build())//记录老业务项目的ID，方便后续映射排查
                        //.remark(serviceItem.getRemark())
                        .build();
                saveItemList.add(build);
            }
            log.info("ProductDataCleanApplicationImpl -> addSelfServiceItem saveItemList={}", JSON.toJSONString(saveItemList));
            //确认无误保存
            saveItemList.forEach(jdhBizItem -> {
                //历史的删掉，插入新的
                jdhBusinessItemRepository.deleteBizItemById(jdhBizItem.getBizItemId(), "system");
                jdhBusinessItemRepository.createBizItem(jdhBizItem);
            });
            return true;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> addSelfServiceItem 处理信息异常", e);
            throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
        } catch (Throwable e) {
            log.error("ProductDataCleanApplicationImpl -> addSelfServiceItem 处理信息异常Throwable", e);
            throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 清洗sku信息
     * @param eventBodyList
     * @return
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.product.service.impl.ProductDataCleanApplicationImpl.cleanProductSku")
    public Boolean cleanProductSku(List<ExaminationSkuBinLakeBody> eventBodyList) {
        if (CollectionUtils.isEmpty(eventBodyList)) {
            return Boolean.FALSE;
        }
        for (ExaminationSkuBinLakeBody body : eventBodyList) {
            SkuInfoDTO skuInfoDTO = skuInfoRpc.queryGoodsInfoBySkuNo(body.getSkuNo());
            if (Objects.isNull(skuInfoDTO)) {
                log.warn("ProductDataCleanApplicationImpl -> cleanProductSku, 未查询到运营端配置的sku信息 body={}", JSON.toJSONString(body));
                continue;
            }
            if (Objects.isNull(skuInfoDTO.getSkuSpecies())) {
                log.warn("ProductDataCleanApplicationImpl -> cleanProductSku, 无法判断sku的类型 body={}, skuInfoDTO={}", JSON.toJSONString(body), JSON.toJSONString(skuInfoDTO));
                continue;
            }
            //基础sku、加项sku判断
            if (Objects.equals(skuInfoDTO.getSkuSpecies(), 1)) {
                //基础sku
                //判断普通套餐 or 组合套餐判断
                if (Objects.isNull(skuInfoDTO.getComposeId()) && Objects.isNull(skuInfoDTO.getGroupNo())) {
                    log.warn("ProductDataCleanApplicationImpl -> cleanProductSku, sku暂未绑定基础套餐或者组合套餐信息 body={}, skuInfoDTO={}", JSON.toJSONString(body), JSON.toJSONString(skuInfoDTO));
                    continue;
                }
                JdhProgram jdhProgram = null;
                //普通套餐
                if (Objects.equals(skuInfoDTO.getSkuUnionType(), 1)) {
                    jdhProgram = jdhProgramRepository.find(JdhProgramIdentifier.builder().programId(skuInfoDTO.getGroupNo()).build());
                }
                //组合套餐
                else if (Objects.equals(skuInfoDTO.getSkuUnionType(), 2)) {
                    jdhProgram = jdhProgramRepository.find(JdhProgramIdentifier.builder().programId(skuInfoDTO.getComposeId()).build());
                }
                if (Objects.isNull(jdhProgram)) {
                    //如果不存在，说明清洗jdhProgram数据有问题
                    log.warn("ProductDataCleanApplicationImpl -> cleanProductSku, jdhProgram不存在, skuInfoDTO={}", JSON.toJSONString(skuInfoDTO));
                    continue;
                } else {
                    //存在，按照查询的运营端数据更新Program实体
                    //jdhProgram.setProgramName(skuInfoDTO.getSkuName());
                    jdhProgram.setBusinessModeCode(BusinessModeEnum.YK.getCode());
                    jdhProgram.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
                    jdhProgram.setProgramDesc(skuInfoDTO.getGroupDesc());
                    jdhProgram.setSaleStatus(1);
                    jdhProgram.setSaleShowMethod(Objects.equals(skuInfoDTO.getSkuUnionType(), 1) ? ProgramSaleShowMethodTypeEnum.NORMAL.getTypeNo() : ProgramSaleShowMethodTypeEnum.COMPOSE.getTypeNo());
                    jdhProgram.setSaleLimitType(1);
                    jdhProgram.setSuitable(ProductProgramConvertor.ins.suitableProgramConvert(skuInfoDTO.getSkuSuitable()));
                    //jdhProgram.setProgramBizItemRelList();
                    jdhProgram.setUpdateUser(skuInfoDTO.getUpdateUser());
                    jdhProgram.setUpdateTime(skuInfoDTO.getUpdateTime());
                }
                jdhProgramRepository.save(jdhProgram);
                //维护program和sku关联关系表
                JdhProgramSku programSku = jdhProgramRepository.findProgramSku(JdhProgramSkuIdentifier.builder().skuId(body.getSkuNo()).build());
                log.info("ProductDataCleanApplicationImpl -> cleanProductSku, programSku={}", JSON.toJSONString(programSku));
                if (Objects.isNull(programSku)) {
                    //如果不存在，组装programSku实体
                    programSku = JdhProgramSku.builder()
                            .businessModeCode(BusinessModeEnum.YK.getCode())
                            .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                            .programId(jdhProgram.getProgramId())
                            .skuId(body.getSkuNo())
                            //.productId()
                            .channelType(1)
                            .channelId(ProductSaleChannelEnum.XFYL.getChannelId())
                            .saleStatus(1)
                            .remark(skuInfoDTO.getSkuDesc())
                            .build();
                } else {
                    //存在，按照查询的运营端数据更新Program实体
                    programSku.setProgramId(jdhProgram.getProgramId());
                    programSku.setRemark(skuInfoDTO.getSkuDesc());
                }
                jdhProgramRepository.saveProgramSku(programSku);
            } else {
                //加项sku
                //查询sku配置的program信息
                JdhProgram jdhProgram = jdhProgramRepository.find(JdhProgramIdentifier.builder().programId(Long.valueOf(skuInfoDTO.getSkuNo())).build());
                if (Objects.isNull(jdhProgram)) {
                    //如果不存在，组装Program实体
                    jdhProgram = JdhProgram.builder()
                            .programId(Long.valueOf(skuInfoDTO.getSkuNo()))
                            .programName(skuInfoDTO.getSkuName())
                            .businessModeCode(BusinessModeEnum.YK.getCode())
                            .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                            .programDesc(skuInfoDTO.getGroupDesc())
                            .saleStatus(1)
                            .saleShowMethod(ProgramSaleShowMethodTypeEnum.NORMAL.getTypeNo())
                            .saleLimitType(2)
                            //.ageRange()
                            //.gender()
                            .suitable(ProductProgramConvertor.ins.suitableProgramConvert(skuInfoDTO.getSkuSuitable()))
                            //.programBizItemRelList()
                            //.createUser()
                            //.createTime()
                            .updateUser(skuInfoDTO.getUpdateUser())
                            .updateTime(skuInfoDTO.getUpdateTime())
                            .build();
                } else {
                    //存在，按照查询的运营端数据更新Program实体
                    jdhProgram.setProgramName(skuInfoDTO.getSkuName());
                    jdhProgram.setBusinessModeCode(BusinessModeEnum.YK.getCode());
                    jdhProgram.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
                    jdhProgram.setProgramDesc(skuInfoDTO.getGroupDesc());
                    jdhProgram.setSaleStatus(1);
                    jdhProgram.setSaleShowMethod(ProgramSaleShowMethodTypeEnum.NORMAL.getTypeNo());
                    jdhProgram.setSaleLimitType(2);
                    jdhProgram.setSuitable(ProductProgramConvertor.ins.suitableProgramConvert(skuInfoDTO.getSkuSuitable()));
                    //jdhProgram.setProgramBizItemRelList();
                    jdhProgram.setUpdateUser(skuInfoDTO.getUpdateUser());
                    jdhProgram.setUpdateTime(skuInfoDTO.getUpdateTime());
                }
                jdhProgramRepository.save(jdhProgram);

                //维护program和sku关联关系表
                JdhProgramSku programSku = jdhProgramRepository.findProgramSku(JdhProgramSkuIdentifier.builder().skuId(body.getSkuNo()).build());
                log.info("ProductDataCleanApplicationImpl -> cleanProductSku, programSku={}", JSON.toJSONString(programSku));
                if (Objects.isNull(programSku)) {
                    //如果不存在，组装programSku实体
                    programSku = JdhProgramSku.builder()
                            .businessModeCode(BusinessModeEnum.YK.getCode())
                            .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                            .programId(jdhProgram.getProgramId())
                            .skuId(body.getSkuNo())
                            //.productId()
                            .channelType(1)
                            .channelId(ProductSaleChannelEnum.XFYL.getChannelId())
                            .saleStatus(1)
                            .remark(skuInfoDTO.getSkuDesc())
                            .build();
                } else {
                    //存在，按照查询的运营端数据更新Program实体
                    programSku.setProgramId(jdhProgram.getProgramId());
                    programSku.setRemark(skuInfoDTO.getSkuDesc());
                }
                jdhProgramRepository.saveProgramSku(programSku);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 修正pop sku信息
     * @param ossKey
     * @return
     */
    @Override
    public Boolean revisePopProductSku(String ossKey, String mappingOssKey) {
        //查询所有新指标数据
        List<JdhStandardIndicator> standardIndicatorList = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
        log.info("ProductDataCleanApplicationImpl -> revisePopProductSku 新指标数量 standardIndicatorList size={}", standardIndicatorList.size());
        Map<String, JdhStandardIndicator> code2StandardIndicatorMap = standardIndicatorList.stream().collect(Collectors.toMap(JdhStandardIndicator::getHealthIndicatorCode, standardIndicator -> standardIndicator, (t, t2) -> t2));
        log.info("ProductDataCleanApplicationImpl -> 新指标按检后ID去重后数量 code2StandardIndicatorMap size={}", code2StandardIndicatorMap.size());
        log.info("ProductDataCleanApplicationImpl -> code2StandardIndicatorMap={}", JSON.toJSONString(code2StandardIndicatorMap));
        Map<Long, JdhStandardIndicator> id2IndicatorMap = standardIndicatorList.stream().collect(Collectors.toMap(JdhStandardIndicator::getIndicatorId, standardIndicator -> standardIndicator, (t, t2) -> t2));


        //查询所有标准项目数据
        List<JdhStandardItem> standardItemList = jdhStandardItemRepository.queryList(JdhStandardItemRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
        log.info("ProductDataCleanApplicationImpl -> revisePopProductSku 所有标准项目数据 standardItemList size={}", standardItemList.size());
        Map<Long, JdhStandardItem> id2StandardItemMap = standardItemList.stream().collect(Collectors.toMap(JdhStandardItem::getItemId, standardItem -> standardItem, (t, t2) -> t2));
        //指标对应的标准项目集合
        Map<Long, Set<Long>> indicatorId2ItemIdMap = new HashMap<>();
        standardItemList.forEach(standardItem -> {
            log.info("ProductDataCleanApplicationImpl -> revisePopProductSku 指标对应的标准项目集合 standardItem={}", JSON.toJSONString(standardItem));
            List<Long> indicatorList = standardItem.getIndicatorList();
            if (CollectionUtils.isNotEmpty(indicatorList)) {
                for (Long indicatorId : indicatorList) {
                    Set<Long> list = indicatorId2ItemIdMap.get(indicatorId);
                    if (CollectionUtils.isEmpty(list)) {
                        list = new HashSet<>();
                        indicatorId2ItemIdMap.put(indicatorId, list);
                    }
                    list.add(standardItem.getItemId());
                }
            }
        });
        log.info("ProductDataCleanApplicationImpl -> 指标对应的标准项目集合 indicatorId2ItemIdMap size={}", indicatorId2ItemIdMap.size());
        log.info("ProductDataCleanApplicationImpl -> 指标对应的标准项目集合 indicatorId2ItemIdMap={}", JSON.toJSONString(indicatorId2ItemIdMap));

        //查询新老指标映射关系
        //获取文件流
        InputStream mappingInputStream = fileManageService.get(mappingOssKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        List<OldStandardIndicatorMappingBO> mappingIndicatorModelList = EasyExcelUtil.read(mappingInputStream, 1, OldStandardIndicatorMappingBO.class);
        log.info("ProductDataCleanApplicationImpl -> revisePopProductSku解析excel集合：importResult size={}", mappingIndicatorModelList.size());
        //设置成老指标ID -> 新指标编码list 格式map，方便处理映射
        Map<String, List<String>> resultMap = new HashMap<>();
        for (OldStandardIndicatorMappingBO oldStandardIndicatorMappingBO : mappingIndicatorModelList) {
            log.info("ProductDataCleanApplicationImpl -> revisePopProductSku解析excel oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
            List<String> healthIndicatorCodeList = resultMap.get(oldStandardIndicatorMappingBO.getOldIndicatorId());
            if (CollectionUtils.isEmpty(healthIndicatorCodeList)) {
                healthIndicatorCodeList = new ArrayList<>();
                resultMap.put(oldStandardIndicatorMappingBO.getOldIndicatorId(), healthIndicatorCodeList);
            }
            if (StringUtil.isNotBlank(oldStandardIndicatorMappingBO.getHealthIndicatorCode()) && oldStandardIndicatorMappingBO.getHealthIndicatorCode().contains("SC")) {
                healthIndicatorCodeList.add(oldStandardIndicatorMappingBO.getHealthIndicatorCode());
            } else {
                log.info("ProductDataCleanApplicationImpl -> revisePopProductSku解析excel 包含非法检后指标ID oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
            }
        }
        log.info("ProductDataCleanApplicationImpl -> revisePopProductSku解析excel resultMap size={}", resultMap.size());
        log.info("ProductDataCleanApplicationImpl -> revisePopProductSku解析excel resultMap={}", JSON.toJSONString(resultMap));


        //获取文件流
        InputStream inputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        try {
            List<ReviseProductExcelBO> importResult = EasyExcelUtil.read(inputStream, 1, ReviseProductExcelBO.class);
            log.info("ProductDataCleanApplicationImpl -> revisePopProductSku解析excel集合：importResult size={}",importResult.size());
            //row : 商品ID
            //col : 老项目ID
            //value ： 指标映射关系
            HashBasedTable<String, Long, List<ReviseProductExcelBO>> basedTable = HashBasedTable.create();
            for (ReviseProductExcelBO popProductExcelBO : importResult) {
                if (basedTable.contains(popProductExcelBO.getProductId(), popProductExcelBO.getOldItemId())) {
                    List<ReviseProductExcelBO> list = basedTable.get(popProductExcelBO.getProductId(), popProductExcelBO.getOldItemId());
                    list.add(popProductExcelBO);
                    continue;
                }
                List<ReviseProductExcelBO> list = Lists.newArrayList(popProductExcelBO);
                basedTable.put(popProductExcelBO.getProductId(), popProductExcelBO.getOldItemId(), list);
            }
            log.info("ProductDataCleanApplicationImpl -> revisePopProductSku处理集合：basedTable size={}", basedTable.size());
            log.info("ProductDataCleanApplicationImpl -> revisePopProductSku处理集合：stringMapMap={}", JSON.toJSONString(basedTable.rowMap()));
            log.info("ProductDataCleanApplicationImpl -> revisePopProductSku处理集合：stringMapMap size={}", JSON.toJSONString(basedTable.rowMap().size()));

            //查询所有业务项目数据（自营体检、pop体检）
            List<ServiceItem> serviceItems = serviceItemRepository.queryServiceItemList(JdhItemListQueryContext.builder().itemIdList(basedTable.columnKeySet()).build());
            log.info("ProductDataCleanApplicationImpl -> revisePopProductSku 所有体检业务项目数据 size={}", serviceItems.size());
            Map<Long, ServiceItem> id2ServiceItem = serviceItems.stream().collect(Collectors.toMap(ServiceItem::getItemId, serviceItem -> serviceItem, (t, t2) -> t2));

            //查询业务项目关联的指标数据
            List<ServiceItemIndicatorRel> serviceItemIndicatorRelList = serviceItemIndicatorRepository.queryItemIndicatorList(ServiceItemQueryContext.builder().itemIds(id2ServiceItem.keySet()).build());
            Map<Long, List<Long>> itemId2IndicatorMap = serviceItemIndicatorRelList.stream().collect(Collectors.groupingBy(ServiceItemIndicatorRel::getItemId, Collectors.mapping(ServiceItemIndicatorRel::getIndicatorId, Collectors.toList())));
            log.info("ProductDataCleanApplicationImpl -> revisePopProductSku 所有关联了指标的体检业务项目数据 itemId2IndicatorMap size={}", itemId2IndicatorMap.size());

            Map<Long, List<String>> suitablePopMap = new HashMap<Long, List<String>>(){{
                put(661004250626L, Lists.newArrayList(SuitableEnum.MAN.getCode()));//一级分类为男性专项，适用人群处理为男
                put(124202627586L, Lists.newArrayList(SuitableEnum.UNMARRY_WOMEN.getCode()));//一级分类为女性专项(不含妇科)，适用人群处理为女未婚
                put(565856855042L, Lists.newArrayList(SuitableEnum.MARRIED_WOMEN.getCode()));//一级分类为女性专项(含妇科)，适用人群处理为女已婚
                put(629554156546L, Lists.newArrayList(SuitableEnum.UNMARRY_WOMEN.getCode(), SuitableEnum.MARRIED_WOMEN.getCode()));//一级分类为女性专项，适用人群处理为女未婚、女已婚
            }};//其余一级分类，适用人群处理为全部人群适用

            //1.依次查询商品主数据，拿取商品配置的结构化项目数据，存入数据库
            itemLoop : for (Map.Entry<String, Map<Long, List<ReviseProductExcelBO>>> cell : basedTable.rowMap().entrySet()) {
                log.info("ProductDataCleanApplicationImpl ->  revisePopProductSku 处理cell={}", JSON.toJSONString(cell));
                String productId = cell.getKey();
                List<JdhProgram> programList = jdhProgramRepository.queryProgramListBySkuOrProductId(JdhProgramSkuQuery.builder().productId(productId).build());
                if (CollectionUtils.isEmpty(programList)) {
                    log.warn("ProductDataCleanApplicationImpl -> revisePopProductSku处理集合：program is null, productId={}", productId);
                    continue;
                }
                JdhProgram program = programList.get(0);
                Map<Long, List<JdhBizItem>> saveServiceItemMap = new HashMap<>();
                //遍历处理老项目数据
                Map<Long, List<ReviseProductExcelBO>> value = cell.getValue();
                for (Map.Entry<Long, List<ReviseProductExcelBO>> entry : value.entrySet()) {
                    //1.查询老项目
                    ServiceItem serviceItem = id2ServiceItem.get(entry.getKey());
                    if (Objects.isNull(serviceItem)) {
                        log.error("ProductDataCleanApplicationImpl -> 未找到老业务项目 itemId={}",entry.getKey());
                        continue;
                    }
                    //2.查老项目对应的老指标ID列表
                    List<Long> oldIndicatorIdList = itemId2IndicatorMap.get(serviceItem.getItemId());

                    //修正的指标映射
                    List<ReviseProductExcelBO> oldIndicatorMappings = basedTable.get(productId, serviceItem.getItemId());
                    oldIndicatorMappings = CollectionUtils.isEmpty(oldIndicatorMappings) ? Lists.newArrayList() : oldIndicatorMappings;
                    Map<Long, List<String>> reviseResultMap = oldIndicatorMappings.stream().collect(Collectors.groupingBy(ReviseProductExcelBO::getOldIndicatorId, Collectors.mapping(ReviseProductExcelBO::getHealthIndicatorCode, Collectors.toList())));

                    //2.1 根据老指标ID映射新指标
                    Set<Long> mappingStandardIndicatorList = new HashSet<>();
                    for (Long oldIndicatorId : oldIndicatorIdList) {
                        if (reviseResultMap.containsKey(oldIndicatorId)) {
                            Set<Long> list = reviseResultMap.get(oldIndicatorId).stream().map(code2StandardIndicatorMap::get).filter(Objects::nonNull).map(JdhStandardIndicator::getIndicatorId).collect(Collectors.toSet());
                            if (list.size() != reviseResultMap.get(oldIndicatorId).size()) {
                                log.error("ProductDataCleanApplicationImpl -> 老指标和新指标的映射关系与数据库存储的新指标不符oldItemId={}, oldIndicatorId={}, mappingStandardIndicatorIdList={}, list={}", serviceItem.getItemId(), oldIndicatorId, JsonUtil.toJSONString(resultMap.get(String.valueOf(oldIndicatorId))), JsonUtil.toJSONString(list));
                            }
                            mappingStandardIndicatorList.addAll(list);
                        } else {
                            if (!resultMap.containsKey(String.valueOf(oldIndicatorId))) {
                                log.error("ProductDataCleanApplicationImpl -> 老指标没有和新指标的映射关系oldItemId={}, oldIndicatorId={}", serviceItem.getItemId(), oldIndicatorId);
                                continue;
                            }
                            Set<Long> list = resultMap.get(String.valueOf(oldIndicatorId)).stream().map(code2StandardIndicatorMap::get).filter(Objects::nonNull).map(JdhStandardIndicator::getIndicatorId).collect(Collectors.toSet());
                            if (list.size() != resultMap.get(String.valueOf(oldIndicatorId)).size()) {
                                log.error("ProductDataCleanApplicationImpl -> 老指标和新指标的映射关系与数据库存储的新指标不符oldItemId={}, oldIndicatorId={}, mappingStandardIndicatorIdList={}, list={}", serviceItem.getItemId(), oldIndicatorId, JsonUtil.toJSONString(resultMap.get(String.valueOf(oldIndicatorId))), JsonUtil.toJSONString(list));
                            }
                            mappingStandardIndicatorList.addAll(list);
                        }
                    }
                    //3.清洗老项目为新项目
                    //2.1 POP业务项目，需要限制匹配的标准项目，应用场景都是to C的
                    Map<Long, Set<Long>> standardItemId2IndicatorMap = dispatchStandardItemList(mappingStandardIndicatorList, indicatorId2ItemIdMap, id2StandardItemMap, StandardItemApplySceneEnum.TO_C.getScene());
                    if (standardItemId2IndicatorMap.isEmpty()) {
                        log.warn("ProductDataCleanApplicationImpl -> 老项目映射标准项目报错 serviceItem={}", JsonUtil.toJSONString(serviceItem));
                        continue itemLoop;
                    }
                    //处理venderId和venderName
                    String venderId = null;
                    String venderName = "";
                    if (Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType())) {
                        Provider provider = providerRepository.findByVender(serviceItem.getChannelNo());
                        venderId = Objects.nonNull(provider) && Objects.nonNull(provider.getVenderId())? provider.getVenderId().toString() : null;
                        venderName = Objects.nonNull(provider)  && StringUtils.isNotBlank(provider.getVenderName()) ? provider.getVenderName() : null;
                    }
                    List<String> suitable = Lists.newArrayList(SuitableEnum.MAN.getCode(),SuitableEnum.UNMARRY_WOMEN.getCode(),SuitableEnum.MARRIED_WOMEN.getCode());
                    if (Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType())) {
                        //pop的项目适用人群，按照业务项目的一级分类做处理
                        //男性专项(661004250626)→男
                        //女性专项(124202627586)（不含妇科）→女未婚
                        //女性专项(565856855042)（含妇科）→女已婚
                        //女性专项(629554156546)→女已婚、女未婚
                        //其他→男、女已婚、女未婚
                        if (Objects.isNull(serviceItem.getFirstIndicatorCategory())) {
                            log.error("ProductDataCleanApplicationImpl -> pop业务项目没有指标一级分类 serviceItem={}", JSON.toJSONString(serviceItem));
                            continue itemLoop;
                        };
                        if (suitablePopMap.containsKey(serviceItem.getFirstIndicatorCategory())) {
                            suitable = suitablePopMap.get(serviceItem.getFirstIndicatorCategory());
                        }
                    } else {
                        //自营一卡万店（两种情况）
                        //情况1：项目已存适用人群
                        if (StringUtils.isNotBlank(serviceItem.getItemSuitable())) {
                            suitable = Arrays.stream(serviceItem.getItemSuitable().split(""))
                                    .sorted()
                                    .collect(Collectors.toList());
                        }
                        //情况2：项目未存适用人群，默认全部
                    }
                    Integer applyScene = null;
                    //pop的项目应用场景均为to c
                    if (Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType())) {
                        applyScene = Integer.valueOf(StandardItemApplySceneEnum.TO_C.getScene());
                    }
                    List<JdhBizItem> tempItemList = Lists.newArrayList();
                    for(Map.Entry<Long, Set<Long>> standardItemEntry :  standardItemId2IndicatorMap.entrySet()){
                        JdhStandardItem jdhStandardItem = id2StandardItemMap.get(standardItemEntry.getKey());
                        Integer tempApplyScene = applyScene;
                        if (Objects.isNull(applyScene)) {
                            //一卡万店的应用场景，默认取标准项目的应用场景，如果标准项目没有，则默认是to B应用场景
                            tempApplyScene = CollectionUtils.isNotEmpty(jdhStandardItem.getApplyScene()) ? Integer.valueOf(jdhStandardItem.getApplyScene().get(0)) : Integer.valueOf(StandardItemApplySceneEnum.TO_B.getScene());
                        }
                        JdhBizItem build = JdhBizItem.builder()
                                //.id()
                                .venderId(venderId)
                                .venderName(venderName)
                                .businessModeCode(Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType()) ? BusinessModeEnum.POP_LOC.getCode() : BusinessModeEnum.YK.getCode())
                                .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                                .applyScene(tempApplyScene)
                                .firstBizCategory(jdhStandardItem.getFirstBizCategory())
                                .secondBizCategory(jdhStandardItem.getSecondBizCategory())
                                .thirdBizCategory(jdhStandardItem.getThirdBizCategory())
                                //.bizItemId(SpringUtil.getBean(GenerateIdFactory.class).getId())
                                .bizItemName(jdhStandardItem.getItemName())
                                .standardItemId(standardItemEntry.getKey())
                                .indicatorList(Lists.newArrayList(standardItemEntry.getValue()))
                                .price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().divide(new BigDecimal("100"), 2, RoundingMode.DOWN) : null)
                                //.price(standardItemId2IndicatorMap.entrySet().size() == 1 ? serviceItem.getItemPrice() : null)
                                .mean(serviceItem.getItemMean())
                                .suitable(suitable)
                                //.gender()
                                .extJson(JdhBizItemExtJsonBO.builder().reviseOldItemId(serviceItem.getItemId()).build())//记录老业务项目的ID，方便后续映射排查
                                .remark(serviceItem.getRemark())
                                .build();

                        tempItemList.add(build);
                    }
                    log.info("ProductDataCleanApplicationImpl -> 老项目数据 serviceItem={}，映射的新项目数据tempItemList={}", JSON.toJSONString(serviceItem), JSON.toJSONString(tempItemList));
                    saveServiceItemMap.put(serviceItem.getItemId(), tempItemList);

                }
                Map<String, Set<Long>> stringSetMap = SCENE_INDICATOR_THREAD_LOCAL.get();
                log.info("ProductDataCleanApplicationImpl -> 未在相应场景的标准项目映射的指标数据 size map={}", Objects.nonNull(stringSetMap) ? stringSetMap.size() : 0);
                log.info("ProductDataCleanApplicationImpl -> 未在相应场景的标准项目映射的指标数据 map={}", JSON.toJSONString(stringSetMap));
                log.info("ProductDataCleanApplicationImpl -> 保存清洗的项目数据 saveServiceItemMap size={}", saveServiceItemMap.size());
                log.info("ProductDataCleanApplicationImpl -> 保存清洗的项目数据 saveServiceItemMap={}", JSON.toJSONString(saveServiceItemMap));

                for (Map.Entry<Long, List<JdhBizItem>> entry : saveServiceItemMap.entrySet()) {
                    //需要保存的业务项目列表
                    List<JdhBizItem> jdhBizItems = entry.getValue();
                    //新增新数据
                    jdhBizItems.forEach(jdhBizItem -> jdhBusinessItemRepository.save(jdhBizItem));
                }

                //查询商品program关联的业务项目，将原老项目对应的业务项目替换为上步骤新生成的业务项目
                List<JdhProgramBizItemRel> jdhProgramBizItemRels = jdhProgramRepository.queryJdhProgramItemRelList(JdhProgramBizItemRelQuery.builder().programId(program.getProgramId()).build());

                // 查询数据库已存在的新业务项目列表
                List<Long> bizItemIdList = jdhProgramBizItemRels.stream().map(JdhProgramBizItemRel::getBizItemId).collect(Collectors.toList());
                List<JdhBizItem> existBizItemList = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().bizItemIdList(bizItemIdList).needIndicatorIdList(true).build());
                log.info("ProductDataCleanApplicationImpl -> 查询数据库已存在的新业务项目列表 existBizItemList size={}", existBizItemList.size());
                Map<Long, JdhBizItem> id2bizItem = existBizItemList.stream().collect(Collectors.toMap(JdhBizItem::getBizItemId, jdhBizItem -> jdhBizItem, (t, t2) -> t2));

                Map<Long, Integer> oldItemImportantMap = new HashMap<>();
                Iterator<JdhProgramBizItemRel> iterator = jdhProgramBizItemRels.iterator();
                //删除过时的项目关系
                while (iterator.hasNext()){
                    JdhProgramBizItemRel rel = iterator.next();
                    JdhBizItem jdhBizItem = id2bizItem.get(rel.getBizItemId());
                    Long oldItemId = Objects.nonNull(jdhBizItem.getExtJson()) && Objects.nonNull(jdhBizItem.getExtJson().getOldItemId()) ? jdhBizItem.getExtJson().getOldItemId() : null;
                    if (Objects.isNull(oldItemId)) {
                        oldItemId = Objects.nonNull(jdhBizItem.getExtJson()) && Objects.nonNull(jdhBizItem.getExtJson().getReviseOldItemId()) ? jdhBizItem.getExtJson().getReviseOldItemId() : null;
                    }
                    if(saveServiceItemMap.containsKey(oldItemId)){
                        if (!oldItemImportantMap.containsKey(oldItemId)) {
                            oldItemImportantMap.put(oldItemId, rel.getIsImportant());
                        }
                        iterator.remove();
                    }
                }
                //新增项目关系
                List<JdhProgramBizItemRel> bizItemRels = saveServiceItemMap.values().stream().flatMap(Collection::stream).map(jdhBizItem -> {
                    JdhProgramBizItemRel rel = new JdhProgramBizItemRel();
                    rel.setBizItemId(jdhBizItem.getBizItemId());
                    rel.setProgramId(program.getProgramId());
                    rel.setIsImportant(oldItemImportantMap.getOrDefault(jdhBizItem.getExtJson().getReviseOldItemId(), 0));
                    return rel;
                }).collect(Collectors.toList());
                jdhProgramBizItemRels.addAll(bizItemRels);
                jdhProgramRepository.batchSaveProgramItemRel(jdhProgramBizItemRels);

                ReviseProductExcelBO reviseProductExcelBO = cell.getValue().values().iterator().next().get(0);

                //新业务项目集合
                List<SaveBizItemCmd> bizItemCmdList = new ArrayList<>();

                saveServiceItemMap.values().stream().flatMap(Collection::stream).forEach(jdhBizItem -> {
                    id2bizItem.put(jdhBizItem.getBizItemId(), jdhBizItem);
                });
               //组装返回值（商品主数据格式）
                for (JdhProgramBizItemRel rel : jdhProgramBizItemRels) {
                    JdhBizItem jdhBizItem = id2bizItem.get(rel.getBizItemId());
                    //设置每个字段的值
                    SaveBizItemCmd bizItemCmd = ProductBizItemConvertor.INSTANCE.convertToSaveBizItemCmd(jdhBizItem, new JSONObject(), id2IndicatorMap);
                    bizItemCmd.setImportantItem(Objects.equals(rel.getIsImportant(), 1));
                    bizItemCmdList.add(bizItemCmd);
                }
                log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku, 执行商品主数据项目更新 bizItemCmdList={}", JSON.toJSONString(bizItemCmdList));
                SaveProductBizItemCmd productBizItemCmd = new SaveProductBizItemCmd();
                productBizItemCmd.setProductId(Long.valueOf(productId));
                productBizItemCmd.setBizItemCmdList(bizItemCmdList);
                productBizItemCmd.setVenderId(reviseProductExcelBO.getVenderId());
                if (duccConfig.getProductItemUpdateSwitch()) {
                    log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku, 执行商品主数据项目更新 productId={}", productId);
                    productServiceItemApplication.publishMerchantProductItem(productBizItemCmd);
                }
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> cleanPopProductSku信息异常", e);
            return Boolean.FALSE;
        }
    }

    /**
     * 修正自营 项目数据
     * @param ossKey
     * @param mappingOssKey
     * @return
     */
    @Override
    public Boolean reviseSelfProductSku(String ossKey, String mappingOssKey) {
        //查询所有新指标数据
        List<JdhStandardIndicator> standardIndicatorList = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
        log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku 新指标数量 standardIndicatorList size={}", standardIndicatorList.size());
        Map<String, JdhStandardIndicator> code2StandardIndicatorMap = standardIndicatorList.stream().collect(Collectors.toMap(JdhStandardIndicator::getHealthIndicatorCode, standardIndicator -> standardIndicator, (t, t2) -> t2));
        log.info("ProductDataCleanApplicationImpl -> 新指标按检后ID去重后数量 code2StandardIndicatorMap size={}", code2StandardIndicatorMap.size());
        log.info("ProductDataCleanApplicationImpl -> code2StandardIndicatorMap={}", JSON.toJSONString(code2StandardIndicatorMap));

        //查询所有标准项目数据
        List<JdhStandardItem> standardItemList = jdhStandardItemRepository.queryList(JdhStandardItemRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
        log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku 所有标准项目数据 standardItemList size={}", standardItemList.size());
        Map<Long, JdhStandardItem> id2StandardItemMap = standardItemList.stream().collect(Collectors.toMap(JdhStandardItem::getItemId, standardItem -> standardItem, (t, t2) -> t2));
        //指标对应的标准项目集合
        Map<Long, Set<Long>> indicatorId2ItemIdMap = new HashMap<>();
        standardItemList.forEach(standardItem -> {
            log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku 指标对应的标准项目集合 standardItem={}", JSON.toJSONString(standardItem));
            List<Long> indicatorList = standardItem.getIndicatorList();
            if (CollectionUtils.isNotEmpty(indicatorList)) {
                for (Long indicatorId : indicatorList) {
                    Set<Long> list = indicatorId2ItemIdMap.get(indicatorId);
                    if (CollectionUtils.isEmpty(list)) {
                        list = new HashSet<>();
                        indicatorId2ItemIdMap.put(indicatorId, list);
                    }
                    list.add(standardItem.getItemId());
                }
            }
        });
        log.info("ProductDataCleanApplicationImpl -> 指标对应的标准项目集合 indicatorId2ItemIdMap size={}", indicatorId2ItemIdMap.size());
        log.info("ProductDataCleanApplicationImpl -> 指标对应的标准项目集合 indicatorId2ItemIdMap={}", JSON.toJSONString(indicatorId2ItemIdMap));

        //查询新老指标映射关系
        //获取文件流
        InputStream mappingInputStream = fileManageService.get(mappingOssKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        List<OldStandardIndicatorMappingBO> mappingIndicatorModelList = EasyExcelUtil.read(mappingInputStream, 1, OldStandardIndicatorMappingBO.class);
        log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku解析excel集合：importResult size={}", mappingIndicatorModelList.size());
        //设置成老指标ID -> 新指标编码list 格式map，方便处理映射
        Map<String, List<String>> resultMap = new HashMap<>();
        for (OldStandardIndicatorMappingBO oldStandardIndicatorMappingBO : mappingIndicatorModelList) {
            log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku解析excel oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
            List<String> healthIndicatorCodeList = resultMap.get(oldStandardIndicatorMappingBO.getOldIndicatorId());
            if (CollectionUtils.isEmpty(healthIndicatorCodeList)) {
                healthIndicatorCodeList = new ArrayList<>();
                resultMap.put(oldStandardIndicatorMappingBO.getOldIndicatorId(), healthIndicatorCodeList);
            }
            if (StringUtil.isNotBlank(oldStandardIndicatorMappingBO.getHealthIndicatorCode()) && oldStandardIndicatorMappingBO.getHealthIndicatorCode().contains("SC")) {
                healthIndicatorCodeList.add(oldStandardIndicatorMappingBO.getHealthIndicatorCode());
            } else {
                log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku解析excel 包含非法检后指标ID oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
            }
        }
        log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku解析excel resultMap size={}", resultMap.size());
        log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku解析excel resultMap={}", JSON.toJSONString(resultMap));

        //获取文件流
        InputStream inputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        try {
            List<ReviseProductExcelBO> importResult = EasyExcelUtil.read(inputStream, 1, ReviseProductExcelBO.class);
            log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku解析excel集合：importResult size={}",importResult.size());
            //row : 老项目ID
            //col : 老指标ID
            //value ： 新指标SC码
            HashBasedTable<Long, Long, List<String>> basedTable = HashBasedTable.create();
            for (ReviseProductExcelBO popProductExcelBO : importResult) {
                if (basedTable.contains(popProductExcelBO.getOldItemId(), popProductExcelBO.getOldIndicatorId())) {
                    List<String> list = basedTable.get(popProductExcelBO.getOldItemId(), popProductExcelBO.getOldIndicatorId());
                    list.add(popProductExcelBO.getHealthIndicatorCode());
                    continue;
                }
                List<String> list = Lists.newArrayList(popProductExcelBO.getHealthIndicatorCode());
                basedTable.put(popProductExcelBO.getOldItemId(), popProductExcelBO.getOldIndicatorId(), list);
            }
            log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku处理集合：basedTable size={}", basedTable.size());
            log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku处理集合：stringMapMap={}", JSON.toJSONString(basedTable.rowMap()));
            log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku处理集合：stringMapMap size={}", JSON.toJSONString(basedTable.rowMap().size()));

            //查询所有业务项目数据（自营体检、pop体检）
            List<ServiceItem> serviceItems = serviceItemRepository.queryServiceItemList(JdhItemListQueryContext.builder().itemIdList(basedTable.rowKeySet()).build());
            log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku 所有体检业务项目数据 size={}", serviceItems.size());
            Map<Long, ServiceItem> id2ServiceItem = serviceItems.stream().collect(Collectors.toMap(ServiceItem::getItemId, serviceItem -> serviceItem, (t, t2) -> t2));

            //查询业务项目关联的指标数据
            List<ServiceItemIndicatorRel> serviceItemIndicatorRelList = serviceItemIndicatorRepository.queryItemIndicatorList(ServiceItemQueryContext.builder().itemIds(id2ServiceItem.keySet()).build());
            Map<Long, List<Long>> itemId2IndicatorMap = serviceItemIndicatorRelList.stream().collect(Collectors.groupingBy(ServiceItemIndicatorRel::getItemId, Collectors.mapping(ServiceItemIndicatorRel::getIndicatorId, Collectors.toList())));
            log.info("ProductDataCleanApplicationImpl -> reviseSelfProductSku 所有关联了指标的体检业务项目数据 itemId2IndicatorMap size={}", itemId2IndicatorMap.size());


            //1.依次查询商品主数据，拿取商品配置的结构化项目数据，存入数据库
            for (Map.Entry<Long, Map<Long, List<String>>> cell : basedTable.rowMap().entrySet()) {
                log.info("ProductDataCleanApplicationImpl ->  reviseSelfProductSku 处理cell={}", JSON.toJSONString(cell));
                Long itemId = cell.getKey();
                //1.查询老项目
                ServiceItem serviceItem = id2ServiceItem.get(itemId);
                if (Objects.isNull(serviceItem)) {
                    log.error("ProductDataCleanApplicationImpl -> 未找到老业务项目 itemId={}",itemId);
                    continue;
                }
                //2.查老项目对应的老指标ID列表
                List<Long> oldIndicatorIdList = itemId2IndicatorMap.get(serviceItem.getItemId());

                //修正的指标映射
                Map<Long, List<String>> reviseResultMap = cell.getValue();
                reviseResultMap = Objects.nonNull(reviseResultMap) ? reviseResultMap : new HashMap<>();

                //2.1 根据老指标ID映射新指标
                Set<Long> mappingStandardIndicatorList = new HashSet<>();
                for (Long oldIndicatorId : oldIndicatorIdList) {
                    if (reviseResultMap.containsKey(oldIndicatorId)) {
                        Set<Long> list = reviseResultMap.get(oldIndicatorId).stream().map(code2StandardIndicatorMap::get).filter(Objects::nonNull).map(JdhStandardIndicator::getIndicatorId).collect(Collectors.toSet());
                        if (list.size() != reviseResultMap.get(oldIndicatorId).size()) {
                            log.error("ProductDataCleanApplicationImpl -> 老指标和新指标的映射关系与数据库存储的新指标不符oldItemId={}, oldIndicatorId={}, mappingStandardIndicatorIdList={}, list={}", serviceItem.getItemId(), oldIndicatorId, JsonUtil.toJSONString(resultMap.get(String.valueOf(oldIndicatorId))), JsonUtil.toJSONString(list));
                        }
                        mappingStandardIndicatorList.addAll(list);
                    } else {
                        if (!resultMap.containsKey(String.valueOf(oldIndicatorId))) {
                            log.error("ProductDataCleanApplicationImpl -> 老指标没有和新指标的映射关系oldItemId={}, oldIndicatorId={}", serviceItem.getItemId(), oldIndicatorId);
                            continue;
                        }
                        Set<Long> list = resultMap.get(String.valueOf(oldIndicatorId)).stream().map(code2StandardIndicatorMap::get).filter(Objects::nonNull).map(JdhStandardIndicator::getIndicatorId).collect(Collectors.toSet());
                        if (list.size() != resultMap.get(String.valueOf(oldIndicatorId)).size()) {
                            log.error("ProductDataCleanApplicationImpl -> 老指标和新指标的映射关系与数据库存储的新指标不符oldItemId={}, oldIndicatorId={}, mappingStandardIndicatorIdList={}, list={}", serviceItem.getItemId(), oldIndicatorId, JsonUtil.toJSONString(resultMap.get(String.valueOf(oldIndicatorId))), JsonUtil.toJSONString(list));
                        }
                        mappingStandardIndicatorList.addAll(list);
                    }
                }
                //3.清洗老项目为新项目
                //2.1 POP业务项目，需要限制匹配的标准项目，应用场景都是to C的
                Map<Long, Set<Long>> standardItemId2IndicatorMap = dispatchStandardItemList(mappingStandardIndicatorList, indicatorId2ItemIdMap, id2StandardItemMap, StandardItemApplySceneEnum.TO_C.getScene());
                if (standardItemId2IndicatorMap.isEmpty()) {
                    log.warn("ProductDataCleanApplicationImpl -> 老项目映射标准项目报错 serviceItem={}", JsonUtil.toJSONString(serviceItem));
                    continue;
                }
                //处理venderId和venderName
                String venderId = null;
                String venderName = "";
                if (Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType())) {
                    Provider provider = providerRepository.findByVender(serviceItem.getChannelNo());
                    venderId = Objects.nonNull(provider) && Objects.nonNull(provider.getVenderId())? provider.getVenderId().toString() : null;
                    venderName = Objects.nonNull(provider)  && StringUtils.isNotBlank(provider.getVenderName()) ? provider.getVenderName() : null;
                }
                List<String> suitable = Lists.newArrayList(SuitableEnum.MAN.getCode(),SuitableEnum.UNMARRY_WOMEN.getCode(),SuitableEnum.MARRIED_WOMEN.getCode());
                //自营一卡万店（两种情况）
                //情况1：项目已存适用人群;//情况2：项目未存适用人群，默认全部
                if (StringUtils.isNotBlank(serviceItem.getItemSuitable())) {
                    suitable = Arrays.stream(serviceItem.getItemSuitable().split(""))
                            .sorted()
                            .collect(Collectors.toList());
                }

                Integer applyScene = null;
                //pop的项目应用场景均为to c
                if (Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType())) {
                    applyScene = Integer.valueOf(StandardItemApplySceneEnum.TO_C.getScene());
                }
                List<JdhBizItem> tempItemList = Lists.newArrayList();
                for(Map.Entry<Long, Set<Long>> standardItemEntry :  standardItemId2IndicatorMap.entrySet()){
                    JdhStandardItem jdhStandardItem = id2StandardItemMap.get(standardItemEntry.getKey());
                    Integer tempApplyScene = applyScene;
                    if (Objects.isNull(applyScene)) {
                        //一卡万店的应用场景，默认取标准项目的应用场景，如果标准项目没有，则默认是to B应用场景
                        tempApplyScene = CollectionUtils.isNotEmpty(jdhStandardItem.getApplyScene()) ? Integer.valueOf(jdhStandardItem.getApplyScene().get(0)) : Integer.valueOf(StandardItemApplySceneEnum.TO_B.getScene());
                    }
                    JdhBizItem build = JdhBizItem.builder()
                            //.id()
                            .venderId(venderId)
                            .venderName(venderName)
                            .businessModeCode(Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType()) ? BusinessModeEnum.POP_LOC.getCode() : BusinessModeEnum.YK.getCode())
                            .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                            .applyScene(tempApplyScene)
                            .firstBizCategory(jdhStandardItem.getFirstBizCategory())
                            .secondBizCategory(jdhStandardItem.getSecondBizCategory())
                            .thirdBizCategory(jdhStandardItem.getThirdBizCategory())
                            //.bizItemId(SpringUtil.getBean(GenerateIdFactory.class).getId())
                            .bizItemName(jdhStandardItem.getItemName())
                            .standardItemId(standardItemEntry.getKey())
                            .indicatorList(Lists.newArrayList(standardItemEntry.getValue()))
                            .price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().divide(new BigDecimal("100"), 2, RoundingMode.DOWN) : null)
                            //.price(standardItemId2IndicatorMap.entrySet().size() == 1 ? serviceItem.getItemPrice() : null)
                            .mean(serviceItem.getItemMean())
                            .suitable(suitable)
                            //.gender()
                            .extJson(JdhBizItemExtJsonBO.builder().oldItemId(serviceItem.getItemId()).build())//记录老业务项目的ID，方便后续映射排查
                            .remark(serviceItem.getRemark())
                            .build();

                    tempItemList.add(build);
                }
                log.info("ProductDataCleanApplicationImpl -> 老项目数据 serviceItem={}，映射的新项目数据tempItemList={}", JSON.toJSONString(serviceItem), JSON.toJSONString(tempItemList));

                Map<String, Set<Long>> stringSetMap = SCENE_INDICATOR_THREAD_LOCAL.get();
                log.info("ProductDataCleanApplicationImpl -> 未在相应场景的标准项目映射的指标数据 size map={}", Objects.nonNull(stringSetMap) ? stringSetMap.size() : 0);
                log.info("ProductDataCleanApplicationImpl -> 未在相应场景的标准项目映射的指标数据 map={}", JSON.toJSONString(stringSetMap));

                // 查询数据库已存在的新业务项目列表
                List<JdhBizItem> existBizItemList = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().oldItemIdList(Sets.newHashSet(itemId)).needIndicatorIdList(true).build());
                log.info("ProductDataCleanApplicationImpl -> 查询数据库已存在的新业务项目列表 existBizItemList size={}", existBizItemList.size());
                Map<Long, JdhBizItem> id2OldBizItem = existBizItemList.stream().collect(Collectors.toMap(JdhBizItem::getBizItemId, jdhBizItem -> jdhBizItem, (t, t2) -> t2));

                //需要保存的业务项目列表
                Map<Long, JdhBizItem> standardIdMap = existBizItemList.stream().collect(Collectors.toMap(JdhBizItem::getStandardItemId, oldItem -> oldItem, (t, t2) -> t2));
                for (JdhBizItem item : tempItemList) {
                    JdhBizItem oldJdhBizItem = standardIdMap.get(item.getStandardItemId());
                    if (Objects.nonNull(oldJdhBizItem)) {
                        item.setBizItemId(oldJdhBizItem.getBizItemId());
                        standardIdMap.remove(oldJdhBizItem.getStandardItemId());
                    }
                }
                if (!standardIdMap.isEmpty()) {
                    standardIdMap.forEach((key, value) -> jdhBusinessItemRepository.deleteBizItemById(value.getBizItemId(), null));
                }

                //新增新数据
                tempItemList.forEach(jdhBizItem -> jdhBusinessItemRepository.save(jdhBizItem));

                //查老项目和京东服务的关联关系
                List<JdhServiceItemRel> jdhServiceItemRelList = serviceItemRelRepository.queryJdhServiceItemRelList(JdhServiceItemRelContext.builder().itemIds(Sets.newHashSet(itemId)).build());
                Map<Long, Integer> programId2Important = jdhServiceItemRelList.stream().collect(Collectors.toMap(JdhServiceItemRel::getServiceId, JdhServiceItemRel::getImportant));

                //查询商品program关联的业务项目，将原老项目对应的业务项目替换为上步骤新生成的业务项目
                List<JdhProgramBizItemRelQuery> jdhProgramBizItemRelQuery = programId2Important.keySet().stream().map(programId -> JdhProgramBizItemRelQuery.builder().programId(programId).build()).collect(Collectors.toList());
                List<JdhProgramBizItemRel> jdhProgramBizItemRels = jdhProgramRepository.queryJdhProgramItemRelList(jdhProgramBizItemRelQuery);

                Map<Long, List<JdhProgramBizItemRel>> program2BizRels = jdhProgramBizItemRels.stream().collect(Collectors.groupingBy(JdhProgramBizItemRel::getProgramId));
                //
                for (Map.Entry<Long, List<JdhProgramBizItemRel>> entry : program2BizRels.entrySet()) {
                    //过滤删掉旧的bizItem
                    List<JdhProgramBizItemRel> value = entry.getValue();
                    //删除过时的项目关系
                    value.removeIf(rel -> id2OldBizItem.containsKey(rel.getBizItemId()));
                    //新增项目关系
                    List<JdhProgramBizItemRel> bizItemRels = tempItemList.stream().map(jdhBizItem -> {
                        JdhProgramBizItemRel rel = new JdhProgramBizItemRel();
                        rel.setBizItemId(jdhBizItem.getBizItemId());
                        rel.setProgramId(entry.getKey());
                        rel.setIsImportant(programId2Important.getOrDefault(jdhBizItem.getExtJson().getOldItemId(), 0));
                        return rel;
                    }).collect(Collectors.toList());
                    value.addAll(bizItemRels);
                    jdhProgramRepository.batchSaveProgramItemRel(value);
                }
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> reviseSelfProductSku信息异常", e);
            return Boolean.FALSE;
        }
    }

    /**
     * 导入老项目列表
     * @param ossKey
     * @param standardItemId
     * @return
     */
    @Override
    public Boolean importOldItemList(String ossKey, Long standardItemId, Set<Long> serviceIdList) {
        //查询所有标准项目数据
        List<JdhStandardItem> standardItemList = jdhStandardItemRepository.queryList(JdhStandardItemRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
        log.info("ProductDataCleanApplicationImpl -> importOldItemList 所有标准项目数据 standardItemList size={}", standardItemList.size());
        Map<Long, JdhStandardItem> id2StandardItemMap = standardItemList.stream().collect(Collectors.toMap(JdhStandardItem::getItemId, standardItem -> standardItem, (t, t2) -> t2));

        //查询新老指标映射关系
        //获取文件流
        InputStream mappingInputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        List<OldBaseItemImportBO> list = EasyExcelUtil.read(mappingInputStream, 1, OldBaseItemImportBO.class);

        // 查询数据库已存在的新业务项目列表
        Set<Long> oldItemSets = list.stream().map(OldBaseItemImportBO::getItemId).collect(Collectors.toSet());
        List<JdhBizItem> existItemList = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().oldItemIdList(oldItemSets).build());
        log.info("ProductDataCleanApplicationImpl -> importOldItemList 查询数据库已存在的新业务项目列表 existBizItemList size={}", existItemList.size());
        Map<Long, List<JdhBizItem>> oldItemId2BizItemMap = existItemList.stream().filter(jdhBizItem -> Objects.nonNull(jdhBizItem.getExtJson()) && Objects.nonNull(jdhBizItem.getExtJson().getOldItemId())).collect(Collectors.groupingBy(jdhBizItem -> jdhBizItem.getExtJson().getOldItemId()));
        log.info("ProductDataCleanApplicationImpl -> importOldItemList oldItemId2BizItemMap size={}", oldItemId2BizItemMap.size());
        log.info("ProductDataCleanApplicationImpl -> importOldItemList oldItemId2BizItemMap={}", JSON.toJSONString(oldItemId2BizItemMap));
        //遍历老项目创建新项目
        for (OldBaseItemImportBO oldBaseItemImportBO : list) {
            JdhStandardItem jdhStandardItem = id2StandardItemMap.get(standardItemId);
            Integer tempApplyScene = CollectionUtils.isNotEmpty(jdhStandardItem.getApplyScene()) ? Integer.valueOf(jdhStandardItem.getApplyScene().get(0)) : Integer.valueOf(StandardItemApplySceneEnum.TO_B.getScene());
            List<String> suitable = Lists.newArrayList(SuitableEnum.MAN.getCode(),SuitableEnum.UNMARRY_WOMEN.getCode(),SuitableEnum.MARRIED_WOMEN.getCode());
            //自营一卡万店（两种情况）
            //情况1：项目已存适用人群;//情况2：项目未存适用人群，默认全部
            if (StringUtils.isNotBlank(oldBaseItemImportBO.getItemSuitable())) {
                suitable = Arrays.stream(oldBaseItemImportBO.getItemSuitable().split(""))
                        .sorted()
                        .collect(Collectors.toList());
            }
            JdhBizItem build = JdhBizItem.builder()
                //.id()
                //.venderId(venderId)
                //.venderName(venderName)
                .businessModeCode(BusinessModeEnum.YK.getCode())
                .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                .applyScene(tempApplyScene)
                .firstBizCategory(jdhStandardItem.getFirstBizCategory())
                .secondBizCategory(jdhStandardItem.getSecondBizCategory())
                .thirdBizCategory(jdhStandardItem.getThirdBizCategory())
                //.bizItemId(SpringUtil.getBean(GenerateIdFactory.class).getId())
                .bizItemName(jdhStandardItem.getItemName())
                .standardItemId(jdhStandardItem.getItemId())
                //.indicatorList(Lists.newArrayList(entry.getValue()))
                //.price(Objects.nonNull(serviceItem.getItemPrice()) ? serviceItem.getItemPrice().divide(new BigDecimal("100"), 2, RoundingMode.DOWN) : null)
                //.price(standardItemId2IndicatorMap.entrySet().size() == 1 ? serviceItem.getItemPrice() : null)
                .mean(oldBaseItemImportBO.getItemMean())
                .suitable(suitable)
                //.gender()
                .extJson(JdhBizItemExtJsonBO.builder().oldItemId(oldBaseItemImportBO.getItemId()).build())//记录老业务项目的ID，方便后续映射排查
                //.remark(serviceItem.getRemark())
                .build();

            //与现有数据库中存的数据进行处理
            if (oldItemId2BizItemMap.containsKey(oldBaseItemImportBO.getItemId())) {
                List<JdhBizItem> oldJdhBizItems = oldItemId2BizItemMap.get(oldBaseItemImportBO.getItemId());
                Map<Long, JdhBizItem> standardIdMap = oldJdhBizItems.stream().collect(Collectors.toMap(JdhBizItem::getStandardItemId, oldItem -> oldItem, (t, t2) -> t2));
                JdhBizItem oldJdhBizItem = standardIdMap.get(build.getStandardItemId());
                if (Objects.nonNull(oldJdhBizItem)) {
                    build.setBizItemId(oldJdhBizItem.getBizItemId());
                    standardIdMap.remove(build.getStandardItemId());
                }
                if (!standardIdMap.isEmpty()) {
                    standardIdMap.forEach((key, value) -> jdhBusinessItemRepository.deleteBizItemById(value.getBizItemId(), null));
                }
            }
            //新增新数据
            jdhBusinessItemRepository.save(build);
        }
        if (CollectionUtils.isNotEmpty(serviceIdList)) {
            //刷服务数据
            //所有有效的service 和 业务项目的关联关系
            List<JdhServiceItemRel> jdhServiceItemRelList = serviceItemRelRepository.queryJdhServiceItemRelList(JdhServiceItemRelContext.builder().serviceIds(serviceIdList).build());
            //按照serviceId分组
            Map<Long, List<JdhServiceItemRel>> serviceId2List = jdhServiceItemRelList.stream().collect(Collectors.groupingBy(JdhServiceItemRel::getServiceId));
            log.info("ProductDataCleanApplicationImpl -> importOldItemList 清洗京东服务数据 serviceId2List size={}", serviceId2List.size());
            Map<Long, JdhProgram> oldServiceId2Map = new HashMap<>();
            //遍历map，依次处理每个service和对应业务项目绑定关系
            for (Map.Entry<Long, List<JdhServiceItemRel>> entry : serviceId2List.entrySet()) {
                Long serviceId = entry.getKey();
                //查询老京东服务表，获取服务数据
                JdhService jdhService = serviceRepository.find(JdhServiceIdentifier.builder().serviceId(serviceId).build());
                //组装京东服务
                JdhProgram program = buildProgram(jdhService, serviceId);
                //查询新表京东服务
                JdhProgram existProgram = jdhProgramRepository.find(JdhProgramIdentifier.builder().programId(serviceId).build());
                if (Objects.nonNull(existProgram)) {
                    program.setId(existProgram.getId());
                    program.setVersion(existProgram.getVersion());
                }
                oldServiceId2Map.put(serviceId, program);
                //处理服务与业务项目关联关系
                List<JdhServiceItemRel> itemRels = entry.getValue();
                Map<Long, Integer> itemIdMap = itemRels.stream().collect(Collectors.toMap(JdhServiceItemRel::getItemId, jdhServiceItemRel -> Optional.ofNullable(jdhServiceItemRel.getImportant()).orElse(0), (o1, o2) -> o2));
                // 查询业务项目列表
                List<JdhBizItem> existBizItemList = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().oldItemIdList(itemIdMap.keySet()).build());
                log.info("ProductDataCleanApplicationImpl -> importOldItemList, existBizItemList, serviceId={}, existBizItemList={}", serviceId, JSON.toJSONString(existBizItemList));
                Map<Long, List<JdhBizItem>> item2BizList = existBizItemList.stream().filter(jdhBizItem -> Objects.nonNull(jdhBizItem.getExtJson()) && Objects.nonNull(jdhBizItem.getExtJson().getOldItemId()))
                        .collect(Collectors.groupingBy(jdhBizItem -> jdhBizItem.getExtJson().getOldItemId()));
                if (itemIdMap.size() != item2BizList.size()) {
                    Set<Long> differenceSet1 = new HashSet<>(itemIdMap.keySet());
                    differenceSet1.removeAll(item2BizList.keySet());
                    Set<Long> differenceSet2 = new HashSet<>(item2BizList.keySet());
                    differenceSet2.removeAll(itemIdMap.keySet());
                    differenceSet1.addAll(differenceSet2);
                    Set<Long> bizItemList = BIZ_ITEM_THREAD_LOCAL.get();
                    if (CollectionUtils.isEmpty(bizItemList)) {
                        bizItemList = new HashSet<>();
                        BIZ_ITEM_THREAD_LOCAL.set(bizItemList);
                    }
                    bizItemList.addAll(differenceSet1);
                    log.warn("ProductDataCleanApplicationImpl -> importOldItemList, 服务绑定的项目与数据库业务项目数量不一致,serviceId={}, itemIdMap={}, item2BizList={}, differenceSet1={}", serviceId, JSON.toJSONString(itemIdMap), JSON.toJSONString(item2BizList), JSON.toJSONString(differenceSet1));
                    // continue;
                }
                List<JdhProgramBizItemRel> saveServiceItemRelList = new ArrayList<>();
                for (Map.Entry<Long, Integer> itemEntry : itemIdMap.entrySet()) {
                    List<JdhBizItem> jdhBizItems = item2BizList.get(itemEntry.getKey());
                    if (CollectionUtils.isEmpty(jdhBizItems)) {
                        log.warn("ProductDataCleanApplicationImpl -> importOldItemList, itemEntry={}", JSON.toJSONString(itemEntry));
                        continue;
                    }
                    for (JdhBizItem jdhBizItem : jdhBizItems) {
                        saveServiceItemRelList.add(JdhProgramBizItemRel.builder().bizItemId(jdhBizItem.getBizItemId()).programId(program.getProgramId()).isImportant(itemEntry.getValue()).build());
                    }
                }
                program.setProgramBizItemRelList(saveServiceItemRelList);
                log.info("ProductDataCleanApplicationImpl -> importOldItemList, 保存 program={}", JSON.toJSONString(program));
                jdhProgramRepository.save(program);
                //jdhProgramRepository.batchSaveProgramItemRel(saveServiceItemRelList);
            }
            log.info("ProductDataCleanApplicationImpl -> 找不到业务项目的数据总数, bizItemList={}", BIZ_ITEM_THREAD_LOCAL.get());
            log.info("ProductDataCleanApplicationImpl -> importOldItemList, oldServiceId2Map={}", JSON.toJSONString(oldServiceId2Map));
        }
        return Boolean.TRUE;
    }

    /**
     *
     * @param ossKey1
     * @param ossKey2
     * @return
     */
    @Override
    public Boolean comparePopProductSku(String ossKey1, String ossKey2) {
        //获取文件流
        InputStream inputStream = fileManageService.get(ossKey1, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        List<PopProductExcelBO> importResult = EasyExcelUtil.read(inputStream, 1, PopProductExcelBO.class);
        log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku解析excel集合：importResult size={}",importResult.size());
        //Product 过滤
        HashBasedTable<String, String, PopProductExcelBO> basedTable = HashBasedTable.create();
        for (PopProductExcelBO popProductExcelBO : importResult) {
            if (basedTable.contains(popProductExcelBO.getProductId(), popProductExcelBO.getSkuId())) {
                continue;
            }
            basedTable.put(popProductExcelBO.getProductId(), popProductExcelBO.getSkuId(), popProductExcelBO);
        }
        log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：basedTable size={}", basedTable.size());
        log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：stringMapMap={}", JSON.toJSONString(basedTable.rowMap()));
        log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：stringMapMap size={}", JSON.toJSONString(basedTable.rowMap().size()));



        //获取文件流
        InputStream inputStream2 = fileManageService.get(ossKey2, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        List<PopProductExcelBO> importResult2 = EasyExcelUtil.read(inputStream2, 1, PopProductExcelBO.class);
        log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku解析excel集合：importResult size={}",importResult.size());
        //Product 过滤
        HashBasedTable<String, String, PopProductExcelBO> basedTable2 = HashBasedTable.create();
        for (PopProductExcelBO popProductExcelBO : importResult2) {
            if (basedTable2.contains(popProductExcelBO.getProductId(), popProductExcelBO.getSkuId())) {
                continue;
            }
            basedTable2.put(popProductExcelBO.getProductId(), popProductExcelBO.getSkuId(), popProductExcelBO);
        }
        log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：basedTable size={}", basedTable2.size());
        log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：stringMapMap={}", JSON.toJSONString(basedTable2.rowMap()));
        log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：stringMapMap size={}", JSON.toJSONString(basedTable2.rowMap().size()));
        List<Pair<String, Set<String>>> list = new ArrayList<>();
        for (Map.Entry<String, Map<String, PopProductExcelBO>> cell : basedTable2.rowMap().entrySet()) {
            Map<String, PopProductExcelBO> row = basedTable.row(cell.getKey());
            if (Objects.nonNull(row) && !row.isEmpty()) {
                Set<String> skuId = cell.getValue().keySet();
                log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：productId={}, skuId = {}", cell.getKey(), JSON.toJSONString(skuId));
                list.add(Pair.of(cell.getKey(),skuId));
            }
        }
        log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：stringMapMap list={}", JSON.toJSONString(list));
        return true;
    }

    /**
     *
     * @param json
     * @return
     */
    @Override
    public Boolean supplementProductSku(String json) {
        //查询所有标准指标
        List<JdhStandardIndicator> standardIndicatorList = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().build());
        Map<Long, JdhStandardIndicator> id2IndicatorMap = standardIndicatorList.stream().collect(Collectors.toMap(JdhStandardIndicator::getIndicatorId, standardIndicator -> standardIndicator, (t, t2) -> t2));

        TypeReference<List<Pair<String, Set<String>>>> typeRef = new TypeReference<List<Pair<String, Set<String>>>>() {};
        List<Pair<String, Set<String>>> list = JSON.parseObject(json, typeRef.getType());
        for (Pair<String, Set<String>> pair : list) {
            log.info("ProductDataCleanApplicationImpl -> supplementProductSku 处理集合：pair={}", JSON.toJSONString(pair));
            String productId = pair.getKey();
            List<JdhProgram> programList = jdhProgramRepository.queryProgramListBySkuOrProductId(JdhProgramSkuQuery.builder().productId(productId).build());
            if (CollectionUtils.isEmpty(programList)) {
                log.warn("ProductDataCleanApplicationImpl -> supplementProductSku 处理集合：program is null, productId={}", productId);
                continue;
            }
            JdhProgram program = programList.get(0);
            Set<String> skuIds = pair.getValue();
            //维护program和sku关联关系表
            List<JdhProgramSku> programSkuList = jdhProgramRepository.queryListProgramSku(JdhProgramSkuQuery.builder().productId(productId).skuIdList(Lists.newArrayList(skuIds)).build());
            log.info("ProductDataCleanApplicationImpl -> supplementProductSku, programSkuList={}", JSON.toJSONString(programSkuList));
            Map<String, JdhProgramSku> programSkuMap = programSkuList.stream().collect(Collectors.toMap(JdhProgramSku::getSkuId, sku -> sku, (t, t2) -> t2));
            boolean isRefresh = false;
            for (String skuId : skuIds) {
                JdhProgramSku programSku = programSkuMap.get(skuId);
                if (Objects.isNull(programSku)) {
                    //如果不存在，组装programSku实体
                    programSku = JdhProgramSku.builder()
                            .businessModeCode(BusinessModeEnum.POP_LOC.getCode())
                            .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                            .programId(program.getProgramId())
                            .skuId(skuId)
                            .productId(productId)
                            .channelType(1)
                            .channelId(ProductSaleChannelEnum.XFYL.getChannelId())
                            .venderId(program.getVenderId())
                            .venderName(program.getVenderName())
                            .saleStatus(1)
                            .build();
                    jdhProgramRepository.saveProgramSku(programSku);
                    isRefresh = true;
                } else {
                    //存在，不处理
                    log.info("ProductDataCleanApplicationImpl -> supplementProductSku, 存在，productId={}, skuId={}", productId, skuId);
                }
            }
            if (!isRefresh) {
                continue;
            }
            //查出所有对应的业务项目ID
            List<JdhProgramBizItemRel> jdhProgramBizItemRels = jdhProgramRepository.queryJdhProgramItemRelList(JdhProgramBizItemRelQuery.builder().programId(program.getProgramId()).build());
            if (CollectionUtils.isEmpty(jdhProgramBizItemRels)) {
                continue;
            }
            Map<Long, Integer> item2ImportMap = jdhProgramBizItemRels.stream().collect(Collectors.toMap(JdhProgramBizItemRel::getBizItemId, jdhProgramBizItemRel -> Optional.ofNullable(jdhProgramBizItemRel.getIsImportant()).orElse(0), (t, t2) -> t2));

            //新的业务项目list
            List<JdhBizItem> jdhBizItems = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().bizItemIdList(Lists.newArrayList(item2ImportMap.keySet())).needIndicatorIdList(true).build());
            if (CollectionUtils.isEmpty(jdhBizItems)) {
                log.warn("ProductDataCleanApplicationImpl -> supplementProductSku 老项目对应新业务项目为空 program={}", JsonUtil.toJSONString(program));
                continue;
            }

            //新业务项目集合
            List<SaveBizItemCmd> bizItemCmdList = new ArrayList<>();

            //组装返回值（商品主数据格式）
            for (JdhBizItem jdhBizItem : jdhBizItems) {
                log.warn("ProductDataCleanApplicationImpl -> supplementProductSku jdhBizItem={}", JsonUtil.toJSONString(jdhBizItem));
                if (Objects.isNull(jdhBizItem) || CollectionUtils.isEmpty(jdhBizItem.getIndicatorList())) {
                    continue;
                }
                //设置每个字段的值
                SaveBizItemCmd bizItemCmd = ProductBizItemConvertor.INSTANCE.convertToSaveBizItemCmd(jdhBizItem, new JSONObject(), id2IndicatorMap);
                bizItemCmd.setImportantItem(Objects.equals(item2ImportMap.getOrDefault(jdhBizItem.getBizItemId(), 0), 1));
                bizItemCmdList.add(bizItemCmd);
            }
            SaveProductBizItemCmd productBizItemCmd = new SaveProductBizItemCmd();
            productBizItemCmd.setProductId(Long.parseLong(productId));
            productBizItemCmd.setBizItemCmdList(bizItemCmdList);
            productBizItemCmd.setVenderId(program.getVenderId());
            if (duccConfig.getProductItemUpdateSwitch()) {
                log.info("ProductDataCleanApplicationImpl -> supplementProductSku, 执行商品主数据项目更新 productId={}", productId);
                productServiceItemApplication.publishMerchantProductItem(productBizItemCmd);
            }
        }
        return true;
    }

    /**
     *
     * @param json
     * @return
     */
    @Override
    public Boolean refreshProductSku(String json) {
        //查询所有标准指标
        List<JdhStandardIndicator> standardIndicatorList = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().build());
        Map<Long, JdhStandardIndicator> id2IndicatorMap = standardIndicatorList.stream().collect(Collectors.toMap(JdhStandardIndicator::getIndicatorId, standardIndicator -> standardIndicator, (t, t2) -> t2));

        List<Long> productList = JSON.parseArray(json, Long.class);
        for (Long productId : productList) {
            JdhProgram program = jdhProgramRepository.findProgramRefresh(JdhProgramQuery.builder().programId(productId).build());
            if (Objects.isNull(program)) {
                log.warn("ProductDataCleanApplicationImpl -> refreshProductSku 处理集合：program is null, productId={}", productId);
                continue;
            }
            //查出所有对应的业务项目ID
            List<JdhProgramBizItemRel> jdhProgramBizItemRels = jdhProgramRepository.queryJdhProgramItemRelListRefresh(JdhProgramBizItemRelQuery.builder().programId(program.getProgramId()).build());
            if (CollectionUtils.isEmpty(jdhProgramBizItemRels)) {
                continue;
            }
            Map<Long, Integer> item2ImportMap = jdhProgramBizItemRels.stream().collect(Collectors.toMap(JdhProgramBizItemRel::getBizItemId, jdhProgramBizItemRel -> Optional.ofNullable(jdhProgramBizItemRel.getIsImportant()).orElse(0), (t, t2) -> t2));

            //新的业务项目list
            List<JdhBizItem> jdhBizItems = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().bizItemIdList(Lists.newArrayList(item2ImportMap.keySet())).needIndicatorIdList(true).build());
            if (CollectionUtils.isEmpty(jdhBizItems)) {
                log.warn("ProductDataCleanApplicationImpl -> refreshProductSku 老项目对应新业务项目为空 programId={}", JsonUtil.toJSONString(program.getProgramId()));
                continue;
            }

            //新业务项目集合
            List<SaveBizItemCmd> bizItemCmdList = new ArrayList<>();

            //组装返回值（商品主数据格式）
            for (JdhBizItem jdhBizItem : jdhBizItems) {
                if (Objects.isNull(jdhBizItem) || CollectionUtils.isEmpty(jdhBizItem.getIndicatorList())) {
                    log.warn("ProductDataCleanApplicationImpl -> refreshProductSku jdhBizItem={}", JsonUtil.toJSONString(jdhBizItem));
                    continue;
                }
                //设置每个字段的值
                SaveBizItemCmd bizItemCmd = ProductBizItemConvertor.INSTANCE.convertToSaveBizItemCmd(jdhBizItem, new JSONObject(), id2IndicatorMap);
                bizItemCmd.setImportantItem(Objects.equals(item2ImportMap.getOrDefault(jdhBizItem.getBizItemId(), 0), 1));
                bizItemCmdList.add(bizItemCmd);
            }
            SaveProductBizItemCmd productBizItemCmd = new SaveProductBizItemCmd();
            productBizItemCmd.setProductId(productId);
            productBizItemCmd.setBizItemCmdList(bizItemCmdList);
            productBizItemCmd.setVenderId(program.getVenderId());
            if (duccConfig.getProductItemUpdateSwitch()) {
                log.info("ProductDataCleanApplicationImpl -> refreshProductSku, 执行商品主数据项目更新 productId={}", productId);
                productServiceItemApplication.publishMerchantProductItem(productBizItemCmd);
            }
        }
        return true;
    }

    /**
     * 刷新适用人群
     * @param ossKey
     * @return
     */
    @Override
    public Boolean refreshProductSkuSuitable(String ossKey) {
        //获取文件流
        InputStream inputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        List<StandardItemSuitableExcelBO> importResult = EasyExcelUtil.read(inputStream, 1, StandardItemSuitableExcelBO.class);
        log.info("ProductDataCleanApplicationImpl -> refreshProductSkuSuitable, importResult={}", JSON.toJSONString(importResult));
        Set<Long> changeBizItemIdList = new HashSet<>();
        for (StandardItemSuitableExcelBO suitableExcelBO : importResult) {
            log.info("ProductDataCleanApplicationImpl -> refreshProductSkuSuitable, suitableExcelBO={}", JSON.toJSONString(suitableExcelBO));
            //查询标准项目
            List<JdhStandardItem> jdhStandardItems = jdhStandardItemRepository.queryList(JdhStandardItemRepQuery.builder().accurateQueryItemName(suitableExcelBO.getItemName()).build());
            if (CollectionUtils.isEmpty(jdhStandardItems)) {
                log.warn("ProductDataCleanApplicationImpl -> refreshProductSkuSuitable, 未找到标准项目， suitableExcelBO={}", JSON.toJSONString(suitableExcelBO));
            }
            JdhStandardItem jdhStandardItem = jdhStandardItems.get(0);
            log.info("ProductDataCleanApplicationImpl -> refreshProductSkuSuitable, jdhStandardItem={}", JSON.toJSONString(jdhStandardItem));
            //1.查询绑定标准项目的业务项目
            List<JdhBizItem> jdhBizItems = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().standardItemId(jdhStandardItem.getItemId()).build());
            //2.刷新业务项目的适用人群
            List<String> suitable = Arrays.stream(suitableExcelBO.getItemSuitable().split("")).sorted().collect(Collectors.toList());
            jdhBizItems.forEach(jdhBizItem -> {
                jdhBizItem.setSuitable(suitable);
                jdhBusinessItemRepository.save(jdhBizItem);
                //3.记录业务项目ID
                changeBizItemIdList.add(jdhBizItem.getBizItemId());
            });
        }
        log.info("ProductDataCleanApplicationImpl -> refreshProductSkuSuitable, changeBizItemIdList={}", JSON.toJSONString(changeBizItemIdList));
        //4.根据业务项目ID查询关联的PRODUCT
        List<JdhProgramBizItemRelQuery> jdhProgramBizItemRelQuery = changeBizItemIdList.stream().map(bizItemId -> JdhProgramBizItemRelQuery.builder()
                .bizItemId(bizItemId)
                .build()).collect(Collectors.toList());
        List<JdhProgramBizItemRel> bizItemRels = jdhProgramRepository.queryJdhProgramItemRelList(jdhProgramBizItemRelQuery);
        //4.1 找出所有关联的programId
        Set<Long> programIdList = bizItemRels.stream().map(JdhProgramBizItemRel::getProgramId).filter(Objects::nonNull).collect(Collectors.toSet());
        log.info("ProductDataCleanApplicationImpl -> refreshProductSkuSuitable, programIdList={}", JSON.toJSONString(programIdList));
        //4.2 找出所有的pop porductId
        List<JdhProgramSku> jdhProgramSkuList = jdhProgramRepository.queryListProgramSku(JdhProgramSkuQuery.builder().businessModeCode(BusinessModeEnum.POP_LOC.getCode()).programIdList(programIdList).build());
        //输出商品列表
        Set<Long> productSet = jdhProgramSkuList.stream().map(jdhProgramSku -> Long.valueOf(jdhProgramSku.getProductId())).collect(Collectors.toSet());
        log.info("ProductDataCleanApplicationImpl -> refreshProductSkuSuitable, productSet={}", JSON.toJSONString(productSet));
        return true;
    }

    /**
     * 清洗pop商品项目
     * @param ossKey
     * @return
     */
    @Override
    public Boolean cleanPopProductItem(String ossKey) {
        //查询所有标准项目
        List<JdhStandardItem> standardItemList = jdhStandardItemRepository.queryList(JdhStandardItemRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
        Map<Long, JdhStandardItem> id2StandardItemMap = standardItemList.stream().collect(Collectors.toMap(JdhStandardItem::getItemId, standardItem -> standardItem, (t, t2) -> t2));
        //查询所有标准指标
        List<JdhStandardIndicator> standardIndicatorList = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().build());
        Map<Long, JdhStandardIndicator> id2IndicatorMap = standardIndicatorList.stream().collect(Collectors.toMap(JdhStandardIndicator::getIndicatorId, standardIndicator -> standardIndicator, (t, t2) -> t2));

        //获取文件流
        InputStream inputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        List<CleanPopProductItemExcelBO> importResult = EasyExcelUtil.read(inputStream, 1, CleanPopProductItemExcelBO.class);
        //商品项目对应适用人群
        Map<String, List<String>> productItem2suitable = new HashMap<>();
        //商品对应商家
        Map<String, String> productId2VenderId = new HashMap<>();
        // row ：商品ID
        // col ：标准项目ID
        // value：标准指标ID
        HashBasedTable<String, Long, Set<Long>> basedTable = HashBasedTable.create();
        for (CleanPopProductItemExcelBO suitableExcelBO : importResult) {
            if (basedTable.contains(suitableExcelBO.getProductId(), suitableExcelBO.getStandardItemId())) {
                Set<Long> indicators = basedTable.get(suitableExcelBO.getProductId(), suitableExcelBO.getStandardItemId());
                indicators.add(suitableExcelBO.getStandardIndicatorId());
                continue;
            }
            if (!id2IndicatorMap.containsKey(suitableExcelBO.getStandardIndicatorId())) {
                throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
            }
            Set<Long> indicators = new HashSet<>();
            indicators.add(suitableExcelBO.getStandardIndicatorId());
            basedTable.put(suitableExcelBO.getProductId(), suitableExcelBO.getStandardItemId(), indicators);
            //添加适用人群
            List<String> suitable = Arrays.stream(suitableExcelBO.getBizItemSuitable().split(""))
                        .sorted()
                        .collect(Collectors.toList());
            productItem2suitable.put(suitableExcelBO.getProductId() + "-" + suitableExcelBO.getStandardItemId(), suitable);
            productId2VenderId.put(suitableExcelBO.getProductId(), suitableExcelBO.getVenderId());
        }
        Map<String, List<SaveBizItemCmd>> saveItemMap = new HashMap<>();
        for (Map.Entry<String, Map<Long, Set<Long>>> cell : basedTable.rowMap().entrySet()) {
            String product = cell.getKey();
            //处理venderId和venderName
            String venderId = productId2VenderId.get(product);
            //遍历创建业务项目
            List<SaveBizItemCmd> bizItemCmdList = new ArrayList<>();
            Map<Long, Set<Long>> value = cell.getValue();
            for (Map.Entry<Long, Set<Long>> entry : value.entrySet()) {
                List<String> suitable = productItem2suitable.get(product + "-" + entry.getKey());
                if (CollectionUtils.isEmpty(suitable)) {
                    throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                }
                JdhStandardItem jdhStandardItem = id2StandardItemMap.get(entry.getKey());
                if (Objects.isNull(jdhStandardItem)) {
                    throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                }
                List<StandardIndicatorCmd> indicatorList = entry.getValue().stream().map(indicatorId -> {
                    JdhStandardIndicator jdhStandardIndicator = id2IndicatorMap.get(indicatorId);
                    if (Objects.isNull(jdhStandardIndicator)) {
                        throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                    }
                    return StandardIndicatorCmd.builder()
                            .indicatorId(jdhStandardIndicator.getIndicatorId())
                            .indicatorName(jdhStandardIndicator.getIndicatorName())
                            .indicatorMean(jdhStandardIndicator.getIndicatorMean())
                            .firstBizCategory(jdhStandardIndicator.getFirstBizCategory())
                            .secondBizCategory(jdhStandardIndicator.getSecondBizCategory())
                            .thirdBizCategory(jdhStandardIndicator.getThirdBizCategory())
                            .serviceType(jdhStandardIndicator.getServiceType())
                            .healthIndicatorCode(jdhStandardIndicator.getHealthIndicatorCode())
                            .healthWikiId(jdhStandardIndicator.getHealthWikiId())
                            .price(jdhStandardIndicator.getPrice())
                            .build();
                }).collect(Collectors.toList());
                SaveBizItemCmd build = SaveBizItemCmd.builder()
                        .bizItemId(SpringUtil.getBean(GenerateIdFactory.class).getId())
                        .itemName(jdhStandardItem.getItemName())
                        .inspectionItemId(jdhStandardItem.getItemId())
                        .importantItem(false)
                        //TODO
                        .itemPrice(BigDecimal.ZERO.toPlainString())
                        .firstBizCategory(jdhStandardItem.getFirstBizCategory())
                        .secondBizCategory(jdhStandardItem.getSecondBizCategory())
                        .thirdBizCategory(jdhStandardItem.getThirdBizCategory())
                        .itemSuitableList(suitable)
                        .venderId(venderId)
                        .standardIndicatorList(indicatorList)
                        .build();
                bizItemCmdList.add(build);
            }
            log.info("ProductDataCleanApplicationImpl -> cleanPopProductItem, 执行商品主数据项目更新 product={}, bizItemCmdList={}", product, JSON.toJSONString(bizItemCmdList));
            saveItemMap.put(product, bizItemCmdList);
        }
        if (duccConfig.getProductItemUpdateSwitch()) {
            for (Map.Entry<String, List<SaveBizItemCmd>> entry : saveItemMap.entrySet()) {
                SaveProductBizItemCmd productBizItemCmd = new SaveProductBizItemCmd();
                productBizItemCmd.setProductId(Long.parseLong(entry.getKey()));
                productBizItemCmd.setBizItemCmdList(entry.getValue());
                productBizItemCmd.setVenderId(productId2VenderId.get(entry.getKey()));
                log.info("ProductDataCleanApplicationImpl -> cleanPopProductItem, 执行商品主数据项目更新 productId={}", productBizItemCmd.getProductId());
                productServiceItemApplication.publishMerchantProductItem(productBizItemCmd);
            }
        }
        return true;
    }

    /**
     * 清洗自营SKU套餐项目名称
     * @param ossKey
     * @return
     */
    @Override
    public Boolean cleanSelfProgramItemName(String ossKey) {
        //获取文件流
        InputStream inputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        List<SelfProgramItemNameExcelBO> importResult = EasyExcelUtil.read(inputStream, 1, SelfProgramItemNameExcelBO.class);
        Map<Long,SelfProgramItemNameExcelBO> updateMap = new HashMap<>();
        for (SelfProgramItemNameExcelBO itemNameExcelBO : importResult) {
            if (updateMap.containsKey(itemNameExcelBO.getBizItemId())) {
                continue;
            }
            updateMap.put(itemNameExcelBO.getBizItemId(), itemNameExcelBO);
        }
        //新的业务项目list
        List<JdhBizItem> jdhBizItems = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().bizItemIdList(Lists.newArrayList(updateMap.keySet())).needIndicatorIdList(true).build());
        for (JdhBizItem jdhBizItem : jdhBizItems) {
            SelfProgramItemNameExcelBO itemNameExcelBO = updateMap.get(jdhBizItem.getBizItemId());
            if (Objects.isNull(itemNameExcelBO)) {
                log.info("ProductDataCleanApplicationImpl -> cleanSelfProgramItemName updateMap not containsKey, bizItemId={}", jdhBizItem.getBizItemId());
                continue;
            }
            //添加适用人群
            List<String> suitable = Arrays.stream(itemNameExcelBO.getBizItemSuitable().split(""))
                        .sorted()
                        .collect(Collectors.toList());
            jdhBizItem.setBizItemName(itemNameExcelBO.getBizItemName());
            jdhBizItem.setSuitable(suitable);
            log.info("ProductDataCleanApplicationImpl -> cleanSelfProgramItemName, save jdhBizItem={}", JSON.toJSONString(jdhBizItem));
            jdhBusinessItemRepository.save(jdhBizItem);
        }
        return true;
    }

    /**
     * 导出业务项目数据（老）
     * @param mappingOssKey
     * @return
     */
    @Override
    public List<ServiceIndicatorExportBO> exportOldServiceItem(String mappingOssKey) {
        try {
            List<JdhService> jdhServices = serviceRepository.queryJdhServiceList(JdhServiceQueryContext.builder().build());
            Map<Long,JdhService> id2Service = new HashMap<>();
            //所有有效的service 和 业务项目的关联关系
            Map<Long,Set<Long>> itemId2Service = new HashMap<>();
            jdhServices.forEach(jdhService -> {
                id2Service.put(jdhService.getServiceId(), jdhService);
                if (CollectionUtils.isNotEmpty(jdhService.getServiceItemRelList())) {
                    for (JdhServiceItemRel serviceItemRel : jdhService.getServiceItemRelList()) {
                        if (itemId2Service.containsKey(serviceItemRel.getItemId())) {
                            itemId2Service.get(serviceItemRel.getItemId()).add(serviceItemRel.getServiceId());
                        }else {
                            Set<Long> set = new HashSet<>();
                            set.add(serviceItemRel.getServiceId());
                            itemId2Service.put(serviceItemRel.getItemId(), set);
                        }
                    }
                }
            });

            //查询所有业务项目数据（自营体检、pop体检）
            List<ServiceItem> serviceItems = serviceItemRepository.queryServiceItemList(JdhItemListQueryContext.builder().firstIndicatorCategory(Sets.newHashSet(906596746242L, 923776581122L, 940956728834L, 949546484226L, 661004250626L, 629554156546L, 124202627586L, 565856855042L, 923776581123L)).vendorType(VenderTypeEnum.SELF.getType()).build());
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 所有体检业务项目数据 size={}", serviceItems.size());
            Set<Long> itemIdSet = serviceItems.stream().map(ServiceItem::getItemId).collect(Collectors.toSet());
            //查询业务项目关联的指标数据
            List<ServiceItemIndicatorRel> serviceItemIndicatorRelList = serviceItemIndicatorRepository.queryItemIndicatorList(ServiceItemQueryContext.builder().itemIds(itemIdSet).build());
            Map<Long, List<Long>> itemId2IndicatorMap = serviceItemIndicatorRelList.stream().collect(Collectors.groupingBy(ServiceItemIndicatorRel::getItemId, Collectors.mapping(ServiceItemIndicatorRel::getIndicatorId, Collectors.toList())));
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 所有关联了指标的体检业务项目数据 itemId2IndicatorMap size={}", itemId2IndicatorMap.size());

            //查询所有老指标数据（按一级分类查，写死的，一级分类除了10000外都是体检的，一共9个ID）
            List<Indicator> oldIndicators = serviceIndicatorRepository.queryIndicatorList(ServiceIndicatorQueryContext.builder().firstIndicatorCategory(Sets.newHashSet(906596746242L, 923776581122L, 940956728834L, 949546484226L, 661004250626L, 629554156546L, 124202627586L, 565856855042L, 923776581123L)).build());
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 老指标数量 oldIndicators size={}", oldIndicators.size());
            Map<Long, Indicator> oldId2IndicatorMap = oldIndicators.stream().collect(Collectors.toMap(Indicator::getIndicatorId, indicator -> indicator, (t, t2) -> t2));

            //查询所有新指标数据
            List<JdhStandardIndicator> standardIndicatorList = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 新指标数量 standardIndicatorList size={}", standardIndicatorList.size());
            Map<String, JdhStandardIndicator> code2StandardIndicatorMap = standardIndicatorList.stream().collect(Collectors.toMap(JdhStandardIndicator::getHealthIndicatorCode, standardIndicator -> standardIndicator, (t, t2) -> t2));
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 新指标按检后ID去重后数量 code2StandardIndicatorMap size={}", code2StandardIndicatorMap.size());
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem code2StandardIndicatorMap={}", JSON.toJSONString(code2StandardIndicatorMap));

            //查询所有标准项目数据
            List<JdhStandardItem> standardItemList = jdhStandardItemRepository.queryList(JdhStandardItemRepQuery.builder().serviceType(ServiceTypeEnum.PHYSICAL.getServiceType()).build());
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 所有标准项目数据 standardItemList size={}", standardItemList.size());
            Map<Long, JdhStandardItem> id2StandardItemMap = standardItemList.stream().collect(Collectors.toMap(JdhStandardItem::getItemId, standardItem -> standardItem, (t, t2) -> t2));
            //指标对应的标准项目集合
            Map<Long, Set<Long>> indicatorId2ItemIdMap = new HashMap<>();
            standardItemList.forEach(standardItem -> {
                log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 指标对应的标准项目集合 standardItem={}", JSON.toJSONString(standardItem));
                List<Long> indicatorList = standardItem.getIndicatorList();
                if (CollectionUtils.isNotEmpty(indicatorList)) {
                    for (Long indicatorId : indicatorList) {
                        Set<Long> list = indicatorId2ItemIdMap.get(indicatorId);
                        if (CollectionUtils.isEmpty(list)) {
                            list = new HashSet<>();
                            indicatorId2ItemIdMap.put(indicatorId, list);
                        }
                        list.add(standardItem.getItemId());
                    }
                }
            });
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 指标对应的标准项目集合 indicatorId2ItemIdMap size={}", indicatorId2ItemIdMap.size());
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 指标对应的标准项目集合 indicatorId2ItemIdMap={}", JSON.toJSONString(indicatorId2ItemIdMap));

            //查询新老指标映射关系
            //获取文件流
            InputStream inputStream = fileManageService.get(mappingOssKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
            List<OldStandardIndicatorMappingBO> mappingIndicatorModelList = EasyExcelUtil.read(inputStream, 1, OldStandardIndicatorMappingBO.class);
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 解析excel集合：importResult size={}", mappingIndicatorModelList.size());
            //设置成老指标ID -> 新指标编码list 格式map，方便处理映射
            Map<String, Set<String>> resultMap = new HashMap<>();
            for (OldStandardIndicatorMappingBO oldStandardIndicatorMappingBO : mappingIndicatorModelList) {
                log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 解析excel oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                Set<String> healthIndicatorCodeList = resultMap.get(oldStandardIndicatorMappingBO.getOldIndicatorId());
                if (CollectionUtils.isEmpty(healthIndicatorCodeList)) {
                    healthIndicatorCodeList = new HashSet<>();
                    resultMap.put(oldStandardIndicatorMappingBO.getOldIndicatorId(), healthIndicatorCodeList);
                }
                if (StringUtil.isNotBlank(oldStandardIndicatorMappingBO.getHealthIndicatorCode()) && oldStandardIndicatorMappingBO.getHealthIndicatorCode().contains("SC")) {
                    healthIndicatorCodeList.add(oldStandardIndicatorMappingBO.getHealthIndicatorCode());
                } else {
                    log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 解析excel 包含非法检后指标ID oldStandardIndicatorMappingBO={}", JSON.toJSONString(oldStandardIndicatorMappingBO));
                }
            }
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 解析excel resultMap size={}", resultMap.size());
            log.info("ProductDataCleanApplicationImpl -> exportOldServiceItem 解析excel resultMap={}", JSON.toJSONString(resultMap));

            //遍历清洗业务项目数据和关联的指标数据
            List<ServiceIndicatorExportBO> result = new ArrayList<>();
            itemLoop : for (ServiceItem serviceItem : serviceItems) {
                Set<Long> serviceIds = itemId2Service.getOrDefault(serviceItem.getItemId(), new HashSet<>());
                List<String> serviceNameList = serviceIds.stream().map(serviceId -> {
                    JdhService jdhService = id2Service.get(serviceId);
                    return Objects.nonNull(jdhService) ? jdhService.getServiceName() : "";
                }).collect(Collectors.toList());
                String serviceNameListStr = Joiner.on(",").join(serviceNameList);
                if (serviceNameListStr.length() > 32767) {
                    serviceNameListStr = serviceNameListStr.substring(0, 32767);
                }
                if (!itemId2IndicatorMap.containsKey(serviceItem.getItemId())) {
                    log.error("ProductDataCleanApplicationImpl -> exportOldServiceItem 该业务项目未绑定指标={}", JsonUtil.toJSONString(serviceItem));
                    result.addAll(ProductServiceItemConvert.ins.serviceItem2ExportDto(serviceItem, "该业务项目未绑定指标", serviceNameListStr));
                    continue;
                } else {
                    List<Long> oldIndicatorIdList = itemId2IndicatorMap.get(serviceItem.getItemId());
                    //1.根据老指标ID映射新指标
                    Set<Long> mappingStandardIndicatorList = new HashSet<>();
                    for (Long oldIndicatorId : oldIndicatorIdList) {
                        if (!resultMap.containsKey(String.valueOf(oldIndicatorId))) {
                            log.error("ProductDataCleanApplicationImpl -> exportOldServiceItem 老指标没有和新指标的映射关系oldItemId={}, oldIndicatorId={}", serviceItem.getItemId(), oldIndicatorId);
                            serviceItem.setIndicatorList(oldIndicatorIdList.stream().map(oldId2IndicatorMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
                            result.addAll(ProductServiceItemConvert.ins.serviceItem2ExportDto(serviceItem, "老指标没有和新指标的映射关系", serviceNameListStr));
                            continue itemLoop;
                        }
                        Set<Long> list = resultMap.get(String.valueOf(oldIndicatorId)).stream().map(code2StandardIndicatorMap::get).filter(Objects::nonNull).map(JdhStandardIndicator::getIndicatorId).collect(Collectors.toSet());
                        if (list.size() != resultMap.get(String.valueOf(oldIndicatorId)).size()) {
                            log.error("ProductDataCleanApplicationImpl -> exportOldServiceItem 老指标和新指标的映射关系与数据库存储的新指标不符oldItemId={}, oldIndicatorId={}, mappingStandardIndicatorIdList={}, list={}", serviceItem.getItemId(), oldIndicatorId, JsonUtil.toJSONString(resultMap.get(String.valueOf(oldIndicatorId))), JsonUtil.toJSONString(list));
                            serviceItem.setIndicatorList(oldIndicatorIdList.stream().map(oldId2IndicatorMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
                            result.addAll(ProductServiceItemConvert.ins.serviceItem2ExportDto(serviceItem,"老指标和新指标的映射关系与老项目绑定指标数不符",serviceNameListStr));
                            continue itemLoop;
                        }
                        mappingStandardIndicatorList.addAll(list);
                    }
                    //2.映射标准项目列表
                    //2.1 POP业务项目，需要限制匹配的标准项目，应用场景都是to C的
                    boolean filterApplyScene = Objects.equals(serviceItem.getVendorType(), VenderTypeEnum.POP.getType());
                    Map<Long, Set<Long>> standardItemId2IndicatorMap = dispatchStandardItemList(mappingStandardIndicatorList, indicatorId2ItemIdMap, id2StandardItemMap, filterApplyScene ? StandardItemApplySceneEnum.TO_C.getScene() : null);
                    if (standardItemId2IndicatorMap.isEmpty()) {
                        log.warn("ProductDataCleanApplicationImpl -> exportOldServiceItem 老项目映射标准项目报错 serviceItem={}", JsonUtil.toJSONString(serviceItem));
                        serviceItem.setIndicatorList(oldIndicatorIdList.stream().map(oldId2IndicatorMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
                        result.addAll(ProductServiceItemConvert.ins.serviceItem2ExportDto(serviceItem,"老项目映射标准项目报错", serviceNameListStr));
                        continue itemLoop;
                    }
                    if (standardItemId2IndicatorMap.size() > 1) {
                        log.warn("ProductDataCleanApplicationImpl -> exportOldServiceItem 老项目映射标准项目报错 serviceItem={}", JsonUtil.toJSONString(serviceItem));
                        serviceItem.setIndicatorList(oldIndicatorIdList.stream().map(oldId2IndicatorMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
                        result.addAll(ProductServiceItemConvert.ins.serviceItem2ExportDto(serviceItem, "老项目映射为多个新项目", serviceNameListStr));
                        continue itemLoop;
                    }
                }
            }
            return result;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> exportOldServiceItem 处理信息异常", e);
            return new ArrayList<>();
        } catch (Throwable e) {
            log.error("ProductDataCleanApplicationImpl -> exportOldServiceItem 处理信息异常Throwable", e);
            return new ArrayList<>();
        }
    }

    /**
     * 清洗pop sku信息
     * @param ossKey
     * @return
     */
    @Override
    //@Transactional(rollbackFor = RuntimeException.class)
    public Boolean cleanPopProductSku(String ossKey) {
        //获取文件流
        InputStream inputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);
        try {
            List<PopProductExcelBO> importResult = EasyExcelUtil.read(inputStream, 1, PopProductExcelBO.class);
            log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku解析excel集合：importResult size={}",importResult.size());
            //Product 过滤
            HashBasedTable<String, String, PopProductExcelBO> basedTable = HashBasedTable.create();
            for (PopProductExcelBO popProductExcelBO : importResult) {
                if (basedTable.contains(popProductExcelBO.getProductId(), popProductExcelBO.getSkuId())) {
                    continue;
                }
                basedTable.put(popProductExcelBO.getProductId(), popProductExcelBO.getSkuId(), popProductExcelBO);
            }
            log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：basedTable size={}", basedTable.size());
            log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：stringMapMap={}", JSON.toJSONString(basedTable.rowMap()));
            log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：stringMapMap size={}", JSON.toJSONString(basedTable.rowMap().size()));

            //1.依次查询商品主数据，拿取商品配置的结构化项目数据，存入数据库
            for (Map.Entry<String, Map<String, PopProductExcelBO>> cell : basedTable.rowMap().entrySet()) {
                log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku 处理cell={}", JSON.toJSONString(cell));
                // 先查商品现有类目属性数据 https://joyspace.jd.com/page/4FTGMPrW7KEHqCKzYWOe
                if (cell.getValue().size() > 1) {
                    log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku 该商品有多个sku，productId={}，sku={}", cell.getKey(), cell.getValue().keySet());
                }

                String productId = cell.getKey();
                List<String> skuIdList = new ArrayList<>(cell.getValue().keySet());
                PopProductExcelBO productExcelBO = cell.getValue().values().iterator().next();
                Product product = skuInfoRpc.getProductById(productExcelBO.getVenderId(), Long.valueOf(productId), Sets.newHashSet("customProps", "multiCategoryId", "categoryId", "productName")
                        , Sets.newHashSet(CommonConstant.SAME_CITY_ATTRIBUTE));
                if (Objects.isNull(product)) {
                    log.warn("ProductDataCleanApplicationImpl -> cleanPopProductSku处理集合：product is null, productId={}", productId);
                    continue;
                }
                // 获取商品自定义属性
                Set<CustomProp> productCustomProps = Optional.ofNullable(product.getCustomProps()).orElse(new HashSet<>());
                Map<String, CustomProp> customPropMap = productCustomProps.stream().collect(Collectors.toMap(CustomProp::getAttrId, customProp -> customProp, (t, t2) -> t2));

                //获取存在商品数据中的结构化x项目数据属性ID
                String attrId = null;
                Map<String, String> skuCategoryAttrIdMap = duccConfig.getSkuCategoryAttrIdMap();
                if (Objects.nonNull(product.getCategoryId())) {
                    attrId = skuCategoryAttrIdMap.get(product.getCategoryId().toString());
                }
                if (Objects.nonNull(product.getMultiCategoryId())) {
                    attrId = skuCategoryAttrIdMap.get(product.getMultiCategoryId().toString());
                }
                if (StringUtil.isBlank(attrId)) {
                    throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("attrId"));
                }

                CustomProp customProp = customPropMap.get(attrId);
                log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku clean customProp={}", JsonUtil.toJSONString(customProp));
                //如果无，不做处理
                if (Objects.isNull(customProp)) {
                    log.warn("ProductDataCleanApplicationImpl -> cleanPopProductSku 商品没有结构化项目属性：productCustomProps={}", JSON.toJSONString(productCustomProps));
                    continue;
                }

                // 保存program和programSku数据
                //老业务项目集合
                List<ServiceItem> oldBizItemCmdList = new ArrayList<>();
                //新业务项目集合
                List<SaveBizItemCmd> bizItemCmdList = new ArrayList<>();
                //新业务项目 to 是否重点项目
                Map<Long, Boolean> bizItemImportantMap = new HashMap<>();

                boolean cleanResult = cleanNewCustomAttrValue(customProp.getCustomAttrValues(), oldBizItemCmdList, bizItemCmdList, bizItemImportantMap);
                log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku clean 老业务项目集合 oldBizItemCmdList={}", JsonUtil.toJSONString(oldBizItemCmdList));
                log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku clean 新业务项目集合 bizItemCmdList={}", JsonUtil.toJSONString(bizItemCmdList));
                if (!cleanResult) {
                    //log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku cleanNewCustomAttrValue 未正确处理 customProp={}", JsonUtil.toJSONString(customProp));
                    log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku 此spu下所有结构化项目均未匹配到新业务项目，将spu下项目做清空更新处理 productId={}, customProp={}", productId, JsonUtil.toJSONString(customProp));
                    // 查询现有类目数据中是否有要修改的属性
                    CustomProp prop = customPropMap.get(attrId);
                    // 如果有，删除现有属性数据
                    if (Objects.nonNull(prop)) {
                        productCustomProps.remove(prop);
                    }
                    log.info("ProductDataCleanApplicationImpl->cleanPopProductSku delete update productCustomProps={}", JsonUtil.toJSONString(productCustomProps));
                    // 商品类目属性更新（全量覆盖）
                    if (duccConfig.getProductItemUpdateSwitch()) {
                        log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku, 执行商品主数据项目更新，删除现有属性数据 productId={}", productId);
                        skuInfoRpc.updateProduct(productExcelBO.getVenderId(), product.getProductId(), Sets.newHashSet(productCustomProps));
                    }
                    continue;
                }


                //商品ID当做programId
                Long programId = product.getProductId();

                //program关联的新项目列表
                List<JdhProgramBizItemRel> programBizItemRelList = bizItemCmdList.stream().map(entry -> {
                    JdhProgramBizItemRel jdhProgramBizItemRel = new JdhProgramBizItemRel();
                    jdhProgramBizItemRel.setProgramId(programId);
                    jdhProgramBizItemRel.setBizItemId(entry.getBizItemId());
                    jdhProgramBizItemRel.setIsImportant(Objects.equals(bizItemImportantMap.get(entry.getBizItemId()), true) ? 1 : 0);
                    return jdhProgramBizItemRel;
                }).collect(Collectors.toList());

                //program适用人群取所有关联业务项目适用人群的并集
                Set<String> suitable = new HashSet<>();
                for (SaveBizItemCmd bizItemCmd : bizItemCmdList) {
                    if (suitable.containsAll(Lists.newArrayList(SuitableEnum.MAN.getCode(), SuitableEnum.UNMARRY_WOMEN.getCode(), SuitableEnum.MARRIED_WOMEN.getCode()))) {
                        break;
                    }
                    suitable.addAll(bizItemCmd.getItemSuitableList());
                }

                //查询数据库是否已存在数据
                JdhProgram jdhProgram = jdhProgramRepository.find(JdhProgramIdentifier.builder().programId(programId).build());
                log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku, jdhProgram={}", JSON.toJSONString(jdhProgram));
                if (Objects.isNull(jdhProgram)) {
                    //如果不存在，说明清洗jdhProgram数据有问题
                    log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku, jdhProgram不存在, programId={}", productId);
                    jdhProgram = JdhProgram.builder()
                            .venderId(productExcelBO.getVenderId())
                            .venderName(productExcelBO.getVenderName())
                            .businessModeCode(BusinessModeEnum.POP_LOC.getCode())
                            .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                            .programId(programId)
                            .programName(product.getProductName())
                            .saleStatus(1)
                            .saleLimitType(1)
                            .saleShowMethod(ProgramSaleShowMethodTypeEnum.NORMAL.getTypeNo())
                            .suitable(Lists.newArrayList(suitable))
                            .programBizItemRelList(programBizItemRelList)
                            .build();
                } else {
                    //存在，按照查询的运营端数据更新Program实体
                    jdhProgram.setVenderId(productExcelBO.getVenderId());
                    jdhProgram.setVenderName(productExcelBO.getVenderName());
                    jdhProgram.setProgramId(programId);
                    jdhProgram.setProgramName(product.getProductName());
                    jdhProgram.setSaleStatus(1);
                    jdhProgram.setSaleShowMethod(ProgramSaleShowMethodTypeEnum.NORMAL.getTypeNo());
                    jdhProgram.setSaleLimitType(1);
                    jdhProgram.setSuitable(Lists.newArrayList(suitable));
                    jdhProgram.setProgramBizItemRelList(programBizItemRelList);
                }
                jdhProgramRepository.save(jdhProgram);

                //维护program和sku关联关系表
                List<JdhProgramSku> programSkuList = jdhProgramRepository.queryListProgramSku(JdhProgramSkuQuery.builder().skuIdList(skuIdList).build());
                log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku, programSkuList={}", JSON.toJSONString(programSkuList));
                Map<String, JdhProgramSku> programSkuMap = programSkuList.stream().collect(Collectors.toMap(JdhProgramSku::getSkuId, sku -> sku, (t, t2) -> t2));
                for (String skuId : skuIdList) {
                    JdhProgramSku programSku = programSkuMap.get(skuId);
                    if (Objects.isNull(programSku)) {
                        //如果不存在，组装programSku实体
                        programSku = JdhProgramSku.builder()
                                .businessModeCode(BusinessModeEnum.POP_LOC.getCode())
                                .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                                .programId(jdhProgram.getProgramId())
                                .skuId(skuId)
                                .productId(productId)
                                .channelType(1)
                                .channelId(ProductSaleChannelEnum.XFYL.getChannelId())
                                .venderId(productExcelBO.getVenderId())
                                .venderName(productExcelBO.getVenderName())
                                .saleStatus(1)
                                .build();
                    } else {
                        //存在，按照查询的运营端数据更新Program实体
                        programSku.setProgramId(jdhProgram.getProgramId());
                        programSku.setProductId(productId);
                        programSku.setVenderId(productExcelBO.getVenderId());
                        programSku.setVenderName(productExcelBO.getVenderName());
                    }
                    jdhProgramRepository.saveProgramSku(programSku);
                }
                SaveProductBizItemCmd productBizItemCmd = new SaveProductBizItemCmd();
                productBizItemCmd.setProductId(product.getProductId());
                productBizItemCmd.setBizItemCmdList(bizItemCmdList);
                productBizItemCmd.setVenderId(productExcelBO.getVenderId());
                if (duccConfig.getProductItemUpdateSwitch()) {
                    log.info("ProductDataCleanApplicationImpl -> cleanPopProductSku, 执行商品主数据项目更新 productId={}", productId);
                    productServiceItemApplication.publishMerchantProductItem(productBizItemCmd);
                }
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> cleanPopProductSku信息异常", e);
            return Boolean.FALSE;
        }
    }

    /**
     *
     * @param customAttrValues
     * @return
     */
    private boolean cleanNewCustomAttrValue(Set<CustomAttrValue> customAttrValues, List<ServiceItem> oldBizItemCmdList, List<SaveBizItemCmd> bizItemCmdList, Map<Long, Boolean> bizItemImportantMap) {

        boolean cleanResult = false;

        //无序，商品保存的业务项目数据列表
        //取出每个业务项目ID，查询数据库映射关系
        for (CustomAttrValue customAttrValue : customAttrValues) {
            //老业务项目id
            Long itemNo = null;
            //老业务项目数据
            JSONObject oldBizItem = null;

            //遍历老项目每个字段值
            for (ComplexExpand complexExpand : customAttrValue.getComplexExpands()) {
                Optional<Feature> enNameOptional = complexExpand.getFeatures().stream().filter(feature -> Objects.equals(feature.getKey(), "enName")).findFirst();
                if (enNameOptional.isPresent()) {
                    String enName = enNameOptional.get().getValue();
                    //如果是业务项目Id字段，取出值
                    if (Objects.equals("itemNo", enName)) {
                        itemNo = StringUtil.isNotBlank(complexExpand.getExpand()) ? Long.parseLong(complexExpand.getExpand()) : null;
                    }
                    //如果是业务项目序列化json字段，反序列化取出数据
                    if (Objects.equals("itemExtra", enName)) {
                        oldBizItem = JSON.parseObject(complexExpand.getExpand());
                    }
                }
            }

            //查询老业务项目对应的新业务项目
            if (Objects.isNull(itemNo) && Objects.nonNull(oldBizItem)) {
                itemNo = oldBizItem.getLong("itemNo");
            }
            if (Objects.isNull(itemNo) || Objects.isNull(oldBizItem)) {
                log.warn("ProductDataCleanApplicationImpl -> cleanNewCustomAttrValue 老项目itemNo或数据为空 old customAttrValue={}", JsonUtil.toJSONString(customAttrValue));
                continue;
            }

            //查询老业务项目放入list方便比较
            ServiceItem serviceItem = serviceItemRepository.find(ServiceItemIdentifier.builder().itemId(itemNo).build());
            if (Objects.isNull(serviceItem)) {
                log.warn("ProductDataCleanApplicationImpl -> cleanNewCustomAttrValue 老项目数据为空 old itemNo={}", itemNo);
                continue;
            }
            oldBizItemCmdList.add(serviceItem);

            //新的业务项目list
            List<JdhBizItem> jdhBizItems = jdhBusinessItemRepository.queryList(JdhBizItemReqQuery.builder().oldItemIdList(Sets.newHashSet(itemNo)).needIndicatorIdList(true).build());
            if (CollectionUtils.isEmpty(jdhBizItems)) {
                log.warn("ProductDataCleanApplicationImpl -> cleanNewCustomAttrValue 老项目对应新业务项目为空 old customAttrValue={}", JsonUtil.toJSONString(customAttrValue));
                continue;
            }

            //查询所有标准指标
            List<JdhStandardIndicator> standardIndicatorList = jdhStandardIndicatorRepository.queryList(JdhStandardIndicatorRepQuery.builder().build());
            Map<Long, JdhStandardIndicator> id2IndicatorMap = standardIndicatorList.stream().collect(Collectors.toMap(JdhStandardIndicator::getIndicatorId, standardIndicator -> standardIndicator, (t, t2) -> t2));

            //组装返回值（商品主数据格式）
            for (JdhBizItem jdhBizItem : jdhBizItems) {
                //设置每个字段的值
                SaveBizItemCmd bizItemCmd = ProductBizItemConvertor.INSTANCE.convertToSaveBizItemCmd(jdhBizItem, oldBizItem, id2IndicatorMap);

                bizItemCmdList.add(bizItemCmd);
                //记录每个新业务项目以及是否重点项目
                bizItemImportantMap.put(jdhBizItem.getBizItemId(), Optional.ofNullable(oldBizItem.getBoolean("importantItem")).orElse(Boolean.FALSE));
            }

        }
        //只要有一个商家业务项目能匹配清洗为新业务项目，则清洗成功
        if (CollectionUtils.isNotEmpty(bizItemCmdList)) {
            cleanResult = true;
        }
        return cleanResult;
    }

    /**
     * 新表保存京东服务
     * @param jdhService
     * @return
     */
    private JdhProgram buildProgram(JdhService jdhService, Long serviceId) {
        JdhProgram jdhProgram = null;
        if (Objects.nonNull(jdhService)) {
            jdhProgram = ProductProgramConvertor.ins.serviceToModel(jdhService);
            jdhProgram.setId(null);
            //jdhProgram.setProgramName(Objects.isNull(jdhService.getServiceName()) ? "" : jdhService.getServiceName());
            jdhProgram.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
            jdhProgram.setBusinessModeCode(BusinessModeEnum.YK.getCode());
        } else {
            jdhProgram = JdhProgram.builder()
                    .programId(serviceId)
                    .programName("")
                    .businessModeCode(BusinessModeEnum.YK.getCode())
                    .serviceType(ServiceTypeEnum.PHYSICAL.getServiceType())
                    //.extJson(JdhProgramExtJsonBO.builder().oldServiceId(serviceId.toString()).build())
                    .build();
        }
        //jdhProgramRepository.save(jdhProgram);
        return jdhProgram;
    }

    /**
     *
     * @param mappingStandardIndicatorList 老业务项目关联的老指标，老指标映射的所有新指标列表
     * @param indicatorId2ItemIdMap 新指标对应的标准项目，可能新指标和多个标准项目有关联关系，所有key是新指标ID，value是关联的标准项目列表
     * @param id2StandardItemMap 标准项目map，key：标准项目ID，value：标准项目数据
     * @return
     */
    private static Map<Long, Set<Long>> dispatchStandardItemList(Set<Long> mappingStandardIndicatorList, Map<Long, Set<Long>> indicatorId2ItemIdMap, Map<Long, JdhStandardItem> id2StandardItemMap, String applyScene) {
        Map<Long, Set<Long>> result = new HashMap<>();
        try {
            extracted(mappingStandardIndicatorList, indicatorId2ItemIdMap, id2StandardItemMap, result, applyScene);
        } catch (BusinessException e) {
            log.error("ProductDataCleanApplicationImpl -> dispatchStandardItemList business error", e);
            return new HashMap<>();
        } catch (Exception e) {
            log.error("ProductDataCleanApplicationImpl -> dispatchStandardItemList error", e);
            return new HashMap<>();
        }
        return result;
    }

    /**
     *
     * @param mappingStandardIndicatorList 老业务项目关联的老指标，老指标映射的所有新指标列表
     * @param indicatorId2ItemIdMap 新指标对应的标准项目，可能新指标和多个标准项目有关联关系，所有key是新指标ID，value是关联的标准项目列表
     * @param id2StandardItemMap 标准项目map，key：标准项目ID，value：标准项目数据
     * @param result
     */
    private static void extracted(Set<Long> mappingStandardIndicatorList, Map<Long, Set<Long>> indicatorId2ItemIdMap, Map<Long, JdhStandardItem> id2StandardItemMap, Map<Long, Set<Long>> result, String applyScene) {
        //遍历所有指标所属的标准项目，找出覆盖指标最多的标准项目
        //标准项目 -> 关联的指标列表
        Map<Long, Set<Long>> standardItemId2ItemIdMap = new HashMap<>();

        for (Long jdhStandardIndicator : mappingStandardIndicatorList) {
           if (!indicatorId2ItemIdMap.containsKey(jdhStandardIndicator)){
               continue;
           }
           Set<Long> list = indicatorId2ItemIdMap.get(jdhStandardIndicator);
           for (Long itemId : list) {
               //如果入参传了限制的场景，查下标准项目是否包含此场景（toB toC），不包含则过滤掉
               if (StringUtils.isNotBlank(applyScene)) {
                   JdhStandardItem jdhStandardItem = id2StandardItemMap.get(itemId);
                   if (Objects.isNull(jdhStandardItem) || CollectionUtils.isEmpty(jdhStandardItem.getApplyScene()) || !jdhStandardItem.getApplyScene().contains(applyScene)) {
                       continue;
                   }
               }
               //记录下每个标准项目，匹配了多少个老业务项目的指标
               Set<Long> set = standardItemId2ItemIdMap.getOrDefault(itemId, Sets.newHashSet());
               set.add(jdhStandardIndicator);
               standardItemId2ItemIdMap.put(itemId, set);
           }
        }
        //取能匹配最多指标的标准项目放入结果集，之后剩余的指标再找出能匹配最多指标的标准项目放入结果集，依次循环直到老业务项目的所有指标都匹配到多个标准项目上，result返回匹配的多个标准项目列表
        Map.Entry<Long, Set<Long>> entryWithMaxSize = standardItemId2ItemIdMap.entrySet().stream().max(Comparator.comparingInt(entry -> entry.getValue().size())).orElse(null);
        //如果当前轮次没有项目能够匹配任何剩余指标，则抛异常（原因大概率是能匹配指标的标准项目，由于上面限制了applyScene被过滤了，未被过滤的标准项目都没有关联剩余指标），逻辑终止
        if (Objects.isNull(entryWithMaxSize)) {
            log.warn("ProductDataCleanApplicationImpl -> 标准指标找不到关联的标准项目，mappingStandardIndicatorList={}, applyScene={}", JsonUtil.toJSONString(mappingStandardIndicatorList), applyScene);
            //放到threadlocal，最后输出问题指标ID
            Map<String, Set<Long>> stringSetMap = SCENE_INDICATOR_THREAD_LOCAL.get();
            if (MapUtils.isEmpty(stringSetMap)) {
                stringSetMap = new HashMap<>();
                SCENE_INDICATOR_THREAD_LOCAL.set(stringSetMap);
            }
            Set<Long> indicatorSet = stringSetMap.get(applyScene);
            if (CollectionUtils.isEmpty(indicatorSet)) {
                indicatorSet = new HashSet<>();
                stringSetMap.put(applyScene, indicatorSet);
            }
            indicatorSet.addAll(mappingStandardIndicatorList);

            throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
        }
        result.put(entryWithMaxSize.getKey(), entryWithMaxSize.getValue());
        mappingStandardIndicatorList.removeAll(entryWithMaxSize.getValue());
        //indicatorId2ItemIdMap.remove(entryWithMaxSize.getKey());
        if (CollectionUtils.isNotEmpty(mappingStandardIndicatorList)) {
            extracted(mappingStandardIndicatorList, indicatorId2ItemIdMap, id2StandardItemMap, result, applyScene);
        }
    }

    /**
     *
     * @param strA
     * @param strB
     * @return
     */
    public static double calculateMatchPercentage(String strA, String strB) {
        int totalLength = strA.length();
        int matchCount = 0;

        for (int i = 0; i < totalLength; i++) {
            if (strB.contains(String.valueOf(strA.charAt(i)))) {
                matchCount++;
            }
        }

        return (double) matchCount / totalLength;
    }

    /**
     *
     * @param importResult
     */
    private void attachBizCategory(List<StandardIndicatorExcelModel> importResult) {
        if (CollectionUtils.isEmpty(importResult)) {
            return;
        }
        String tempFirstBizCategoryName = "";
        String tempSecondBizCategoryName = "";
        String tempThirdBizCategoryName = "";
        String tempItemName = "";
        for (StandardIndicatorExcelModel standardIndicatorExcelModel : importResult) {
            //设置一级类别
            if (StringUtils.isNotBlank(standardIndicatorExcelModel.getFirstBizCategoryName())) {
                tempFirstBizCategoryName = standardIndicatorExcelModel.getFirstBizCategoryName();
            } else {
                standardIndicatorExcelModel.setFirstBizCategoryName(tempFirstBizCategoryName);
            }
            //设置二级类别
            if (StringUtils.isNotBlank(standardIndicatorExcelModel.getSecondBizCategoryName())) {
                tempSecondBizCategoryName = standardIndicatorExcelModel.getSecondBizCategoryName();
            } else {
                standardIndicatorExcelModel.setSecondBizCategoryName(tempSecondBizCategoryName);
            }
            //设置三级类别
            if (StringUtils.isNotBlank(standardIndicatorExcelModel.getThirdBizCategoryName())) {
                tempThirdBizCategoryName = standardIndicatorExcelModel.getThirdBizCategoryName();
            } else {
                standardIndicatorExcelModel.setThirdBizCategoryName(tempThirdBizCategoryName);
            }
            //设置体检项目
            if (StringUtils.isNotBlank(standardIndicatorExcelModel.getItemName())) {
                tempItemName = standardIndicatorExcelModel.getItemName();
            } else {
                standardIndicatorExcelModel.setItemName(tempItemName);
            }
            //过滤指标数量非数字
            if (StringUtil.isNotBlank(standardIndicatorExcelModel.getIndicatorNum()) && !NumberUtil.isNumber(standardIndicatorExcelModel.getIndicatorNum())) {
                standardIndicatorExcelModel.setIndicatorNum(null);
            }

        }

    }
}