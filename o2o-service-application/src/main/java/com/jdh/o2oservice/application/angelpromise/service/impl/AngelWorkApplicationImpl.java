package com.jdh.o2oservice.application.angelpromise.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelLocationApplication;
import com.jdh.o2oservice.application.angelpromise.ability.AngelWorkFinishServiceAbility;
import com.jdh.o2oservice.application.angelpromise.ability.AngelWorkFinishServiceAbilityFactory;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseApplicationConverter;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.dispatch.convert.DispatchApplicationConverter;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.ProductServiceItemExtApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.application.trade.service.impl.TradeApplicationImpl;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.ShipTypeConfig;
import com.jdh.o2oservice.base.ducc.model.ShipTypeExpression;
import com.jdh.o2oservice.base.ducc.model.UavConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.common.ext.O2oBusinessIdentifier;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.enums.AngelEventTypeEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.model.JdhStationIdentifier;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.angelpromise.bo.*;
import com.jdh.o2oservice.core.domain.angelpromise.context.*;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelTaskStatusCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelWorkStartEndDateCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelWorkStatusDbCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.*;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelTaskDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelWorkDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.service.ability.WorkShipCallTransferAbility;
import com.jdh.o2oservice.core.domain.angelpromise.vo.*;
import com.jdh.o2oservice.core.domain.angelpromise.vo.AngelOrder;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelTaskExtVo;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelWorkExtVo;
import com.jdh.o2oservice.core.domain.angelpromise.vo.ShipTask;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchDetail;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchIdentifier;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchDetailRepQuery;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedicalDeliveryTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleEventTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.UserName;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DirectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.businessMode.ServiceHomeTypeDomainService;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.DirectionRequestParam;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.DirectionRequestParam;
import com.jdh.o2oservice.core.domain.trade.enums.JdOrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.event.OrderCreateEventBody;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.export.angel.dto.AngelLocationDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angelpromise.cmd.*;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.*;
import com.jdh.o2oservice.export.dispatch.cmd.CancelDispatchCmd;
import com.jdh.o2oservice.export.medicalpromise.cmd.BatchMedicalPromiseSubmitCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseBindDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseConditionDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromisePatientDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseBindRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.product.cmd.ItemMaterialPackageRelCmd;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.product.query.ServiceItemExtQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseExtendDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.PromiseServiceDetailDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderItemDTO;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import com.jdh.o2oservice.ext.ship.enums.StanderAngelShipStatusEnum;
import com.jdh.o2oservice.ext.ship.param.DeliveryOrderDetailRequest;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryOrderDetailResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName:AngelWorkApplicationImpl
 * @Description: 服务者工单application实现
 * @Author: yaoqinghai
 * @Date: 2024/4/18 16:55
 * @Vserion: 1.0
 **/
@Service
@Slf4j
public class AngelWorkApplicationImpl implements AngelWorkApplication {
    /** */
    @Resource
    private AngelWorkRepository angelWorkRepository;
    /** */
    @Resource
    private AngelTaskRepository angelTaskRepository;
    /** */
    @Resource
    private AngelWorkHistoryRepository angelWorkHistoryRepository;
    /** */
    @Resource
    private AngelShipHistoryRepository angelShipHistoryRepository;
    /** */
    @Resource
    private AngelShipRepository angelShipRepository;
    /** */
    @Resource
    private AngelShipDomainService angelShipDomainService;

    /** */
    @Resource
    private AngelApplication angelApplication;
    /** */
    @Resource
    private PromiseApplication promiseApplication;
    /** */
    @Resource
    private TradeApplicationImpl tradeApplication;
    /** */
    @Resource
    private ProductApplication productApplication;
    /** */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;
    /**
     * 检测单仓储层
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    /** */
    @Resource
    private AngelLocationApplication angelLocationApplication;
    /** */
    @Resource
    private RedisLockUtil redisLockUtil;
    /** */
    @Resource
    private AngelTaskDomainService angelTaskDomainService;
    /** */
    @Resource
    private AngelWorkDomainService angelWorkDomainService;
    /** */
    @Resource
    private EventCoordinator eventCoordinator;
    /** */
    @Resource
    private AddressRpc addressRpc;
    /** */
    @Resource
    private JdOrderApplication jdOrderApplication;
    /** */
    @Resource
    private FileManageApplication fileManageApplication;

    /**
     * 商品查询
     */
    @Resource
    ProductServiceItemExtApplication productServiceItemExtApplication;

    @Resource
    private JdhStationRepository jdhStationRepository;
    /** */
    @Resource
    private DuccConfig duccConfig;

    /** */
    @Resource
    private WorkShipCallTransferAbility workShipCallTransferAbility;


    /** */
    private static final String SHIP_INFO = "需携带密封罐";


    @Autowired
    private FileManageService fileManageService;
    
    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private AngelWorkFinishServiceAbilityFactory angelWorkFinishServiceAbilityFactory;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private ServiceHomeTypeDomainService serviceHomeTypeDomainService;
    @Resource
    private DispatchApplication dispatchApplication;
    @Resource
    private DirectionServiceRpc directionServiceRpc;

    @Resource
    private DispatchRepository dispatchRepository;

    @Resource
    private AngelRepository angelRepository;


    /**
     * 创建工单
     *
     * @param jdhAngelWorkSaveCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "AngelWorkApplicationImpl.createAngelWork")
    public AngelWorkCreateResultDto createAngelWork(JdhAngelWorkSaveCmd jdhAngelWorkSaveCmd) {
        log.info("AngelWorkApplicationImpl -> createAngelWork start, jdhAngelWorkSaveCmd={}", JSON.toJSONString(jdhAngelWorkSaveCmd));
        AssertUtils.hasText(jdhAngelWorkSaveCmd.getVerticalCode(), "垂直业务身份Code不能为空");
        AssertUtils.hasText(jdhAngelWorkSaveCmd.getServiceType(), "服务类型不能为空");
        //AssertUtils.nonNull(jdhAngelWorkSaveCmd.getOrderId(), "京东订单id不能为空");精准营养没有订单id;
        AssertUtils.nonNull(jdhAngelWorkSaveCmd.getSourceId(), "工单来源id不能为空");//派单id
        AssertUtils.nonNull(jdhAngelWorkSaveCmd.getPromiseId(), "履约单id不能为空");
        AssertUtils.isNotEmpty(jdhAngelWorkSaveCmd.getAppointmentList(), "患者列表不能为空");

        SaveAngelWorkContext workContext = swapSaveAngelWorkContext(jdhAngelWorkSaveCmd);
        log.info("AngelWorkApplicationImpl -> createAngelWork start, workContext={}", JSON.toJSONString(workContext));
        AngelWorkCreateResultBo resultBo = angelWorkDomainService.createWork(workContext);
        log.info("AngelWorkApplicationImpl -> createAngelWork end, resultBo={}", JSON.toJSONString(resultBo));
        return AngelPromiseApplicationConverter.instance.convertToAngelWorkCreateResultDto(resultBo);
    }

    /**
     * 检查工单是否绑定了条码
     *
     * @param angelCheckBarCodeCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean submitBindBarCode(AngelCheckBarCodeCmd angelCheckBarCodeCmd) {
        AssertUtils.nonNull(angelCheckBarCodeCmd.getWorkId(), "工单id不能为空");
        //检查数据权限
        authorityCheck(angelCheckBarCodeCmd.getWorkId(), angelCheckBarCodeCmd.getUserPin());
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.SUBMIT_SAMPLING_CODE, angelCheckBarCodeCmd.getWorkId());
        try{
            if (!redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.SUBMIT_SAMPLING_CODE.getExpireTime(), RedisKeyEnum.SUBMIT_SAMPLING_CODE.getExpireTimeUnit())) {
                log.info("AngelWorkApplicationImpl -> submitBindBarCode,正在执行请稍后重试!", JSON.toJSONString(angelCheckBarCodeCmd));
                return Boolean.TRUE;
            }
            AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(angelCheckBarCodeCmd.getWorkId()));
            if(Objects.isNull(angelWork)){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
            }
            if(!angelWork.checkValidStatus(true, true)){
                throw new BusinessException(AngelPromiseBizErrorCode.WORK_STATUS_INVALID_ERROR);
            }
            List<AngelTask> angelTaskAllList = getAngelTasks(angelCheckBarCodeCmd);
            List<AngelTask> angelTaskList = angelTaskAllList.stream().filter(task -> task.checkValidStatus(true, true)).collect(Collectors.toList());

            //上门检测模式下需要检查样本编码是否绑定，并且要提交样本到实验室
            if(Objects.equals(AngelWorkTypeEnum.NURSE.getType(), angelWork.getWorkType())){
                //检测单查询项目及绑码信息的接口
                MedicalPromiseBindRequest medicalPromiseBindRequest = new MedicalPromiseBindRequest();
                medicalPromiseBindRequest.setPromiseId(angelWork.getPromiseId());
                medicalPromiseBindRequest.setPromisePatientIdList(angelTaskList.stream().map(item -> Long.valueOf(item.getPatientId())).collect(Collectors.toList()));
                medicalPromiseBindRequest.setInvalid(false);
                medicalPromiseBindRequest.setFreezeQuery(false);
                MedicalPromiseBindDTO medicalPromiseBindDTO = medicalPromiseApplication.queryMedicalPromiseBindCondition(medicalPromiseBindRequest);
                //检查采样码
                List<MedicalPromisePatientDTO> anyNoSampling = medicalPromiseBindDTO.getMedicalPromisePatientDTOList().stream().filter(item -> {
                    List<MedicalPromiseConditionDTO> medicalPromiseConditionDTOList = item.getMedicalPromiseConditionDTOList();
                    if (CollectionUtils.isEmpty(medicalPromiseConditionDTOList)) {
                        return true;
                    }
                    return medicalPromiseConditionDTOList.stream().anyMatch(sampling -> StringUtils.isBlank(sampling.getSpecimenCode()));
                }).collect(Collectors.toList());

                if(CollectionUtils.isNotEmpty(anyNoSampling)){
                    throw new BusinessException(AngelPromiseBizErrorCode.SPECIMEN_CODE_HAS_EMPTY_ERROR);
                }
                //检查检测单状态
                List<MedicalPromisePatientDTO> waitSubmitList = medicalPromiseBindDTO.getMedicalPromisePatientDTOList().stream().filter(item -> {
                    List<MedicalPromiseConditionDTO> medicalPromiseConditionDTOList = item.getMedicalPromiseConditionDTOList();
                    if (CollectionUtils.isEmpty(medicalPromiseConditionDTOList)) {
                        return true;
                    }
                    return medicalPromiseConditionDTOList.stream().anyMatch(sampling -> MedicalPromiseStatusEnum.needChangeBindStatus(sampling.getStatus()));
                }).collect(Collectors.toList());

                if(CollectionUtils.isNotEmpty(waitSubmitList)){
                    //调用检测单批量推送样本条码到实验室
                    BatchMedicalPromiseSubmitCmd batchMedicalPromiseSubmitCmd = new BatchMedicalPromiseSubmitCmd();
                    batchMedicalPromiseSubmitCmd.setPromiseId(angelWork.getPromiseId());
                    medicalPromiseApplication.batchSubmitMedicalPromiseToStation(batchMedicalPromiseSubmitCmd);
                }
            }

            //更新业务状态
            List<AngelTaskExtStateBo> taskExtStateBoList = angelTaskList.stream()
                    .map(task -> AngelTaskExtStateBo.builder()
                            .taskId(task.getTaskId())
                            .taskExtStatus(Objects.equals(AngelWorkTypeEnum.NURSE.getType(), angelWork.getWorkType()) ? AngelBizExtStatusEnum.CHOOSE_DELIVERY_WAY.getType() : AngelBizExtStatusEnum.CARE_SERVICE_FINISH.getType())
                            .build())
                    .collect(Collectors.toList());
            AngelTaskExtStatusContext statusContext = AngelTaskExtStatusContext.builder()
                    .workId(angelWork.getWorkId())
                    .angelTaskExtStateBoList(taskExtStateBoList)
                    .build();
            return angelTaskDomainService.executeTaskExt(statusContext);
        }finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * 护士呼叫选择配送方式
     *
     * @param angelWorkCreateShipCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean createDeliveryShip(AngelWorkCreateShipCmd angelWorkCreateShipCmd) {
        AssertUtils.hasText(angelWorkCreateShipCmd.getShopNo(), "实验室ID不能为空");
        AssertUtils.hasText(angelWorkCreateShipCmd.getSupplierAddress(), AngelPromiseBizErrorCode.SUPPLIER_ADDRESS);
        AssertUtils.hasText(angelWorkCreateShipCmd.getWorkId(), "工单ID不能为空");
        AssertUtils.hasText(angelWorkCreateShipCmd.getUserPin(), "操作人不能为空");


        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.DADA_CREATE_SHIP, angelWorkCreateShipCmd.getWorkId(), angelWorkCreateShipCmd.getShopNo());
        if (!redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.DADA_CREATE_SHIP.getExpireTime(), RedisKeyEnum.DADA_CREATE_SHIP.getExpireTimeUnit())) {
            throw new BusinessException(AngelPromiseBizErrorCode.REPEAT_DO);
        }
        //数据权限检查
        authorityCheck(Long.valueOf(angelWorkCreateShipCmd.getWorkId()), angelWorkCreateShipCmd.getUserPin());

        try{
            AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(Long.valueOf(angelWorkCreateShipCmd.getWorkId())));
            if(Objects.isNull(angelWork)){
                log.error("[AngelWorkApplicationImpl.createDeliveryShip],工单不存在!angelWorkCreateShipCmd={}", JSON.toJSONString(angelWorkCreateShipCmd));
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
            }

            AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
            angelTaskDBQuery.setWorkId(angelWork.getWorkId());
            List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
            if(CollectionUtils.isEmpty(angelTaskList)){
                log.error("[AngelWorkApplicationImpl.createDeliveryShip],任务单信息不存在!angelWorkCreateShipCmd={}", JSON.toJSONString(angelWorkCreateShipCmd));
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_TASK_NOT_EXIST);
            }
            Map<String, AngelTask> taskMap = angelTaskList.stream().collect(Collectors.toMap(AngelTask::getPatientId, Function.identity(), (k1, k2) -> k1));
            //查询实验室包含了哪些任务单
            MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
            medicalPromiseListRequest.setPromiseId(angelWork.getPromiseId());
            medicalPromiseListRequest.setInvalid(false);
            List<MedicalPromiseDTO> medicalPromiseDTOS = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
            if(CollectionUtils.isEmpty(medicalPromiseDTOS)){
                log.error("[AngelWorkApplicationImpl.createDeliveryShip],检测单信息不存在!angelWorkCreateShipCmd={}", JSON.toJSONString(angelWorkCreateShipCmd));
                throw new BusinessException(AngelPromiseBizErrorCode.MEDICAL_PROMISE_NOT_EXIST);
            }
            List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseDTOS.stream().filter(medical -> Objects.equals(medical.getStationId(), angelWorkCreateShipCmd.getShopNo())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(medicalPromiseDTOList)){
                log.error("[AngelWorkApplicationImpl.createDeliveryShip],没有有效的实验室!angelWorkCreateShipCmd={}", JSON.toJSONString(angelWorkCreateShipCmd));
                throw new BusinessException(AngelPromiseBizErrorCode.RECEIVER_IS_NOT_EXIST_ERROR);
            }

            Map<String, List<ShipTask>> stationTaskMap = medicalPromiseDTOList.stream().
                    collect(Collectors.groupingBy(MedicalPromiseDTO::getStationId, Collectors.mapping(item -> ShipTask.builder()
                            .taskId(taskMap.get(String.valueOf(item.getPromisePatientId())).getTaskId())
                            .promisePatientId(item.getPromisePatientId())
                            .build(), Collectors.toList())));
            List<ShipTask> shipTasks = stationTaskMap.get(angelWorkCreateShipCmd.getShopNo());
            if(CollectionUtils.isEmpty(shipTasks)){
                log.error("[AngelWorkApplicationImpl.createDeliveryShip],实验室下不在该检测单中!angelWorkCreateShipCmd={}", JSON.toJSONString(angelWorkCreateShipCmd));
                throw new BusinessException(AngelPromiseBizErrorCode.RECEIVER_ADDRESS_INFO_ERROR);
            }

            MedicalPromiseDTO medicalPromiseDTO = medicalPromiseDTOList.get(CommonConstant.ZERO);
            ArrayList<ShipTask> shipTaskList =
                    shipTasks.stream().collect(Collectors.collectingAndThen(Collectors.toMap(ShipTask::getTaskId, Function.identity(), (k1, k2) -> k1), map -> Lists.newArrayList(map.values())));
            AngelWorkShipCreateContext shipCreateContext = swapCreateDeliveryShipContext(angelWorkCreateShipCmd, angelWork, medicalPromiseDTO);
            shipCreateContext.setShipTaskList(shipTaskList);
            shipCreateContext.setAngelWork(angelWork);
            shipCreateContext.setVerticalCode(angelWork.getVerticalCode());
            shipCreateContext.setServiceType(angelWork.getServiceType());

            //呼叫运力供应商规则判断，护士呼叫骑手当前默认闪送
            if(DeliveryTypeEnum.SELF_DELIVERY.getType().equals(angelWorkCreateShipCmd.getDeliveryType())){
                shipCreateContext.setAngelDetailType(AngelDetailTypeEnum.SELF_SUPPLIER.getType());
                shipCreateContext.setDeliveryType(DeliveryTypeEnum.fetchDeliveryTypeByDetailType(AngelDetailTypeEnum.SELF_SUPPLIER.getType()));
                shipCreateContext.setInfo(SHIP_INFO);
            }else if (DeliveryTypeEnum.THIRD_DELIVERY.getType().equals(angelWorkCreateShipCmd.getDeliveryType())){
                //如果是三方运力配送
                shipCreateContext.setAngelDetailType(AngelDetailTypeEnum.THIRD_SUPPLIER.getType());
                shipCreateContext.setSupplierAddress(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getFullAddress());
                shipCreateContext.setDeliveryType(DeliveryTypeEnum.fetchDeliveryTypeByDetailType(AngelDetailTypeEnum.THIRD_SUPPLIER.getType()));
            } else {
                shipCreateContext.setAngelDetailType(AngelDetailTypeEnum.SHANSONG_SUPPLIER.getType());
                shipCreateContext.setDeliveryType(DeliveryTypeEnum.fetchDeliveryTypeByDetailType(AngelDetailTypeEnum.SHANSONG_SUPPLIER.getType()));
            }
            AngelShip angelShip = angelShipDomainService.createAngelShip(shipCreateContext);
            if(Objects.equals(DeliveryTypeEnum.SELF_DELIVERY.getType(), shipCreateContext.getDeliveryType())
                    || Objects.equals(DeliveryTypeEnum.THIRD_DELIVERY.getType(), shipCreateContext.getDeliveryType())
            ) {
                Event publishEvent = EventFactory.newDefaultEvent(angelShip, AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_IN_DELIVERY, null);
                eventCoordinator.publish(publishEvent);
            }
            return true;
        }catch(Exception ex){
           log.error("[AngelWorkApplicationImpl.createDeliveryShip],创建运单失败!", ex);
           throw ex;
        }finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * 接收达达状态回调
     *
     * @param
     */
    @Override
    @LogAndAlarm
    public Boolean shipStatusCallback(ShipInfoForCallBackRequest callBackRequest) {
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.CREATE_SHIP_ORDER_LOCK_KEY_PREFIX, String.valueOf(callBackRequest.getOrderId()), String.valueOf(callBackRequest.getOrderStatus()));
        if (!redisLockUtil.tryLock(lockKey, "1", 5, TimeUnit.SECONDS)) {
            throw new BusinessException(AngelPromiseBizErrorCode.REPEAT_DO);
        }
        AngelShipCallBackContext angelShipDadaCallBackContext = AngelPromiseApplicationConverter.instance.convertToAngelShipDadaCallBackContext(callBackRequest);

        try {
            AngelShipOrderDetailContext detailContext = new AngelShipOrderDetailContext();
            detailContext.setShipId(Long.valueOf(callBackRequest.getOrderId()));
            detailContext.setOutShipId(callBackRequest.getClientId());
            if (Objects.equals(AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus(), callBackRequest.getOrderStatus())) {
                DeliveryOrderDetailResponse shipOrderDetail = angelShipDomainService.getShipOrderDetail(detailContext);
                if (Objects.nonNull(shipOrderDetail)) {
                    //预发环境,通过工具模拟回调,可以传经纬度过来
                    angelShipDadaCallBackContext.setLatitude(StringUtils.isNotBlank(shipOrderDetail.getTransporterLat()) ? Double.valueOf(shipOrderDetail.getTransporterLat()) : (callBackRequest.getLatitude()!=0?callBackRequest.getLatitude():0.0));
                    angelShipDadaCallBackContext.setLongitude(StringUtils.isNotBlank(shipOrderDetail.getTransporterLng()) ? Double.valueOf(shipOrderDetail.getTransporterLng()) : (callBackRequest.getLongitude()!=0?callBackRequest.getLongitude():0.0));
                }
            }
        } catch (Exception ex) {
            log.error("[AngelWorkApplicationImpl -> shipStatusCallback],处理骑手位置异常");
        }

        // 达达异常状态回调逻辑处理
        boolean success =  this.updateAngelShipWithDdCancelReason(angelShipDadaCallBackContext);
        log.info("[AngelWorkApplicationImpl -> updateAngelShipWithDdCancelReason],success={}", success);
        if (!success) {
            return Boolean.TRUE;
        }
        return angelShipDomainService.receiveAngelShipCallback(angelShipDadaCallBackContext);
    }

    /**
     * 接收运力供应商状态回调
     *
     * @param callBackRequest
     */
    @Override
    @LogAndAlarm
    public Boolean shipStatusCallback(AngelShipCallBackContext callBackRequest) {
        String lockKey = "";
        if(callBackRequest.getOrderId()!=null){
            lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.CREATE_SHIP_ORDER_LOCK_KEY_PREFIX, String.valueOf(callBackRequest.getOrderId()), String.valueOf(callBackRequest.getOrderStatus()));
        }else{
            lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.CREATE_SHIP_ORDER_LOCK_KEY_PREFIX, callBackRequest.getClientId(), String.valueOf(callBackRequest.getOrderStatus()));
        }
        if (!redisLockUtil.tryLock(lockKey, "1", 5, TimeUnit.SECONDS)) {
            throw new BusinessException(AngelPromiseBizErrorCode.REPEAT_DO);
        }
        return angelShipDomainService.receiveAngelShipCallback(callBackRequest);
    }

    /**
     * 取消运单
     *
     * @param cancelShipCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean cancelShip(AngelWorkCancelShipCmd cancelShipCmd) {
        AssertUtils.hasText(cancelShipCmd.getWorkId(), "工单id不能为空");
        AssertUtils.hasText(cancelShipCmd.getShipId(), "运单id不能为空");

        //检查数据权限(运营端操作不校验)
        //非运营端、C端取消运单，校验数据权限
        if (!Objects.equals(CommonConstant.ONE,cancelShipCmd.getOperateSource()) && !Objects.equals(CommonConstant.TWO,cancelShipCmd.getOperateSource())){
            authorityCheck(Long.valueOf(cancelShipCmd.getWorkId()), cancelShipCmd.getUserPin());
        }

        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.DADA_CANCEL_SHIP, cancelShipCmd.getWorkId(), cancelShipCmd.getShipId());
        if (!redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.DADA_CANCEL_SHIP.getExpireTime(), RedisKeyEnum.DADA_CANCEL_SHIP.getExpireTimeUnit())) {
            throw new BusinessException(AngelPromiseBizErrorCode.REPEAT_DO);
        }
        AngelWorkShipCancelContext cancelShipContext = AngelPromiseApplicationConverter.instance.convertToAngelWorkShipCancelContext(cancelShipCmd);
        return angelShipDomainService.cancelShip(cancelShipContext);
    }

    /**
     * 取消工单下的运单，并更新工单状态
     *
     * @param angelWorkCancelShipCmd angelWorkCancelShipCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean cancelShipFormWork(AngelWorkCancelShipCmd angelWorkCancelShipCmd, AngelShipCancelCodeStatusEnum angelShipCancelCodeStatusEnum) {
        log.info("AngelWorkApplicationImpl cancelShipFormWork start angelWorkCancelShipCmd={}",JSON.toJSONString(angelWorkCancelShipCmd));
        AssertUtils.nonNull(angelWorkCancelShipCmd, "工单对象不允许为空");
        AssertUtils.hasText(angelWorkCancelShipCmd.getWorkId(), "工单ID不允许为空");
        AssertUtils.nonNull(angelShipCancelCodeStatusEnum, "操作类型不允许为空");
        Integer workType = angelWorkCancelShipCmd.getWorkType();
        Long workId = Long.parseLong(angelWorkCancelShipCmd.getWorkId());
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(workId).build());
        if (workType == null) {
            workType = angelWork != null ? angelWork.getWorkType() : null;
        }
        if (!AngelWorkTypeEnum.RIDER.getType().equals(workType)) {
            log.info("AngelWorkApplicationImpl -> cancelShipFormWork 非运力单,不执行取消");
            return true;
        }
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setWorkId(workId);
        List<AngelShip> ships = angelShipRepository.findList(angelShipDBQuery);
        if (CollUtil.isEmpty(ships)) {
            log.info("AngelWorkApplicationImpl -> cancelShipFormWork 查询运单列表为空, workId={}", workId);
            return true;
        }
        for (AngelShip ship : ships) {
            try {
                if (AngelShipStatusEnum.ORDER_SHIP_CANCEL.getShipStatus().equals(ship.getShipStatus())) {
                    log.info("AngelWorkApplicationImpl -> cancelShipFormWork shipId={}, 已取消不再调用取消", ship.getShipId());
                    continue;
                }

                AngelWorkCancelShipCmd workCancelShipCmd = new AngelWorkCancelShipCmd();
                workCancelShipCmd.setWorkId(String.valueOf(ship.getWorkId()));
                workCancelShipCmd.setShipId(String.valueOf(ship.getShipId()));
                workCancelShipCmd.setOperateSource(angelWorkCancelShipCmd.getOperateSource());
                workCancelShipCmd.setStandCancelCode(angelShipCancelCodeStatusEnum.getType());
                //NOTE 取消运单
                Boolean ret = this.cancelShip(workCancelShipCmd);
                if (!Boolean.TRUE.equals(ret)) {
                    throw new BusinessException(AngelPromiseBizErrorCode.CANCEL_SHIP_ERROR);
                }
                // 更新工单状态，否则重新呼叫运单会被拦截
                angelWorkRepository.updateAngelWorkStatus(AngelWorkStatusDbCmd.builder().workStatus(AngelWorkStatusEnum.WAIT_RECEIVE.getType()).workId(workId).build());
                AngelTaskStatusCmd angelTaskStatusCmd = new AngelTaskStatusCmd();
                angelTaskStatusCmd.setWorkId(workId);
                angelTaskStatusCmd.setTaskStatus(AngelTaskStatusEnum.INIT.getType());
                angelTaskStatusCmd.setBizExtStatus(AngelBizExtStatusEnum.INIT_STATUS.getType());
                angelTaskRepository.updateStatusByWorkId(angelTaskStatusCmd);

                Date now = new Date();
                AngelWorkHistory angelWorkHistory = AngelWorkHistory.builder()
                        .workId(workId)
                        .operateEvent(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_WAIT_RECEIVED.getCode())
                        .beforeStatus(angelWork.getWorkStatus())
                        .afterStatus(AngelWorkStatusEnum.WAIT_RECEIVE.getType())
                        .creator(angelWorkCancelShipCmd.getOperator())
                        .operateTime(now)
                        .createTime(now)
                        .yn(YnStatusEnum.YES.getCode())
                        .build();
                angelWorkHistoryRepository.save(angelWorkHistory);
            } catch (Exception e) {
                log.error("AngelWorkApplicationImpl -> cancelShipFormWork 取消运单失败", e);
                throw new BusinessException(AngelPromiseBizErrorCode.CANCEL_SHIP_ERROR);
            }
        }
        return true;
    }

    /**
     * 重新呼叫工单下的运单，并更新工单状态
     *
     * @param angelWorkReCallShipCmd angelWorkReCallShipCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean reCallShipFormWork(AngelWorkReCallShipCmd angelWorkReCallShipCmd, AngelShipCancelCodeStatusEnum angelShipCancelCodeStatusEnum) {
        log.info("AngelWorkApplicationImpl cancelShipFormWork start reCallShipFormWork={}",JSON.toJSONString(angelWorkReCallShipCmd));
        AssertUtils.nonNull(angelWorkReCallShipCmd, "工单对象不允许为空");
        AssertUtils.hasText(angelWorkReCallShipCmd.getWorkId(), "工单ID不允许为空");
        AssertUtils.nonNull(angelShipCancelCodeStatusEnum, "操作类型不允许为空");
        Long workId = Long.parseLong(angelWorkReCallShipCmd.getWorkId());
        // 如果已存在运单，先取消原有运单
        AngelWorkCancelShipCmd angelWorkCancelShipCmd = new AngelWorkCancelShipCmd();
        angelWorkCancelShipCmd.setWorkId(angelWorkReCallShipCmd.getWorkId());
        angelWorkCancelShipCmd.setShipId(angelWorkReCallShipCmd.getShipId());
        angelWorkCancelShipCmd.setCancelReasonId(angelWorkReCallShipCmd.getCancelReasonId());
        angelWorkCancelShipCmd.setCancelReason(angelWorkReCallShipCmd.getCancelReason());
        angelWorkCancelShipCmd.setOperator(angelWorkReCallShipCmd.getOperator());
        angelWorkCancelShipCmd.setOperateSource(angelWorkReCallShipCmd.getOperateSource());
        angelWorkCancelShipCmd.setWorkType(angelWorkReCallShipCmd.getWorkType());
        cancelShipFormWork(angelWorkCancelShipCmd, angelShipCancelCodeStatusEnum);

        // 查询工单信息
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setWorkIds(Lists.newArrayList(workId));
        angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
        AngelWork angelWorkSnapShot = angelWorkRepository.findAngelWork(angelWorkDBQuery);;
        // 非运单类型不执行呼叫,调用取消会反查工单的类型
        if (!AngelWorkTypeEnum.RIDER.getType().equals(angelWorkSnapShot.getWorkType())) {
            log.info("AngelWorkApplicationImpl -> reCallShipFormWork 非运力单,不执行取消");
            return true;
        }
        // 重新呼叫运单
        log.info("AngelWorkApplicationImpl -> reCallShipFormWork 骑手重新呼叫");
        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setWorkId(workId);
        List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
        //任务分组
        Map<String, AngelTask> angelMap = angelTaskList.stream().collect(Collectors.toMap(AngelTask::getPatientId, Function.identity(), (k1, k2) -> k1));
        log.info("AngelWorkApplicationImpl -> reCallShipFormWork,angelMap={}", com.jd.fastjson.JSON.toJSONString(angelMap));

        //查询派出的实验室
        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        Long promiseId = angelWorkReCallShipCmd.getPromiseId();
        if (promiseId == null) {
            AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(workId).build());
            promiseId = angelWork != null ? angelWork.getPromiseId() : null;
        }
        medicalPromiseListRequest.setPromiseId(promiseId);
        medicalPromiseListRequest.setInvalid(Boolean.FALSE);
        List<MedicalPromiseDTO> jdhMedicalPromiseQueryVos = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
        Map<String, List<MedicalPromiseDTO>> promiseGroup = Optional.ofNullable(jdhMedicalPromiseQueryVos).map(List::stream).orElseGet(Stream::empty)
                .collect(Collectors.groupingBy(MedicalPromiseDTO::getStationId));
        log.info("AngelWorkApplicationImpl -> reCallShipFormWork,promiseGroup={}", com.jd.fastjson.JSON.toJSONString(promiseGroup));
        //呼叫运力
        promiseGroup.forEach((k, v) -> {
            //运单扩展信息
            List<ShipTask> shipTaskList = org.apache.commons.compress.utils.Lists.newArrayList();
            Map<Long, MedicalPromiseDTO> promisePatientMap = v.stream().collect(Collectors.toMap(MedicalPromiseDTO::getPromisePatientId, Function.identity(), (k1, k2) -> k1));
            promisePatientMap.values().forEach(item -> {
                ShipTask shipTask = new ShipTask();
                AngelTask angelTask = angelMap.get(String.valueOf(item.getPromisePatientId()));
                shipTask.setTaskId(angelTask.getTaskId());
                shipTask.setPromisePatientId(item.getPromisePatientId());
                shipTaskList.add(shipTask);
            });
            log.info("AngelWorkApplicationImpl -> reCallShipFormWork,shipTaskList={}", com.jd.fastjson.JSON.toJSONString(shipTaskList));

            //组装运单备注信息
            StringBuffer buff = new StringBuffer();
            try{
                //处理采样管的数量
                assert jdhMedicalPromiseQueryVos != null;
                Map<String, Long> medicalPromiseItemGroup = jdhMedicalPromiseQueryVos.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getServiceItemId, Collectors.counting()));

                Map<String, Integer> info = Maps.newHashMap();
                medicalPromiseItemGroup.forEach((key, value) -> {
                    ServiceItemExtQuery serviceItemExtQuery = new ServiceItemExtQuery();
                    serviceItemExtQuery.setItemIds(Sets.newHashSet(Long.valueOf(key)));
                    serviceItemExtQuery.setStationId(k);
                    List<ServiceItemDto> serviceItemDtos = productServiceItemExtApplication.queryServiceItemList(serviceItemExtQuery);
                    Optional.ofNullable(serviceItemDtos).map(List::stream).orElseGet(Stream::empty)
                            .forEach(materialL -> {
                                List<ItemMaterialPackageRelCmd> materialList = materialL.getMaterialList();
                                for (ItemMaterialPackageRelCmd serviceItemMaterialPackageVo : materialList) {
                                    if (info.containsKey(serviceItemMaterialPackageVo.getMaterialPackageName())) {
                                        info.put(serviceItemMaterialPackageVo.getMaterialPackageName(),
                                                info.get(serviceItemMaterialPackageVo.getMaterialPackageName()) + medicalPromiseItemGroup.get(String.valueOf(materialL.getItemId())).intValue());
                                    } else {
                                        info.put(serviceItemMaterialPackageVo.getMaterialPackageName(),
                                                medicalPromiseItemGroup.get(String.valueOf(materialL.getItemId())).intValue());
                                    }
                                }
                            });
                });

                info.forEach((name, material) -> buff.append(name).append(":").append(material).append("件;"));
                log.info("[AngelWorkApplicationImpl -> reCallShipFormWorkl], 骑手备注:{}", buff.toString());
            }catch (Exception ex) {
                log.error("[AngelWorkApplicationImpl -> reCallShipFormWork],查询项目的耗材信息异常!");
            }


            AngelWorkShipCreateContext shipCreateContext = new AngelWorkShipCreateContext();
            shipCreateContext.setVerticalCode(angelWorkSnapShot.getVerticalCode());
            shipCreateContext.setServiceType(angelWorkSnapShot.getServiceType());
            shipCreateContext.setWorkId(angelWorkSnapShot.getWorkId());
            shipCreateContext.setShopNo(mappingShopNo(v.get(0).getAngelStationId()));
            //NOTE 对应运单表中的实验室id字段
            shipCreateContext.setProviderShopNo(k);
            shipCreateContext.setIsPrepay(CommonConstant.ZERO);

            if(CollectionUtils.isEmpty(v.get(0).getDeliveryStepFlow())||v.get(0).getDeliveryStepFlow().size()<=1){
                log.info("[WorkCreateBizCheckService -> call], 不走无人机逻辑,只有达达运单");
                shipCreateContext.setReceiverAddress(v.get(0).getStationAddress());
                shipCreateContext.setReceiverName(v.get(0).getStationName());
                shipCreateContext.setReceiverPhone(v.get(0).getStationPhone());
            }else {
                //无人机
                log.info("[WorkCreateBizCheckService -> call], 走无人机逻辑,传接驳点地址");
                shipCreateContext.setReceiverAddress(v.get(0).getDeliveryStepFlow().get(0).getEndAddress());
                UavConfig.UavFlightInfo uavFlightInfo = duccConfig.getUavConfig().getUavFlightInfoByFlightId(v.get(0).getDeliveryStepFlow().get(1).getThirdStationId(),v.get(0).getDeliveryStepFlow().get(1).getThirdStationTargetId());
                shipCreateContext.setReceiverName(uavFlightInfo.getReceiverName());
                shipCreateContext.setReceiverPhone(uavFlightInfo.getReceiverPhone());
            }

            shipCreateContext.setCargoWeight(0.1);

            shipCreateContext.setSupplierAddress(angelWorkSnapShot.getJdhAngelWorkExtVo().getAngelOrder().getFullAddress());
            shipCreateContext.setSupplierPhone(angelWorkSnapShot.getJdhAngelWorkExtVo().getAngelOrder().getAppointPhone());
            shipCreateContext.setSupplierName(angelWorkSnapShot.getJdhAngelWorkExtVo().getAngelOrder().getAppointName());

            shipCreateContext.setAngelType(AngelTypeEnum.DELIVERY.getType());

            boolean verticalCodeBool = serviceHomeTypeDomainService.getServiceHomeVerticalCode("XFYL_HOME_SELF_TEST_TRANSPORT").equals(angelWorkSnapShot.getVerticalCode());
            if(verticalCodeBool){
                shipCreateContext.setDeliveryType(DeliveryTypeEnum.JD_LOGISTICS_DELIVERY.getType());
                shipCreateContext.setAngelDetailType(AngelDetailTypeEnum.getTypeByDelivery(DeliveryTypeEnum.JD_LOGISTICS_DELIVERY.getType()));
                shipCreateContext.setCargoNum(v.size());
                JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(angelWorkSnapShot.getPromiseId()).build());
                shipCreateContext.setSupplierAddress(jdhPromise.getStore().getStoreAddr());
                shipCreateContext.setSupplierPhone(jdhPromise.getAppointmentPhone());
                shipCreateContext.setSupplierName(jdhPromise.findExtend(PromiseExtendKeyEnum.APPOINTMENT_USER_NAME.getFiledKey()).getValue());
            }else{
                JdhStation jdhStation =  jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(Long.parseLong(v.get(0).getAngelStationId())).build());
                shipCreateContext.setDeliveryType(SupplierTypeEnum.getDelivery(jdhStation.getDeliverySupplier()));
                shipCreateContext.setAngelDetailType(AngelDetailTypeEnum.getTypeByDelivery(SupplierTypeEnum.getDelivery(jdhStation.getDeliverySupplier())));
            }
            shipCreateContext.setIsDirectDelivery(CommonConstant.ZERO);
            shipCreateContext.setAngelWork(angelWorkSnapShot);
            shipCreateContext.setInfo(buff.toString());
            shipCreateContext.setShipTaskList(shipTaskList);
            shipCreateContext.setInvokeRecall(false);

            shipCreateContext.setAngelStationId(v.get(0).getAngelStationId());
            shipCreateContext.setMedicalPromiseIds(v.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList()));

            AngelShip angelShip = angelShipDomainService.createAngelShip(shipCreateContext);
            angelWorkSnapShot.setPlanCallTime(angelShip.getPlanCallTime());

            angelWorkRepository.save(angelWorkSnapShot);
            angelWorkRepository.updateAngelWorkStatus(AngelWorkStatusDbCmd.builder().workId(angelWorkSnapShot.getWorkId()).workStopStatus(angelWorkSnapShot.getStopStatus()).workStatus(AngelWorkStatusEnum.WAIT_RECEIVE.getType()).build());
        });
        return true;
    }

    /**
     * 统计工单数量
     *
     * @param angelWorkCountQuery
     * @return
     */
    @Override
    public AngelWorkCountDto countAngelWorkCount(AngelWorkCountQuery angelWorkCountQuery) {
        if(DateUtils.truncatedCompareTo(angelWorkCountQuery.getCountEndDate(), angelWorkCountQuery.getCountStartDate(), Calendar.YEAR) >= 1){
            log.error("[AngelWorkApplicationImpl.countAngelWorkCount],参数检查失败!时间跨度大于一年.angelWorkCountQuery={}", JSON.toJSONString(angelWorkCountQuery));
            throw new BusinessException(AngelPromiseBizErrorCode.TRUNCATED_GREATER_THAN_YEARS);
        }

        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setAngelIds(Objects.nonNull(angelWorkCountQuery.getAngelId()) ? Lists.newArrayList(String.valueOf(angelWorkCountQuery.getAngelId())) : null);
        angelWorkDBQuery.setAngelPin(angelWorkCountQuery.getAngelPin());
        angelWorkDBQuery.setCreateStartTime(angelWorkCountQuery.getCountStartDate());
        angelWorkDBQuery.setCreateEndTime(angelWorkCountQuery.getCountEndDate());
        List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);

        if(CollectionUtils.isEmpty(angelWorkList)){
            return AngelWorkCountDto.builder()
                    .angelPin(angelWorkCountQuery.getAngelPin())
                    .angelId(Objects.nonNull(angelWorkCountQuery.getAngelId()) ? String.valueOf(angelWorkCountQuery.getAngelId()) : null)
                    .servedWorkCount(CommonConstant.ZERO)
                    .settledWorkCount(CommonConstant.ZERO)
                    .unServedWorkCount(CommonConstant.ZERO)
                    .build();
        }

        //计算未服务订单量
        long unServedCount = angelWorkList.stream().filter(work -> AngelWorkStatusEnum.RECEIVED.getType().equals(work.getWorkStatus())).count();

        //计算已服务订单量
        long servedCount = angelWorkList.stream().filter(work -> AngelWorkStatusEnum.COMPLETED.getType().equals(work.getWorkStatus())).count();

        return AngelWorkCountDto.builder()
                .angelPin(angelWorkList.get(0).getAngelPin())
                .angelId(String.valueOf(angelWorkList.get(0).getAngelId()))
                .unServedWorkCount((int) unServedCount)
                .servedWorkCount((int) servedCount)
                .settledWorkCount(CommonConstant.ZERO)
                .build();
    }

    /**
     * 查询护士实时位置
     *
     * @param angelWork
     * @return
     */
    @Override
    public AngelRealLocationDto queryAngelRealLocation(AngelWork angelWork) {
        if(Objects.equals(angelWork.getWorkType(), AngelWorkTypeEnum.RIDER.getType())) {
            log.error("[AngelWorkApplicationImpl -> queryAngelRealLocation],非护士模式无实时位置！angelWork={}", JSON.toJSONString(angelWork));
            return null;
        }
        AngelRealLocationDto angelRealLocationDto = AngelRealLocationDto.builder()
                .angelWorkId(String.valueOf(angelWork.getWorkId()))
                .angelPin(angelWork.getAngelPin())
                .angelId(angelWork.getAngelId())
                .angelName(angelWork.getAngelName())
                .angelHeadImg(angelWork.getAngelHeadImg())
                .workStatus(angelWork.getWorkStatus())
                .build();

        AngelLocationDto location = angelLocationApplication.getLocation(Long.valueOf(angelWork.getAngelId()));
        if(Objects.isNull(location)){
            return angelRealLocationDto;
        }
        angelRealLocationDto.setLat(location.getLatitude());
        angelRealLocationDto.setLng(location.getLongitude());
        return angelRealLocationDto;
    }

    /**
     * 查询护士轨迹
     *
     * @param angelRealLocationQuery
     * @return
     */
    @Override
    public AngelRealTrackDto queryAngelRealTrack(AngelRealLocationQuery angelRealLocationQuery) {
        AssertUtils.nonNull(angelRealLocationQuery, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(angelRealLocationQuery.getPromiseId(), BusinessErrorCode.ILLEGAL_ARG_ERROR);
        if(AngelTypeEnum.DELIVERY.getType().equals(angelRealLocationQuery.getAngelType())
                && StringUtils.isBlank(angelRealLocationQuery.getShipId())) {
            log.error("[AngelWorkApplicationImpl -> queryAngelRealTrack],骑手轨迹时运单号不能为空!angelRealLocationQuery={}",JSON.toJSONString(angelRealLocationQuery));
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        //查询护士经纬度
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(Long.valueOf(angelRealLocationQuery.getPromiseId()));
        angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
        List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
        log.info("AngelWorkApplicationImpl queryAngelRealTrack angelWorkList={}", JSON.toJSONString(angelWorkList));
        if(CollectionUtils.isEmpty(angelWorkList)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        AngelWork angelWork = angelWorkList.get(0);

        //获取服务者实时位置信息
        AngelShipTrackContext angelShipTrackContext = AngelPromiseApplicationConverter.instance.convertToAngelShipTrackContext(angelRealLocationQuery);
        angelShipTrackContext.setVerticalCode(angelWork.getVerticalCode());
        angelShipTrackContext.setServiceType(angelWork.getServiceType());
        angelShipTrackContext.setWorkId(angelWork.getWorkId());
        angelShipTrackContext.setAngelId(angelWork.getAngelId());
        angelShipTrackContext.setAngelPin(angelWork.getAngelPin());
        if(StringUtils.isNotBlank(angelRealLocationQuery.getShipId())){
            AngelShip angelShip = angelShipRepository.find(new AngelShipIdentifier(Long.valueOf(angelRealLocationQuery.getShipId())));
            angelShipTrackContext.setShipId(angelShip.getShipId());
            angelShipTrackContext.setOutShipId(angelShip.getOutShipId());
            angelShipTrackContext.setAngelPhone(angelShip.getTransferPhone());
            angelShipTrackContext.setShipStatus(angelShip.getShipStatus());
            angelShipTrackContext.setShipStatusShortDesc(AngelShipStatusEnum.getStatusShortDesc(angelShip.getShipStatus()));
            angelShipTrackContext.setEstimateGrabTime(TimeUtils.dateTimeToStr(angelShip.getEstimateGrabTime()));
            angelShipTrackContext.setEstimatePickUpTime(TimeUtils.dateTimeToStr(angelShip.getEstimatePickUpTime()));
            angelShipTrackContext.setEstimateReceiveTime(TimeUtils.dateTimeToStr(angelShip.getEstimateReceiveTime()));
        }else {
            angelShipTrackContext.setShipStatus(AngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus());
            angelShipTrackContext.setShipStatusShortDesc(AngelShipStatusEnum.getStatusShortDesc(AngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus()));

            JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(angelWork.getVerticalCode());
            if (Arrays.asList(BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_CARE.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode()).contains(verticalBusiness.getBusinessModeCode())){
                JdhSkuRequest jdhSkuRequest = new JdhSkuRequest();
                jdhSkuRequest.setSkuId(new ArrayList<>(angelWork.getAngelOrder().getSkuIds()).get(0));
                JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(jdhSkuRequest);
                String angelType = Objects.requireNonNull(AngelWorkTypeEnum.matchType(verticalBusiness.getBusinessModeCode())).getAngelType();
                String shipStatusShortDesc = angelType + "正在赶来";
                angelShipTrackContext.setShipStatusShortDesc(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(),"angelRealTrack", shipStatusShortDesc));
            }
        }
        AngelRealTrackBo angelRealTrackBo = angelShipDomainService.getTransferRealTrack(angelShipTrackContext);

        if(Objects.isNull(angelRealTrackBo) || Objects.isNull(angelRealTrackBo.getLat())) {
            log.error("[AngelWorkApplicationImpl.queryAngelRealTrack], 服务者位置信息不存在!angelShipTrackContext={}",JSON.toJSONString(angelShipTrackContext));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_STATION_ERROR);
        }

        //查询用户和实验室位置并计算配送时间
        UserAndStationPositionBo userAndStationPosition = angelShipDomainService.getUserAndStationPosition(angelWork, angelShipTrackContext, angelRealTrackBo);
        String timeString = "";
        if(Objects.nonNull(userAndStationPosition)){
            log.error("[AngelWorkApplicationImpl.queryAngelRealTrack],查询到预计到达时间!");
            LocalDateTime localDateTime = TimeUtils.getCurrentLocalDateTime().plusMinutes(userAndStationPosition.getDuration().longValue());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            timeString = localDateTime.format(formatter);
        }else {
            userAndStationPosition = new UserAndStationPositionBo();
            userAndStationPosition.setMode(DirectionServiceRpc.TravelMode.BICYCLING.getMode());
        }

        //解析ETA数据
        AngelRealTrackDto realTrackDto = AngelRealTrackDto.builder()
                .angelPin(StringUtils.isNotBlank(angelRealTrackBo.getAngelPin()) ? angelRealTrackBo.getAngelPin() : angelWork.getAngelPin())
                .angelId(angelRealTrackBo.getAngelId())
                .mode(userAndStationPosition.getMode())
                .angelHeadImg(StringUtils.isNotBlank(angelRealTrackBo.getAngelHeadImg()) ? angelRealTrackBo.getAngelHeadImg() : angelWork.getAngelHeadImg())
                .angelName(StringUtils.isNotBlank(angelRealTrackBo.getAngelName()) ? new UserName(angelRealTrackBo.getAngelName()).mask() : new UserName(angelWork.getAngelName()).mask())
                .angelPhone(new PhoneNumber(angelShipTrackContext.getAngelPhone()).encrypt())
                .distance(userAndStationPosition.getDistance())
                .duration(timeString)
                .angelPosition(userAndStationPosition.getAngelPosition())
                .userPosition(userAndStationPosition.getUserPos())
                .stationPosition(userAndStationPosition.getStationPos())
                .shipStatus(angelShipTrackContext.getShipStatus())
                .estimateDeliveryTimeTip(angelShipTrackContext.getShipStatusShortDesc())
                .estimateGrabTime(angelShipTrackContext.getEstimateGrabTime())
                .estimatePickUpTime(angelShipTrackContext.getEstimatePickUpTime())
                .estimateReceiveTime(angelShipTrackContext.getEstimateReceiveTime())
                .build();

        // 护士轨迹外呼电话
        List<Integer> workStatusList = Arrays.asList(AngelWorkStatusEnum.WAIT_SERVICE.getType(),AngelWorkStatusEnum.SERVICING.getType());
        realTrackDto.setInspectTrackCall(false);
        if (workStatusList.contains(angelWork.getWorkStatus())){
            realTrackDto.setInspectTrackCall(true);
        }
        return realTrackDto;
    }

    /**
     * 运营端查询工单明细
     *
     * @param detailForManQuery
     * @return
     */
    @Override
    public AngelWorkDetailForManDto queryAngelWorkForMan(AngelWorkDetailForManRequest detailForManQuery) {
        AssertUtils.nonNull(detailForManQuery.getPromiseId(), AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);

        log.info("[AngelWorkApplicationImpl.queryAngelWorkForMan],服务者工单!detailForManQuery={}", JSON.toJSONString(detailForManQuery));
        //查询服务者工单
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(detailForManQuery.getPromiseId());
        angelWorkDBQuery.setCreateTimeOrderByAsc(true);
        List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
        if(CollectionUtils.isEmpty(angelWorkList)){
            log.error("[AngelWorkApplicationImpl.queryAngelWorkForMan],服务者工单不存在!detailForManQuery={}", JSON.toJSONString(detailForManQuery));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        AngelWork angelWork = angelWorkList.get(0);
        AngelWorkDetailForManDto rstDto = AngelPromiseApplicationConverter.instance.convertToAngelWorkDetailForManDto(angelWork);
        //查询工单历史
        AngelWorkHistoryDbQuery workHistoryDbQuery = AngelWorkHistoryDbQuery.builder().workId(angelWorkList.get(0).getWorkId()).build();
        List<AngelWorkHistory> workHistoryList = angelWorkHistoryRepository.findList(workHistoryDbQuery);
        rstDto.setWorkHistoryDtoList(AngelPromiseApplicationConverter.instance.convertToAngelWorkHistoryList(workHistoryList));

        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setWorkId(angelWork.getWorkId());
        List<AngelTask> angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
        log.info("[AngelWorkApplicationImpl.queryAngelWorkForMan],服务者工单明细!angelTaskList={}", JSON.toJSONString(angelTaskList));

        //组装服务记录信息
        buildServiceRecord(rstDto, angelWork.getJdhAngelWorkExtVo(), angelTaskList);

        //查询检测单
        if(Objects.nonNull(detailForManQuery.getMedPromiseId())){
            MedicalPromiseRequest medicalPromiseRequest = new MedicalPromiseRequest();
            medicalPromiseRequest.setMedicalPromiseId(detailForManQuery.getMedPromiseId());
            MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(medicalPromiseRequest);
            if(Objects.isNull(medicalPromiseDTO)){
                log.error("[AngelWorkApplicationImpl.queryAngelWorkForMan],检验单信息为空!detailForManQuery={}", JSON.toJSONString(detailForManQuery));
                throw new BusinessException(AngelPromiseBizErrorCode.MEDICAL_PROMISE_NOT_EXIST);
            }

            //查询工单下的运单
            AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
            angelShipDBQuery.setWorkId(angelWorkList.get(0).getWorkId());
            angelShipDBQuery.setReceiverId(medicalPromiseDTO.getStationId());
            List<AngelShipDto> angelShipDtos = AngelPromiseApplicationConverter.instance.convertToAngelShipDtoList(angelShipRepository.findList(angelShipDBQuery));
            rstDto.setShipDtoList(angelShipDtos);

            //查询运单历史信息
            AngelShipHistoryDBQuery historyDBQuery = AngelShipHistoryDBQuery.builder().workId(angelWorkList.get(0).getWorkId()).build();
            List<AngelShipHistory> shipHistoryList = angelShipHistoryRepository.findList(historyDBQuery);
            List<AngelShipHistoryDto> angelShipHistoryDtos = AngelPromiseApplicationConverter.instance.convertToAngelShipHistoryDtoList(shipHistoryList);
            if(CollectionUtils.isEmpty(angelShipHistoryDtos)){
                return rstDto;
            }
            Map<Long, List<AngelShipHistoryDto>> shipGroup = angelShipHistoryDtos.stream().collect(Collectors.groupingBy(AngelShipHistoryDto::getShipId));
            angelShipDtos.stream().forEach(ship -> {
                ship.setShipHistoryDtoList(shipGroup.get(ship.getShipId()));
            });
        }

        log.error("[AngelWorkApplicationImpl.queryAngelWorkForMan],服务者工单!rstDto={}", JSON.toJSONString(rstDto));
        return rstDto;
    }

    /**
     * 构建服务记录信息
     *
     * @param rstDto
     * @param jdhAngelWorkExtVo
     */
    private void buildServiceRecord(AngelWorkDetailForManDto rstDto, JdhAngelWorkExtVo jdhAngelWorkExtVo, List<AngelTask> angelTaskList) {
        if (Objects.isNull(jdhAngelWorkExtVo)){
            return;
        }
        Date endTime =TimeUtils.localDateTimeToDate(LocalDateTime.now().plusHours(CommonConstant.TWO));
        if (CollectionUtils.isNotEmpty(jdhAngelWorkExtVo.getServiceRecordFileIds())){
            GenerateGetUrlCommand command = new GenerateGetUrlCommand();
            command.setFileIds(Sets.newHashSet(jdhAngelWorkExtVo.getServiceRecordFileIds()));
            command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
            command.setIsPublic(Boolean.TRUE);
            command.setExpireTime(endTime);
            List<FilePreSignedUrlDto> urlDtos = fileManageApplication.generateGetUrl(command);
            List<String> medicalCertificateUrls = urlDtos.stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
            rstDto.setServiceRecordPicUrls(medicalCertificateUrls);
        }
        if (CollectionUtils.isNotEmpty(jdhAngelWorkExtVo.getClothingFileIds())){
            GenerateGetUrlCommand command = new GenerateGetUrlCommand();
            command.setFileIds(Sets.newHashSet(jdhAngelWorkExtVo.getClothingFileIds()));
            command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
            command.setIsPublic(Boolean.TRUE);
            command.setExpireTime(endTime);
            List<String> clothongPic = fileManageApplication.generateGetUrl(command).stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
            rstDto.setClothingPicUrls(clothongPic);
        }
        if (CollectionUtils.isNotEmpty(jdhAngelWorkExtVo.getWasteDestroyFileIds())){
            GenerateGetUrlCommand command = new GenerateGetUrlCommand();
            command.setFileIds(Sets.newHashSet(jdhAngelWorkExtVo.getWasteDestroyFileIds()));
            command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
            command.setIsPublic(Boolean.TRUE);
            command.setExpireTime(endTime);
            List<String> wasteUrls = fileManageApplication.generateGetUrl(command).stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
            rstDto.setMedicalWastePicUrls(wasteUrls);
        }

        if(CollectionUtils.isNotEmpty(angelTaskList)){
            List<AngelTaskDetailDto> angelTaskDetailDtoList = new ArrayList<>();
            for (AngelTask angelTask : angelTaskList) {
                AngelTaskDetailDto angelTaskDetailDto =  AngelTaskDetailDto.builder()
                        .taskId(angelTask.getTaskId())
                        .workId(angelTask.getWorkId())
                        .patientId(angelTask.getPatientId())
                        .build();
                angelTaskDetailDtoList.add(angelTaskDetailDto);
            }
            rstDto.setAngelTaskDetailDtoList(angelTaskDetailDtoList);
        }

        // 电子签名图片
        List<String> electSignaturePicUrls = new ArrayList<>();
        // 知情同意书
        List<String> letterOfConsentPicUrls = new ArrayList<>();
        // 就诊记录图片
        List<String> visitRecordPicUrls = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(angelTaskList)){
            angelTaskList.forEach(task->{
                JdhAngelTaskExtVo angelTaskExtVo = task.getJdhAngelTaskExtVo();
                if (Objects.isNull(angelTaskExtVo)){
                    return;
                }
                if (angelTaskExtVo.getElectSignatureImg() != null){
                    GenerateGetUrlCommand command = new GenerateGetUrlCommand();
                    command.setFileIds(Sets.newHashSet(angelTaskExtVo.getElectSignatureImg()));
                    command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
                    command.setIsPublic(Boolean.TRUE);
                    command.setExpireTime(endTime);
                    List<String> electSignatureUrls = fileManageApplication.generateGetUrl(command).stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
                    electSignaturePicUrls.addAll(electSignatureUrls);
                }
                if (angelTaskExtVo.getLetterOfConsentFileId() != null){
                    GenerateGetUrlCommand command = new GenerateGetUrlCommand();
                    command.setFileIds(Sets.newHashSet(angelTaskExtVo.getLetterOfConsentFileId()));
                    command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
                    command.setIsPublic(Boolean.TRUE);
                    command.setExpireTime(endTime);
                    List<String> letterOfConsentUrls = fileManageApplication.generateGetUrl(command).stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
                    letterOfConsentPicUrls.addAll(letterOfConsentUrls);
                }
                if (CollectionUtils.isNotEmpty(angelTaskExtVo.getVisitRecordImg())){
                    GenerateGetUrlCommand command = new GenerateGetUrlCommand();
                    command.setFileIds(new HashSet<>(angelTaskExtVo.getVisitRecordImg()));
                    command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
                    command.setIsPublic(Boolean.TRUE);
                    command.setExpireTime(endTime);
                    List<String> visitRecordUrls = fileManageApplication.generateGetUrl(command).stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
                    visitRecordPicUrls.addAll(visitRecordUrls);
                }
            });
        }
        rstDto.setElectSignaturePicUrls(electSignaturePicUrls);
        rstDto.setLetterOfConsentPicUrls(letterOfConsentPicUrls);
        rstDto.setVisitRecordPicUrls(visitRecordPicUrls);
    }

    /**
     * 服务完成
     *
     * @param angelCheckBarCodeCmd
     * @return
     */
    @Override
    public Boolean finishService(AngelCheckBarCodeCmd angelCheckBarCodeCmd) {
        AssertUtils.nonNull(angelCheckBarCodeCmd.getWorkId(), "工单id不能为空");
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.SERVICE_FINISH, angelCheckBarCodeCmd.getWorkId());
        try{
            if (!redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.SERVICE_FINISH.getExpireTime(), RedisKeyEnum.SERVICE_FINISH.getExpireTimeUnit())) {
                throw new BusinessException(AngelPromiseBizErrorCode.REPEAT_DO);
            }

            //检查数据权限
            authorityCheck(angelCheckBarCodeCmd.getWorkId(), angelCheckBarCodeCmd.getUserPin());

            AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(Long.valueOf(angelCheckBarCodeCmd.getWorkId())));
            if(Objects.isNull(angelWork)){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
            }
            List<AngelTask> angelTaskList = getAngelTasks(angelCheckBarCodeCmd);

            // 查询业务模式
            JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(angelWork.getVerticalCode());

            AngelWorkFinishServiceAbility angelWorkFinishServiceAbility = angelWorkFinishServiceAbilityFactory.createAngelWorkFinishServiceAbility(verticalBusiness.getBusinessModeCode(), angelWork.getServiceType());
            AngelTaskExtStatusContext statusContext = angelWorkFinishServiceAbility.execute(angelWork, angelTaskList);
            return angelTaskDomainService.executeTaskExt(statusContext);
        }finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * 确认全部运单已配送
     *
     * @param angelCheckBarCodeCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean confirmTransferOrderDeliver(AngelCheckBarCodeCmd angelCheckBarCodeCmd) {
        AssertUtils.nonNull(angelCheckBarCodeCmd.getWorkId(), "服务者工单id不能为空");
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.EXECUTE_TASK_CONFIRM_LOCK_KEY, angelCheckBarCodeCmd.getWorkId());
        if (!redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.EXECUTE_TASK_CONFIRM_LOCK_KEY.getExpireTime(), RedisKeyEnum.EXECUTE_TASK_CONFIRM_LOCK_KEY.getExpireTimeUnit())) {
            throw new BusinessException(AngelPromiseBizErrorCode.REPEAT_DO);
        }
        try{
            //检查数据权限
            authorityCheck(angelCheckBarCodeCmd.getWorkId(), angelCheckBarCodeCmd.getUserPin());

            AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(angelCheckBarCodeCmd.getWorkId()));
            if(Objects.isNull(angelWork)){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
            }

            AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
            angelShipDBQuery.setWorkId(angelWork.getWorkId());
            angelShipDBQuery.setStatus(Sets.newHashSet(AngelShipStatusEnum.getSendingStatus()));
            List<AngelShip> angelShipList = angelShipRepository.findList(angelShipDBQuery);

            Integer stationNum = null;
            if (CollectionUtils.isNotEmpty(angelCheckBarCodeCmd.getStationBar())){
                stationNum = angelCheckBarCodeCmd.getStationBar().size();
            }else {
                List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(
                        MedicalPromiseListQuery.builder().promiseId(angelWork.getPromiseId()).freezeQuery(Boolean.FALSE).invalid(Boolean.FALSE).build()
                );
                Set<String> stationBar = medicalPromises.stream().map(MedicalPromise::getStationId).collect(Collectors.toSet());

                //检查所有的实验室是否都配送完成
                if(CollectionUtils.isEmpty(stationBar)){
                    log.error("[AngelWorkApplicationImpl.confirmTransferOrderDeliver],检测点信息不能为空!angelCheckBarCodeCmd=", JSON.toJSONString(angelCheckBarCodeCmd));
                    throw new BusinessException(AngelPromiseBizErrorCode.MEDICAL_PROMISE_STATION_NOT_EXIST);
                }
                stationNum = stationBar.size();
            }

            if(angelShipList.size() != stationNum) {
                log.error("[AngelWorkApplicationImpl.confirmTransferOrderDeliver],存在未配送的检测点!angelCheckBarCodeCmd=", JSON.toJSONString(angelCheckBarCodeCmd));
                throw new BusinessException(AngelPromiseBizErrorCode.EXIST_NOT_DELIVER_STATION);
            }
            //如果该该工单下的运单全部送达，业务扩展状态直接推送到服务完成
            Integer targetStatus;
            boolean allFinish = angelShipList.stream().allMatch(item -> AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus().equals(item.getShipStatus()));
            if(allFinish){
                targetStatus = AngelBizExtStatusEnum.SERVICE_FINISH.getType();
            }else {
                targetStatus = AngelBizExtStatusEnum.SAMPLE_DELIVERING.getType();
            }
            List<AngelTask> angelTaskList = getAngelTasks(angelCheckBarCodeCmd);
            //更新业务状态
            List<AngelTaskExtStateBo> taskExtStateBoList = angelTaskList.stream()
                    .map(task -> AngelTaskExtStateBo.builder()
                            .taskId(task.getTaskId())
                            .taskExtStatus(targetStatus).build())
                    .collect(Collectors.toList());
            AngelTaskExtStatusContext statusContext = AngelTaskExtStatusContext.builder()
                    .workId(angelWork.getWorkId())
                    .angelTaskExtStateBoList(taskExtStateBoList)
                    .build();
            return angelTaskDomainService.executeTaskExt(statusContext);
        }finally {
            redisLockUtil.unLock(lockKey);
        }

    }

    /**
     * 查询工单分配的实验室信息
     *
     * @param angelWorkQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public List<AngelWorkCourierFloorDto> queryWorkStationList(AngelWorkQuery angelWorkQuery) {

        //待派单详情调用接口,走以下逻辑
        if(angelWorkQuery.getDispatchDetailId()!=null){
            log.info("[AngelWorkApplicationImpl.queryWorkStationList] 走派单详情调用逻辑!!!");
            return this.queryDispatchStationList(angelWorkQuery);
        }


        List<AngelWorkCourierFloorDto> workSpecimenDtoRst = Lists.newArrayList();
        AssertUtils.nonNull(angelWorkQuery.getWorkId(), AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        AssertUtils.hasText(angelWorkQuery.getUserPin(), AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        AssertUtils.isNotEmpty(angelWorkQuery.getTaskStationQueryList(), AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        //数据越权校验
        authorityCheck(angelWorkQuery.getWorkId(), angelWorkQuery.getUserPin());

        AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(angelWorkQuery.getWorkId()));
        if(Objects.isNull(angelWork)){
            log.error("[AngelWorkApplicationImpl.queryWorkStationList],服务者工单不存在");
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }

        //查询任务单列表
        List<AngelTaskStationQuery> taskStationQueryList = angelWorkQuery.getTaskStationQueryList();
        List<Long> taskIds = taskStationQueryList.stream().map(AngelTaskStationQuery::getTaskId).collect(Collectors.toList());
        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setWorkId(angelWork.getWorkId());
        angelTaskDBQuery.setTaskIds(taskIds);
        List<AngelTask> taskList = angelTaskRepository.findList(angelTaskDBQuery);
        if(CollectionUtils.isEmpty(taskList)){
            log.error("[AngelWorkApplicationImpl.queryWorkStationList],服务者任务单为空");
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_TASK_NOT_EXIST);
        }

        //查询检测单信息
        Long promiseId = angelWork.getPromiseId();
        List<MedicalPromiseDTO> medicalPromiseDTOS = queryMedicalPromise(promiseId, taskList.stream().map(task -> Long.valueOf(task.getPatientId())).collect(Collectors.toList()));
        if(CollectionUtils.isEmpty(medicalPromiseDTOS)) {
            log.error("[AngelWorkApplicationImpl.queryWorkStationList],检测单信息不存在!");
            throw new BusinessException(AngelPromiseBizErrorCode.MEDICAL_PROMISE_NOT_EXIST);
        }
        List<MedicalPromiseDTO> filterList = medicalPromiseDTOS.stream().filter(item -> StringUtils.isNotBlank(item.getStationId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filterList)) {
            log.error("[AngelWorkApplicationImpl.queryWorkStationList],检测单有未派实验室的项目!filterList={}", JSON.toJSONString(filterList));
            throw new BusinessException(AngelPromiseBizErrorCode.MEDICAL_STATION_NOT_DISPATCH_ERROR);
        }
        Map<String, List<MedicalPromiseDTO>> medicalPromiseMap = medicalPromiseDTOS.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getStationId));

        //查询运单
        Map<String, AngelShip> validShipMap;
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setWorkId(angelWork.getWorkId());
        angelShipDBQuery.setReceiverIds(medicalPromiseMap.keySet());
        List<AngelShip> shipList = angelShipRepository.findList(angelShipDBQuery);
        List<AngelShip> validShip = shipList.stream().filter(item -> AngelShipStatusEnum.checkForwardStatus(item.getShipStatus())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(validShip)) {
            validShipMap = validShip.stream().collect(Collectors.toMap(AngelShip::getReceiverId, Function.identity(), (k1, k2)-> k1));
        }else {
            validShipMap = Maps.newHashMap();
        }


        AngelTask angelTask = taskList.stream().filter(p -> !Objects.equals(AngelBizExtStatusEnum.CHOOSE_DELIVERY_WAY.getType(), p.getBizExtStatus())).findFirst().orElse(null);

        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(promiseId);
        PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
        JdOrder jdOrder = jdOrderApplication.queryJdOrderAndItemExt(Long.valueOf(promiseDto.getSourceVoucherId()));

        medicalPromiseMap.forEach((k, v) -> {
            AngelShip angelShip = Objects.isNull(validShipMap.get(k)) ? null : validShipMap.get(k);
            AngelWorkCourierFloorDto angelWorkSpecimenDto = swapSpecimenDto(v, angelShip, angelWork,Objects.isNull(angelTask),jdOrder,promiseDto);
            workSpecimenDtoRst.add(angelWorkSpecimenDto);
        });
        workSpecimenDtoRst.sort(Comparator.comparing(AngelWorkCourierFloorDto::getStationDeliveryType).reversed());
        return workSpecimenDtoRst;
    }

    /**
     * 查询派单分配的实验室信息 (待接单页面查询,此时还没有产生angelWork)
     *
     * @param angelWorkQuery
     * @return
     */
    @Override
    public List<AngelWorkCourierFloorDto> queryDispatchStationList(AngelWorkQuery angelWorkQuery) {
        List<AngelWorkCourierFloorDto> workSpecimenDtoRst = Lists.newArrayList();
        AssertUtils.nonNull(angelWorkQuery.getDispatchDetailId(), AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        AssertUtils.hasText(angelWorkQuery.getUserPin(), AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        //数据越权校验
        JdhDispatchDetail jdhDispatchDetail = authorityCheck2(angelWorkQuery.getDispatchDetailId(), angelWorkQuery.getUserPin());

        JdhDispatch jdhDispatch = dispatchRepository.find(JdhDispatchIdentifier.builder().dispatchId(jdhDispatchDetail.getDispatchId()).build());
        if(Objects.isNull(jdhDispatch)){
            log.error("[AngelWorkApplicationImpl.queryWorkStationList],派单信息不存在");
            throw new BusinessException(DispatchErrorCode.DISPATCH_NOT_EXIST);
        }


        //查询检测单信息
        Long promiseId = jdhDispatch.getPromiseId();
        //查询检测单明细
        MedicalPromiseListRequest promiseListRequest = new MedicalPromiseListRequest();
        promiseListRequest.setPromiseId(promiseId);
        promiseListRequest.setInvalid(false);
        List<MedicalPromiseDTO> medicalPromiseDTOS = medicalPromiseApplication.queryMedicalPromiseList(promiseListRequest);
        if(CollectionUtils.isEmpty(medicalPromiseDTOS)) {
            log.error("[AngelWorkApplicationImpl.queryWorkStationList],检测单信息不存在!");
            throw new BusinessException(AngelPromiseBizErrorCode.MEDICAL_PROMISE_NOT_EXIST);
        }
        List<MedicalPromiseDTO> filterList = medicalPromiseDTOS.stream().filter(item -> StringUtils.isNotBlank(item.getStationId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filterList)) {
            log.error("[AngelWorkApplicationImpl.queryWorkStationList],检测单有未派实验室的项目!filterList={}", JSON.toJSONString(filterList));
            throw new BusinessException(AngelPromiseBizErrorCode.MEDICAL_STATION_NOT_DISPATCH_ERROR);
        }
        Map<String, List<MedicalPromiseDTO>> medicalPromiseMap = medicalPromiseDTOS.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getStationId));

        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(promiseId);
        PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
        JdOrder jdOrder = jdOrderApplication.queryJdOrderAndItemExt(Long.valueOf(promiseDto.getSourceVoucherId()));


        medicalPromiseMap.forEach((k, v) -> {
            //NOTE 组转实验室信息
            AngelWorkCourierFloorDto angelWorkSpecimenDto = swapSpecimenDto2(v,jdhDispatch,jdOrder,promiseDto);
            workSpecimenDtoRst.add(angelWorkSpecimenDto);
        });

        workSpecimenDtoRst.sort(Comparator.comparing(AngelWorkCourierFloorDto::getStationDeliveryType).reversed());

        return workSpecimenDtoRst;
    }

    @Override
    @LogAndAlarm
    public AngelTrackDto getTransferTrack(AngelTrackQuery angelTrackQuery) {
        log.info("[AngelWorkApplicationImpl -> getTransferTrack], angelTrackQuery={}", JSON.toJSONString(angelTrackQuery));
        AssertUtils.nonNull(angelTrackQuery, "入参不能为空!");
        AssertUtils.nonNull(angelTrackQuery.getPromiseId(), "履约单不能为空!");

        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(angelTrackQuery.getPromiseId());
        angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
        AngelWork angelWork = angelWorkRepository.findAngelWork(angelWorkDBQuery);
        if(Objects.isNull(angelWork)){
            log.error("[AngelWorkApplicationImpl -> getTransferTrack],服务者工单不存在");
            return null;
        }
        angelTrackQuery.setWorkId(angelWork.getWorkId());
        if(Objects.equals(angelWork.getWorkType(), AngelWorkTypeEnum.RIDER.getType())) {
            AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
            angelShipDBQuery.setWorkId(angelWork.getWorkId());
            List<AngelShip> angelShipList = angelShipRepository.findList(angelShipDBQuery);
            if(CollectionUtils.isEmpty(angelShipList)){
                log.error("[AngelWorkApplicationImpl -> getTransferTrack],运单信息不存在!");
                throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
            }
            angelTrackQuery.setShipId(angelShipList.get(0).getShipId());
            angelTrackQuery.setOutShipId(angelShipList.get(0).getOutShipId());
            angelTrackQuery.setShipStatus(angelShipList.get(0).getShipStatus());
            angelTrackQuery.setDeliveryType(angelShipList.get(0).getType());
        }

        AngelWorkTypeEnum enumByCode = AngelWorkTypeEnum.getEnumByCode(angelWork.getWorkType());
        Integer angelType = Objects.nonNull(angelTrackQuery.getAngelType()) ? angelTrackQuery.getAngelType() : enumByCode.getAngelTypeEnum().getType();

        AngelWorkTransferTrackContext trackContext = AngelWorkTransferTrackContext.builder()
                .angelWork(angelWork)
                .promiseId(angelWork.getPromiseId())
                .workId(angelWork.getWorkId())
                .shipId(angelTrackQuery.getShipId())
                .deliveryType(angelTrackQuery.getDeliveryType())
                .angelType(angelType)
                .angelDetailType(DeliveryTypeEnum.fetchAngelDetailType(angelTrackQuery.getDeliveryType()))
                .angelId(angelWork.getAngelId())
                .outShipId(angelTrackQuery.getOutShipId())
                .build();
        trackContext.setVerticalCode(angelWork.getVerticalCode());
        trackContext.setServiceType(angelWork.getServiceType());
        trackContext.setShipStatus(angelTrackQuery.getShipStatus());
        trackContext.setAngelDetailType(angelTrackQuery.getAngelDetailType());
        AngelShipTrack transferTrack = angelShipDomainService.getTransferTrack(trackContext);
        log.info("[AngelWorkApplicationImpl -> getTransferTrack],transferTrack={}", JSON.toJSONString(transferTrack));
        if(Objects.isNull(transferTrack) || StringUtils.isBlank(transferTrack.getTrackUrl())){
            return null;
        }
        return new AngelTrackDto(transferTrack.getTrackUrl());
    }

    @Override
    @LogAndAlarm(jKey = "MedicalEventConsumer.medicalFreeze")
    public Boolean angelWorkFreeze(List<AngelTask> angelTaskList) {
        if(CollectionUtils.isEmpty(angelTaskList)) {
            log.error("[AngelWorkApplicationImpl -> angelWorkFreeze],任务单信息不存在!");
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
        }

        Set<Long> taskIdSet = angelTaskList.stream().map(AngelTask::getTaskId).collect(Collectors.toSet());
        angelTaskList.forEach(task -> task.setStopStatus(AngelWorkStopStatusEnum.REFUND_STOP.getStatus()));
        angelTaskRepository.batchSave(angelTaskList);

        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setWorkId(angelTaskList.get(0).getWorkId());
        List<AngelTask> angelTaskList1 = angelTaskRepository.findList(angelTaskDBQuery);

        List<AngelTask> angelTasks = angelTaskList1.stream()
                .filter(task -> !taskIdSet.contains(task.getTaskId()))
                .filter(task -> Objects.equals(AngelWorkStopStatusEnum.INIT.getStatus(), task.getStopStatus()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(angelTasks)){
            AngelWorkStatusDbCmd angelWorkStatusDbCmd = AngelWorkStatusDbCmd.builder()
                    .workStopStatus(AngelWorkStopStatusEnum.REFUND_STOP.getStatus())
                    .workId(angelTaskList.get(0).getWorkId())
                    .build();
            angelWorkRepository.updateAngelWorkStatus(angelWorkStatusDbCmd);
        }

        //取消运单
//        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
//        angelShipDBQuery.setWorkId(angelTaskList.get(0).getWorkId());
//        List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);
//        if(CollectionUtils.isEmpty(list)){
//            log.info("[AngelWorkEventConsumer.medicalFreeze],当前工单下没有运单信息,不需要取消运单!angelTaskList={}", JSON.toJSONString(angelTaskList));
//            return Boolean.FALSE;
//        }
//
//        Set<String> patientIdSet = angelTaskList.stream().map(AngelTask::getPatientId).collect(Collectors.toSet());
//        list.forEach(ship -> {
//            //闪送供应商，如果是运单的状态是送检中，不需要取消运单
//            if (DeliveryTypeEnum.SHANSONG_DELIVERY.getType().equals(ship.getType())
//                    && AngelShipStatusEnum.matchNoNeedCancel(ship.getShipStatus())) {
//                log.info("[AngelWorkEventConsumer.medicalFreeze],当前运单不需要取消!ship={}", JSON.toJSONString(ship));
//                return;
//            }
//
//            //检查供应商运单下是否还有其他待配送的项目
//            String receiverId = ship.getReceiverId();
//            if(medicalPromiseApplication.checkProgressMedPromise(receiverId, patientIdSet)) {
//                log.error("[AngelWorkApplicationImpl -> angelWorkInvalid],存在进行中检测单,不能取消运单!");
//                return;
//            }
//
//            AngelWorkShipCancelContext cancelShipContext = AngelWorkShipCancelContext.builder()
//                    .workId(angelTaskList.get(0).getWorkId())
//                    .shipId(ship.getShipId())
//                    .cancelReasonId(CommonConstant.FOUR)
//                    .cancelReason("顾客取消订单")
//                    .operator("system")
//                    .build();
//            try{
//                angelShipDomainService.cancelShip(cancelShipContext);
//            } catch (BusinessException e) {
//                if (e.getErrorCode() != null && (e.getErrorCode().getCode().equalsIgnoreCase(AngelPromiseBizErrorCode.SHIP_STATUS_CAN_NOT_CANCEL.getCode()))) {
//                    // 正常无法取消的运单抛出异常，忽略异常，不影响退款逻辑执行
//                    log.info("取消运单异常忽略错误码, code={}", e.getErrorCode().getCode());
//                } else {
//                    throw e;
//                }
//            }
//        });
        return Boolean.TRUE;
    }

    /**
     * 作废服务者工单
     *
     * @param angelTaskList
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "MedicalEventConsumer.medicalInvalid")
    public Boolean angelWorkInvalid(List<AngelTask> angelTaskList) {
        List<AngelTaskStateBo> angelTaskStateBoList = Lists.newArrayList();
        angelTaskList.stream().forEach(task -> {
            AngelTaskStateBo angelTaskStateBo = AngelTaskStateBo.builder()
                    .taskId(task.getTaskId())
                    .taskStatus(AngelTaskStatusEnum.REFUND.getType())
                    .build();
            angelTaskStateBoList.add(angelTaskStateBo);
        });
        AngelTaskStatusContext angelTaskStatusContext = AngelTaskStatusContext.builder()
                .workId(angelTaskList.get(0).getWorkId())
                .angelTaskStateBoList(angelTaskStateBoList)
                .angelTaskEventTypeEnum(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_REFUND_SERVED)
                .build();
        angelTaskDomainService.executeTask(angelTaskStatusContext);

//        try {
//            //取消运单
//            AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
//            angelShipDBQuery.setWorkId(angelTaskList.get(0).getWorkId());
//            List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);
//            if(CollectionUtils.isEmpty(list)){
//                log.info("[AngelWorkApplicationImpl -> angelWorkInvalid],当前工单下没有运单信息,不需要取消运单!angelTaskList={}", JSON.toJSONString(angelTaskList));
//                return Boolean.FALSE;
//            }
//            Set<String> patientIdSet = angelTaskList.stream().map(AngelTask::getPatientId).collect(Collectors.toSet());
//            list.forEach(ship -> {
//                //闪送供应商，如果是运单的状态是送检中，不需要取消运单
//                if (DeliveryTypeEnum.SHANSONG_DELIVERY.getType().equals(ship.getType())
//                        && AngelShipStatusEnum.matchNoNeedCancel(ship.getShipStatus())) {
//                    log.info("[AngelWorkApplicationImpl -> angelWorkInvalid],当前运单不需要取消!ship={}", JSON.toJSONString(ship));
//                    return;
//                }
//
//                //检查供应商运单下是否还有其他待配送的项目
//                String receiverId = ship.getReceiverId();
//                if(medicalPromiseApplication.checkProgressMedPromise(receiverId, patientIdSet)) {
//                    log.error("[AngelWorkApplicationImpl -> angelWorkInvalid],存在进行中检测单,不能取消运单!");
//                    return;
//                }
//
//                AngelWorkShipCancelContext cancelShipContext = AngelWorkShipCancelContext.builder()
//                        .workId(angelTaskList.get(0).getWorkId())
//                        .shipId(ship.getShipId())
//                        .cancelReasonId(CommonConstant.FOUR)
//                        .cancelReason("顾客取消订单")
//                        .operator("system")
//                        .build();
//                try{
//                    angelShipDomainService.cancelShip(cancelShipContext);
//                } catch (BusinessException e) {
//                    if (e.getErrorCode() != null && (e.getErrorCode().getCode().equalsIgnoreCase(AngelPromiseBizErrorCode.SHIP_STATUS_CAN_NOT_CANCEL.getCode()))) {
//                        // 正常无法取消的运单抛出异常，忽略异常，不影响退款逻辑执行
//                        log.info("[AngelWorkApplicationImpl -> angelWorkInvalid],取消运单异常忽略错误码, code={}", e.getErrorCode().getCode());
//                    } else {
//                        throw e;
//                    }
//                }
//            });
//        }catch (Exception ex) {
//            log.info("[MedicalEventConsumer -> medicalInvalid],取消运单异常!", ex);
//            return Boolean.FALSE;
//        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean delayTest(Long id) {
        AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(id));
        Event event = EventFactory.newDelayEvent(angelWork, AngelWorkDelayEventTypeEnum.ANGEL_WORK_SERVICE_TEST_DELAY, null, 60L);
        eventCoordinator.publishDelay(event);
        return true;
    }

    /**
     *
     * @param promiseId
     * @return
     */
    @Override
    public AngelWorkDto getAngelWorkByPromiseId(Long promiseId) {
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(promiseId);
        angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
        AngelWork angelWork = angelWorkRepository.findAngelWork(angelWorkDBQuery);
        if(Objects.nonNull(angelWork)) {
            List<AngelWorkHistory> workHistories = angelWorkHistoryRepository.findList(AngelWorkHistoryDbQuery.builder().workId(angelWork.getWorkId()).build());
            angelWork.setWorkHistories(workHistories);
        }
        return AngelPromiseApplicationConverter.instance.entity2WorkDto(angelWork);
    }

    /**
     * 修改工单时间
     *
     * @param jdhAngelWorkModifyDateCmd
     * @return
     */
    @Override
    public Boolean modifyAngelWorkStartEndDate(JdhAngelWorkModifyDateCmd jdhAngelWorkModifyDateCmd) {
        int count = angelWorkRepository.updateAngelWorkStartEndDate(AngelWorkStartEndDateCmd.builder()
                .workStartTime(jdhAngelWorkModifyDateCmd.getServiceStartTime())
                .workEndTime(jdhAngelWorkModifyDateCmd.getServiceEndTime())
                .planCallTime(jdhAngelWorkModifyDateCmd.getPlanCallTime())
                .planOutTime(jdhAngelWorkModifyDateCmd.getPlanOutTime())
                .planOutTime(jdhAngelWorkModifyDateCmd.getPlanOutTime())
                .planFinishTime(jdhAngelWorkModifyDateCmd.getPlanFinishTime())
                .workId(jdhAngelWorkModifyDateCmd.getWorkId())
                .extend(jdhAngelWorkModifyDateCmd.getExtend())
                .build());
        return count > 0;
    }

    /**
     * 查询工单对应的运单列表
     *
     * @param angelShipDBQuery angelShipDBQuery
     * @return
     */
    @Override
    public List<AngelShipDto> queryAngelShipList(AngelShipDBQuery angelShipDBQuery) {
        return AngelPromiseApplicationConverter.instance.convertToAngelShipDtoList(angelShipRepository.findList(angelShipDBQuery));
    }

    /**
     *
     * @param angelShipDBQuery
     * @return
     */
    @Override
    public AngelShipDto getAngelShipByShipInfo(AngelShipDBQuery angelShipDBQuery) {
        List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);
        if(CollectionUtils.isEmpty(list)){
            log.error("[AngelWorkApplicationImpl -> getAngelShipByShipInfo], angelShipDBQuery={}", JSON.toJSONString(angelShipDBQuery));
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
        }
        AngelShip angelShip = list.get(0);
        AngelShipDto angelShipDto = AngelPromiseApplicationConverter.instance.convertToAngelShipDto(angelShip);
        return angelShipDto;
    }

    /**
     *
     * @param angelShipQuery
     * @return
     */
    @Override
    public AngelShipDto getShipOrderDetail(AngelShipQuery angelShipQuery) {

        AngelShip angelShip = angelShipRepository.find(AngelShipIdentifier.builder().shipId(angelShipQuery.getShipId()).build());

        O2oBusinessIdentifier businessIdentifier = new O2oBusinessIdentifier();
        businessIdentifier.setAngelType(AngelTypeEnum.DELIVERY.getType());
        businessIdentifier.setAngelDetailType(AngelDetailTypeEnum.getTypeByDelivery(angelShip.getType()));

        DeliveryOrderDetailRequest deliveryOrderDetailRequest = new DeliveryOrderDetailRequest();
        deliveryOrderDetailRequest.setOrderId(String.valueOf(angelShipQuery.getShipId()));
        deliveryOrderDetailRequest.setOutOrderId(angelShip.getOutShipId());
        ExtResponse<DeliveryOrderDetailResponse> shipOrderDetail = workShipCallTransferAbility.getShipOrderDetail(businessIdentifier, deliveryOrderDetailRequest);
        if(Objects.isNull(shipOrderDetail) || Objects.isNull(shipOrderDetail.getData())){
            log.info("[AngelWorkApplicationImpl -> getShipOrderDetail],查询运单详情是空的!");
            return null;
        }

        DeliveryOrderDetailResponse result = shipOrderDetail.getData();
        AngelShipDto angelShipDto = new AngelShipDto();
        angelShipDto.setTransporterLng(result.getTransporterLng());
        angelShipDto.setTransporterLat(result.getTransporterLat());
        angelShipDto.setAlt(result.getAlt());
        angelShipDto.setRemainingDistance(result.getRemainingDistance());
        angelShipDto.setRemainingTime(result.getRemainingTime());
        angelShipDto.setUavTimestamp(result.getUavTimestamp());

        return angelShipDto;
    }

    /**
     * 根据运单状态给护士结算
     *
     * @param angelWork
     */
    @Override
    @LogAndAlarm
    public Boolean angelSettleByShip(AngelWork angelWork) {
        if(Objects.isNull(angelWork)) {
            log.error("AngelWorkApplicationImpl -> angelSettleByShip,工单信息不能为空");
            return false;
        }
        if(!AngelWorkTypeEnum.NURSE.getType().equals(angelWork.getWorkType())) {
            log.info("AngelWorkApplicationImpl -> angelSettleByShip,非护士检测模式不处理");
            return true;
        }
        //查询运单信息
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setWorkId(angelWork.getWorkId());
        angelShipDBQuery.setStatus(Sets.newHashSet(AngelShipStatusEnum.getValidStatus()));
        List<AngelShip> workShipList = angelShipRepository.findList(angelShipDBQuery);
        if(CollectionUtils.isEmpty(workShipList)) {
            return false;
        }

        //查询检测单实验室信息
        MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
        medicalPromiseListQuery.setInvalid(false);
        medicalPromiseListQuery.setFreezeQuery(false);
        medicalPromiseListQuery.setPromiseId(angelWork.getPromiseId());
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        if(CollectionUtils.isEmpty(medicalPromises)) {
            return false;
        }
        Set<String> stationIdSet = medicalPromises.stream().map(MedicalPromise::getStationId).collect(Collectors.toSet());


        if(workShipList.size() != stationIdSet.size()) {
            log.error("AngelWorkApplicationImpl -> angelSettleByShip,检测单数量不一致");
             return false;
        }
        List<AngelShip> angelShipList = workShipList.stream().filter(ship -> DeliveryTypeEnum.SELF_DELIVERY.getType().equals(ship.getType())).collect(Collectors.toList());
        List<AngelShip> riderShipList = workShipList.stream().filter(ship -> !DeliveryTypeEnum.SELF_DELIVERY.getType().equals(ship.getType())).collect(Collectors.toList());

        boolean angelInPlace = CollectionUtils.isNotEmpty(angelShipList) ? angelShipList.stream().allMatch(item -> item.getShipStatus().equals(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus())) : true;
        boolean riderInPlace = CollectionUtils.isNotEmpty(riderShipList) ? riderShipList.stream().allMatch(item ->
                AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus().equals(item.getShipStatus()) || AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus().equals(item.getShipStatus())) : true;
        log.info("AngelWorkApplicationImpl -> angelSettleByShip,处理护士结算,angelInPlace={}, riderInPlace={}", angelInPlace, riderInPlace);
        if(angelInPlace && riderInPlace) {
            eventCoordinator.publish(EventFactory.newDefaultEvent(angelWork, SettleEventTypeEnum.ANGEL_SETTLE_BY_WORK_SHIP, null));
        }
        return true;
    }

    /**
     * 查询运单详情
     * @param getDetailByPromiseIdQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelShipDto getDetailByPromiseId(GetDetailByPromiseIdQuery getDetailByPromiseIdQuery) {
        //查询工单
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(getDetailByPromiseIdQuery.getPromiseId());
        angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
        AngelWork angelWork = angelWorkRepository.findAngelWork(angelWorkDBQuery);
        if(angelWork==null){
            return null;
        }
        //根据工单查询运单
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setWorkId(angelWork.getWorkId());
        List<AngelShip> angelShipList = angelShipRepository.findList(angelShipDBQuery);
        if(CollectionUtils.isEmpty(angelShipList)){
            return null;
        }
        AngelShip angelShip = angelShipList.get(0);
        return AngelPromiseApplicationConverter.instance.convertToAngelShipDto(angelShip);
    }

    /**
     * 查询工单列表
     * @param angelWorkQuery
     * @return
     */
    @Override
    public List<AngelWorkDto> getAngelWorkList(AngelWorkQuery angelWorkQuery) {
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseIds(angelWorkQuery.getPromiseIdList());
        List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
        log.info("AngelWorkApplicationImpl getAngelWorkList angelWorkList={}", JSON.toJSONString(angelWorkList));
        return AngelPromiseApplicationConverter.instance.convertToAngelWorkDtoList(angelWorkList);
    }

    /**
     * 记录无人机实时位置
     * @param uavCallBackPositionRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean shipPositionCallback(UavCallBackPositionRequest uavCallBackPositionRequest) {
        AngelShipPositionContext angelShipPositionContext = new AngelShipPositionContext();
        angelShipPositionContext.setOutShipId(uavCallBackPositionRequest.getFlight_work_id());
        angelShipPositionContext.setLat(uavCallBackPositionRequest.getGps().getLat());
        angelShipPositionContext.setLon(uavCallBackPositionRequest.getGps().getLon());
        angelShipPositionContext.setAlt(uavCallBackPositionRequest.getGps().getAlt());
        angelShipPositionContext.setVel(uavCallBackPositionRequest.getGps().getVel());
        angelShipPositionContext.setRemainingDistance(uavCallBackPositionRequest.getGps().getRemaining_distance());
        angelShipPositionContext.setRemainingTime(uavCallBackPositionRequest.getGps().getRemaining_time());
        angelShipPositionContext.setTimestamp(uavCallBackPositionRequest.getTimestamp());
        return angelShipDomainService.savePosition(angelShipPositionContext);
    }

    /**
     * 重置上门取件
     * @param resetShipCmd
     * @return
     */
    @LogAndAlarm
    @Override
    public Boolean resetShip(ResetShipCmd resetShipCmd) {
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(resetShipCmd.getAngelWorkId()).build());

        AngelWorkCancelShipCmd angelWorkCancelShipCmd = new AngelWorkCancelShipCmd();
        angelWorkCancelShipCmd.setWorkId(resetShipCmd.getAngelWorkId()+"");
        angelWorkCancelShipCmd.setShipId(resetShipCmd.getAngelShipId()+"");
        angelWorkCancelShipCmd.setOperateSource(CommonConstant.ONE);
        angelWorkCancelShipCmd.setStandCancelCode(AngelShipCancelCodeStatusEnum.OPERATE_CANCEL.getType());
        angelWorkCancelShipCmd.setOperator(resetShipCmd.getErp());
        Boolean result = this.cancelShip(angelWorkCancelShipCmd);
        if(result){

            //取消工单
            AngelWorkStatusDbCmd angelWorkStatusDbCmd = AngelWorkStatusDbCmd.builder().build();
            angelWorkStatusDbCmd.setWorkId(resetShipCmd.getAngelWorkId());
            angelWorkStatusDbCmd.setWorkStatus(AngelWorkStatusEnum.CANCEL.getType());
            angelWorkRepository.updateAngelWorkStatus(angelWorkStatusDbCmd);

            //修改履约单状态到2
            UpdatePromiseStatusCmd updatePromiseStatusCmd = new UpdatePromiseStatusCmd();
            updatePromiseStatusCmd.setPromiseId(angelWork.getPromiseId());
            //1 待履约状态
            updatePromiseStatusCmd.setPromiseStatus(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus());
            promiseApplication.updatePromiseStatus(updatePromiseStatusCmd);

            //作废派单
            CancelDispatchCmd cancelDispatchCmd = new CancelDispatchCmd();
            cancelDispatchCmd.setPromiseId(angelWork.getPromiseId());
            cancelDispatchCmd.setCancelType(3);
            dispatchApplication.invalidDispatch(cancelDispatchCmd);
        }
        return true;
    }

    /**
     * 数据越权校验
     * @param workId
     * @param angelPin
     * @return
     */
    private AngelWork authorityCheck(Long workId, String angelPin){
        AngelWork work = angelWorkRepository.authorityFind(new AngelWorkIdentifier( workId), angelPin);
        if (Objects.isNull(work)){
            throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
        }
        return work;
    }

    /**
     * 数据越权校验2
     * @param angelPin
     * @return
     */
    private JdhDispatchDetail authorityCheck2(Long dispatchDetailId, String angelPin){
        JdhAngel jdhAngel = angelRepository.queryByUniqueId(JdhAngelRepQuery.builder().angelPin(angelPin).build());
        if (Objects.isNull(jdhAngel)){
            throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
        }
        JdhDispatchDetail jdhDispatchDetail = dispatchRepository.findDispatchDetail(DispatchDetailRepQuery.builder().dispatchDetailId(dispatchDetailId).angelId(jdhAngel.getAngelId()).build());
        if (Objects.isNull(jdhDispatchDetail)){
            throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
        }
        return jdhDispatchDetail;
    }

    /**
     * 组装实验室查询结果
     *
     * @param medicalPromiseDTOList
     * @return
     */
    private AngelWorkCourierFloorDto swapSpecimenDto2(List<MedicalPromiseDTO> medicalPromiseDTOList,JdhDispatch jdhDispatch,JdOrder jdOrder,PromiseDto promiseDto) {
        AngelWorkCourierFloorDto angelWorkSpecimenDto = new AngelWorkCourierFloorDto();


        MedicalPromiseDTO medicalPromiseDTO = medicalPromiseDTOList.get(0);
        angelWorkSpecimenDto.setReceiverName(new UserName(medicalPromiseDTO.getStationName()).mask());
        angelWorkSpecimenDto.setReceiverPhone(new PhoneNumber(medicalPromiseDTO.getStationPhone()).mask());
        angelWorkSpecimenDto.setReceiverPhoneEncrypt(new PhoneNumber(medicalPromiseDTO.getStationPhone()).encrypt());
        angelWorkSpecimenDto.setReceiverFullAddress(medicalPromiseDTO.getStationAddress());
        if(StringUtils.isNotBlank(medicalPromiseDTO.getStationAddress())){
            GisPointBo lngLatByAddress = addressRpc.getLngLatByAddress(medicalPromiseDTO.getStationAddress());
            angelWorkSpecimenDto.setReceiverLat(lngLatByAddress.getLatitude().doubleValue());
            angelWorkSpecimenDto.setReceiverLng(lngLatByAddress.getLongitude().doubleValue());
        }
        angelWorkSpecimenDto.setReceiverId(medicalPromiseDTO.getStationId());

        String specimenNames = medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getServiceItemName).collect(Collectors.joining("、"));
        String specimenCodes = medicalPromiseDTOList.stream().filter(code -> StringUtils.isNotBlank(code.getSpecimenCode()))
                .map(medical -> medical.getSpecimenCode().substring(Math.max(medical.getSpecimenCode().length() - 4, 0)))
                .collect(Collectors.joining("、"));
        angelWorkSpecimenDto.setSpecimenNames(StringUtils.isNotBlank(specimenCodes) ? specimenCodes : specimenNames);
        angelWorkSpecimenDto.setSpecimenType(StringUtils.isNotBlank(specimenCodes) ? CommonConstant.TWO : CommonConstant.ONE);

        if (StringUtils.isNotBlank(medicalPromiseDTO.getStationId())){
            String guideUrl = duccConfig.getAcceptSampleGuideConfig().get(medicalPromiseDTO.getStationId());
            if (StringUtils.isNotBlank(guideUrl)){
                String publicUrl = fileManageService.getPublicUrl(guideUrl, Boolean.TRUE, DateUtil.offsetMinute(new Date(), CommonConstant.NUMBER_THIRTY));
                angelWorkSpecimenDto.setAcceptSampleGuide(publicUrl);
            }
        }

        long angelDelivery = medicalPromiseDTOList.stream().filter(medicalPromise -> !Objects.isNull(medicalPromise.getDeliveryType()) && MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ONE.getType().equals(medicalPromise.getDeliveryType())).count();
        if(angelDelivery!=0){

            //全部自送
            angelWorkSpecimenDto.setStationDeliveryType(AngelWorkDeliveryTypeEnum.SELF_DELIVERY.getType());


            DirectionResultBO directionResult2 = directionServiceRpc.getDirectionResult(DirectionRequestParam.builder()
                    .fromLocation(String.format("%s,%s", jdhDispatch.getServiceInfo().getGisPoint().getLatitude(), jdhDispatch.getServiceInfo().getGisPoint().getLongitude()))
                    .toLocation(String.format("%s,%s", angelWorkSpecimenDto.getReceiverLat(), angelWorkSpecimenDto.getReceiverLng()))
                    .travelMode(DirectionServiceRpc.TravelMode.BICYCLING).build());
            if (Objects.nonNull(directionResult2)) {
                double distance = Objects.nonNull(directionResult2.getDistance()) ? directionResult2.getDistance() : 0;
                angelWorkSpecimenDto.setPatientToStationDistance(GeoDistanceUtil.distanceDynamicUnit2((int) distance));
            }


            if(jdOrder!=null){
                if(promiseDto.getAppointmentTime().getIsImmediately()){
                    //即时单 支付时间+1小时
                    angelWorkSpecimenDto.setDeliverySampleTimeDesc(DispatchApplicationConverter.INS.getFormatDate(promiseDto.getAppointmentTime().getAppointmentStartTime())+" "+TimeUtils.dateTimeToStr(TimeUtils.add(promiseDto.getAppointmentTime().getAppointmentStartTime(),Calendar.MINUTE,90), TimeFormat.DATE_PATTERN_HM_SIMPLE)+"前送达");
                }else{
                    //预约单 用户预约开始时间
                    angelWorkSpecimenDto.setDeliverySampleTimeDesc(DispatchApplicationConverter.INS.getFormatDate(promiseDto.getAppointmentTime().getAppointmentStartTime())+" "+TimeUtils.dateTimeToStr(TimeUtils.add(promiseDto.getAppointmentTime().getAppointmentStartTime(),Calendar.MINUTE,90),TimeFormat.DATE_PATTERN_HM_SIMPLE)+"前送达");
                }
            }
        }else{
            //全部自送
            angelWorkSpecimenDto.setStationDeliveryType(AngelWorkDeliveryTypeEnum.STANDER_DELIVERY.getType());
        }

        return angelWorkSpecimenDto;
    }

    /**
     * 组装实验室查询结果
     *
     * @param medicalPromiseDTOList
     * @param angelShip
     * @param angelWork
     * @return
     */
    private AngelWorkCourierFloorDto swapSpecimenDto(List<MedicalPromiseDTO> medicalPromiseDTOList, AngelShip angelShip, AngelWork angelWork,Boolean canClick,JdOrder jdOrder,PromiseDto promiseDto) {
        AngelWorkCourierFloorDto angelWorkSpecimenDto = new AngelWorkCourierFloorDto();

        //sender信息在未呼叫运力之前是订单下单人信息，呼叫运力之后是运单寄件人信息
        angelWorkSpecimenDto.setSenderPhone(new PhoneNumber(angelWork.getAngelPhone()).mask());
        angelWorkSpecimenDto.setSenderPhoneEncrypt(new PhoneNumber(angelWork.getAngelPhone()).encrypt());
        angelWorkSpecimenDto.setSenderName(angelWork.getAngelName());
        angelWorkSpecimenDto.setSenderFullAddress(angelWork.getAngelOrder().getFullAddress());
        if(Objects.nonNull(angelShip)){
            if(DeliveryTypeEnum.RIDER_DELIVERY.getType().equals(angelShip.getType())) {
                angelWorkSpecimenDto.setTransferId(angelShip.getTransferId());
            }
            angelWorkSpecimenDto.setSenderFullAddress(angelShip.getSenderFullAddress());
            angelWorkSpecimenDto.setSenderName(new UserName(angelShip.getSenderName()).mask());
            angelWorkSpecimenDto.setSenderPhone(new PhoneNumber(angelShip.getSenderPhone()).mask());
            angelWorkSpecimenDto.setSenderPhoneEncrypt(new PhoneNumber(angelShip.getSenderPhone()).encrypt());

            angelWorkSpecimenDto.setTransferName(new UserName(angelShip.getTransferName()).mask());
            angelWorkSpecimenDto.setTransferPhone(new PhoneNumber(angelShip.getTransferPhone()).mask());
            angelWorkSpecimenDto.setTransferPhoneEncrypt(new PhoneNumber(angelShip.getTransferPhone()).encrypt());
            angelWorkSpecimenDto.setShipStatusDesc(AngelShipStatusEnum.getStatusDesc(angelShip.getShipStatus()));
            angelWorkSpecimenDto.setShipStatus(angelShip.getShipStatus());
            angelWorkSpecimenDto.setShipId(angelShip.getShipId());
            angelWorkSpecimenDto.setOutShipNo(angelShip.getOutShipId());
            angelWorkSpecimenDto.setShipType(angelShip.getType());
        }else {
            Integer workAttribute = angelWork.getJdhAngelWorkExtVo().getWorkAttribute();
            String shipListType = Objects.nonNull(workAttribute)? String.valueOf(workAttribute) : "default";

            AngelWorkDomainService angelWorkDomainService = SpringUtil.getBean(AngelWorkDomainService.class);
            StationDeliveryContext deliveryContext = new StationDeliveryContext();
            deliveryContext.setWorkDelivery(shipListType);
            MedicalPromiseDTO medicalPromiseDTO1 = medicalPromiseDTOList.stream().filter(item -> MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ONE.getType().equals(item.getDeliveryType())).findFirst().orElse(medicalPromiseDTOList.get(0));
            deliveryContext.setStationDelivery(Objects.nonNull(medicalPromiseDTO1.getDeliveryType()) ? String.valueOf(medicalPromiseDTO1.getDeliveryType()) : "default");
            List<AngelDeliveryTypeDTO> angelDeliveryTypeDTOS = angelWorkDomainService.stationDelivery(deliveryContext);

            if (canClick){
                MedicalPromiseDTO medicalPromiseDTO = medicalPromiseDTOList.stream().filter(p -> StringUtils.isBlank(p.getSpecimenCode())).findFirst().orElse(null);
                if (Objects.nonNull(medicalPromiseDTO)){
                    for (AngelDeliveryTypeDTO angelDeliveryTypeDTO : angelDeliveryTypeDTOS){
                        if (DeliveryTypeEnum.THIRD_DELIVERY.getType().equals(angelDeliveryTypeDTO.getDeliveryType()) || DeliveryTypeEnum.SELF_DELIVERY.getType().equals(angelDeliveryTypeDTO.getDeliveryType())){
                            angelDeliveryTypeDTO.setClickYn(CommonConstant.ZERO);
                        }
                    }
                }
            }else {
                for (AngelDeliveryTypeDTO angelDeliveryTypeDTO : angelDeliveryTypeDTOS){
                    if (DeliveryTypeEnum.THIRD_DELIVERY.getType().equals(angelDeliveryTypeDTO.getDeliveryType()) || DeliveryTypeEnum.SELF_DELIVERY.getType().equals(angelDeliveryTypeDTO.getDeliveryType())){
                        angelDeliveryTypeDTO.setClickYn(CommonConstant.ZERO);
                    }
                }
            }

            angelWorkSpecimenDto.setShipTypeList(angelDeliveryTypeDTOS);
        }

        MedicalPromiseDTO medicalPromiseDTO = medicalPromiseDTOList.get(0);
        angelWorkSpecimenDto.setReceiverName(new UserName(medicalPromiseDTO.getStationName()).mask());
        angelWorkSpecimenDto.setReceiverPhone(new PhoneNumber(medicalPromiseDTO.getStationPhone()).mask());
        angelWorkSpecimenDto.setReceiverPhoneEncrypt(new PhoneNumber(medicalPromiseDTO.getStationPhone()).encrypt());
        angelWorkSpecimenDto.setReceiverFullAddress(medicalPromiseDTO.getStationAddress());
        if(StringUtils.isNotBlank(medicalPromiseDTO.getStationAddress())){
            GisPointBo lngLatByAddress = addressRpc.getLngLatByAddress(medicalPromiseDTO.getStationAddress());
            angelWorkSpecimenDto.setReceiverLat(lngLatByAddress.getLatitude().doubleValue());
            angelWorkSpecimenDto.setReceiverLng(lngLatByAddress.getLongitude().doubleValue());
        }
        angelWorkSpecimenDto.setReceiverId(medicalPromiseDTO.getStationId());

        String specimenNames = medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getServiceItemName).collect(Collectors.joining("、"));
        String specimenCodes = medicalPromiseDTOList.stream().filter(code -> StringUtils.isNotBlank(code.getSpecimenCode()))
                .map(medical -> medical.getSpecimenCode().substring(Math.max(medical.getSpecimenCode().length() - 4, 0)))
                .collect(Collectors.joining("、"));
        angelWorkSpecimenDto.setSpecimenNames(StringUtils.isNotBlank(specimenCodes) ? specimenCodes : specimenNames);
        angelWorkSpecimenDto.setSpecimenType(StringUtils.isNotBlank(specimenCodes) ? CommonConstant.TWO : CommonConstant.ONE);

        if (StringUtils.isNotBlank(medicalPromiseDTO.getStationId())){
            String guideUrl = duccConfig.getAcceptSampleGuideConfig().get(medicalPromiseDTO.getStationId());
            if (StringUtils.isNotBlank(guideUrl)){
                String publicUrl = fileManageService.getPublicUrl(guideUrl, Boolean.TRUE, DateUtil.offsetMinute(new Date(), CommonConstant.NUMBER_THIRTY));
                angelWorkSpecimenDto.setAcceptSampleGuide(publicUrl);
            }
        }


        long angelDelivery = medicalPromiseDTOList.stream().filter(medicalPromise -> !Objects.isNull(medicalPromise.getDeliveryType()) && MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ONE.getType().equals(medicalPromise.getDeliveryType())).count();
        if(angelDelivery!=0){
            //如果有检测单需要自配送,则走一下逻辑

            //全部自送
            angelWorkSpecimenDto.setStationDeliveryType(AngelWorkDeliveryTypeEnum.SELF_DELIVERY.getType());


            GisPointBo lngLatByAddress = addressRpc.getLngLatByAddress(promiseDto.getStore().getStoreAddr());


            DirectionResultBO directionResult2 = directionServiceRpc.getDirectionResult(DirectionRequestParam.builder()
                    .fromLocation(String.format("%s,%s", lngLatByAddress.getLatitude().doubleValue(), lngLatByAddress.getLongitude().doubleValue()))
                    .toLocation(String.format("%s,%s", angelWorkSpecimenDto.getReceiverLat(), angelWorkSpecimenDto.getReceiverLng()))
                    .travelMode(DirectionServiceRpc.TravelMode.BICYCLING).build());
            if (Objects.nonNull(directionResult2)) {
                double distance = Objects.nonNull(directionResult2.getDistance()) ? directionResult2.getDistance() : 0;
                angelWorkSpecimenDto.setPatientToStationDistance(GeoDistanceUtil.distanceDynamicUnit2((int) distance));
            }


            if(jdOrder!=null){
                if(promiseDto.getAppointmentTime().getIsImmediately()){
                    //即时单 支付时间+1小时
                    angelWorkSpecimenDto.setDeliverySampleTimeDesc(TimeUtils.dateTimeToStr(promiseDto.getAppointmentTime().getAppointmentStartTime(),TimeFormat.SHORT_PATTERN_DOT)+" "+TimeUtils.dateTimeToStr(TimeUtils.add(promiseDto.getAppointmentTime().getAppointmentStartTime(),Calendar.MINUTE,90), TimeFormat.DATE_PATTERN_HM_SIMPLE)+"前送达");
                }else{
                    //预约单 用户预约开始时间
                    angelWorkSpecimenDto.setDeliverySampleTimeDesc(TimeUtils.dateTimeToStr(promiseDto.getAppointmentTime().getAppointmentStartTime(),TimeFormat.SHORT_PATTERN_DOT)+" "+TimeUtils.dateTimeToStr(TimeUtils.add(promiseDto.getAppointmentTime().getAppointmentStartTime(),Calendar.MINUTE,90),TimeFormat.DATE_PATTERN_HM_SIMPLE)+"前送达");
                }
            }
        }else{
            angelWorkSpecimenDto.setStationDeliveryType(AngelWorkDeliveryTypeEnum.STANDER_DELIVERY.getType());
        }

        return angelWorkSpecimenDto;
    }


    /**
     * 查检验单
     * @param promiseId
     * @return
     */
    private List<MedicalPromiseDTO> queryMedicalPromise(Long promiseId, List<Long> patientIds){
        if(Objects.isNull(promiseId) && CollectionUtils.isEmpty(patientIds)){
            return null;
        }
        MedicalPromiseListRequest promiseListRequest = new MedicalPromiseListRequest();
        promiseListRequest.setPromiseIdList(Lists.newArrayList(promiseId));
        promiseListRequest.setPromisePatientIdList(patientIds);
        promiseListRequest.setInvalid(false);
        List<MedicalPromiseDTO> medicalPromises = medicalPromiseApplication.queryMedicalPromiseList(promiseListRequest);
        return medicalPromises;
    }

    /**
     * 包装创建运单参数
     *
     * @param angelWorkCreateShipCmd
     * @param angelWork
     * @param medicalPromiseDTO
     * @return
     */
    private AngelWorkShipCreateContext swapCreateDeliveryShipContext(AngelWorkCreateShipCmd angelWorkCreateShipCmd, AngelWork angelWork, MedicalPromiseDTO medicalPromiseDTO) {
        AngelWorkShipCreateContext shipCreateContext = AngelWorkShipCreateContext.builder().build();
        shipCreateContext.setWorkId(Long.valueOf(angelWorkCreateShipCmd.getWorkId()));
        shipCreateContext.setShopNo(medicalPromiseDTO.getStationId());
        shipCreateContext.setProviderShopNo(angelWorkCreateShipCmd.getShopNo());
        shipCreateContext.setIsPrepay(CommonConstant.ZERO);
        shipCreateContext.setReceiverAddress(medicalPromiseDTO.getStationAddress());
        shipCreateContext.setReceiverName(medicalPromiseDTO.getStationName());
        shipCreateContext.setReceiverPhone(medicalPromiseDTO.getStationPhone());
        shipCreateContext.setIsDirectDelivery(CommonConstant.ZERO);
        shipCreateContext.setIsExpectFinishOrder(CommonConstant.ZERO);
        shipCreateContext.setCargoWeight(1.0);
        shipCreateContext.setSupplierAddress(angelWorkCreateShipCmd.getSupplierAddress());
        shipCreateContext.setSupplierPhone(angelWork.getAngelPhone());
        shipCreateContext.setSupplierName(angelWork.getAngelName());
        shipCreateContext.setUserPin(angelWorkCreateShipCmd.getUserPin());
        shipCreateContext.setAngelPin(angelWorkCreateShipCmd.getUserPin());
        shipCreateContext.setAngelWorkCreateShipExt(angelWorkCreateShipCmd.getAngelWorkCreateShipExt());
        return shipCreateContext;
    }

    /**
     * 映射服务站id
     *
     * @param angelStationId
     * @return
     */
    private String mappingShopNo(String angelStationId) {
        Map<String, String> angelStationMap = duccConfig.getAngelStationMap();
        if(MapUtils.isEmpty(angelStationMap)) {
            return angelStationId;
        }
        return StringUtils.isBlank(angelStationMap.get(angelStationId)) ? angelStationId : angelStationMap.get(angelStationId);
    }

    /**
     * swapSaveAngelWorkContext
     * @param jdhAngelWorkSaveCmd
     * @return
     */
    private SaveAngelWorkContext swapSaveAngelWorkContext(JdhAngelWorkSaveCmd jdhAngelWorkSaveCmd) {
        //接口参数组装
        SaveAngelWorkContext angelWorkContext = SaveAngelWorkContext.builder()
                .angelType(jdhAngelWorkSaveCmd.getAngelType())
                .angelDeliveryType(jdhAngelWorkSaveCmd.getAngelDeliveryType())
                .jobNature(jdhAngelWorkSaveCmd.getJobNature())
                .promiseId(jdhAngelWorkSaveCmd.getPromiseId())
                .sourceId(jdhAngelWorkSaveCmd.getSourceId())
                .operator(StringUtils.isNotBlank(jdhAngelWorkSaveCmd.getOperator()) ? jdhAngelWorkSaveCmd.getOperator() : "system")
                .build();
        angelWorkContext.setVerticalCode(jdhAngelWorkSaveCmd.getVerticalCode());
        angelWorkContext.setServiceType(jdhAngelWorkSaveCmd.getServiceType());
        angelWorkContext.initVertical();

        //查询业务身份得到工单类型
        BusinessModeEnum enumByCode = BusinessModeEnum.getEnumByCode(angelWorkContext.getVerticalBusiness().getBusinessModeCode());
        angelWorkContext.setWorkType(AngelWorkTypeEnum.matchType(enumByCode));

        List<Appointment> appointmentList = jdhAngelWorkSaveCmd.getAppointmentList();
        if(CollectionUtils.isEmpty(appointmentList)){
            return angelWorkContext;
        }
        List<AngelTaskSaveContext > taskSaveContexts = Lists.newArrayList();
        angelWorkContext.setAngelTaskContextList(taskSaveContexts);
        //工单开始和结束时间
        handleAngelWorkTime(angelWorkContext, appointmentList, taskSaveContexts);

        //预计服务费
        if(Objects.nonNull(jdhAngelWorkSaveCmd.getChargeInfo())){
            angelWorkContext.setAngelCharge(jdhAngelWorkSaveCmd.getChargeInfo().getOrderSettleAmount().add(jdhAngelWorkSaveCmd.getChargeInfo().getDispatchMarkupPrice()));
        }else {
            angelWorkContext.setAngelCharge(BigDecimal.ZERO);
        }
        angelWorkContext.setPlanOutTime(jdhAngelWorkSaveCmd.getPlanOutTime());
        angelWorkContext.setPlanFinishTime(jdhAngelWorkSaveCmd.getPlanFinishTime());

        //上门护理查询服务者获取护士信息
        fillAngelInfo(jdhAngelWorkSaveCmd, angelWorkContext);

        //查询并补充订单信息
        fillOrderInfo(jdhAngelWorkSaveCmd, angelWorkContext);

        return angelWorkContext;
    }

    /**
     * 处理工单时间
     *
     * @param angelWorkContext
     * @param appointmentList
     * @param taskSaveContexts
     */
    private static void handleAngelWorkTime(SaveAngelWorkContext angelWorkContext, List<Appointment> appointmentList, List<AngelTaskSaveContext> taskSaveContexts) {
        AtomicReference<Date> workStartTime = new AtomicReference<>(appointmentList.get(0).getServiceStartTime());
        AtomicReference<Date> workEndTime = new AtomicReference<>(appointmentList.get(0).getServiceEndTime());
        appointmentList.stream().forEach(task -> {
            JdhAngelTaskExtVo taskExtVo = JdhAngelTaskExtVo.builder()
                    .patientName(task.getPatientName())
                    .patientAge(task.getPatientAge())
                    .patientGender(task.getPatientGender())
                    .build();
            AngelTaskSaveContext taskSaveContext = AngelTaskSaveContext.builder()
                    .patientId(String.valueOf(task.getPromisePatientId()))
                    .patientFullAddress(task.getPatientFullAddress())
                    .taskStartTime(task.getServiceStartTime())
                    .taskEndTime(task.getServiceEndTime())
                    .taskExtVo(JSON.toJSONString(taskExtVo))
                    .build();
            taskSaveContexts.add(taskSaveContext);

            if(workStartTime.get().after(task.getServiceStartTime())){
                workStartTime.set(task.getServiceStartTime());
            }
            if(workEndTime.get().before(task.getServiceEndTime())){
                workEndTime.set(task.getServiceEndTime());
            }
        });
        angelWorkContext.setWorkStartTime(workStartTime.get());
        angelWorkContext.setWorkEndTime(workEndTime.get());
    }

    /**
     * 服务者信息
     *
     * @param jdhAngelWorkSaveCmd
     * @param angelWorkContext
     */
    private void fillAngelInfo(JdhAngelWorkSaveCmd jdhAngelWorkSaveCmd, SaveAngelWorkContext angelWorkContext) {
        if(AngelWorkTypeEnum.matchToHome(angelWorkContext.getWorkType())){
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelId(jdhAngelWorkSaveCmd.getAngelId());
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            if(Objects.isNull(jdhAngelDto)){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_NOT_EXIST);
            }
            angelWorkContext.setAngelId(String.valueOf(jdhAngelWorkSaveCmd.getAngelId()));
            angelWorkContext.setAngelName(jdhAngelDto.getAngelName());
            angelWorkContext.setAngelPhone(jdhAngelDto.getPhone());
            angelWorkContext.setAngelPin(jdhAngelDto.getAngelPin());
            angelWorkContext.setAngelPhoneIndex(jdhAngelDto.getPhone());
            angelWorkContext.setAngelHeadImg(jdhAngelDto.getHeadImg());
            angelWorkContext.setJobNature(jdhAngelDto.getJobNature());
            angelWorkContext.setAngelStationId(Objects.nonNull(jdhAngelDto.getStationId())?String.valueOf(jdhAngelDto.getStationId()):null);
        }
    }

    /**
     * 从履约域-补充订单信息(精准营养需求需要用到该方法,目前需求暂停了,后续作为参考)
     *
     * @param jdhAngelWorkSaveCmd
     * @param angelWorkContext
     */
    private void fillOrderInfoByPromise(JdhAngelWorkSaveCmd jdhAngelWorkSaveCmd, SaveAngelWorkContext angelWorkContext) {

        //查询履约单信息
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(angelWorkContext.getPromiseId());
        PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
        if(Objects.isNull(promiseDto)){
            throw new BusinessException(AngelPromiseBizErrorCode.PROMISE_NOT_EXIST);
        }

        AngelOrder angelOrder = new AngelOrder();

        //TODO 下单人姓名和收获地址 等世博在履约域中维护好了,通知张灿维护!!!
        //angelOrder.setAppointName(promiseDto.getPromiseExtends().stream().filter(PromiseExtendDto.ORDER_PHONE));
        angelOrder.setAppointPhone(promiseDto.getPromiseExtends().stream().filter(t->PromiseExtendDto.ORDER_PHONE.equals(t.getAttribute())).findFirst().get().getValue());
        //angelOrder.setFullAddress();



        angelOrder.setOrderRemark(promiseDto.getPromiseExtends().stream().filter(t->PromiseExtendDto.ORDER_REMARK.equals(t.getAttribute())).findFirst().get().getValue());


        JdhAngelWorkExtVo jdhAngelWorkExtVo = JdhAngelWorkExtVo.builder().build();


        Set<Long> skuIdSet = promiseDto.getServices().stream().map(PromiseServiceDetailDto::getServiceId).collect(Collectors.toSet());
        angelOrder.setSkuIds(skuIdSet);

        jdhAngelWorkExtVo.setAngelOrder(angelOrder);
        jdhAngelWorkExtVo.setAngelCharge(AngelPromiseApplicationConverter.instance.convertToAngelCharge(jdhAngelWorkSaveCmd.getChargeInfo()));

        //查询商品信息
        JdhSkuListRequest request = new JdhSkuListRequest();
        request.setSkuIdList(skuIdSet);
        Map<Long, JdhSkuDto> longJdhSkuDtoMap = productApplication.queryJdhSkuInfoList(request);
        List<Integer> customerConfirm = Lists.newArrayList();
        List<Integer> serviceRecord = Lists.newArrayList();
        AtomicReference<Integer> serviceDuration = new AtomicReference<>(0);
        longJdhSkuDtoMap.forEach((k, v) -> {
            if(CollectionUtils.isNotEmpty(v.getCustomerConfirmType())){
                customerConfirm.addAll(customerConfirm.stream().filter(item -> !v.getCustomerConfirmType().contains(item)).collect(Collectors.toList()));
            }
            if(CollectionUtils.isNotEmpty(v.getServiceRecordType())){
                serviceRecord.addAll(serviceRecord.stream().filter(item -> !v.getServiceRecordType().contains(item)).collect(Collectors.toList()));
            }
            serviceDuration.updateAndGet(v1 -> v1 + v.getServiceDuration());

        });
        jdhAngelWorkExtVo.setServiceRecord(serviceRecord);
        jdhAngelWorkExtVo.setCustomerConfirm(customerConfirm);
        angelWorkContext.setExtend(JSON.toJSONString(jdhAngelWorkExtVo));
    }

    /**
     * 补充订单信息
     *
     * @param jdhAngelWorkSaveCmd
     * @param angelWorkContext
     */
    private void fillOrderInfo(JdhAngelWorkSaveCmd jdhAngelWorkSaveCmd, SaveAngelWorkContext angelWorkContext) {
        String orderId;
        String remark;//预约单备注信息

        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(angelWorkContext.getPromiseId());
        PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
        if(Objects.isNull(promiseDto)){
            throw new BusinessException(AngelPromiseBizErrorCode.PROMISE_NOT_EXIST);
        }
        orderId = promiseDto.getSourceVoucherId();
        remark = promiseDto.getPromiseExtends().stream().filter(t->PromiseExtendDto.ORDER_REMARK.equals(t.getAttribute())).findFirst().orElse(new PromiseExtendDto()).getOrderRemark();


        //查询订单扩展信息
        JdOrder jdOrder = jdOrderApplication.queryJdOrderAndItemExt(jdhAngelWorkSaveCmd.getOrderId());
        log.info("AngelWorkApplicationImpl -> swapSaveAngelWorkContext, jdOrder={}",JSON.toJSONString(jdOrder));
        JdOrderExt jdOrderExtDetail = null;
        if (Objects.nonNull(jdOrder) && CollectionUtils.isNotEmpty(jdOrder.getJdOrderExtList())) {
            jdOrderExtDetail = jdOrder.getJdOrderExtList().stream().filter(ext -> Objects.equals(JdOrderExtTypeEnum.APPOINTMENT_INFO.getExtType(), ext.getExtType())).findFirst().orElse(null
            );
        }
        AngelOrder angelOrder = new AngelOrder();

        boolean verticalCodeBool = serviceHomeTypeDomainService.getServiceHomeVerticalCode("XFYL_HOME_SELF_TEST_TRANSPORT").equals(promiseDto.getVerticalCode());
        if(verticalCodeBool){
            angelOrder.setAppointName(promiseDto.getPromiseExtends().stream().filter(t-> PromiseExtendKeyEnum.APPOINTMENT_USER_NAME.getFiledKey().equals(t.getAttribute())).findFirst().orElse(new PromiseExtendDto()).getValue());
            angelOrder.setAppointPhone(promiseDto.getAppointmentPhone());
            angelOrder.setFullAddress(promiseDto.getStore().getStoreAddr());
            angelOrder.setCreateTime(jdOrder.getCreateTime());
        }else{
            if(Objects.nonNull(jdOrderExtDetail) && StringUtils.isNotBlank(jdOrderExtDetail.getExtContext())) {
                //护士端 工单详情页-展示下单人信息
                OrderAppointmentInfoValueObject orderAppointmentInfo = JsonUtil.parseObject(jdOrderExtDetail.getExtContext(),OrderAppointmentInfoValueObject.class);
                angelOrder.setAppointName(Objects.nonNull(orderAppointmentInfo) && Objects.nonNull(orderAppointmentInfo.getAddressInfo()) ? orderAppointmentInfo.getAddressInfo().getName() : null);
                angelOrder.setAppointPhone(Objects.nonNull(orderAppointmentInfo) && Objects.nonNull(orderAppointmentInfo.getAddressInfo()) ? orderAppointmentInfo.getAddressInfo().getMobile() : null);
                angelOrder.setFullAddress(orderAppointmentInfo.getAddressInfo().getFullAddress());
                angelOrder.setCreateTime(jdOrder.getCreateTime());
            }
        }

        angelOrder.setOrderId(jdhAngelWorkSaveCmd.getOrderId());
        //VTP订单没有备注信息,提交预约的时候,维护备注信息,存在履约域中
        angelOrder.setOrderRemark(StringUtils.defaultIfBlank(remark,Objects.isNull(jdOrder) ? "" : jdOrder.getRemark()));

        //查询订单详情
        //无订单模式
        Set<Long> skuIdSet;
        Long jdOrderId;
        if(VerticalEnum.isNonOrderVertical(promiseDto.getVerticalCode())){
            skuIdSet = promiseDto.getServices().stream().map(PromiseServiceDetailDto::getServiceId).collect(Collectors.toSet());
            jdOrderId = Long.parseLong(promiseDto.getSourceVoucherId());
            //这里把用户信息一期补充一下
            if(CollectionUtils.isNotEmpty(promiseDto.getPatients())){
                PromisePatientDto promisePatientDto = promiseDto.getPatients().get(NumConstant.NUM_0);
                angelOrder.setAppointName(Objects.nonNull(promisePatientDto.getUserName()) ? promisePatientDto.getUserName().getName() : null);
                angelOrder.setAppointPhone(Objects.nonNull(promisePatientDto.getPhoneNumber()) ? promisePatientDto.getPhoneNumber().getPhone() : null);
            }
            // 无订单模式,如果promise有预约人姓名和手机号进行补充
            String userName = promiseDto.getPromiseExtends().stream().filter(t-> PromiseExtendKeyEnum.APPOINTMENT_USER_NAME.getFiledKey().equals(t.getAttribute())).findFirst().orElse(new PromiseExtendDto()).getValue();
            if(StringUtils.isNotBlank(userName)){
                angelOrder.setAppointName(userName);
            }
            if(StringUtils.isNotBlank(promiseDto.getAppointmentPhone())){
                angelOrder.setAppointPhone(promiseDto.getAppointmentPhone());
            }
            if(Objects.nonNull(promiseDto.getStore())){
                angelOrder.setFullAddress(promiseDto.getStore().getStoreAddr());
            }
            angelOrder.setCreateTime(promiseDto.getCreateTime());

        }else{
            OrderDetailParam orderDetailParam = new OrderDetailParam();
            orderDetailParam.setOrderId(orderId);
            JdOrderDTO orderDetail = tradeApplication.getOrderDetail(orderDetailParam);
            if(Objects.isNull(orderDetail)){
                throw new BusinessException(AngelPromiseBizErrorCode.ORDER_NOT_EXIST);
            }
            List<JdOrderItemDTO> jdOrderItemList = orderDetail.getJdOrderItemList();
            skuIdSet = jdOrderItemList.stream().map(JdOrderItemDTO::getSkuId).collect(Collectors.toSet());
            jdOrderId = orderDetail.getOrderId();
        }

        angelOrder.setSkuIds(skuIdSet);
        angelWorkContext.setJdOrderId(jdOrderId);

        JdhAngelWorkExtVo jdhAngelWorkExtVo = JdhAngelWorkExtVo.builder().build();
        jdhAngelWorkExtVo.setAngelOrder(angelOrder);
        jdhAngelWorkExtVo.setAngelCharge(AngelPromiseApplicationConverter.instance.convertToAngelCharge(jdhAngelWorkSaveCmd.getChargeInfo()));

        //查询商品信息
        JdhSkuListRequest request = new JdhSkuListRequest();
        request.setSkuIdList(skuIdSet);
        Map<Long, JdhSkuDto> longJdhSkuDtoMap = productApplication.queryJdhSkuInfoList(request);
        List<Integer> customerConfirm = Lists.newArrayList();
        List<Integer> serviceRecord = Lists.newArrayList();
        AtomicReference<Integer> serviceDuration = new AtomicReference<>(0);
        longJdhSkuDtoMap.forEach((k, v) -> {
            if(CollectionUtils.isNotEmpty(v.getCustomerConfirmType())){
                customerConfirm.addAll(customerConfirm.stream().filter(item -> !v.getCustomerConfirmType().contains(item)).collect(Collectors.toList()));
            }
            if(CollectionUtils.isNotEmpty(v.getServiceRecordType())){
                serviceRecord.addAll(serviceRecord.stream().filter(item -> !v.getServiceRecordType().contains(item)).collect(Collectors.toList()));
            }
            serviceDuration.updateAndGet(v1 -> v1 + v.getServiceDuration());

        });
        jdhAngelWorkExtVo.setServiceRecord(serviceRecord);
        jdhAngelWorkExtVo.setCustomerConfirm(customerConfirm);

        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("verticalCode",promiseDto.getVerticalCode());
        paramMap.put("serviceType",promiseDto.getServiceType());
        paramMap.put("provinceCode",jdhAngelWorkSaveCmd.getProvinceCode());
        paramMap.put("jobNature",jdhAngelWorkSaveCmd.getJobNature());

        String canSelectShipTypeConfig = duccConfig.getCanSelectShipTypeConfig();
        ShipTypeConfig shipTypeConfig = JsonUtil.parseObject(canSelectShipTypeConfig, ShipTypeConfig.class);
        for (ShipTypeExpression shipTypeExpression : shipTypeConfig.getShipTypeExpressions()){
            if ((Boolean) AviatorEvaluator.compile(shipTypeExpression.getExpression(),Boolean.TRUE).execute(paramMap)){
                jdhAngelWorkExtVo.setWorkAttribute(Integer.valueOf(shipTypeExpression.getShipType()));
                break;
            }
        }

        //在扩展字段中落当前工单是自配、部分自配还是标准配送
        MedicalPromiseRequest medicalPromiseRequest = new MedicalPromiseRequest();
        medicalPromiseRequest.setPromiseId(jdhAngelWorkSaveCmd.getPromiseId());
        medicalPromiseRequest.setFreezeQuery(false);
        medicalPromiseRequest.setInvalid(false);
        List<MedicalPromiseDTO> medicalPromiseDTOS = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseRequest);
        Map<String, MedicalPromiseDTO> stationMap = medicalPromiseDTOS.stream().collect(Collectors.toMap(MedicalPromiseDTO::getStationId, Function.identity(), (k1, k2) -> {
            if (MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ONE.getType().equals(k1.getDeliveryType())) {
                return k1;
            }
            if (MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ONE.getType().equals(k2.getDeliveryType())) {
                return k2;
            }
            return k1;
        }));
        List<MedicalPromiseDTO> medicalPromiseDTOList = stationMap.values().stream().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(medicalPromiseDTOList)) {
            angelWorkContext.setWorkDeliveryType(AngelWorkDeliveryTypeEnum.STANDER_DELIVERY.getType());
        }else {
            List<MedicalPromiseDTO> selfDeliveryMedical = medicalPromiseDTOList.stream().filter(item -> MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ONE.getType().equals(item.getDeliveryType())).collect(Collectors.toList());
            List<MedicalPromiseDTO> standerDeliveryMedical = medicalPromiseDTOList.stream()
                    .filter(item -> Objects.isNull(item.getDeliveryType()) || MedicalDeliveryTypeEnum.ANGEL_DELIVERY_ZERO.getType().equals(item.getDeliveryType()))
                    .collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(selfDeliveryMedical) && CollectionUtils.isNotEmpty(standerDeliveryMedical)) {
                angelWorkContext.setWorkDeliveryType(AngelWorkDeliveryTypeEnum.ANY_SELF_DELIVERY.getType());
            }else if(CollectionUtils.isNotEmpty(selfDeliveryMedical)) {
                angelWorkContext.setWorkDeliveryType(AngelWorkDeliveryTypeEnum.SELF_DELIVERY.getType());
            }else {
                angelWorkContext.setWorkDeliveryType(AngelWorkDeliveryTypeEnum.STANDER_DELIVERY.getType());
            }
        }
        angelWorkContext.setExtend(JSON.toJSONString(jdhAngelWorkExtVo));
    }

    /**
     * 查询服务者任务单
     *
     * @param angelCheckBarCodeCmd
     * @return
     */
    private List<AngelTask> getAngelTasks(AngelCheckBarCodeCmd angelCheckBarCodeCmd) {
        List<AngelTask> angelTaskList;
        List<AngelCheckTaskBarCodeQuery> taskBarQuerys = angelCheckBarCodeCmd.getTaskBar();
        if(CollectionUtils.isEmpty(taskBarQuerys)){
            AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
            angelTaskDBQuery.setWorkId(angelCheckBarCodeCmd.getWorkId());
            angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
        }else {
            AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
            angelTaskDBQuery.setTaskIds(taskBarQuerys.stream().map(item -> Long.valueOf(item.getTaskId())).collect(Collectors.toList()));
            angelTaskList = angelTaskRepository.findList(angelTaskDBQuery);
        }
        return angelTaskList;
    }

    /**
     * 根据达达取消原因更新运单表数据
     * @param angelShipDadaCallBackContext 达达传入参数
     * @return 返回是否需要继续执行状态
     */
    private Boolean updateAngelShipWithDdCancelReason(AngelShipCallBackContext angelShipDadaCallBackContext) {
        try {
            if (StringUtils.isBlank(angelShipDadaCallBackContext.getCancelReason())) {
                return Boolean.TRUE;
            }

            AngelShipDBQuery angelShipDBQuery = AngelShipDBQuery.builder().shipIds(Lists.newArrayList(angelShipDadaCallBackContext.getOrderId())).build();
            List<AngelShip> angelShipList = angelShipRepository.findList(angelShipDBQuery);
            // 根据运单id查询样本状态
            if (CollectionUtils.isEmpty(angelShipList) || !DeliveryTypeEnum.RIDER_DELIVERY.getType().equals(angelShipList.get(0).getType())) {
                return Boolean.FALSE;
            }
            AngelShip angelShip = angelShipList.get(0);
            // 运单已经完成或者取消，
            if (AngelShipStatusEnum.ORDER_SHIP_CANCEL.getShipStatus().equals(angelShip.getShipStatus())) {
                AngelShip updageAngelShip = AngelShip.builder().shipId(angelShipDadaCallBackContext.getOrderId()).build();
                updageAngelShip.setLogisticsMessage(angelShipDadaCallBackContext.getCancelReason());
                Integer angelShipCancelCode = this.getDdTimeoutCancelStandCancelCode(angelShipDadaCallBackContext, angelShip);
                updageAngelShip.setStandCancelCode(angelShipCancelCode);
                angelShipRepository.updateByShipId(updageAngelShip);
                // 发送达达异常告警事件，部分原因不发送
                if (!duccConfig.getDdCancelAngelShipNotSendAlarmReasonIdList().contains(angelShipDadaCallBackContext.getCancelReasonId())) {
                    eventCoordinator.publish(EventFactory.newDefaultEvent(angelShip, AngelShipEventTypeEnum.DD_CANCEL_ANGEL_WORK_SHIP, null));
                }
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("AngelWorkApplicationImpl ->updateAngelShipWithDdCancelReason has error", e);
            return Boolean.FALSE;
        }
    }

    /**
     * 获得达达超过72h未配送的异常状态
     * @param angelShipDadaCallBackContext 达达传入参数
     * @param angelShip 运单数据
     * @return 超时异常类型
     */
    private Integer getDdTimeoutCancelStandCancelCode(AngelShipCallBackContext angelShipDadaCallBackContext, AngelShip angelShip) {
        Integer ddOvertimeCancelId = 20025;
        if (angelShipDadaCallBackContext.getCancelReasonId() == null || !ddOvertimeCancelId.equals(angelShipDadaCallBackContext.getCancelReasonId())) {
            return AngelShipCancelCodeStatusEnum.DD_OPERATE_CANCEL.getType();
        }
        JdhAngelShipExtVo jdhAngelShipExtVo = angelShip.getJdhAngelShipExtVo();
        if (jdhAngelShipExtVo == null || CollectionUtils.isEmpty(jdhAngelShipExtVo.getMedicalPromiseIds())) {
            return null;
        }
        List<Long> medicalPromiseIds = jdhAngelShipExtVo.getMedicalPromiseIds();
        MedicalPromiseRequest medicalPromiseRequest = MedicalPromiseRequest.builder().medicalPromiseIds(medicalPromiseIds).build();
        List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseRequest);
        if (CollectionUtils.isEmpty(medicalPromiseDTOList)) {
            return null;
        }
        // 达达72超时取消,如果用户订单已经在收样、检测和出报告的阶段，则无需处理、不取消用户订单；否则联动取消用户订单。
        List<Integer> filterMedicalPromiseStatus = Lists.newArrayList(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(),
                MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),
                MedicalPromiseStatusEnum.COMPLETED.getStatus());
        medicalPromiseDTOList = medicalPromiseDTOList.stream().filter(e -> filterMedicalPromiseStatus.contains(e.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(medicalPromiseDTOList)) {
            return null;
        }
        return AngelShipCancelCodeStatusEnum.DD_TIMEOUT_CANCEL.getType();
    }
}
