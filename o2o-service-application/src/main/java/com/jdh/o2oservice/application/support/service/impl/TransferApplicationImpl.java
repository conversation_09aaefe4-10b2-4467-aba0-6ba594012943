package com.jdh.o2oservice.application.support.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.health.xfyl.merchant.export.dto.ServiceGuaranteelDTO;
import com.jd.health.xfyl.merchant.export.param.ServiceDetailParam;
import com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService;
import com.jdh.o2oservice.application.support.service.TransferApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.model.Birthday;
import com.jdh.o2oservice.base.model.CredentialNumber;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.UserName;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseTypeEnum;
import com.jdh.o2oservice.core.domain.promise.rpc.LocPromiseCodeRpc;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.LocCodeBO;
import com.jdh.o2oservice.core.domain.promise.rpc.bo.ServiceRpcBO;
import com.jdh.o2oservice.core.domain.support.basic.enums.*;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.JdhVoucherSourceTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.trade.enums.LocCodeStatusEnum;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderCalculationQueryServiceRpc;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAmount;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAmountExpand;
import com.jdh.o2oservice.core.domain.trade.vo.SkuFeaturesVo;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdOrderItemPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdOrderMoneyPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdOrderPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.transfer.XfylAppointmentInfoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.transfer.XfylAppointmentSkuMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.transfer.XfylOrderInfoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.transfer.XfylOrderSkuMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.transfer.po.XfylAppointmentInfoPo;
import com.jdh.o2oservice.infrastructure.repository.db.dao.transfer.po.XfylAppointmentInfoSkuPo;
import com.jdh.o2oservice.infrastructure.repository.db.dao.transfer.po.XfylOrderPo;
import com.jdh.o2oservice.infrastructure.repository.db.dao.transfer.po.XfylOrderSkuPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdOrderItemPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdOrderMoneyPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdOrderPo;
import com.jdh.o2oservice.infrastructure.rpc.JsfRpcInvokeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 * （）
 * @author: yangxiyu
 * @date: 2024/1/29 6:47 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class TransferApplicationImpl implements TransferApplication {


    @Resource
    private XfylAppointmentSkuMapper xfylAppointmentSkuMapper;
    @Resource
    private XfylAppointmentInfoMapper xfylAppointmentInfoMapper;
    @Resource
    private XfylOrderInfoMapper xfylOrderInfoMapper;
    @Resource
    private XfylOrderSkuMapper xfylOrderSkuMapper;
    @Resource
    private JdOrderPoMapper jdOrderPoMapper;
    @Resource
    private JdOrderItemPoMapper jdOrderItemPoMapper;


    @Resource
    private JdOrderMoneyPoMapper jdOrderMoneyPoMapper;
    @Resource
    private GenerateIdFactory generateIdFactory;
    @Resource
    private OrderCalculationQueryServiceRpc orderCalculationQueryServiceRpc;
    @Resource
    private SkuInfoRpc skuInfoRpc;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private PromiseRepository promiseRepository;
    @Resource
    private VoucherRepository voucherRepository;
    @Resource
    private LocPromiseCodeRpc locPromiseCodeRpc;
    @Resource
    private XfylMerchantAppointApiExportService xfylMerchantAppointApiExportService;
    private static final ThreadLocal<List<Long>> NOT_EXIST_LOC_CODE_ORDER = new ThreadLocal<>();
    /**
     * 当前配置
     */
    private static String ACTIVE;
    /**
     * 初始注入maven环境
     */
    @Value("${spring.profiles.active}")
    public void setActive(String value){
        ACTIVE = value;
    }
    /**
     * 同步历史疫苗订单
     * @param orderId
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.TransferApplicationImpl.syncPopLocOldOrder")
    public void syncPopLocOldOrder(String orderId, String createTime) {

        try {
            int pageNum = 1;
            Date start = TimeUtils.strToDate(createTime);
            Page<XfylOrderPo> page = pageOrder(orderId, pageNum, start);
            log.info("TransferApplicationImpl->syncPopLocOldOrder page={}", JSON.toJSONString(page));
            Page<XfylOrderPo> finalPage = page;
            processOrder(finalPage);
            while (page.hasNext()) {
                page = pageOrder(orderId, page.getCurrent() + 1, start);
                log.info("TransferApplicationImpl->syncPopLocOldOrder page={}", JSON.toJSONString(page));
                Page<XfylOrderPo> innerPage = page;
                processOrder(innerPage);
                log.info("TransferApplicationImpl->syncPopLocOldOrder hasNext={}", page.hasNext());

//                transactionTemplate.execute(k -> processOrder(innerPage));
            }
        }catch (Throwable e){
            log.error("TransferApplicationImpl->syncPopLocOldOrder error", e);
        }finally {
            log.warn("TransferApplicationImpl->syncPopLocOldOrder NOT_EXIST_LOC_CODE_ORDER={}", JSON.toJSONString(NOT_EXIST_LOC_CODE_ORDER.get()));
            NOT_EXIST_LOC_CODE_ORDER.remove();
        }
    }

    private static final Map<Integer, Integer> LOC_CODE_MAP_ORDER_STATUS = Maps.newHashMap();
    static{
        for (LocCodeStatusEnum value : LocCodeStatusEnum.values()) {
            LOC_CODE_MAP_ORDER_STATUS.put(value.getStatus(), value.getOrderStatus());
        }
    }

    private boolean processOrder(Page<XfylOrderPo> page) {

        List<Long> orderIds = page.getRecords().stream().map(XfylOrderPo::getOrderId).collect(Collectors.toList());
        LambdaQueryWrapper<XfylOrderSkuPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(XfylOrderSkuPo::getOrderId, orderIds)
                .eq(XfylOrderSkuPo::getYn, YnStatusEnum.YES.getCode());
        List<XfylOrderSkuPo> skuPos = xfylOrderSkuMapper.selectList(queryWrapper);
        Map<Long, List<XfylOrderSkuPo>> skuMap = skuPos.stream().collect(Collectors.groupingBy(XfylOrderSkuPo::getOrderId));

        LambdaQueryWrapper<JdOrderPo> deleteWrapper = new LambdaQueryWrapper<JdOrderPo>();
        deleteWrapper.in(JdOrderPo::getOrderId, orderIds);
        jdOrderPoMapper.delete(deleteWrapper);

        LambdaQueryWrapper<JdOrderItemPo> deleteItem = new LambdaQueryWrapper<JdOrderItemPo>();
        deleteItem.in(JdOrderItemPo::getOrderId, orderIds);
        jdOrderItemPoMapper.delete(deleteItem);

        LambdaQueryWrapper<JdOrderMoneyPo> deleteMoney = new LambdaQueryWrapper<JdOrderMoneyPo>();
        deleteMoney.in(JdOrderMoneyPo::getOrderId, orderIds);
        jdOrderMoneyPoMapper.delete(deleteMoney);

        voucherRepository.removeByOrderId(orderIds);

        promiseRepository.removeByOrderId(orderIds);
        Set<String> skuNos = skuPos.stream().map(XfylOrderSkuPo::getSkuNo).collect(Collectors.toSet());
        Map<String, RpcSkuBO> rpcSkuMap = skuInfoRpc.getSkuInfo(skuNos);


        for (XfylOrderPo orderPo : page.getRecords()) {
            try {
                log.info("TransferApplicationImpl->processOrder start orderId={}", orderPo.getOrderId());
                JdOrderPo order = new JdOrderPo();
                order.setVerticalCode("xfylPop");
                order.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
                order.setOrderId(orderPo.getOrderId());
                order.setParentId(orderPo.getParentId());

                List<LocCodeBO>  locCode = locPromiseCodeRpc.listLocCode(orderPo.getUserPin(), orderPo.getOrderId());
                if (CollectionUtils.isEmpty(locCode)){
                    NOT_EXIST_LOC_CODE_ORDER.get().add(orderPo.getOrderId());
                    continue;
                }
                LocCodeBO code = locCode.get(0);
                Integer orderStatus = LOC_CODE_MAP_ORDER_STATUS.get(code.getStatus());
                if (Objects.isNull(orderStatus)){
                    log.error("TransferApplicationImpl->processOrder locCode status not match status={}", code.getStatus());
                    continue;
                }
                order.setOrderStatus(orderStatus);

                order.setOrderType(orderPo.getOrderType());
                order.setSendPay(orderPo.getSendPay());
                order.setUserPin(orderPo.getUserPin());
                order.setOrderUserPhone(orderPo.getOrderUserPhone());
                order.setOrderUserPhoneIndex(orderPo.getOrderUserPhone());
                order.setOrderUserName(orderPo.getOrderUserName());
                order.setOrderUserNameIndex(orderPo.getOrderUserName());
                order.setVenderId(orderPo.getVenderId());
                order.setVenderName(orderPo.getVenderName());
                order.setPaymentTime(orderPo.getPaymentTime());
                order.setPaymentWay(orderPo.getPaymentWay());
                order.setPayType(orderPo.getPayType());

                order.setOrderAmount(new BigDecimal(orderPo.getOrderAmount()).
                        divide(new BigDecimal("100.00"), 2, RoundingMode.DOWN));
                if (Objects.nonNull(orderPo.getOrderDiscount())) {
                    order.setOrderDiscount(new BigDecimal(orderPo.getOrderDiscount()).
                            divide(new BigDecimal("100.00"), 2, RoundingMode.DOWN));
                }
                order.setOrderTotalAmount(new BigDecimal(orderPo.getOrderTotalAmount()).
                        divide(new BigDecimal("100.00"), 2, RoundingMode.DOWN));

                order.setInvoiceState(orderPo.getInvoiceState());
                order.setInvoiceType(orderPo.getInvoiceType());
                order.setTitle(orderPo.getInvoiceTitle());
                order.setInvoiceContent(orderPo.getInvoiceContent());
                order.setOrgId(orderPo.getOrgId());
                order.setVersion(1);

                List<XfylOrderSkuPo> skuList = skuMap.get(orderPo.getOrderId());
                if (CollectionUtil.isEmpty(skuList)) {
                    log.error("processOrder skuList is empty orderId={}", orderPo.getOrderId());
                    throw new NullPointerException("skuList is empty");
                }

                // 遍历 examination_order_sku_info
                List<JdOrderItemPo> jdOrderItemList = Lists.newArrayList();
                for (XfylOrderSkuPo xfylOrderSkuPo : skuList) {
                    JdOrderItemPo item = new JdOrderItemPo();
                    item.setVerticalCode("xfylPop");
                    item.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
                    item.setOrderId(orderPo.getOrderId());
                    item.setOrderItemId(generateIdFactory.getId());
                    item.setUserPin(orderPo.getUserPin());
                    item.setSkuId(Long.valueOf(xfylOrderSkuPo.getSkuNo()));
                    item.setSkuName(xfylOrderSkuPo.getSkuName());
                    item.setSkuNum(xfylOrderSkuPo.getSkuNum());
                    item.setSkuImage(xfylOrderSkuPo.getSkuImage());

                    item.setItemAmount(new BigDecimal(xfylOrderSkuPo.getSkuAmount()).
                            divide(new BigDecimal("100.00"), 2, RoundingMode.DOWN));


                    item.setSkuExpireDate(xfylOrderSkuPo.getSkuExpireDate());
                    item.setStoreId(orderPo.getStoreId());
                    item.setVenderId(orderPo.getVenderId());

                    RpcSkuBO rpcSku = rpcSkuMap.get(xfylOrderSkuPo.getSkuNo());
                    SkuFeaturesVo featuresVo = new SkuFeaturesVo();
                    featuresVo.setCid1(rpcSku.getFirstCategoryId());
                    featuresVo.setCid2(rpcSku.getSecondCategoryId());
                    featuresVo.setCid3(rpcSku.getThirdCategoryId());
                    item.setSkuFeatures(JSON.toJSONString(featuresVo));
                    jdOrderItemList.add(item);
                }


                String amountAndExpand = orderCalculationQueryServiceRpc.queryOrderSplitAmountAndExpand(orderPo.getOrderId());
                List<OrderAmount> orderAmountList = JSON.parseArray(amountAndExpand, OrderAmount.class);
                Map<Long, JdOrderItemPo> skuPoMap = jdOrderItemList.stream().collect(Collectors.toMap(JdOrderItemPo::getSkuId, Function.identity(), (o, n) -> o));
                List<JdOrderMoneyPo> jdOrderMoneyList = Lists.newArrayList();
                log.info("TransferApplicationImpl->processOrder orderId={} orderAmountList={}", orderPo.getOrderId(), JSON.toJSONString(amountAndExpand));
                for (OrderAmount amount : orderAmountList) {
                    Map<Integer, List<OrderAmountExpand>> orderAmountExpandMap = amount.getAmountExpands().stream().collect(Collectors.groupingBy(b -> b.getType()));
                    orderAmountExpandMap.forEach((type, amounts) -> {
                        JdOrderMoneyPo moneyPo = new JdOrderMoneyPo();
                        JdOrderItemPo orderItem = skuPoMap.get(amount.getSkuId());
                        moneyPo.setOrderItemId(orderItem.getOrderItemId());
                        moneyPo.setOrderId(orderPo.getOrderId());
                        moneyPo.setSkuId(orderItem.getSkuId());
                        moneyPo.setMoneyType(type);

                        BigDecimal curAmount = new BigDecimal(0);
                        for (OrderAmountExpand amountExpand : amounts) {
                            curAmount = curAmount.add(amountExpand.getAmount());
                        }
                        moneyPo.setAmount(curAmount);
                        jdOrderMoneyList.add(moneyPo);
                    });

                    jdOrderPoMapper.insert(order);
                    for (JdOrderItemPo jdOrderItemPo : jdOrderItemList) {
                        jdOrderItemPoMapper.insert(jdOrderItemPo);
                    }
                    for (JdOrderMoneyPo moneyPo : jdOrderMoneyList) {
                        jdOrderMoneyPoMapper.insert(moneyPo);
                    }
                }
            }catch (Exception e){
                log.error("processOrder error orderId={}", orderPo.getOrderId(), e);
            }
        }

        return Boolean.TRUE;
    }




    private Page<XfylOrderPo> pageOrder(String orderId, long pageNum, Date start){
        Page<XfylOrderPo> param = new Page<>(pageNum, 10);

        LambdaQueryWrapper<XfylOrderPo> queryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(orderId)){
            queryWrapper.eq(XfylOrderPo::getOrderId,Long.valueOf(orderId));
        }
        queryWrapper.eq(XfylOrderPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.ge(XfylOrderPo::getCreateTime, start);
        queryWrapper.eq(XfylOrderPo::getAppointSource, 7);
        queryWrapper.eq(XfylOrderPo::getOrderAppointType, 0);
        queryWrapper.orderByAsc(XfylOrderPo::getCreateTime);
        Page<XfylOrderPo> page = xfylOrderInfoMapper.selectPage(param, queryWrapper);
        return page;
    }


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.TransferApplicationImpl.syncPopLocOldAppointment")
    public void syncPopLocOldAppointment(String orderId) {

        try {
            if (StringUtils.isNotBlank(orderId)){
                doSyncPromise(orderId);
            }else{
                int pageNum = 1;
                Date start = TimeUtils.strToDate("2010-01-01");
                Page<XfylOrderPo> page = pageOrder(orderId, pageNum, start);
                Page<XfylOrderPo> finalPage = page;
                for (XfylOrderPo record : finalPage.getRecords()) {
                    try {
                        doSyncPromise(String.valueOf(record.getOrderId()));
                    }catch (Exception e){
                        log.error("TransferApplicationImpl->syncPopLocOldAppointment doSyncPromise error orderId={}", record.getOrderId());
                    }
                }
//            transactionTemplate.execute(k -> processOrder(finalPage));
                while (page.hasNext()) {
                    page = pageOrder(orderId, page.getCurrent() + 1, start);
                    Page<XfylOrderPo> innerPage = page;
                    for (XfylOrderPo record : innerPage.getRecords()) {
                        try {
                            doSyncPromise(String.valueOf(record.getOrderId()));
                        }catch (Exception e){
                            log.error("TransferApplicationImpl->syncPopLocOldAppointment doSyncPromise error orderId={}", record.getOrderId());
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("TransferApplicationImpl->syncPopLocOldAppointment error", e);
        }
    }

    /**
     * 如果订单号不为空的话同步这个订单，否则刷全部数据
     * （1）查询当前订单数据；
     * （2）根据订单查询预约中心的预约单以及预约单SKU信息
     * @param orderId
     */
    public void doSyncPromise(String orderId) {

        log.info("TransferApplicationImpl->doSyncPromise orderId={}", orderId);
        voucherRepository.removeByOrderId(Lists.newArrayList(Long.valueOf(orderId)));

        promiseRepository.removeByOrderId(Lists.newArrayList(Long.valueOf(orderId)));

        // 查询订单数据
        // 根据订单查询老的预约单
        // 构建服务单
        JdhVoucher voucher = new JdhVoucher();

        voucher.setBranch(ACTIVE);
        voucher.setCreateUser("system");
        voucher.setUpdateUser(orderId);
        voucher.setVerticalCode("xfylPop");
        voucher.setSourceType(JdhVoucherSourceTypeEnum.JD_ORDER.getType());
        // 根据orderAppointType进行识别
        voucher.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
        // 生成唯一ID
        voucher.setVoucherId(generateIdFactory.getId());

        LambdaQueryWrapper<JdOrderItemPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdOrderItemPo::getOrderId, orderId)
                .eq(JdOrderItemPo::getYn, YnStatusEnum.YES.getCode());
        List<JdOrderItemPo> itemPos = jdOrderItemPoMapper.selectList(queryWrapper);
        log.info("TransferApplicationImpl->doSyncPromise orderItems={}", JSON.toJSONString(itemPos));

        LambdaQueryWrapper<JdOrderPo> queryOrder = Wrappers.lambdaQuery();
        queryOrder.eq(JdOrderPo::getOrderId, orderId)
                .eq(JdOrderPo::getYn, YnStatusEnum.YES.getCode());
        JdOrderPo orderPo = jdOrderPoMapper.selectOne(queryOrder);

        List<JdhPromise> promises = Lists.newArrayList();
        for (JdOrderItemPo itemPo : itemPos) {
            voucher.setUserPin(orderPo.getUserPin());
            voucher.setSourceVoucherId(String.valueOf(itemPo.getOrderItemId()));
            voucher.setSourceType(JdhVoucherSourceTypeEnum.JD_ORDER.getType());
            voucher.setPromiseNum(1);
            // 退款中的订单会被冻结
            if (Objects.equals(OrderStatusEnum.ORDER_REFUNDING.getStatus(), orderPo.getOrderStatus())){
                voucher.setFreeze(JdhFreezeEnum.FREEZE.getStatus());
            }else{
                voucher.setFreeze(JdhFreezeEnum.UN_FREEZE.getStatus());
            }

            // 根据订单查询预约单，如果存在多个预约单(不应该存在多个)，需要记录并break

            LambdaQueryWrapper<XfylAppointmentInfoPo> oldAppointmentWrapper = Wrappers.lambdaQuery();
            oldAppointmentWrapper.eq(XfylAppointmentInfoPo::getOrderId, orderId);
            oldAppointmentWrapper.eq(XfylAppointmentInfoPo::getYn, YnStatusEnum.YES.getCode());
            List<XfylAppointmentInfoPo> oldAppointments = xfylAppointmentInfoMapper.selectList(oldAppointmentWrapper);
            if (CollectionUtil.isEmpty(oldAppointments)){
                log.error("oldAppointments is empty orderId={}",orderId);
                continue;
            }
            if (oldAppointments.size() > 1){
                log.error("oldAppointments overflow orderId={}",orderId);
                continue;
            }
            XfylAppointmentInfoPo xfylAppointmentInfoPo = oldAppointments.get(0);
            log.info("TransferApplicationImpl->doSyncPromise xfylAppointmentInfoPo={}", JSON.toJSONString(xfylAppointmentInfoPo));

            /**
             * orderStatus -> vouchderStatus
             * 已付款 -> 待服务
             * 已完成 -> 完成
             * 退款中 -> 待服务
             * 退款完成 -> 作废
             */
            if(Objects.equals(orderPo.getOrderStatus(), OrderStatusEnum.ORDER_PAID.getStatus())){ // 退款作废
                voucher.setStatus(JdhVoucherStatusEnum.WAIT_SERVICE.getStatus());
            }else if(Objects.equals(orderPo.getOrderStatus(), OrderStatusEnum.ORDER_COMPLETE.getStatus())){
                voucher.setStatus(JdhVoucherStatusEnum.COMPLETE.getStatus());
            }else if(Objects.equals(orderPo.getOrderStatus(), OrderStatusEnum.ORDER_REFUNDING.getStatus())){
                voucher.setStatus(JdhVoucherStatusEnum.WAIT_SERVICE.getStatus());
            }else if(Objects.equals(orderPo.getOrderStatus(), OrderStatusEnum.ORDER_REFUND.getStatus())){
                voucher.setStatus(JdhVoucherStatusEnum.INVALID.getStatus());
            }
            if (Objects.nonNull(xfylAppointmentInfoPo.getExpireDate())){
                voucher.setExpireDate(TimeUtils.timeStrToDate(String.valueOf(xfylAppointmentInfoPo.getExpireDate()), TimeFormat.LONG_PATTERN_NONE_COLON));
            }

            JdhVoucherExtend extend = new JdhVoucherExtend();
            extend.setOrderId(String.valueOf(orderPo.getOrderId()));
            extend.setAutoPromise(Boolean.FALSE);
            extend.setSkuId(String.valueOf(itemPo.getSkuId()));
            extend.setSkuName(itemPo.getSkuName());
            extend.setVenderId(orderPo.getVenderId());

            voucher.setExtend(extend);

            // 构建履约单
            JdhPromise promise = buildPromise(voucher, xfylAppointmentInfoPo);
            promises.add(promise);
        }

        LambdaQueryWrapper<XfylOrderSkuPo> skuWrapper = Wrappers.lambdaQuery();
        skuWrapper.eq(XfylOrderSkuPo::getOrderId, orderId)
                .eq(XfylOrderSkuPo::getYn, YnStatusEnum.YES.getCode());
        List<XfylOrderSkuPo> skuPos = xfylOrderSkuMapper.selectList(skuWrapper);
        if(CollectionUtil.isNotEmpty(skuPos)){
            List<JdhVoucherItem> voucherItemList = Lists.newArrayList();
            for (XfylOrderSkuPo skuPo : skuPos) {
                JdhVoucherItem item = new JdhVoucherItem();
                item.setVerticalCode("xfylPop");
                item.setServiceType(voucher.getServiceType());
                item.setVoucherId(voucher.getVoucherId());
                // skuNo
                item.setServiceId(Long.valueOf(skuPo.getSkuNo()));
                item.setTag(1);
                item.setBranch(ACTIVE);
                item.setCreateUser("system");
                // 设置orderID，实现回滚删除
                item.setUpdateUser(orderId);
                voucherItemList.add(item);
            }
            voucher.setVoucherItemList(voucherItemList);
        }

        log.info("TransferApplicationImpl->doSyncPromise voucher={}", JSON.toJSONString(voucher));
        log.info("TransferApplicationImpl->doSyncPromise promise={}", JSON.toJSONString(promises));
        transactionTemplate.execute(status -> {
            voucherRepository.save(voucher);
            for (JdhPromise promise : promises) {
                promiseRepository.save(promise);
            }
            return 0;
        });
    }

    /**
     * 构建履约单
     * @return
     */
    @SuppressWarnings("JdUndefineMagicConstant")
    private JdhPromise buildPromise(JdhVoucher voucher, XfylAppointmentInfoPo xfylAppointmentInfoPo){
        LambdaQueryWrapper<XfylAppointmentInfoSkuPo> skuWrapper = Wrappers.lambdaQuery();
        skuWrapper.eq(XfylAppointmentInfoSkuPo::getJdAppointmentId, xfylAppointmentInfoPo.getJdAppointmentId());
        skuWrapper.eq(XfylAppointmentInfoSkuPo::getYn, YnStatusEnum.YES.getCode());


        JdhPromise promise = new JdhPromise();
        List<LocCodeBO>  locCode = locPromiseCodeRpc.listLocCode(voucher.getUserPin(), xfylAppointmentInfoPo.getOrderId());
        LocCodeBO locCodeBO = locCode.get(0);
        promise.setUserPin(voucher.getUserPin());
        promise.setVerticalCode(voucher.getVerticalCode());
        promise.setServiceType(voucher.getServiceType());

        // 老的预约单ID
        promise.setPromiseId(xfylAppointmentInfoPo.getJdAppointmentId());
        promise.setVoucherId(voucher.getVoucherId());
        promise.setSerialNum(1);
        promise.setCodeId(String.valueOf(locCodeBO.getCodeRelationId()));
        promise.setCode(locCodeBO.getCodeNum());
        promise.setCodePwd(locCodeBO.getPwdNumber());


        /**
         * （1）优先根据LOC code码的状态映射
         * com.jdh.o2oservice.core.domain.promise.rpc.LocPromiseCodeRpc
         * 参考：com.jdh.o2oservice.core.domain.trade.enums.LocCodeStatusEnum
         * （2）只有LOC核销码是待消费，才使用之前预约单的预约状态进行映射
         */
        if (Objects.equals(voucher.getStatus(), JdhVoucherStatusEnum.COMPLETE.getStatus())){
            promise.setPromiseStatus(JdhPromiseStatusEnum.COMPLETE.getStatus());
        }else if(Objects.equals(voucher.getStatus(), JdhVoucherStatusEnum.INVALID.getStatus())){
            promise.setPromiseStatus(JdhPromiseStatusEnum.INVALID.getStatus());
        }else{
            if (Objects.equals(xfylAppointmentInfoPo.getOrderStatus(), 0)){
                promise.setPromiseStatus(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus());
            }else if(Objects.equals(xfylAppointmentInfoPo.getOrderStatus(), 1)){
                promise.setPromiseStatus(JdhPromiseStatusEnum.APPOINTMENT_FAIL.getStatus());
            }else if(Objects.equals(xfylAppointmentInfoPo.getOrderStatus(), 2)){
                promise.setPromiseStatus(JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus());
            }else if(Objects.equals(xfylAppointmentInfoPo.getOrderStatus(), 3)){
                promise.setPromiseStatus(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus());
            }else if(Objects.equals(xfylAppointmentInfoPo.getOrderStatus(), 4)){
                promise.setPromiseStatus(JdhPromiseStatusEnum.COMPLETE.getStatus());
            }else if(Objects.equals(xfylAppointmentInfoPo.getOrderStatus(), 5)){
                promise.setPromiseStatus(JdhPromiseStatusEnum.EXPIRATION.getStatus());
            }else if(Objects.equals(xfylAppointmentInfoPo.getOrderStatus(), 6)){
                promise.setPromiseStatus(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus());
                promise.setFreeze(JdhFreezeEnum.FREEZE.getStatus());
            }else if(Objects.equals(xfylAppointmentInfoPo.getOrderStatus(), 8)){
                promise.setPromiseStatus(JdhPromiseStatusEnum.INVALID.getStatus());
            }else if(Objects.equals(xfylAppointmentInfoPo.getOrderStatus(), 12)){
                promise.setPromiseStatus(JdhPromiseStatusEnum.CANCEL_ING.getStatus());
            }else{
                log.error("TransferApplicationImpl->buildPromise promiseId={}", xfylAppointmentInfoPo.getJdAppointmentId());
                throw new IllegalArgumentException("xfylAppointmentInfoPo order status illegal");
            }
        }


        String skuInfo = xfylAppointmentInfoPo.getSkuInfo();
        JSONObject jsonObject = JSON.parseObject(skuInfo);
        /**
         * skuInfo不包含预约类型，则根据是否存在预约时间进行判断
         */
        if (Objects.isNull(jsonObject) || !jsonObject.containsKey("appointmentType")){
                // 有预约时间就是在线预约
            if(Objects.nonNull(xfylAppointmentInfoPo.getAppointmentStartTime())){
                promise.setPromiseType(PromiseTypeEnum.ONLINE_APPOINTMENT.getCode());
                // 没有预约时间则查询商品信息
            }else{
                ServiceDetailParam serviceDetailParam = new ServiceDetailParam();
                serviceDetailParam.setSkuNo(xfylAppointmentInfoPo.getSkuNo());
                ServiceGuaranteelDTO guaranteelDTO = null;
                try {
                    guaranteelDTO = JsfRpcInvokeUtil.invoke(() -> xfylMerchantAppointApiExportService.queryServiceDetail(serviceDetailParam));
                }catch (Exception e){
                    log.warn("queryServiceDetail error skuNo={}", xfylAppointmentInfoPo.getSkuNo());
                }
                if(Objects.isNull(guaranteelDTO)){
                    promise.setPromiseType(1);
                }else{
                    promise.setPromiseType(guaranteelDTO.getAppointmentType());
                }
            }

        }else{
            Integer type=(Integer) jsonObject.get("appointmentType");
            promise.setPromiseType(type);
        }

        if (Objects.nonNull(xfylAppointmentInfoPo.getExpireDate())){
            promise.setExpireDate(TimeUtils.dateToLocalDateTime(voucher.getExpireDate()));
        }

        promise.setChannelNo(xfylAppointmentInfoPo.getChannelNo());
        promise.setAppointmentId(xfylAppointmentInfoPo.getJdAppointmentId());
        // 判断老的预约单状态如果是预约成功、已到检、核销需要设置
        promise.setChannelAppointmentId(String.valueOf(xfylAppointmentInfoPo.getAppointmentNo()));
        promise.setSourceVoucherId(voucher.getSourceVoucherId());

        // 如果时间有则格式化落库
        //【备注】：已到检并不一定有预约时间，商品虽然配置的是在线预约，但是用户可以不通过线上预约，而是通过电话预约，然后去门店打完疫苗后，推送到检。
        if (Objects.nonNull(xfylAppointmentInfoPo.getAppointmentStartTime())){
            PromiseAppointmentTime appointmentTime = new PromiseAppointmentTime();
            LocalDateTime start = TimeUtils.dateToLocalDateTime(xfylAppointmentInfoPo.getAppointmentStartTime());
            appointmentTime.setAppointmentStartTime(LocalDateTime.of(start.toLocalDate(), LocalTime.of(0,0,0)));
            appointmentTime.setAppointmentEndTime(LocalDateTime.of(start.toLocalDate(), LocalTime.of(0,0,0)));

            appointmentTime.setDateType(DateTypeEnum.SCHEDULE_BY_DATE.getType());
            promise.setAppointmentTime(appointmentTime);
        }

        JdhPromisePatient user = new JdhPromisePatient();
        if (StringUtils.isNotBlank(xfylAppointmentInfoPo.getUserCredentialNo())){
            user.setCredentialNum(new CredentialNumber(xfylAppointmentInfoPo.getUserCredentialType(), xfylAppointmentInfoPo.getUserCredentialNo()));
        }
        if (StringUtils.isNotBlank(xfylAppointmentInfoPo.getBirthday())){
            user.setBirthday(new Birthday(xfylAppointmentInfoPo.getBirthday()));
        }
        user.setPatientId(xfylAppointmentInfoPo.getPatientId());
        user.setUserPin(xfylAppointmentInfoPo.getUserPin());
        if (StringUtils.isNotBlank(xfylAppointmentInfoPo.getUserPhone())){
            user.setPhoneNumber(new PhoneNumber(xfylAppointmentInfoPo.getUserPhone()));
        }
        user.setMarriage(xfylAppointmentInfoPo.getUserMarriage());
        user.setGender(xfylAppointmentInfoPo.getUserGender());
        if (StringUtils.isNotBlank(xfylAppointmentInfoPo.getUserName())){
            user.setUserName(new UserName(xfylAppointmentInfoPo.getUserName()));
        }
        user.setRelativesType(xfylAppointmentInfoPo.getRelativesType());
        user.setPromisePatientId(generateIdFactory.getId());
        user.setVerticalCode(voucher.getVerticalCode());
        user.setServiceType(voucher.getServiceType());
        user.setPromiseId(xfylAppointmentInfoPo.getJdAppointmentId());
        user.setVoucherId(promise.getVoucherId());
        user.setSourceVoucherId(promise.getSourceVoucherId());
        user.setCreateUser("system");
        user.setUpdateUser(voucher.getUpdateUser());
        List<JdhPromisePatient> patients = Lists.newArrayList(user);
        promise.setPatients(patients);

        PromiseStation store = new PromiseStation();
        store.setChannelNo(xfylAppointmentInfoPo.getChannelNo());
        store.setStoreId(xfylAppointmentInfoPo.getStoreId());
        store.setStoreName(xfylAppointmentInfoPo.getStoreName());
        store.setStoreAddr(xfylAppointmentInfoPo.getStoreAddr());
        store.setStorePhone(xfylAppointmentInfoPo.getStorePhone());
        promise.setStore(store);

        /**
         * 预约单SKU进行同步 examination_appointment_sku_info
         */
        List<PromiseService> services = Lists.newArrayList();
        if (StringUtils.isNotBlank(xfylAppointmentInfoPo.getSkuNo())){
            PromiseService service = new PromiseService();
            service.setPromiseId(promise.getPromiseId());
            service.setServiceId(Long.valueOf(xfylAppointmentInfoPo.getSkuNo()));
            service.setServiceName(xfylAppointmentInfoPo.getSkuName());
            service.setTags(1);
            service.setOutServiceId(xfylAppointmentInfoPo.getGoodsId());
            ServiceRpcBO rpcBO = new ServiceRpcBO();
            rpcBO.setSupportMarry(xfylAppointmentInfoPo.getUserMarriage());
            rpcBO.setSupportGender(xfylAppointmentInfoPo.getUserGender());
            service.setOutServiceName(rpcBO.dynamicGoodsName());
            service.setBranch(ACTIVE);
            service.setCreateUser("system");
            service.setUpdateUser(voucher.getUpdateUser());
            services.add(service);
        }


        promise.setServices(services);
        promise.setCreateUser("system");
        promise.setUpdateUser(voucher.getUpdateUser());
        return promise;
    }
}
