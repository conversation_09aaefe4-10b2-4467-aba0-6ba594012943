package com.jdh.o2oservice.application.dispatch.event;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.event.*;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.DispatchTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.dispatch.context.QueryAngelWorkContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchExecuteRouteEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.FlowExecuteTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.event.*;
import com.jdh.o2oservice.core.domain.dispatch.flow.condition.FlowCondition;
import com.jdh.o2oservice.core.domain.dispatch.model.*;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchFlowTaskRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchRepQuery;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.service.JdhDispatchDomainService;
import com.jdh.o2oservice.core.domain.support.basic.enums.DispatchFlowTaskStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelPageRequest;
import com.jdh.o2oservice.export.dispatch.cmd.AngelDispatchCmd;
import com.jdh.o2oservice.export.dispatch.cmd.DispatchAngelDetail;
import com.jdh.o2oservice.export.dispatch.cmd.DispatchCallbackCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchFlowEventSubscriber
 * @Description
 * <AUTHOR>
 * @Date 2024/10/28 20:13
 **/
@Slf4j
@Component
public class DispatchFlowEventSubscriber {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * dispatchApplication
     */
    @Resource
    private DispatchApplication dispatchApplication;

    /**
     * angelApplication
     */
    @Resource
    private AngelApplication angelApplication;

    /**
     * dispatchFlowTaskRepository
     */
    @Resource
    private DispatchFlowTaskRepository dispatchFlowTaskRepository;

    /**
     * dispatchRepository
     */
    @Resource
    private DispatchRepository dispatchRepository;

    /**
     *
     */
    @Autowired
    private VerticalBusinessRepository businessRepository;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * commonDuccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * dongDongRobotRpc
     */
    @Resource
    private DongDongRobotRpc dongDongRobotRpc;
    @Resource
    private JdhDispatchDomainService jdhDispatchDomainService;

    @Resource
    private FlowCondition flowCondition;

    /**
     * 注册事件使用者
     */
    @PostConstruct
    public void registerEventConsumer() {
        //------------------------派单流程--------------------------

        //=======>>>>>>派单任务 发起流程执行
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_FLOW_EXECUTE, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchFlowExecuteHandle", this::dispatchFlowExecuteHandle, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3, 5000, 3.0, 600000)));

        //=======>>>>>>派单任务 派单执行超时提醒
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_FLOW_ALARM_DELAY, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchFlowAlarmDelay", this::dispatchFlowAlarmDelay, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3, 2000, 2.0, 30000)));

        /** 护士接单成功 */
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_RECEIVED, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "dispatchConflictAnalysis", this::dispatchConflict, Boolean.TRUE));
        /** 系统指定护士接单,护士被动接单成功 */
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_TARGET_DISPATCH, WrapperEventConsumer.newInstance(DomainEnum.BASE,
                "dispatchConflictAnalysis", this::dispatchConflict, Boolean.TRUE));

        //=======>>>>>>派单任务 流程执行成功
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_FLOW_SUCCESS, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchFlowSuccessHandle", this::dispatchFlowResultHandle, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3, 15000, 2.0, 300000)));

        //=======>>>>>>派单任务 流程执行成功-计时x分钟后，无人接单发送派单失败事件
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_FLOW_SUCCESS_DECISION, WrapperEventConsumer.newDelayInstance(DomainEnum.DISPATCH,
                "dispatchFlowSuccessDecision", this::dispatchFlowSuccessDecision));

        //=======>>>>>>派单任务 流程执行失败
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_FLOW_FAIL, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchFlowFailHandle", this::dispatchFlowResultHandle, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3, 15000, 2.0, 300000)));
    }

    /**
     * 派单任务 发起流程执行
     *
     * @param event
     */
    private void dispatchFlowExecuteHandle(Event event) {
        log.info("DispatchEventSubscriber -> dispatchFlowExecuteHandle event:{}", JSON.toJSONString(event));
        DispatchFlowResultEventBody eventBody = JSON.parseObject(event.getBody(), DispatchFlowResultEventBody.class);
        AngelDispatchCmd cmd = AngelDispatchCmd.builder()
                .dispatchId(eventBody.getDispatchId())
                //.userPin()
                .voucherId(eventBody.getVoucherId())
                .promiseId(eventBody.getPromiseId())
                .flowId(eventBody.getFlowId())
                .build();
        dispatchApplication.dispatchFlowExecuteHandle(cmd);
    }

    /**
     * 派单执行超时提醒
     *
     * @param event
     */
    private void dispatchFlowAlarmDelay(Event event) {
        log.info("DispatchEventSubscriber -> dispatchFlowAlarmDelay event:{}", JSON.toJSONString(event));
        DispatchFlowResultEventBody eventBody = JSON.parseObject(event.getBody(), DispatchFlowResultEventBody.class);
        //查询派单任务是否有人接单，如果没有，发送派单失败报警
        JdhDispatchFlowTask dispatchFlowTask = dispatchFlowTaskRepository.find(JdhDispatchFlowTaskIdentifier.builder().flowTaskId(eventBody.getFlowId()).build());
        if (Objects.isNull(dispatchFlowTask)) {
            log.info("DispatchEventSubscriber -> dispatchFlowAlarmDelay, 没有有效的派单任务，默认不做处理 dispatchFlowTask:{}", JSON.toJSONString(dispatchFlowTask));
            return;
        }


        //待执行、执行中的任务报警，默认派单失败
        ArrayList<Integer> alarmStatus = Lists.newArrayList(DispatchFlowTaskStatusEnum.DISPATCH_FLOW_WAIT.getStatus(), DispatchFlowTaskStatusEnum.DISPATCH_FLOW_EXECUTING.getStatus());
        if (alarmStatus.contains(dispatchFlowTask.getFlowStatus())) {
            log.info("DispatchEventSubscriber -> dispatchFlowAlarmDelay, 派单任务超时，默认按派单失败处理 dispatchFlowTask:{}", JSON.toJSONString(dispatchFlowTask));
            dispatchFlowTask.setFlowStatus(DispatchFlowTaskStatusEnum.DISPATCH_FAIL.getStatus());
            dispatchFlowTaskRepository.save(dispatchFlowTask);
            //派单失败，发送事件
            eventCoordinator.publish(EventFactory.newDefaultEvent(dispatchFlowTask, DispatchEventTypeEnum.DISPATCH_FLOW_FAIL, DispatchFlowResultEventBody.builder().flowId(dispatchFlowTask.getFlowTaskId()).dispatchId(dispatchFlowTask.getDispatchId()).build()));

        }


    }

    /**
     * 判断派单任务是否存在冲突
     *
     * @param event
     */
    public void dispatchConflict(Event event) {
        Long dispatchId = Long.valueOf(event.getAggregateId());

        Long angelId = null;
        if (StringUtils.equals(event.getEventCode(), DispatchEventTypeEnum.DISPATCH_TARGET_DISPATCH.getCode())) {
            TargetDispatchEventBody eventBody = JSON.parseObject(event.getBody(), TargetDispatchEventBody.class);
            angelId = eventBody.getTargetAngelId();
        } else if (StringUtils.equals(event.getEventCode(), DispatchEventTypeEnum.DISPATCH_RECEIVED.getCode())) {
            DispatchCallbackEventBody eventBody = JSON.parseObject(event.getBody(), DispatchCallbackEventBody.class);
            angelId = eventBody.getAngelId();
        } else {
            return;
        }

        JdhDispatch dispatch = dispatchRepository.find(new JdhDispatchIdentifier(dispatchId));
        QueryAngelWorkContext param = new QueryAngelWorkContext();
        param.setDispatchId(dispatch.getDispatchId());
        param.setAngelId(angelId);
        AngelWorkData workData = jdhDispatchDomainService.queryValidAngelWork(param);
        int size = 0;
        if (CollectionUtils.isNotEmpty(workData.getAngelWorkDetailList())) {
            size = workData.getAngelWorkDetailList().size();
        }
        if (size > 1) {
            DispatchConflictEventBody eventBody = DispatchConflictEventBody.builder()
                    .targetAngelId(angelId)
                    .conflictSize(2)
                    .build();
            eventCoordinator.publish(EventFactory.newDefaultEvent(dispatch, DispatchEventTypeEnum.DISPATCH_TIME_CONFLICT, eventBody));
        }

    }

    /**
     * 派单流程执行成功
     *
     * @param event
     */
    private void dispatchFlowResultHandle(Event event) {
        log.info("DispatchEventSubscriber -> dispatchFlowResultHandle event:{}", JSON.toJSONString(event));
        try {
            DispatchFlowResultEventBody eventBody = JSON.parseObject(event.getBody(), DispatchFlowResultEventBody.class);
            DispatchFlowResultEventContent eventContent = eventBody.getContent();
            JdhDispatch snapshot = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(eventBody.getDispatchId()).build());
            if (Objects.isNull(snapshot)) {
                log.info("DispatchEventSubscriber -> dispatchFlowResultHandle 非消费医疗派单消息:{}", com.alibaba.fastjson.JSON.toJSONString(eventBody));
                return;
            }
            if (!Objects.equals(snapshot.getServiceInfo().getFlowId(), eventBody.getFlowId())) {
                log.info("DispatchEventSubscriber -> dispatchFlowResultHandle 非最新派单结果消息，不处理: snapshot={}, eventBody={}", JSON.toJSONString(snapshot), JSON.toJSONString(eventBody));
                return;
            }

            //派单失败
            if (Objects.equals(DispatchEventTypeEnum.DISPATCH_FLOW_FAIL.getCode(), event.getEventCode())) {
                log.info("DispatchEventSubscriber -> dispatchFlowResultHandle 派单失败消息:{}", com.alibaba.fastjson.JSON.toJSONString(eventBody));

                // 派单是失败，如果是意向派单，且轮次是1，则需要重新派
                JdhDispatchFlowTask dispatchFlowTask = dispatchFlowTaskRepository.find(JdhDispatchFlowTaskIdentifier.builder().flowTaskId(eventBody.getFlowId()).build());
                log.info("DispatchEventSubscriber -> dispatchFlowResultHandle dispatchFlowTask:{}", com.alibaba.fastjson.JSON.toJSONString(dispatchFlowTask));

                FlowExecuteTypeEnum flowExecuteType = flowCondition.getFlowExecuteType(dispatchFlowTask, snapshot);
                log.info("DispatchEventSubscriber -> dispatchFlowResultHandle flowExecuteType={}", flowExecuteType.getType());
                // 多轮次派单
                if (flowExecuteType == FlowExecuteTypeEnum.MULTIPLE_ROUNDS || flowExecuteType == FlowExecuteTypeEnum.INTENDED_BACK || flowExecuteType == FlowExecuteTypeEnum.STANDARD_ASSIGN_ROUNDS_BACK || dispatchFlowTask.executeDispatchPipelineContinue()) {
                    AngelDispatchCmd retryCmd = AngelDispatchCmd.builder()
                            .dispatchId(dispatchFlowTask.getDispatchId())
                            .flowId(dispatchFlowTask.getFlowTaskId())
                            .flowExecuteType(flowExecuteType.getType())
                            .executeDispatchPipelineContinue(dispatchFlowTask.executeDispatchPipelineContinue())
                            .build();
                    dispatchApplication.dispatchFlowExecuteHandle(retryCmd);
                }else{
                    //更新派单任务
                    DispatchCallbackCmd cmd = new DispatchCallbackCmd();
                    cmd.setDispatchId(snapshot.getDispatchId());
                    cmd.setDispatchStatus(JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus());
                    cmd.setDispatchType(DispatchTypeEnum.valuesOf(snapshot.getDispatchType()));
                    cmd.setPromiseId(snapshot.getPromiseId());
                    cmd.setDispatchExecuteRoute(DispatchExecuteRouteEnum.FLOW.getRouteCode());
                    dispatchApplication.nethpTriageDispatchCallBack(cmd);
                }
            }
            //派单成功
            else if (Objects.equals(DispatchEventTypeEnum.DISPATCH_FLOW_SUCCESS.getCode(), event.getEventCode())) {
                log.info("DispatchEventSubscriber -> dispatchFlowResultHandle 派单成功消息:{}", com.alibaba.fastjson.JSON.toJSONString(eventBody));
                List<DispatchAngelDetail> angelDetailList = Lists.newArrayList();
                //过期时间
                Date expireDate = TimeUtils.add(eventBody.getEventTime(), Calendar.SECOND, eventContent.getRedispatchTime());
                //查询派单结果
                JdhDispatchFlowTask dispatchFlowTask = dispatchFlowTaskRepository.find(JdhDispatchFlowTaskIdentifier.builder().flowTaskId(eventBody.getFlowId()).build());

                List<DispatchAngelBO> angelList = dispatchFlowTask.getExtendInfo().getAngelList();
                //抢单类型
                AngelPageRequest angelRequest = new AngelPageRequest();
                angelRequest.setAngelIdList(angelList.stream().map(DispatchAngelBO::getAngelId).collect(Collectors.toList()));
                angelRequest.setPageSize(angelList.size());
                angelRequest.setPageNum(1);
                angelRequest.setNameMask(false);
                PageDto<JdhAngelDto> jdhAngelDtoPageDto = angelApplication.queryAngelByPage(angelRequest);
                angelDetailList.addAll(convertDispatchAngelDetail(jdhAngelDtoPageDto.getList(), eventContent.getAssignType(), expireDate));

                //更新派单任务
                DispatchCallbackCmd cmd = new DispatchCallbackCmd();
                cmd.setDispatchId(snapshot.getDispatchId());
                cmd.setDispatchStatus(JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus());
                cmd.setDispatchType(DispatchTypeEnum.valuesOf(snapshot.getDispatchType()));
                cmd.setAngelDetailList(angelDetailList);
                cmd.setEventTime(eventBody.getEventTime());
                cmd.setExpireDate(expireDate);
                cmd.setPromiseId(snapshot.getPromiseId());
                cmd.setDispatchExecuteRoute(DispatchExecuteRouteEnum.FLOW.getRouteCode());
                cmd.setAssignType(eventContent.getAssignType());
                cmd.setPlanOutTime(eventBody.getPlanOutTime());
                cmd.setPlanFinishTime(eventBody.getPlanFinishTime());
                cmd.setCurrentDispatchRoundConfig(JSON.toJSONString(eventBody.getCurrentDispatchRoundConfig()));
                dispatchApplication.nethpTriageDispatchCallBack(cmd);
                // 延迟事件需要比 dispatchDetail过期事件要长，避免再后续处理事件时，护士还可以接单，产生并发问题
                Long delayTime = Long.valueOf(eventContent.getRedispatchTime()) + 3;
                log.info("DispatchEventSubscriber -> dispatchFlowResultHandle 发送延迟消息，到期未接单处理 dispatchId={}, delayTime={}", snapshot, delayTime);


                eventCoordinator.publishDelay(EventFactory.newDelayEvent(dispatchFlowTask, DispatchEventTypeEnum.DISPATCH_FLOW_SUCCESS_DECISION
                        , DispatchFlowResultEventBody.builder().flowId(dispatchFlowTask.getFlowTaskId()).dispatchId(dispatchFlowTask.getDispatchId()).build(), delayTime));
            }
        } catch (Exception e) {
            log.error("NethpTriageDispatchHandler->processMessage error", e);
            throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
        }
    }

    /**
     * 派单任务到期
     *
     * @param event
     */
    private void dispatchFlowSuccessDecision(Event event) {
        log.info("DispatchEventSubscriber -> dispatchFlowSuccessDecision event:{}", JSON.toJSONString(event));
        DispatchFlowResultEventBody eventBody = JSON.parseObject(event.getBody(), DispatchFlowResultEventBody.class);
        //查询派单
        JdhDispatch snapshot = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(eventBody.getDispatchId()).build());
        //查询派单任务
        JdhDispatchFlowTask dispatchFlowTask = dispatchFlowTaskRepository.find(JdhDispatchFlowTaskIdentifier.builder().flowTaskId(eventBody.getFlowId()).build());
        if (Objects.isNull(dispatchFlowTask) || Objects.isNull(snapshot)) {
            log.info("DispatchEventSubscriber -> dispatchFlowSuccessDecision 数据已过期，不处理: snapshot={}, dispatchFlowTask={}", JSON.toJSONString(snapshot), JSON.toJSONString(dispatchFlowTask));
            return;
        }
        //派单不是本地执行获取的结果，或者派单最新任务与当前任务不一致，说明是 过期数据不处理
        if (!Objects.equals(snapshot.getServiceInfo().getDispatchExecuteRoute(), DispatchExecuteRouteEnum.FLOW.getRouteCode()) || !Objects.equals(snapshot.getServiceInfo().getFlowId(), dispatchFlowTask.getFlowTaskId())) {
            log.info("DispatchEventSubscriber -> dispatchFlowSuccessDecision 数据已过期，不处理: snapshot={}, dispatchFlowTask={}", JSON.toJSONString(snapshot), JSON.toJSONString(dispatchFlowTask));
            return;
        }

        List<Integer> failFlowTaskStatusList = Lists.newArrayList(0, 1, 2);
        if (failFlowTaskStatusList.contains(dispatchFlowTask.getFlowStatus())) {
            //派单失败，发送事件
            eventCoordinator.publish(EventFactory.newDefaultEvent(dispatchFlowTask, DispatchEventTypeEnum.DISPATCH_FLOW_FAIL, DispatchFlowResultEventBody.builder().flowId(dispatchFlowTask.getFlowTaskId()).dispatchId(dispatchFlowTask.getDispatchId()).build()));
        }
    }

    /**
     * @param angelDtoList
     * @param dispatchDetailType
     * @return
     */
    private List<DispatchAngelDetail> convertDispatchAngelDetail(List<JdhAngelDto> angelDtoList, Integer dispatchDetailType, Date expireDate) {
        if (CollectionUtils.isEmpty(angelDtoList)) {
            return new ArrayList<>();
        }
        List<DispatchAngelDetail> result = Lists.newArrayListWithCapacity(angelDtoList.size());
        for (JdhAngelDto angelDto : angelDtoList) {
            result.add(convertDispatchAngelDetail(angelDto, dispatchDetailType, expireDate));
        }
        return result;
    }

    /**
     * @param jdhAngelDto
     * @param dispatchDetailType
     * @return
     */
    private DispatchAngelDetail convertDispatchAngelDetail(JdhAngelDto jdhAngelDto, Integer dispatchDetailType, Date expireDate) {
        DispatchAngelDetail dispatchAngelDetail = new DispatchAngelDetail();
        dispatchAngelDetail.setAngelId(jdhAngelDto.getAngelId());
        dispatchAngelDetail.setOutAngelId(String.valueOf(jdhAngelDto.getNethpDocId()));
        dispatchAngelDetail.setAngelName(jdhAngelDto.getAngelName());
        dispatchAngelDetail.setDispatchDetailType(dispatchDetailType);
        dispatchAngelDetail.setExpireDate(expireDate);
        dispatchAngelDetail.setJobNature(jdhAngelDto.getJobNature());
        dispatchAngelDetail.setProfessionTitleCodeList(jdhAngelDto.getProfessionTitleCodeList());
        return dispatchAngelDetail;
    }
}