package com.jdh.o2oservice.application.provider.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.health.medical.examination.export.dto.XfylManApprovalRecordDTO;
import com.jd.health.medical.examination.export.param.supplier.XfylManApprovalRecordParam;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseHistoryApplication;
import com.jdh.o2oservice.application.provider.convert.ProviderApplicationBillConverter;
import com.jdh.o2oservice.application.provider.service.ProviderBillApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseSubStatusEnum;
import com.jdh.o2oservice.common.enums.XfylApprovalStatusTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseEsQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.es.JdMedicalPromiseEsRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.provider.model.Provider;
import com.jdh.o2oservice.core.domain.provider.model.ProviderBillDetail;
import com.jdh.o2oservice.core.domain.provider.model.ProviderBillRecord;
import com.jdh.o2oservice.core.domain.provider.query.ProviderBillRecordQuery;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderBillDetatilRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderBillRecordRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderRepository;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JoySkyServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.ProcessApprovalMsgBO;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.export.medicalpromise.cmd.InitPhyBillCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.provider.cmd.BillRecordUpdateCmd;
import com.jdh.o2oservice.export.provider.cmd.JdhProviderBillCmd;
import com.jdh.o2oservice.export.provider.cmd.ProviderBillDetailCmd;
import com.jdh.o2oservice.export.provider.dto.JdhProviderBillDTO;
import com.jdh.o2oservice.export.provider.dto.ProviderBillDetailDTO;
import com.jdh.o2oservice.export.provider.dto.ProviderStatisticsDTO;
import com.jdh.o2oservice.export.provider.dto.ProviderTdDTO;
import com.jdh.o2oservice.export.provider.query.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description: 供应商账单应用层实现类
 * @Author: wangpengfei144
 * @Date: 2024/6/11
 */
@Service
@Slf4j
public class ProviderBillApplicationImpl implements ProviderBillApplication {

    @Resource
    private ProviderBillDetatilRepository providerBillDetatilRepository;

    @Resource
    private ProviderBillRecordRepository providerBillRecordRepository;

    /**
     * 检测单
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * generateIdFactory
     */
    @Autowired
    private GenerateIdFactory generateIdFactory;

    /**
     * jdMedicalPromiseEsRepository
     */
    @Autowired
    private JdMedicalPromiseEsRepository jdMedicalPromiseEsRepository;

    /**
     * 线程池
     */
    @Autowired
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * ducc
     */
    @Autowired
    private DuccConfig duccConfig;

    /**
     * businessMode
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    @Autowired
    private MedPromiseHistoryApplication medPromiseHistoryApplication;
    /**
     * 报告
     */
    @Autowired
    private MedicalReportRepository medicalReportRepository;

    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    @Autowired
    private VoucherRepository jdhVoucherRepository;

    @Autowired
    private JdOrderRepository jdOrderRepository;

    @Autowired
    private JoySkyServiceRpc joySkyServiceRpc;

    @Autowired
    private ProviderRepository providerRepository;

    /**
     * 创建账单
     *
     * @param jdhProviderBillCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean createBill(JdhProviderBillCmd jdhProviderBillCmd) {
        List<JdhVerticalBusiness> byBusinessMode = verticalBusinessRepository.findByBusinessMode(duccConfig.getNeedCreateBillBusinessMode());
        Set<String> verticalCodeSet = byBusinessMode.stream().map(JdhVerticalBusiness::getVerticalCode).collect(Collectors.toSet());
        MedicalPromiseListRequest build = MedicalPromiseListRequest.builder().verticalCodeSet(verticalCodeSet).reportStatus(CommonConstant.ONE).reportStartTime(jdhProviderBillCmd.getStartDate()).reportEndTime(jdhProviderBillCmd.getEndDate()).build();
        List<MedicalPromiseDTO> medicalPromiseDTOS = medicalPromiseApplication.queryMedicalPromiseList(build);
        if (CollectionUtils.isEmpty(medicalPromiseDTOS)){
            log.info("ProviderBillApplicationImpl,createBill,结束，检测单为空，param={}", JsonUtil.toJSONString(jdhProviderBillCmd));
            return Boolean.TRUE;
        }
        medicalPromiseDTOS.removeIf(p->Objects.isNull(p.getProviderId()));
        if (CollectionUtils.isEmpty(medicalPromiseDTOS)){
            log.info("ProviderBillApplicationImpl,createBill,结束，after 检测单为空，param={}", JsonUtil.toJSONString(jdhProviderBillCmd));
            return Boolean.TRUE;
        }

        medicalPromiseDTOS.stream().map(MedicalPromiseDTO::getProviderId).collect(Collectors.toList());

        List<Provider> byBusinessType = providerRepository.findByBusinessType(16);
        Map<Long,String> channelNoToName = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(byBusinessType)){
            channelNoToName = byBusinessType.stream().collect(Collectors.toMap(Provider::getChannelNo, Provider::getChannelName));
        }

        Date billDate = jdhProviderBillCmd.getStartDate();
        Map<Long, List<MedicalPromiseDTO>> providerToMedPromise = medicalPromiseDTOS.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getProviderId));
        Map<Long, String> finalChannelNoToName = channelNoToName;
        providerToMedPromise.forEach((providerId, medicalPromises)->{
            Map<String, List<MedicalPromiseDTO>> stationToMedPromise = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getStationId));
            stationToMedPromise.forEach((stationId,medicalPromiseList)->{
                try {
                    saveBill(providerId, stationId, medicalPromiseList, finalChannelNoToName, billDate);
                }catch (Exception e){
                    log.info("createBill,saveBill,异常，providerId={},stationId={},medicalPromiseList={}", providerId, stationId,JsonUtil.toJSONString(medicalPromiseList),e);
                }
            });

        });
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveBill(Long providerId, String stationId, List<MedicalPromiseDTO> medicalPromiseList, Map<Long, String> finalChannelNoToName, Date billDate) {
        Long billId = generateIdFactory.getId();
        MedicalPromiseDTO medicalPromiseDTO = medicalPromiseList.get(0);
        ProviderBillRecord providerBillRecord = new ProviderBillRecord();
        String provderName = finalChannelNoToName.get(medicalPromiseDTO.getProviderId());
        providerBillRecord.setBillDate(billDate);
        providerBillRecord.setProviderId(providerId);
        providerBillRecord.setStationId(stationId);
        providerBillRecord.setStationName(medicalPromiseDTO.getStationName());
        providerBillRecord.setFinishAllNum(medicalPromiseList.size());
        providerBillRecord.setBillId(billId);
        providerBillRecord.setProviderName(provderName);
        List<MedicalPromiseDTO> devList = medicalPromiseList.stream().filter(p->Objects.nonNull(p.getDevMedicalPromise()) && p.getDevMedicalPromise()).collect(Collectors.toList());
        int devMP = CollectionUtils.isNotEmpty(devList) ? devList.size() : CommonConstant.ZERO;
        providerBillRecord.setFinishCheckNum(medicalPromiseList.size()-devMP);

        Map<Long,String> voucherToOrder = Maps.newHashMap();
        Map<Long,JdOrder> orderIdToObj= Maps.newHashMap();

        List<Long> voucherList = medicalPromiseList.stream().map(MedicalPromiseDTO::getVoucherId).collect(Collectors.toList());
        List<JdhVoucher> jdhVouchers = jdhVoucherRepository.listByVoucherIds(voucherList);
        if (CollectionUtils.isNotEmpty(jdhVouchers)){
            voucherToOrder = jdhVouchers.stream().collect(Collectors.toMap(JdhVoucher::getVoucherId, JdhVoucher::getSourceVoucherId));

            List<Long> orderId = jdhVouchers.stream().map(p -> Long.valueOf(p.getSourceVoucherId())).collect(Collectors.toList());
            List<JdOrder> ordersByList = jdOrderRepository.findOrdersByList(orderId);
            orderIdToObj = ordersByList.stream().collect(Collectors.toMap(JdOrder::getOrderId, p->p));

        }
        ;
        List<ProviderBillDetail> providerBillDetails  = Lists.newArrayList();
        for (MedicalPromiseDTO p : medicalPromiseList){
            ProviderBillDetail providerBillDetail = new ProviderBillDetail();
            providerBillDetail.setBillDate(billDate);
            providerBillDetail.setProviderId(p.getProviderId());
            providerBillDetail.setBillId(billId);
            providerBillDetail.setStationId(p.getStationId());
            providerBillDetail.setStationName(p.getStationName());
            providerBillDetail.setMedicalPromiseId(p.getMedicalPromiseId());
            providerBillDetail.setSpecimenCode(p.getSpecimenCode());
            providerBillDetail.setReportTime(p.getReportTime());
            providerBillDetail.setCheckTime(p.getCheckTime());
            providerBillDetail.setServiceItemName(p.getServiceItemName());
            providerBillDetail.setUpdateUser(p.getUpdateUser());
            providerBillDetail.setCreateUser(p.getCreateUser());
            providerBillDetail.setProviderName(provderName);
            providerBillDetail.setSkuNo(String.valueOf(p.getServiceId()));
            if (voucherToOrder.containsKey(p.getVoucherId())){
                if (orderIdToObj.containsKey(Long.valueOf(voucherToOrder.get(p.getVoucherId())))){
                    JdOrder order = orderIdToObj.get(Long.valueOf(voucherToOrder.get(p.getVoucherId())));
                    providerBillDetail.setOrderId(order.getOrderId());
                    providerBillDetail.setOrderCreateTime(order.getCreateTime());
                }
            }
            providerBillDetails.add(providerBillDetail);
        }
        providerBillDetatilRepository.batchInsertBillDeatil(providerBillDetails);
        providerBillRecordRepository.save(providerBillRecord);
    }


    /**
     * 分页查询账单记录
     *
     * @param providerBillPageRequest
     * @return
     */
    @Override
    public PageDto<JdhProviderBillDTO> queryBillPage(ProviderBillPageRequest providerBillPageRequest) {
        ProviderBillRecordQuery providerBillRecordQuery = ProviderApplicationBillConverter.INSTANCE.convert(providerBillPageRequest);
        Page<ProviderBillRecord> providerBillRecordPage = providerBillRecordRepository.queryProviderBillRecordPage(providerBillRecordQuery);

        Map<String,XfylManApprovalRecordDTO> approvalNoToObj = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(providerBillRecordPage.getRecords())){
            List<String> approvalNos = providerBillRecordPage.getRecords().stream().map(ProviderBillRecord::getApprovalNo).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(approvalNos)){
                XfylManApprovalRecordParam xfylManApprovalRecordParam = new XfylManApprovalRecordParam();
                xfylManApprovalRecordParam.setApprovalNos(approvalNos);
                List<XfylManApprovalRecordDTO> xfylManApprovalRecordDTOS = joySkyServiceRpc.queryApprovalRecordList(xfylManApprovalRecordParam);
                if (CollectionUtils.isNotEmpty(xfylManApprovalRecordDTOS)){
                    approvalNoToObj = xfylManApprovalRecordDTOS.stream().collect(Collectors.toMap(XfylManApprovalRecordDTO::getApprovalNo, p -> p));
                }
            }
        }
        PageDto<JdhProviderBillDTO> pageDto = new PageDto<JdhProviderBillDTO>();
        List<JdhProviderBillDTO> jdhProviderBillDTOS = ProviderApplicationBillConverter.INSTANCE.convertRecord(providerBillRecordPage.getRecords(),approvalNoToObj);
        pageDto.setList(jdhProviderBillDTOS);
        pageDto.setPageNum(providerBillRecordPage.getCurrent());
        pageDto.setPageSize(providerBillRecordPage.getSize());
        pageDto.setTotalPage(providerBillRecordPage.getPages());
        pageDto.setTotalCount(providerBillRecordPage.getTotal());
        return pageDto;
    }

    /**
     * 查询待办
     *
     * @param providerTdRequest
     * @return
     */
    @Override
    public ProviderTdDTO queryProviderTd(ProviderTdRequest providerTdRequest) {

        Date appointEndTime = TimeUtils.getEndTime(new Date());
        Date appointStartTime = TimeUtils.getStartTime(TimeUtils.addDays(appointEndTime, -6));

        //线程池
        ExecutorService executorService = executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT);
        List<Throwable> collectedExceptions = Collections.synchronizedList(new ArrayList<>());
        Date now = new Date();

        //待检测
        MedicalPromiseEsQuery todayWaitCheckTotalBO = getMedicalPromiseEsQuery(providerTdRequest, now);
        todayWaitCheckTotalBO.setShipStatusList(Stream.of("4").collect(Collectors.toList()));
        todayWaitCheckTotalBO.setCheckStatus(0);
        todayWaitCheckTotalBO.setAppointmentStartTime(appointStartTime);
        todayWaitCheckTotalBO.setAppointmentEndTime(appointEndTime);
        CompletableFuture<Long> todayWaitCheckTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(todayWaitCheckTotalBO), executorService).exceptionally(e -> {
            collectedExceptions.add(e);
            return 0L;
        });

        //待出报告
        MedicalPromiseEsQuery todayWaitReportTotalBO = getMedicalPromiseEsQuery(providerTdRequest, now);
        todayWaitReportTotalBO.setCheckStatus(1);
        todayWaitReportTotalBO.setReportStatus(0);
        todayWaitReportTotalBO.setAppointmentStartTime(appointStartTime);
        todayWaitReportTotalBO.setAppointmentEndTime(appointEndTime);
        CompletableFuture<Long> todayWaitReportTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(todayWaitReportTotalBO), executorService).exceptionally(e -> {
            collectedExceptions.add(e);
            return 0L;
        });

        //报告超时
        MedicalPromiseEsQuery todayReportTimeOutTotalBO = getMedicalPromiseEsQuery(providerTdRequest, now);
        todayReportTimeOutTotalBO.setReportStatus(0);
        todayReportTimeOutTotalBO.setReportCheckTimeOutStatus(1);
        Map<String, Object> maps = duccConfig.getQuickCheckQueryStoreStatus();
        if (CollUtil.isNotEmpty(maps) && maps.containsKey("reportCheckTimeOut")) {
            Long reportCheckTimeOut = Long.parseLong(maps.get("reportCheckTimeOut").toString());
            todayReportTimeOutTotalBO.setReportCheckTimeOut(reportCheckTimeOut);
        }
        todayReportTimeOutTotalBO.setAppointmentStartTime(appointStartTime);
        todayReportTimeOutTotalBO.setAppointmentEndTime(appointEndTime);
        log.info("ProviderBillApplicationImpl queryQuickCheckBacklog todayReportTimeOutTotalBO={}", JSONUtil.toJsonStr(todayReportTimeOutTotalBO));
        CompletableFuture<Long> todayReportTimeOutTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(todayReportTimeOutTotalBO), executorService).exceptionally(e -> {
            collectedExceptions.add(e);
            return 0L;
        });

        // 待审核
        MedicalPromiseEsQuery todayWaitAuditTotalBO = getMedicalPromiseEsQuery(providerTdRequest, now);
        todayWaitAuditTotalBO.setInSubStatusList(Lists.newArrayList(MedicalPromiseSubStatusEnum.REPORT_CHECK.getSubStatus()));
        todayWaitAuditTotalBO.setAppointmentStartTime(appointStartTime);
        todayWaitAuditTotalBO.setAppointmentEndTime(appointEndTime);
        CompletableFuture<Long> todayWaitAuditTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(todayWaitAuditTotalBO), executorService).exceptionally(e -> {
            collectedExceptions.add(e);
            return 0L;
        });

        // 用户同意让步检测
        MedicalPromiseEsQuery todayAgreeConcessionTotalBO = getMedicalPromiseEsQuery(providerTdRequest, now);
        todayAgreeConcessionTotalBO.setInSubStatusList(Lists.newArrayList(MedicalPromiseSubStatusEnum.SAMPLE_DEAL_ERROR_RE_COLLECT_AGREE.getSubStatus()));
        todayAgreeConcessionTotalBO.setAppointmentStartTime(appointStartTime);
        todayAgreeConcessionTotalBO.setAppointmentEndTime(appointEndTime);
        CompletableFuture<Long> todayAgreeConcessionTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(todayAgreeConcessionTotalBO), executorService).exceptionally(e -> {
            collectedExceptions.add(e);
            return 0L;
        });

        try {
            CompletableFuture.allOf(todayWaitCheckTotalFuture, todayWaitReportTotalFuture, todayReportTimeOutTotalFuture, todayWaitAuditTotalFuture, todayAgreeConcessionTotalFuture).get();
            ProviderTdDTO dto = new ProviderTdDTO();
            dto.setTodayWaitCheckTotal(todayWaitCheckTotalFuture.get());
            dto.setTodayWaitReportTotal(todayWaitReportTotalFuture.get());
            dto.setTodayReportTimeOutTotal(todayReportTimeOutTotalFuture.get());
            dto.setTodayWaitReportAuditTotal(todayWaitAuditTotalFuture.get());
            // 前端暂无展示，不计入待办总量
            dto.setTodayAgreeConcessionTotal(todayAgreeConcessionTotalFuture.get());
            dto.setTodayWaitTotal(dto.getTodayWaitCheckTotal() + dto.getTodayWaitReportTotal() + dto.getTodayReportTimeOutTotal() + dto.getTodayWaitReportAuditTotal());
            return dto;
        } catch (InterruptedException | ExecutionException e) {
            log.error("ProviderBillApplicationImpl -> queryQuickCheckBacklog error e={}", JsonUtil.toJSONString(e));
            throw new RuntimeException(e);
        }
    }

    /**
     * getMedicalPromiseEsQuery
     * @param providerTdRequest
     * @param now
     * @return
     */
    private  MedicalPromiseEsQuery getMedicalPromiseEsQuery(ProviderTdRequest providerTdRequest, Date now) {
        MedicalPromiseEsQuery medicalPromiseEsQuery = new MedicalPromiseEsQuery();
        medicalPromiseEsQuery.setLaboratoryStationId(providerTdRequest.getStationId());
        medicalPromiseEsQuery.setProviderId(providerTdRequest.getProviderId());
        medicalPromiseEsQuery.setOrderStatusNotInList(Lists.newArrayList(MedicalPromiseStatusEnum.INVALID.getStatus()));
        medicalPromiseEsQuery.setAppointmentStartTime(TimeUtils.getStartTime(now));
        medicalPromiseEsQuery.setAppointmentEndTime(TimeUtils.getEndTime(now));
        medicalPromiseEsQuery.setHeartSmart(providerTdRequest.getHeartSmart());
        return medicalPromiseEsQuery;
    }

    /**
     * getMedicalPromiseEsQuery
     * @param param
     * @return
     */
    private  MedicalPromiseEsQuery getMedicalPromiseEsQuery(ProviderStatisticsRequest param) {
        MedicalPromiseEsQuery medicalPromiseEsQuery = new MedicalPromiseEsQuery();
        medicalPromiseEsQuery.setLaboratoryStationId(param.getStationId());
        medicalPromiseEsQuery.setProviderId(param.getProviderId());
        medicalPromiseEsQuery.setOrderStatusNotInList(Lists.newArrayList(MedicalPromiseStatusEnum.INVALID.getStatus()));
        return medicalPromiseEsQuery;
    }

    /**
     * getMedicalPromiseEsQuery
     * @param param
     * @param now
     * @return
     */
    private  MedicalPromiseEsQuery getMedicalPromiseEsQuery(ProviderStatisticsRequest param,Date now) {
        MedicalPromiseEsQuery medicalPromiseEsQuery = new MedicalPromiseEsQuery();
        medicalPromiseEsQuery.setLaboratoryStationId(param.getStationId());
        medicalPromiseEsQuery.setProviderId(param.getProviderId());
        medicalPromiseEsQuery.setOrderStatusNotInList(Lists.newArrayList(MedicalPromiseStatusEnum.INVALID.getStatus()));
        medicalPromiseEsQuery.setAppointmentStartTime(TimeUtils.getStartTime(now));
        medicalPromiseEsQuery.setAppointmentEndTime(TimeUtils.getEndTime(now));
        return medicalPromiseEsQuery;
    }

    /**
     * 查询统计
     *
     * @param providerStatisticsRequest
     * @return
     */
    @Override
    public ProviderStatisticsDTO queryProviderStatistics(ProviderStatisticsRequest providerStatisticsRequest) {

        ExecutorService executorService = executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT);

        MedicalPromiseEsQuery allAppointTotalBO = getMedicalPromiseEsQuery(providerStatisticsRequest);
        allAppointTotalBO.setStatusList(Stream.of(0, 1,2,3,4,6,8).collect(Collectors.toList()));
        allAppointTotalBO.setShipStatusList(Stream.of("3", "4").collect(Collectors.toList()));
        log.info("ProviderBillApplicationImpl queryQuickCheckStatistics allAppointTotalBO={}", JSONUtil.toJsonStr(allAppointTotalBO));
        CompletableFuture<Long> allAppointTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(allAppointTotalBO), executorService).exceptionally(e -> {
            return 0L;
        });

        MedicalPromiseEsQuery allCheckedAppointTotalBO = getMedicalPromiseEsQuery(providerStatisticsRequest);
        allCheckedAppointTotalBO.setCheckStatus(1);
        log.info("ProviderBillApplicationImpl queryQuickCheckStatistics allCheckedAppointTotalBO={}", JSONUtil.toJsonStr(allCheckedAppointTotalBO));
        CompletableFuture<Long> allCheckedAppointTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(allCheckedAppointTotalBO), executorService).exceptionally(e -> {
            return 0L;
        });

        MedicalPromiseEsQuery allReportedAppointTotalBO = getMedicalPromiseEsQuery(providerStatisticsRequest);
        allReportedAppointTotalBO.setReportStatus(1);
        log.info("ProviderBillApplicationImpl queryQuickCheckStatistics allReportedAppointTotalBO={}", JSONUtil.toJsonStr(allReportedAppointTotalBO));
        CompletableFuture<Long> allReportedAppointTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(allReportedAppointTotalBO), executorService).exceptionally(e -> {
            return 0L;
        });

        Date now = new Date();
        MedicalPromiseEsQuery todayAppointTotalBO = getMedicalPromiseEsQuery(providerStatisticsRequest, now);
        todayAppointTotalBO.setStatusList(Stream.of(0, 1,2,3,4,6,8).collect(Collectors.toList()));
        todayAppointTotalBO.setShipStatusList(Stream.of("3", "4").collect(Collectors.toList()));
        log.info("ProviderBillApplicationImpl queryQuickCheckStatistics todayAppointTotalBO={}", JSONUtil.toJsonStr(todayAppointTotalBO));
        CompletableFuture<Long> todayAppointTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(todayAppointTotalBO), executorService).exceptionally(e -> {
            return 0L;
        });

        MedicalPromiseEsQuery todayCheckedAppointTotalBO = getMedicalPromiseEsQuery(providerStatisticsRequest, now);
        todayCheckedAppointTotalBO.setCheckStatus(1);
        log.info("ProviderBillApplicationImpl queryQuickCheckStatistics todayCheckedAppointTotalBO={}", JSONUtil.toJsonStr(todayCheckedAppointTotalBO));
        CompletableFuture<Long> todayCheckedAppointTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(todayCheckedAppointTotalBO), executorService).exceptionally(e -> {
            return 0L;
        });

        MedicalPromiseEsQuery todayReportedAppointTotalBO = getMedicalPromiseEsQuery(providerStatisticsRequest, now);
        todayReportedAppointTotalBO.setReportStatus(1);
        log.info("ProviderBillApplicationImpl queryQuickCheckStatistics todayReportedAppointTotalBO={}", JSONUtil.toJsonStr(todayReportedAppointTotalBO));
        CompletableFuture<Long> todayReportedAppointTotalFuture = CompletableFuture.supplyAsync(() -> jdMedicalPromiseEsRepository.queryQuickCheckCount(todayReportedAppointTotalBO), executorService).exceptionally(e -> {
            return 0L;
        });

        try {
            CompletableFuture.allOf(allAppointTotalFuture, allCheckedAppointTotalFuture, allReportedAppointTotalFuture, todayAppointTotalFuture, todayCheckedAppointTotalFuture, todayReportedAppointTotalFuture).get();
            ProviderStatisticsDTO dto = new ProviderStatisticsDTO();
            dto.setAllAppointTotal(allAppointTotalFuture.get());
            dto.setAllCheckedAppointTotal(allCheckedAppointTotalFuture.get());
            dto.setAllReportedAppointTotal(allReportedAppointTotalFuture.get());
            dto.setTodayAppointTotal(todayAppointTotalFuture.get());
            //dto.setTodayDeliveryAppointTotal(todayDeliveryAppointTotalFuture.get());
            dto.setTodayCheckedAppointTotal(todayCheckedAppointTotalFuture.get());
            dto.setTodayReportedAppointTotal(todayReportedAppointTotalFuture.get());
            return dto;
        } catch (InterruptedException | ExecutionException e) {
            log.error("ProviderBillApplicationImpl -> queryQuickCheckStatistics error e={}", JsonUtil.toJSONString(e));
            throw new RuntimeException(e);
        }
    }



    /**
     * 查询账单详情
     *
     * @param providerBillDetailRequest
     * @return
     */
    @Override
    public List<ProviderBillDetailDTO> queryProviderBillDetailList(ProviderBillDetailRequest providerBillDetailRequest) {
        ProviderBillRecordQuery convert = ProviderApplicationBillConverter.INSTANCE.convert(providerBillDetailRequest);
        List<ProviderBillDetail> providerBillDetails = providerBillDetatilRepository.queryProviderBillDetailList(convert);
        return ProviderApplicationBillConverter.INSTANCE.convert(providerBillDetails);
    }

    /**
     * 批量创建账单详情
     *
     * @param providerBillDetailCmds
     * @return
     */
    @Override
    public Boolean batchCreateBillDeatil(List<ProviderBillDetailCmd> providerBillDetailCmds) {
        List<ProviderBillDetail> providerBillDetails = ProviderApplicationBillConverter.INSTANCE.convertBillDetailList(providerBillDetailCmds);
        return providerBillDetatilRepository.batchInsertBillDeatil(providerBillDetails);
    }

    /**
     * 查询账单
     *
     * @param providerBillRecordRequest
     * @return
     */
    @Override
    public JdhProviderBillDTO queryBillRecord(ProviderBillRecordRequest providerBillRecordRequest) {
        ProviderBillRecordQuery providerBillRecordQuery = ProviderApplicationBillConverter.INSTANCE.convert(providerBillRecordRequest);
        ProviderBillRecord providerBillRecord = providerBillRecordRepository.queryProviderBillRecord(providerBillRecordQuery);

        JdhProviderBillDTO dto = ProviderApplicationBillConverter.INSTANCE.convert(providerBillRecord);
        if (Objects.nonNull(dto) && StringUtils.isNotBlank(dto.getApprovalNo())){
            XfylManApprovalRecordParam xfylManApprovalRecordParam = new XfylManApprovalRecordParam();
            xfylManApprovalRecordParam.setApprovalNo(providerBillRecord.getApprovalNo());
            List<XfylManApprovalRecordDTO> xfylManApprovalRecordDTOS = joySkyServiceRpc.queryApprovalRecordList(xfylManApprovalRecordParam);
            if (CollectionUtils.isNotEmpty(xfylManApprovalRecordDTOS)){
                XfylManApprovalRecordDTO approvalRecord = xfylManApprovalRecordDTOS.get(0);
                dto.setJoyApprovalStatus(approvalRecord.getStatus());
                dto.setApprovalUrl(approvalRecord.getExtendUrl());

                if (XfylApprovalStatusTypeEnum.STATUS_APPROVAL_PASS.getType().equals(approvalRecord.getStatus())) {
                    dto.setJoyApprovalStatusDesc("审批已通过");
                } else if (XfylApprovalStatusTypeEnum.STATUS_APPROVAL_REJECT.getType().equals(approvalRecord.getStatus())) {
                    if (StringUtils.isNotBlank(approvalRecord.getResult())){
                        ProcessApprovalMsgBO processApprovalMsgBO = JsonUtil.parseObject(approvalRecord.getResult(), ProcessApprovalMsgBO.class);
                        dto.setJoyApprovalStatusDesc(StringUtils.isNotBlank(processApprovalMsgBO.getTaskComment()) ? processApprovalMsgBO.getTaskComment() : "驳回原因获取失败");
                    }
                } else {
                    dto.setJoyApprovalStatusDesc(XfylApprovalStatusTypeEnum.getDescOfType(approvalRecord.getStatus()));
                }
            }
        }
        return dto;
    }

    /**
     * 更新
     *
     * @param billRecordUpdateCmd
     * @return
     */
    @Override
    public Boolean updateBillRecord(BillRecordUpdateCmd billRecordUpdateCmd) {
        ProviderBillRecord providerBillRecord = ProviderApplicationBillConverter.INSTANCE.convert(billRecordUpdateCmd);
        return providerBillRecordRepository.updateProviderRecord(providerBillRecord);
    }

    /**
     * 查询账单列表
     *
     * @param providerBillPageRequest
     * @return
     */
    @Override
    public List<JdhProviderBillDTO> queryBillRecordList(ProviderBillPageRequest providerBillPageRequest) {
        ProviderBillRecordQuery providerBillRecordQuery = ProviderApplicationBillConverter.INSTANCE.convert(providerBillPageRequest);
        List<ProviderBillRecord> providerBillRecords = providerBillRecordRepository.queryProviderBillRecordList(providerBillRecordQuery);

        Map<String,XfylManApprovalRecordDTO> approvalNoToObj = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(providerBillRecords)){
            List<String> approvalNos = providerBillRecords.stream().map(ProviderBillRecord::getApprovalNo).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(approvalNos)){
                XfylManApprovalRecordParam xfylManApprovalRecordParam = new XfylManApprovalRecordParam();
                xfylManApprovalRecordParam.setApprovalNos(approvalNos);
                List<XfylManApprovalRecordDTO> xfylManApprovalRecordDTOS = joySkyServiceRpc.queryApprovalRecordList(xfylManApprovalRecordParam);
                if (CollectionUtils.isNotEmpty(xfylManApprovalRecordDTOS)){
                    approvalNoToObj = xfylManApprovalRecordDTOS.stream().collect(Collectors.toMap(XfylManApprovalRecordDTO::getApprovalNo, p -> p));
                }
            }
        }
        return ProviderApplicationBillConverter.INSTANCE.convertRecord(providerBillRecords,approvalNoToObj);
    }

    /**
     * 初始化检测单(报告时间、检测状态)
     *
     * @param jdhProviderBillCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean initMp(JdhProviderBillCmd jdhProviderBillCmd) {
        MedicalPromiseListQuery medicalPromiseListRequest = MedicalPromiseListQuery.builder().startDate(jdhProviderBillCmd.getStartDate()).endDate(jdhProviderBillCmd.getEndDate()).build();
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListRequest);

        if (CollectionUtils.isEmpty(medicalPromises)){
            log.info("ProviderBillApplicationImpl->initMp,medicalPromiseDTOS is null");
            return Boolean.TRUE;
        }
        //分片
        List<List<MedicalPromise>> mpPartition = Lists.partition(medicalPromises, CommonConstant.ONE_HUNDRED);
        CountDownLatch countDownLatch = new CountDownLatch(mpPartition.size());
        ExecutorService executorService = executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT);
        for (List<MedicalPromise> medicalPromiseDTOList : mpPartition){
            executorService.submit(()->{
                try {
                    List<Long> medicalPromiseList = medicalPromiseDTOList.stream().map(MedicalPromise::getMedicalPromiseId).collect(Collectors.toList());
                    List<MedicalReport> byMedicalPromiseIdList = medicalReportRepository.getByMedicalPromiseIdList(medicalPromiseList);
                    log.info("ProviderBillApplicationImpl->initMp,medicalPromiseDTOS byMedicalPromiseIdList={}",JsonUtil.toJSONString(byMedicalPromiseIdList));
                    Map<Long, MedicalReport> mpToReport = Maps.newHashMap();

                    if (CollectionUtils.isNotEmpty(byMedicalPromiseIdList)){
                        mpToReport = byMedicalPromiseIdList.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId, p -> p));
                        log.info("ProviderBillApplicationImpl->initMp,medicalPromiseDTOS mpToReport={}",JsonUtil.toJSONString(mpToReport));

                    }
                    for (MedicalPromise medicalPromise : medicalPromiseDTOList){
                        medicalPromise.setCheckStatus(Objects.nonNull(medicalPromise.getCheckTime())? CommonConstant.ONE : CommonConstant.ZERO);
                        if (mpToReport.containsKey(medicalPromise.getMedicalPromiseId())){
                            MedicalReport medicalReport = mpToReport.get(medicalPromise.getMedicalPromiseId());
                            log.info("ProviderBillApplicationImpl->initMp,medicalPromiseDTOS medicalReport={}",JsonUtil.toJSONString(medicalReport));
                            medicalPromise.setReportTime(Objects.nonNull(medicalReport.getReportTime()) ? medicalReport.getReportTime() : medicalReport.getCreateTime());
                        }
                        medicalPromiseRepository.updateInit(medicalPromise);
                    }

                }catch (Exception e){
                    log.info("ProviderBillApplicationImpl->initMp, error ",e);
                }finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await();
        }catch (Exception e){
            log.info("ProviderBillApplicationImpl->initMp,countDownLatch.await error ",e);
        }
        return Boolean.TRUE;
    }

    /**
     * 初始化账单
     *
     * @param initPhyBillCmd
     * @return
     */
    @Override
    public Boolean initPhyBill(InitPhyBillCmd initPhyBillCmd) {
        return Boolean.TRUE;
    }

//    List<MedPromiseHistoryDTO> medPromiseHistoryDTOS = medPromiseHistoryApplication.queryMedPromiseHistoryList(MedPromiseHistoryRequest.builder().medicalPromiseIds(medicalPromiseSet).build());
//                    if (CollectionUtils.isEmpty(medPromiseHistoryDTOS)){
//        return;
//    }
//    Map<Long, List<MedPromiseHistoryDTO>> mpToHistory = medPromiseHistoryDTOS.stream().collect(Collectors.groupingBy(MedPromiseHistoryDTO::getMedicalPromiseId));
//
//                    for (MedicalPromiseDTO medicalPromiseDTO : medicalPromiseDTOList){
//        if (!mpToHistory.containsKey(medicalPromiseDTO.getMedicalPromiseId())){
//            continue;
//        }
//        List<MedPromiseHistoryDTO> medPromiseHistory = mpToHistory.get(medicalPromiseDTO.getMedicalPromiseId());
//        Map<Integer, List<MedPromiseHistoryDTO>> afterStatusToHistory = medPromiseHistory.stream().collect(Collectors.groupingBy(MedPromiseHistoryDTO::getAfterStatus));
//        //报告时间
//
//
//    }

}
