package com.jdh.o2oservice.application.provider.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.health.xfyl.merchant.export.dto.XfylProviderResourceDTO;
import com.jd.health.xfyl.merchant.export.param.GetProviderParam;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.medicalpromise.convert.MedicalPromiseConvert;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseHistoryApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderEquipmentApplication;
import com.jdh.o2oservice.application.provider.service.ProviderPromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.support.service.DictApplication;
import com.jdh.o2oservice.application.support.service.PromisegoApplication;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.QuickCheckStatusMappingConfig;
import com.jdh.o2oservice.base.enums.*;
import com.jdh.o2oservice.base.event.*;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.statemachine.StateMachine;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseSubStatusEnum;
import com.jdh.o2oservice.common.enums.WsEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchAggregateEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchIdentifier;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.medpromise.context.MedicalPromiseSubmitContext;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.event.PromiseModifyEventBody;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.model.Provider;
import com.jdh.o2oservice.core.domain.provider.model.ProviderIdentifier;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderRepository;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.QuickCheckThirdExportServiceRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.LabPromisegoBo;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseHistoryDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedPromiseHistoryRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.VoucherIdRequest;
import com.jdh.o2oservice.export.provider.cmd.ModifyProcessCmd;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.ProviderEquipmentDto;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import com.jdh.o2oservice.export.support.dto.DictInfoDto;
import com.jdh.o2oservice.export.support.query.DictRequest;
import com.jdh.o2oservice.export.support.query.SendLaboratorySocketMsgRequest;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
/**
 * ProviderPromiseEventSubscriber
 *
 * <AUTHOR>
 * @date 2024/01/25
 */
@Slf4j
@Service
public class ProviderPromiseEventSubscriber {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * providerApplication
     */
    @Resource
    private ProviderPromiseApplication providerApplication;

    @Resource
    private ProviderRepository providerRepository;

    @Resource
    private ReachApplication reachApplication;

    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    @Resource
    private AngelShipRepository angelShipRepository;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private QuickCheckThirdExportServiceRpc quickCheckThirdExportServiceRpc;

    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private DispatchRepository dispatchRepository;

    @Resource
    private PromisegoApplication promisegoApplication;

    /**
     * dispatchStatemachine
     */
    @Resource
    private StateMachine<MedicalPromiseStatusEnum, MedPromiseEventTypeEnum, StateContext> medicalPromiseStatemachine;


    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * dongDongRobotRpc
     */
    @Resource
    private DongDongRobotRpc dongDongRobotRpc;

    @Resource
    private ProviderStoreApplication providerStoreApplication;

    @Resource
    private Cluster jimClient;
    /**
     * medPromiseHistoryApplication
     */
    @Autowired
    private MedPromiseHistoryApplication medPromiseHistoryApplication;

    @Autowired
    private DictApplication dictApplication;

    /**
     * 门店查询rpc
     */
    @Resource
    ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    /**
     * jdOrderFullApplication
     */
    @Resource
    private TradeApplication tradeApplication;

    /**
     * voucherApplication
     */
    @Resource
    private VoucherApplication voucherApplication;


    @Autowired
    private ProviderEquipmentApplication providerEquipmentApplication;

    @Autowired
    private ExecutorPoolFactory executorPoolFactory;



    /**
     * 注册事件使用者
     */
    @PostConstruct
    public void registerEventConsumer() {
        // 用户主动修改预约
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_USER_SUBMIT_MODIFY, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "userModify", this::userModify, Boolean.TRUE));

        // 提交预约处理
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_SUBMIT, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "appointment", this::appointment, Boolean.TRUE));

        // 自动预约处理
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_AUTO_SUBMIT, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "appointment", this::appointment, Boolean.TRUE));

        // 提交预约处理
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_USER_SUBMIT_CANCEL, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "cancel", this::cancel, Boolean.TRUE));

        // 护士实验室分配完成
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_LAB_DISTRIBUTE, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseLabDistributeSyncProvider", this::quickCheckPushInfo, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 聚合状态:2（待取货）==============
        // 已接单
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_RECEIVED, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "workReceivedSyncProvider", this::quickCheckPushInfo, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));


        // 聚合状态:3（样本送检中）==============
        // 送检中
        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_IN_DELIVERY, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "shipInDeliverySyncProvider", this::quickCheckPushInfo, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));


        // 聚合状态:4（样本送达实验室）==============
        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_FINISH, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "shipFinishSyncProvider", this::quickCheckPushInfo, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 聚合状态:5（检测中）==============
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseStationReceiveSyncProvider", this::quickCheckPushInfo, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 聚合状态:6（已出报告）==============
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseGenerateReportSyncProvider", this::quickCheckPushInfo, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 聚合状态:8（已作废）==============
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_INVALID, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseInvalidSyncProvider", this::quickCheckPushInfo, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 聚合状态:3（样本送检中）==============
        // 实验室履约单 - 绑定条码(骑手角色，绑定条码+提交，检测单状态由1-2->3)
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_BIND_SPECIMEN_CODE, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseBindSpecimenCodeSyncProvider", this::quickCheckAppointSelfTest, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));
        // 实验室履约单 - 运营工具绑码
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_TOOL_SUBMIT, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseToolSubmitSyncProvider", this::quickCheckAppoint, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));
        // 实验室履约单 - 主动提交信息(护士角色，提交，检测单状态由2->3)
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SUBJECTIVE_SUBMIT, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseSubjectiveSubmitSyncProvider", this::quickCheckAppoint, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 实验室迁移
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_STATION_MIGRATION, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseStationMigrationPush", this::targetStation, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 实验室指派
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_TARGET_STATION, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseStationTargetPush", this::targetStation, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 重置报告
        eventConsumerRegister.register(MedPromiseEventTypeEnum.RESET_REPORT_STATUS, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseReportStatusReset", this::reportReset, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 换绑样本编号
        eventConsumerRegister.register(MedPromiseEventTypeEnum.RESET_SPECIMEN_CODE, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseCodeReset", this::codeReset, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 聚合状态:8（已作废）==============
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_CALL_BACK_SYNC_STATION, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseCallBackSyncStationDeal", this::quickCheckPushInfo, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        // 让步检测
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_USER_AGREE_CONCESSION_TEST, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseConcessionTest", this::quickCheckPushInfo, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

        /**
         * 质控单创建，同步实验室
         */
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_QUALITY_CREATE, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseQualityCreate", this::dealMedPromiseQuality, Boolean.TRUE, Boolean.TRUE));

        // 样本重测
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SAMPLE_RETESTING, WrapperEventConsumer.newInstance(DomainEnum.PROVIDER,
                "medPromiseSampleRetesting", this::quickCheckPushInfo, Boolean.TRUE, Boolean.TRUE,  EventConsumerRetryTemplate.fixRetryInstance(8,5000)));

    }

    /**
     * 预约处理
     * @param event
     */
    public void appointment(Event event){
        Long promiseId = Long.valueOf(event.getAggregateId());
        providerApplication.appointmentProcess(promiseId);
    }

    /**
     * 提交用户修改预约
     *
     * @param event 活动
     */
    private void userModify(Event event) {
        ModifyProcessCmd cmd = new ModifyProcessCmd();
        String promiseId = event.getAggregateId();
        cmd.setPromiseId(Long.parseLong(promiseId));
        PromiseModifyEventBody body = JSON.parseObject(event.getBody(), PromiseModifyEventBody.class);
        cmd.setDateType(body.getAfterTime().getDateType());
        cmd.setAppointmentStartTime(body.getAfterTime().formatAppointmentStartTime());
        cmd.setAppointmentEndTime(body.getAfterTime().formatAppointmentEndTime());
        cmd.setDateTypeBefore(body.getBeforeTime().getDateType());
        cmd.setAppointmentStartTimeBefore(body.getBeforeTime().formatAppointmentStartTime());
        cmd.setAppointmentEndTimeBefore(body.getBeforeTime().formatAppointmentEndTime());
        log.info("ProviderPromiseEventSubscriber -> userModify cmd:{}",JSON.toJSONString(cmd));
        JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(Long.parseLong(promiseId)).build());
        if (jdhPromise != null) {
            // 快检业务
            JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(jdhPromise.getVerticalCode());
            if (jdhVerticalBusiness != null && Lists.newArrayList(BusinessModeEnum.SELF_TEST.getCode(), BusinessModeEnum.ANGEL_CARE.getCode(), BusinessModeEnum.SELF_TEST_TRANSPORT.getCode()).contains(jdhVerticalBusiness.getBusinessModeCode())) {
                MedicalPromiseListQuery medPromiseListQuery = new MedicalPromiseListQuery();
                medPromiseListQuery.setPromiseId(Long.parseLong(promiseId));
                List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(medPromiseListQuery);
                if (CollUtil.isEmpty(medicalPromiseList)) {
                    return;
                }
                for (MedicalPromise medicalPromise : medicalPromiseList) {
                    QuickCheckModifyBO modifyBO = new QuickCheckModifyBO();
                    modifyBO.setSampleId(medicalPromise.getMedicalPromiseId().toString());
                    modifyBO.setAppointmentStartTime(body.getAfterTime().formatAppointmentStartTime());
                    modifyBO.setStoreId(medicalPromise.getStationId());
                    quickCheckThirdExportServiceRpc.quickCheckModify(buildQuickCheckModifyV3(modifyBO));
                }
                return;
            }
        }
        providerApplication.modifyProcess(cmd);
    }

    /**
     * 提交用户修改预约
     *
     * @param event 活动
     */
    private void cancel(Event event) {
        ModifyProcessCmd cmd = new ModifyProcessCmd();
        String promiseId = event.getAggregateId();
        providerApplication.cancelProcess(Long.parseLong(promiseId));
    }


    /**
     * 快检推送信息（绑码之外）
     * @param event
     */
    private void quickCheckPushInfo(Event event) {
        log.info("ProviderPromiseEventSubscriber quickCheckPushInfo event={}", JSON.toJSONString(event));
        List<MedicalPromise> medicalPromiseList = new ArrayList<>();
        AngelWork angelWork = null;
        AngelShip angelShip = null;
        if ((DomainEnum.ANGEL_PROMISE.getCode().equals(event.getDomainCode()) && AngelWorkAggregateEnum.WORK.getCode().equals(event.getAggregateCode()))){
            angelWork = getAngelWork(Long.valueOf(event.getAggregateId()), null);
            medicalPromiseList = getMedicalPromiseList(null, angelWork.getPromiseId(), null);
        }else if ((DomainEnum.MED_PROMISE.getCode().equals(event.getDomainCode()) &&  MedPromiseAggregateEnum.MED_PROMISE.getCode().equals(event.getAggregateCode()))){
            //TODO 兼容medicalPromise yn=0的场景
            medicalPromiseList = getMedicalPromiseList(Long.valueOf(event.getAggregateId()), null, null);
            //TODO 兼容angelWork yn=0的场景
            angelWork = getAngelWork(null, medicalPromiseList.get(0).getPromiseId());
        }else if ((DomainEnum.DISPATCH.getCode().equals(event.getDomainCode()) && DispatchAggregateEnum.DISPATCH.getCode().equals(event.getAggregateCode()))){
            JdhDispatch dispatch = dispatchRepository.find(JdhDispatchIdentifier.builder().dispatchId(Long.valueOf(event.getAggregateId())).build());
            medicalPromiseList = getMedicalPromiseList(null, dispatch.getPromiseId(), null);
            angelWork = getAngelWork(null, dispatch.getPromiseId());
        }else if ((DomainEnum.ANGEL_PROMISE.getCode().equals(event.getDomainCode()) && AngelWorkAggregateEnum.SHIP.getCode().equals(event.getAggregateCode()))){
            angelShip = getAngelShip(Long.valueOf(event.getAggregateId()),null, null);
            angelWork = getAngelWork(angelShip.getWorkId(), null);
            medicalPromiseList = getMedicalPromiseList(null, angelWork.getPromiseId(), angelShip.getReceiverId());

            if (Objects.equals(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus(),angelShip.getShipStatus())){
                Boolean update = Boolean.FALSE;
                for (MedicalPromise medicalPromise : medicalPromiseList) {
                    if (StringUtil.equals(medicalPromise.getStationId(),angelShip.getReceiverId()) && Objects.isNull(medicalPromise.getSubStatus())){
                        medicalPromise.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_CHECK.getSubStatus());
                        medicalPromiseRepository.save(medicalPromise);
                        update= Boolean.TRUE;
                    }
                }
                if (update){
                    medicalPromiseList = getMedicalPromiseList(null, angelWork.getPromiseId(), angelShip.getReceiverId());
                }
            }

        }
        Map<Long, List<MedPromiseHistoryDTO>> medPromiseToHistory = new HashMap<>();
        if (CollectionUtils.isNotEmpty(medicalPromiseList)){
            Set<Long> medicalPromiseIds = medicalPromiseList.stream().map(MedicalPromise::getMedicalPromiseId).collect(Collectors.toSet());
            List<MedPromiseHistoryDTO> medPromiseHistoryDTOS = medPromiseHistoryApplication.queryMedPromiseHistoryList(MedPromiseHistoryRequest.builder().medicalPromiseIds(medicalPromiseIds).build());
            medPromiseToHistory = medPromiseHistoryDTOS.stream().collect(Collectors.groupingBy(MedPromiseHistoryDTO::getMedicalPromiseId));
        }
        log.info("ProviderPromiseEventSubscriber quickCheckPushInfo medicalPromiseList={}, angelWork={}, angelShip={}", JSON.toJSONString(medicalPromiseList), JSON.toJSONString(angelWork), JSON.toJSONString(angelShip));
        // {"quickCheckPushRepeatExpires":60,"quickCheckPushRepeatSwitch":true}
        JSONObject quickCheckPushConfigObj = JSON.parseObject(duccConfig.getQuickCheckPushConfig());
        for (MedicalPromise medicalPromise : medicalPromiseList) {
            if (!verifyQuickCheck(medicalPromise)){
                continue;
            }

            //查询
            Integer needFlowCode = 0;
            ProviderEquipmentDto providerEquipmentDto = providerEquipmentApplication.queryEquipmentDtoByStationIdAndItemId(medicalPromise.getStationId(), Long.valueOf(medicalPromise.getServiceItemId()));
            if (Objects.nonNull(providerEquipmentDto) && Objects.nonNull(providerEquipmentDto.getStreamTranscoding())){
                needFlowCode= providerEquipmentDto.getStreamTranscoding();
            }

            for (QuickCheckStatusMappingConfig statusMappingConfig : duccConfig.getQuickCheckStatusMappingConfig()) {
                Map<String, Object> expParam = buildExpParam(angelWork, medicalPromise, angelShip);
                Boolean matchStatus = (Boolean) AviatorEvaluator.compile(statusMappingConfig.getStatusExpression(), Boolean.TRUE).execute(expParam);
                log.info("ProviderPromiseEventSubscriber quickCheckPushInfo matchStatus={}", matchStatus);
                if (matchStatus){
                    QuickCheckPushInfoBO pushInfoBO = buildQuickCheckPushInfoBO(medicalPromise, angelWork, statusMappingConfig,medPromiseToHistory);
                    pushInfoBO.setBizProcessType(needFlowCode);
                    try {
                        RedisKeyEnum keyEnum = RedisKeyEnum.MEDICAL_PROMISE_LAB_PUSH_STATUS_PREFIX_KEY;
                        String cacheKey = RedisKeyEnum.getRedisKey(keyEnum, pushInfoBO.getSampleId(), pushInfoBO.getStoreId(), pushInfoBO.getStatus(), pushInfoBO.getSampleBarcode());
                        // 快检实验室推送防重-开关
                        if (quickCheckPushConfigObj.getBoolean("quickCheckPushRepeatSwitch")){
                            if (StringUtils.isNotBlank(jimClient.get(cacheKey))){
                                log.info("ProviderPromiseEventSubscriber quickCheckPushInfo exist cacheKey={}", cacheKey);
                                continue;
                            }
                        }
                        //如果是作废，则先调用实验室
                        if (Objects.equals(8,pushInfoBO.getStatus())){
                            try {
                                quickCheckThirdExportServiceRpc.invalidQuickCheckAppoint(QuickCheckInvalidBO.builder().sampleId(pushInfoBO.getSampleId()).sampleBarcode(pushInfoBO.getSampleBarcode()).storeId(pushInfoBO.getStoreId()).build());
                            }catch (Exception e){
                                log.info("quickCheckPushInfo,作废失败，exception", e);
                            }
                        }
                        if(isAPIVersion3(medicalPromise)) {
                            log.info("ProviderPromiseEventSubscriber quickCheckPushInfo V3");
                            // 新版推送检测单基础信息和运力接口进行了拆分，分配实验室调用基础接口
                            if (Lists.newArrayList(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_RECEIVED.getCode(), AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_IN_DELIVERY.getCode(), AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_FINISH.getCode()).contains(event.getEventCode())) {
                                log.info("ProviderPromiseEventSubscriber quickCheckPushInfo V3 推送检测单运力信息");
                                quickCheckThirdExportServiceRpc.pushSampleDeliver(buildQuickCheckPushSampleDeliverV3(pushInfoBO));
                            } else {
                                // 快检推送信息
                                log.info("ProviderPromiseEventSubscriber quickCheckPushInfo V3 推送检测单基础信息");
                                quickCheckThirdExportServiceRpc.quickCheckPushInfo(buildPushInfoV3(pushInfoBO));
                            }
                        } else {
                            // 快检推送信息
                            quickCheckThirdExportServiceRpc.quickCheckPushInfo(pushInfoBO);
                        }
                        jimClient.setEx(cacheKey, cacheKey, quickCheckPushConfigObj.getLong("quickCheckPushRepeatExpires"), TimeUnit.SECONDS);
                    }catch (BusinessException b){
                        log.info("ProviderPromiseEventSubscriber quickCheckAppoint error:{}", b);
                        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
                        JSONObject jsonObject = robotAlarmMap.get("同步实验室失败");
                        dongDongRobotRpc.sendDongDongRobotMessage(String.format("quickCheckPushInfo，同步实验室检测信息失败，（实验室名称：%s，检测单号：%s，错误code：%s，错误msg：%s，，检测项目名称：%s）",
                                        medicalPromise.getStationName(), medicalPromise.getMedicalPromiseId(),
                                        b.getErrorCode(), b.getMessage(), medicalPromise.getServiceItemName()),
                                jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
                        throw b;
                    }catch (Exception e){
                        log.info("ProviderPromiseEventSubscriber quickCheckPushInfo error:{}", e.getMessage());
                        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
                        JSONObject jsonObject = robotAlarmMap.get("同步实验室失败");
                        dongDongRobotRpc.sendDongDongRobotMessage(String.format("quickCheckPushInfo，同步实验室检测信息失败，（实验室名称：%s，检测单号：%s，错误msg：%s，，检测项目名称：%s）",
                                        medicalPromise.getStationName(), medicalPromise.getMedicalPromiseId(),
                                        e.getMessage(), medicalPromise.getServiceItemName()),
                                jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
                        throw e;
                    }

                    // webSocket消息推送
                    wsPushMsg(medicalPromise, statusMappingConfig);
                    break;
                }
            }
        }
    }

    /**
     * 快检绑码推送信息（仅绑码）
     * @param event
     */
    private void quickCheckAppoint(Event event) {
        log.info("ProviderPromiseEventSubscriber quickCheckAppoint event={}", JSON.toJSONString(event));
        List<MedicalPromise> medicalPromiseList = getMedicalPromiseList(Long.valueOf(event.getAggregateId()), null, null);
        AngelWork angelWork = getAngelWork(null, medicalPromiseList.get(0).getPromiseId());
        log.info("ProviderPromiseEventSubscriber quickCheckAppoint medicalPromiseList={}, angelWork={}", JSON.toJSONString(medicalPromiseList), JSON.toJSONString(angelWork));
        try {
            deaWithBindSpecimenCodeLogic(medicalPromiseList, angelWork);
        }catch (BusinessException b){
            log.info("ProviderPromiseEventSubscriber quickCheckAppoint error", b);
            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("同步实验室失败");
            MedicalPromise medicalPromise = medicalPromiseList.get(0);
            dongDongRobotRpc.sendDongDongRobotMessage(String.format("quickCheckAppoint，同步实验室检测信息失败，（实验室名称：%s，检测单号：%s，错误code：%s，错误msg：%s，，检测项目名称：%s）",
                            medicalPromise.getStationName(), medicalPromise.getMedicalPromiseId(),
                            b.getErrorCode(), b.getMessage(), medicalPromise.getServiceItemName()),
                    jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            throw b;
        }catch (Exception e){
            log.info("ProviderPromiseEventSubscriber quickCheckAppoint error:{}", e.getMessage(), e);
            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("同步实验室失败");
            MedicalPromise medicalPromise = medicalPromiseList.get(0);
            dongDongRobotRpc.sendDongDongRobotMessage(String.format("quickCheckAppoint，同步实验室检测信息失败，（实验室名称：%s，检测单号：%s，错误msg：%s，，检测项目名称：%s）",
                            medicalPromise.getStationName(), medicalPromise.getMedicalPromiseId(),
                           e.getMessage(), medicalPromise.getServiceItemName()),
                    jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            throw e;
        }
    }

    /**
     * 快检绑码推送信息（仅绑码）
     * @param event
     */
    private void quickCheckAppointSelfTest(Event event) {
        log.info("ProviderPromiseEventSubscriber quickCheckAppointSelfTest event={}", JSON.toJSONString(event));
        List<MedicalPromise> medicalPromiseList = getMedicalPromiseList(Long.valueOf(event.getAggregateId()), null, null);
        AngelWork angelWork = getAngelWork(null, medicalPromiseList.get(0).getPromiseId());
        log.info("ProviderPromiseEventSubscriber quickCheckAppointSelfTest medicalPromiseList={}, angelWork={}", JSON.toJSONString(medicalPromiseList), JSON.toJSONString(angelWork));
        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(medicalPromiseList.get(0).getVerticalCode());
        if (!BusinessModeEnum.SELF_TEST.getCode().equals(jdhVerticalBusiness.getBusinessModeCode()) && !BusinessModeEnum.SELF_TEST_TRANSPORT.getCode().equals(jdhVerticalBusiness.getBusinessModeCode())){
            log.info("ProviderPromiseEventSubscriber quickCheckAppointSelfTest businessModeCode no match");
            return;
        }
        deaWithBindSpecimenCodeLogic(medicalPromiseList, angelWork);
    }

    private void deaWithBindSpecimenCodeLogic(List<MedicalPromise> medicalPromiseList, AngelWork angelWork) {
        Map<Long, List<MedPromiseHistoryDTO>> medPromiseToHistory = new HashMap<>();
        if (CollectionUtils.isNotEmpty(medicalPromiseList)){
            Set<Long> medicalPromiseIds = medicalPromiseList.stream().map(MedicalPromise::getMedicalPromiseId).collect(Collectors.toSet());
            List<MedPromiseHistoryDTO> medPromiseHistoryDTOS = medPromiseHistoryApplication.queryMedPromiseHistoryList(MedPromiseHistoryRequest.builder().medicalPromiseIds(medicalPromiseIds).build());
            medPromiseToHistory = medPromiseHistoryDTOS.stream().collect(Collectors.groupingBy(MedPromiseHistoryDTO::getMedicalPromiseId));
        }

        for (MedicalPromise medicalPromise : medicalPromiseList) {
            if (!verifyQuickCheck(medicalPromise)){
                continue;
            }
            Integer needFlowCode = 0;
            ProviderEquipmentDto providerEquipmentDto = providerEquipmentApplication.queryEquipmentDtoByStationIdAndItemId(medicalPromise.getStationId(), Long.valueOf(medicalPromise.getServiceItemId()));
            if (Objects.nonNull(providerEquipmentDto) && Objects.nonNull(providerEquipmentDto.getStreamTranscoding())){
                needFlowCode= providerEquipmentDto.getStreamTranscoding();
            }
            for (QuickCheckStatusMappingConfig statusMappingConfig : duccConfig.getQuickCheckStatusMappingConfig()) {
                Map<String, Object> expParam = buildExpParam(angelWork, medicalPromise, null);
                if ((Boolean) AviatorEvaluator.compile(statusMappingConfig.getStatusExpression(), Boolean.TRUE).execute(expParam)){
                    QuickCheckPushInfoBO pushInfoBO = buildQuickCheckPushInfoBO(medicalPromise, angelWork, statusMappingConfig,medPromiseToHistory);
                    pushInfoBO.setBizProcessType(needFlowCode);
                    if(isAPIVersion3(medicalPromise)) {
                        // 快检推送信息
                        quickCheckThirdExportServiceRpc.quickCheckPushInfo(buildPushInfoV3(pushInfoBO));
                    }else {
                        // 快检推送信息
                        quickCheckThirdExportServiceRpc.quickCheckAppoint(pushInfoBO);
                    }

                    Integer beforeStatus = medicalPromise.getStatus();

                    //查询履约单受检人信息
                    PromiseDto jdhPromise = getJdhPromise(medicalPromise.getPromiseId());
                    List<PromisePatientDto> patients = jdhPromise.getPatients();
                    Map<Long, PromisePatientDto> patientToDto = patients.stream().collect(Collectors.toMap(PromisePatientDto::getPromisePatientId, p -> p));
                    PromisePatientDto promisePatientDto = patientToDto.get(medicalPromise.getPromisePatientId());

                    MedicalPromiseSubmitContext context = MedicalPromiseConvert.INSTANCE.convertMedicalPromiseSubmitContext(medicalPromise, promisePatientDto);

                    context.init(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SUBMIT);

                    MedicalPromiseStatusEnum curStatus = MedicalPromiseStatusEnum.getByType(context.getSnapshot().getStatus());
                    medicalPromiseStatemachine.fireEvent(curStatus, Convert.convert(MedPromiseEventTypeEnum.class, context.getTriggerCommand()), context);
                    medicalPromiseRepository.save(context.getMedicalPromise());

                    eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SUBMIT,
                            MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).beforeStatus(beforeStatus).status(medicalPromise.getStatus()).specimenCode(medicalPromise.getSpecimenCode()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).build()));

                    // webSocket消息推送
                    wsPushMsg(medicalPromise, statusMappingConfig);
                    break;
                }
            }
        }
    }

    public Map<String, Object> buildExpParam(AngelWork angelWork, MedicalPromise medicalPromise, AngelShip angelShip) {
        Map<String, Object> expParam = new HashMap<>();
        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(medicalPromise.getVerticalCode());
        expParam.put("businessModeCode", jdhVerticalBusiness.getBusinessModeCode());
        expParam.put("verticalCode", medicalPromise.getVerticalCode());
        expParam.put("medicalPromiseStatus", medicalPromise.getStatus());
        expParam.put("freeze", medicalPromise.getFreeze());
        if (Objects.nonNull(angelWork)){
            expParam.put("angelWorkStatus", angelWork.getWorkStatus());
            AngelShip ship = getAngelShip(null, angelWork.getWorkId(), medicalPromise.getStationId());
            if (Objects.nonNull(ship)){
                expParam.put("angelShipStatus", ship.getShipStatus());
            }
        }
        log.info("ProviderPromiseEventSubscriber buildExpParam expParam={}, medicalPromiseId={}", JSON.toJSONString(expParam), medicalPromise.getMedicalPromiseId());
        return expParam;
    }

    /**
     * webSocket消息推送
     * @param medicalPromise
     * @param statusMappingConfig
     */
    private void wsPushMsg(MedicalPromise medicalPromise, QuickCheckStatusMappingConfig statusMappingConfig) {
        try {
            if (!statusMappingConfig.getWsPushOpen()){
                log.info("ProviderPromiseEventSubscriber wsPushMsg wsPushOpen false");
                return;
            }
            Provider provider = providerRepository.find(ProviderIdentifier.builder().channelNo(medicalPromise.getProviderId()).build());
            log.info("ProviderPromiseEventSubscriber wsPushMsg provider={}", JSON.toJSONString(provider));
            // 商家端推送socket消息
            if (Objects.nonNull(provider) && DockingTypeEnum.MERCHANT.getType().equals(provider.getDockingType())){
                SendLaboratorySocketMsgRequest wsMsg = new SendLaboratorySocketMsgRequest();
                wsMsg.setStationId(medicalPromise.getStationId());
                wsMsg.setWsEventType(WsEventTypeEnum.LAB_STATUS_SYNC.getType());
                Map<String, Object> data = new HashMap<>();
                data.put("medicalPromiseId", medicalPromise.getMedicalPromiseId());
                data.put("medicalPromiseStatus", medicalPromise.getStatus());
                data.put("compositeStatus", statusMappingConfig.getStatus());
                data.put("compositeStatusDesc", statusMappingConfig.getStatusDesc());
                wsMsg.setData(data);
                reachApplication.sendLaboratorySocketMsg(wsMsg);
                log.info("ProviderPromiseEventSubscriber wsPushMsg end");
            }
        } catch (Exception e) {
            log.error("ProviderPromiseEventSubscriber wsPushMsg error e", e);
        }
    }

    public QuickCheckPushInfoBO buildQuickCheckPushInfoBO(MedicalPromise medicalPromise, AngelWork angelWork, QuickCheckStatusMappingConfig statusMappingConfig,Map<Long, List<MedPromiseHistoryDTO>> medPromiseToHistory){
        log.info("ProviderPromiseEventSubscriber buildQuickCheckPushInfoBO medicalPromise={}, angelWork={}, statusMappingConfig={}", JSON.toJSONString(medicalPromise)
                , JSON.toJSONString(angelWork), JSON.toJSONString(statusMappingConfig));
        JdhPromise promise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(medicalPromise.getPromiseId()).build());
        log.info("ProviderPromiseEventSubscriber buildQuickCheckPushInfoBO promise={}", JSON.toJSONString(promise));
        QuickCheckPushInfoBO pushInfoBO = new QuickCheckPushInfoBO();
        pushInfoBO.setSampleId(String.valueOf(medicalPromise.getMedicalPromiseId()));
        pushInfoBO.setSampleBarcode(medicalPromise.getSpecimenCode());
        Map<Long, JdhPromisePatient> promisePatientMap = promise.getPatients().stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId,
                Function.identity(), (key1, key2) -> key2));
        JdhPromisePatient promisePatient = promisePatientMap.get(medicalPromise.getPromisePatientId());
        pushInfoBO.setUserName(promisePatient.getUserName().getName());
        pushInfoBO.setUserPhone(promisePatient.getPhoneNumber().getPhone());
        pushInfoBO.setUserGender(promisePatient.getGender());
        pushInfoBO.setUserAge(promisePatient.getBirthday().getAge());
        pushInfoBO.setServiceItemId(medicalPromise.getServiceItemId());
        pushInfoBO.setServiceItemName(medicalPromise.getServiceItemName());
        pushInfoBO.setStoreId(medicalPromise.getStationId());
        pushInfoBO.setStoreName(medicalPromise.getStationName());
        if (StringUtils.isNotBlank(medicalPromise.getSpecimenCode()) && MapUtil.isNotEmpty(medPromiseToHistory) && medPromiseToHistory.containsKey(medicalPromise.getMedicalPromiseId())){
            List<MedPromiseHistoryDTO> collectList = medPromiseToHistory.get(medicalPromise.getMedicalPromiseId()).stream().filter(p -> Objects.equals(MedicalPromiseStatusEnum.COLLECTED.getStatus(), p.getAfterStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collectList)){
                collectList.sort(Comparator.comparing(MedPromiseHistoryDTO::getCreateTime).reversed());
                pushInfoBO.setAppointmentStartTime(DateUtil.format(collectList.get(0).getCreateTime(),CommonConstant.YMDHM));
            }
        }
        if (StringUtils.isBlank(pushInfoBO.getAppointmentStartTime())){
            pushInfoBO.setAppointmentStartTime(promise.getAppointmentTime().formatAppointmentStartTime());

        }
        pushInfoBO.setUserMarriage(promisePatient.getMarriage());
        pushInfoBO.setUserBirth(promisePatient.getBirthday().getBirth());
        if (Objects.nonNull(medicalPromise.getSubStatus()) && !Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(),medicalPromise.getStatus())) {
            MedicalPromiseSubStatusEnum enumBySubStatus = MedicalPromiseSubStatusEnum.getEnumBySubStatus(medicalPromise.getSubStatus());
            pushInfoBO.setCoreStatus(enumBySubStatus.getCallBackMainStatus());
        } else {
            pushInfoBO.setCoreStatus(statusMappingConfig.getStatus());
        }
        pushInfoBO.setStatus(statusMappingConfig.getStatus());

        pushInfoBO.setSubStatus(medicalPromise.getSubStatus());
        pushInfoBO.setVersion(medicalPromise.getVersion());
        if (Objects.nonNull(angelWork)){
            AngelShip angelShip = getAngelShip(null, angelWork.getWorkId(), medicalPromise.getStationId());
            if (Objects.nonNull(angelShip)){
                pushInfoBO.setDeliverType(angelShip.getType());
                pushInfoBO.setPickUpCode(angelShip.getFinishCode());
                pushInfoBO.setSenderName(angelShip.getTransferName());
                pushInfoBO.setSenderPhone(angelShip.getTransferPhone());
            }
        }

        // 查询耗材
        JdhStationServiceItemRelRequest stationServiceItemRelRequest = new JdhStationServiceItemRelRequest();
        stationServiceItemRelRequest.setStationId(medicalPromise.getStationId());
        stationServiceItemRelRequest.setServiceItemId(Long.valueOf(medicalPromise.getServiceItemId()));
        JdhStationServiceItemRelDto stationServiceItemRelDto = providerStoreApplication.queryStationServiceItemRel(stationServiceItemRelRequest);
        if (Objects.nonNull(stationServiceItemRelDto) && Objects.nonNull(stationServiceItemRelDto.getSampleType())){
            DictRequest dictRequest = new DictRequest();
            dictRequest.setDictGroups(Sets.newHashSet("sampleType"));
            Map<String, List<DictInfoDto>> stringListMap = dictApplication.queryMultiDictList(dictRequest);
            if (MapUtil.isNotEmpty(stringListMap) && CollectionUtils.isNotEmpty(stringListMap.get("sampleType"))){
                List<DictInfoDto> sampleType = stringListMap.get("sampleType");
                DictInfoDto dictInfoDto = sampleType.stream().filter(p -> Objects.equals(p.getValue(), stationServiceItemRelDto.getSampleType())).findFirst().orElse(null);
                if (Objects.nonNull(dictInfoDto)){
                    pushInfoBO.setConsumableName(dictInfoDto.getLabel());
                }
            }
//            pushInfoBO.setConsumableName(stationServiceItemRelDto.getSamplingWay());
        }

        // 查询ETA
        if (statusMappingConfig.getPromiseGoOpen()){
            try {
                LabPromisegoBo labPromisegoBo = promisegoApplication.queryLabPromisego(medicalPromise.getMedicalPromiseId()
                        , LabAggStatusEnum.getEnumByStatus(statusMappingConfig.getPromiseGoAggregateStatus()));
                log.info("ProviderPromiseEventSubscriber buildQuickCheckPushInfoBO labPromisegoBo={}", JSON.toJSONString(labPromisegoBo));
                if (Objects.nonNull(labPromisegoBo)){
                    Date endTime = null;
                    if ("termScript".equals(statusMappingConfig.getPromiseGoScript()) && Objects.nonNull(labPromisegoBo.getTermScript())){
                        endTime = labPromisegoBo.getTermScript().getEndTime();
                    }else if ("currScript".equals(statusMappingConfig.getPromiseGoScript()) && Objects.nonNull(labPromisegoBo.getCurrScript())){
                        endTime = labPromisegoBo.getCurrScript().getEndTime();
                    }
                    if ("deliverEtaTime".equals(statusMappingConfig.getPromiseGoValidityTime())){
                        pushInfoBO.setDeliverEtaTime(endTime);// 预计送达时间  0：待服务者接单 2：待采样 3：样本送检中
                    }else if ("requireCollectSampleTime".equals(statusMappingConfig.getPromiseGoValidityTime())){
                        pushInfoBO.setRequireCollectSampleTime(endTime);// 要求最晚收样时间  4：样本送达实验室
                    }else if ("requirePushReportTime".equals(statusMappingConfig.getPromiseGoValidityTime())){
                        pushInfoBO.setRequirePushReportTime(endTime);// 要求最晚推送报告时间  5：检测中
                    }
                }
            } catch (Exception e) {
                log.error("ProviderPromiseEventSubscriber buildQuickCheckPushInfoBO queryLabPromisego error e", e);
            }
        }
        VoucherDto voucherDto = voucherApplication.findByVoucherId(VoucherIdRequest.builder().voucherId(medicalPromise.getVoucherId()).build());
        if (isAPIVersion3(medicalPromise)) {
            List<MedPromiseHistoryDTO> medPromiseHistoryDTOList = medPromiseHistoryApplication.queryMedPromiseHistoryList(MedPromiseHistoryRequest.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).build());
            if (CollUtil.isNotEmpty(medPromiseHistoryDTOList)) {
                //取检测时间
                for (MedPromiseHistoryDTO ele : medPromiseHistoryDTOList) {
                    if(MedicalPromiseStatusEnum.COLLECTED.getStatus().equals(ele.getAfterStatus())){
                        pushInfoBO.setSamplingTime(ele.getCreateTime());
                        break;
                    }
                }
            }
            if (voucherDto != null) {
                JdOrderDTO jdOrderDTO = tradeApplication.queryJdOrderDTO(OrderDetailParam.builder().orderId(voucherDto.getSourceVoucherId()).build());
                if (jdOrderDTO != null) {
                    pushInfoBO.setOrderingTime(jdOrderDTO.getPaymentTime());
                    //0 C端交易  1 互医检验单
                    if(PartnerSourceEnum.JDH_NETDIAG.getCode().equals(jdOrderDTO.getPartnerSource()) || PartnerSourceEnum.JDH_HOMEDIAG.getCode().equals(jdOrderDTO.getPartnerSource())){
                        pushInfoBO.setOrderingPhysician("京东医生");
                    } else {
                        pushInfoBO.setOrderingPhysician("用户自选");
                    }
                }
            }
            if (angelWork != null && AngelWorkTypeEnum.matchToHome(angelWork.getWorkType())) {
                pushInfoBO.setSamplingTechnician(angelWork.getAngelName());
            }
        }
        if (voucherDto != null && voucherDto.getExtend() != null) {
            pushInfoBO.setPartnerSourceOrderId(voucherDto.getExtend().getPartnerSourceOrderId());
            pushInfoBO.setOuterPatientId(voucherDto.getExtend().getOuterPatientId());
        }
        log.info("ProviderPromiseEventSubscriber buildQuickCheckPushInfoBO pushInfoBO={}", JSON.toJSONString(pushInfoBO));
        return pushInfoBO;
    }

    /**
     * 运单
     * @param shipId
     * @return
     */
    public AngelShip getAngelShip(Long shipId, Long workId, String receiverId){
        AngelShipDBQuery query = new AngelShipDBQuery();
        if (shipId != null){
            query.setShipIds(Collections.singletonList(shipId));
        }
        query.setWorkId(workId);
//        query.setReceiverId(receiverId);
        List<AngelShip> angelShipList = angelShipRepository.findList(query);
        log.info("ProviderPromiseEventSubscriber getAngelShip query={}, angelShipList={}", JSON.toJSONString(query), JSON.toJSONString(angelShipList));
        if (CollectionUtils.isEmpty(angelShipList)){
            return null;
        }
        List<AngelShip> receiveList = angelShipList.stream().filter(p -> StringUtils.equals(receiverId, p.getReceiverId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(receiveList)){
            receiveList = receiveList.stream().sorted(Comparator.comparing(AngelShip::getCreateTime).reversed()).collect(Collectors.toList());
            return receiveList.get(0);
        }
//        if(CollectionUtils.isEmpty(angelShipList)){
//            log.info("ProviderPromiseEventSubscriber getAngelShip angelShipList empty");
//            return null;
//        }
        angelShipList = angelShipList.stream().sorted(Comparator.comparing(AngelShip::getCreateTime).reversed()).collect(Collectors.toList());
        return angelShipList.get(0);
    }

    /**
     * 服务者工单
     * @param workId
     * @return
     */
    public AngelWork getAngelWork(Long workId, Long promiseId){
        AngelWorkDBQuery query = new AngelWorkDBQuery();
        if (workId != null){
            query.setWorkIds(Collections.singletonList(workId));
        }
        query.setPromiseId(promiseId);
        List<AngelWork> angelWorkList = angelWorkRepository.findList(query);
        log.info("ProviderPromiseEventSubscriber getAngelWork query={}, angelWorkList={}", JSON.toJSONString(query), JSON.toJSONString(angelWorkList));
        if(CollectionUtils.isEmpty(angelWorkList)){
            log.info("ProviderPromiseEventSubscriber getAngelWork angelWorkList empty");
            return null;
        }
        angelWorkList = angelWorkList.stream().sorted(Comparator.comparing(AngelWork::getCreateTime).reversed()).collect(Collectors.toList());
        return angelWorkList.get(0);
    }

    /**
     * 检测单
     * @return
     */
    public List<MedicalPromise> getMedicalPromiseList(Long medicalPromiseId, Long promiseId, String stationId){
        MedicalPromiseListQuery query = new MedicalPromiseListQuery();
        query.setMedicalPromiseId(medicalPromiseId);
        query.setPromiseId(promiseId);
        query.setStationId(stationId);
        List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(query);
        log.info("ProviderPromiseEventSubscriber getMedicalPromiseList query={}, medicalPromiseList={}", JSON.toJSONString(query), JSON.toJSONString(medicalPromiseList));
        if(CollectionUtils.isEmpty(medicalPromiseList)){
            log.error("ProviderPromiseEventSubscriber getMedicalPromiseList medicalPromiseList empty");
            throw new BusinessException(AngelPromiseBizErrorCode.MEDICAL_PROMISE_NOT_EXIST);
        }
        return medicalPromiseList;
    }

    /**
     * 创建质控单，同步实验室
     * @param event
     */
    @LogAndAlarm
    public void dealMedPromiseQuality(Event event){

        String aggregateId = event.getAggregateId();

        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(Long.valueOf(aggregateId)).build());
        if (Objects.isNull(medicalPromise)){
            return;
        }
        QuickCheckPushInfoBO quickCheckPushInfoBO = new QuickCheckPushInfoBO();
        quickCheckPushInfoBO.setSampleId(aggregateId);
        quickCheckPushInfoBO.setSampleBarcode(medicalPromise.getSpecimenCode());
        //TODO user
        quickCheckPushInfoBO.setUserAge(28);
        quickCheckPushInfoBO.setUserBirth("2021-01-01 00:00:00");
        quickCheckPushInfoBO.setUserName("测试质控");
        quickCheckPushInfoBO.setUserPhone("15800000000");
        quickCheckPushInfoBO.setUserGender(1);
        quickCheckPushInfoBO.setUserMarriage(1);

        quickCheckPushInfoBO.setServiceItemName(medicalPromise.getServiceItemName());
        quickCheckPushInfoBO.setServiceItemId(medicalPromise.getServiceItemId());
        quickCheckPushInfoBO.setStoreId(medicalPromise.getStationId());
        quickCheckPushInfoBO.setStoreName(medicalPromise.getStationName());
        quickCheckPushInfoBO.setAppointmentStartTime(DateUtil.format(new Date(),CommonConstant.YMDHMS));

        quickCheckPushInfoBO.setStatus(MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getStatus());
        quickCheckPushInfoBO.setCoreStatus(MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getCallBackMainStatus());
        quickCheckPushInfoBO.setSubStatus(MedicalPromiseSubStatusEnum.SAMPLE_DEAL.getSubStatus());

        ProviderEquipmentDto providerEquipmentDto = providerEquipmentApplication.queryEquipmentDtoByStationIdAndItemId(medicalPromise.getStationId(), Long.valueOf(medicalPromise.getServiceItemId()));
        if (Objects.nonNull(providerEquipmentDto)){
            quickCheckPushInfoBO.setBizProcessType(providerEquipmentDto.getStreamTranscoding());
        }

        quickCheckThirdExportServiceRpc.quickCheckPushInfo(quickCheckPushInfoBO);
        QuickPushInfoResultBO quickPushInfoResultBO = quickCheckThirdExportServiceRpc.quickCheckPushInfo(quickCheckPushInfoBO);
        if (Objects.nonNull(quickPushInfoResultBO) && Boolean.TRUE.equals(quickPushInfoResultBO.getResult())){
            FlowCodeBO flowCodeBO = quickCheckThirdExportServiceRpc.queryFlowCode(FlowCodeQueryBO.builder().medicalPromiseId(String.valueOf(medicalPromise.getMedicalPromiseId())).specimenCode(medicalPromise.getSpecimenCode()).stationId(medicalPromise.getStationId()).specimenCodeList(com.google.common.collect.Lists.newArrayList(medicalPromise.getSpecimenCode())).build());
            if (Objects.nonNull(flowCodeBO) && CollUtil.isNotEmpty(flowCodeBO.getTranCodeVoList())){
                Map<String, FlowCodeListBO> transCodeMap = flowCodeBO.getTranCodeVoList().stream().collect(Collectors.toMap(
                        FlowCodeListBO::getTransCode,
                        codeDto -> codeDto,
                        (oldValue, newValue) -> oldValue // 保留第一个
                ));
                if (CollUtil.isNotEmpty(transCodeMap) && transCodeMap.containsKey(medicalPromise.getSpecimenCode())) {
                    FlowCodeListBO flowCodeListBO = transCodeMap.get(medicalPromise.getSpecimenCode());
                    medicalPromise.setFlowCode(flowCodeListBO.getTransCode());
                    medicalPromiseRepository.save(medicalPromise);
                } else {
                    //TODO 报警
                }
            }else {
                //TODO 报警
            }

    }
    }

    /**
     * 指定实验室
     */
    private void targetStation(Event event) {
        log.info("ProviderPromiseEventSubscriber targetStation event={}", JSON.toJSONString(event));
        MedicalPromiseEventBody body = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);

        //通知原实验室作废
        if (StringUtils.isNotBlank(body.getBeforeStationId())) {
            executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
                Boolean invalid = quickCheckThirdExportServiceRpc.invalidQuickCheckAppoint(
                        QuickCheckInvalidBO.builder()
                                .storeId(body.getBeforeStationId())
                                .sampleId(String.valueOf(body.getMedicalPromiseId()))
                                .sampleBarcode(body.getSpecimenCode())
                                .build()
                );
                log.info("ProviderPromiseEventSubscriber targetStation invalid,result={}", invalid);
            });

        }

        List<MedicalPromise> medicalPromiseList = getMedicalPromiseList(Long.valueOf(event.getAggregateId()), null, null);
        AngelWork angelWork = getAngelWork(null, medicalPromiseList.get(0).getPromiseId());
        log.info("ProviderPromiseEventSubscriber targetStation medicalPromiseList={}, angelWork={}", JSON.toJSONString(medicalPromiseList), JSON.toJSONString(angelWork));
        try {
            deaWithBindSpecimenCodeLogic(medicalPromiseList, angelWork);
        }catch (BusinessException b){
            log.info("ProviderPromiseEventSubscriber targetStation error:{}", b);
            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("同步实验室失败");
            MedicalPromise medicalPromise = medicalPromiseList.get(0);
            dongDongRobotRpc.sendDongDongRobotMessage(String.format("targetStation，同步实验室检测信息失败，（实验室名称：%s，检测单号：%s，错误code：%s，错误msg：%s，，检测项目名称：%s）",
                            medicalPromise.getStationName(), medicalPromise.getMedicalPromiseId(),
                            b.getErrorCode(), b.getMessage(), medicalPromise.getServiceItemName()),
                    jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            throw b;
        }catch (Exception e){
            log.info("ProviderPromiseEventSubscriber targetStation error:{}", e.getMessage());
            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("同步实验室失败");
            MedicalPromise medicalPromise = medicalPromiseList.get(0);
            dongDongRobotRpc.sendDongDongRobotMessage(String.format("targetStation，同步实验室检测信息失败，（实验室名称：%s，检测单号：%s，错误msg：%s，，检测项目名称：%s）",
                            medicalPromise.getStationName(), medicalPromise.getMedicalPromiseId(),
                            e.getMessage(), medicalPromise.getServiceItemName()),
                    jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            throw e;
        }


//        log.info("ProviderPromiseEventSubscriber quickCheckAppoint event={}", JSON.toJSONString(event));
//        List<MedicalPromise> medicalPromiseList = getMedicalPromiseList(Long.valueOf(event.getAggregateId()), null, null);
//        AngelWork angelWork = getAngelWork(null, medicalPromiseList.get(0).getPromiseId());
//        log.info("ProviderPromiseEventSubscriber targetStation medicalPromiseList={}, angelWork={}", JSON.toJSONString(medicalPromiseList), JSON.toJSONString(angelWork));
//        for (MedicalPromise medicalPromise : medicalPromiseList) {
//            if (!verifyQuickCheck(medicalPromise)){
//                continue;
//            }
//            Integer beforeStatus = medicalPromise.getStatus();
//            for (QuickCheckStatusMappingConfig statusMappingConfig : duccConfig.getQuickCheckStatusMappingConfig()) {
//                Map<String, Object> expParam = buildExpParam(angelWork, medicalPromise);
//                if ((Boolean) AviatorEvaluator.compile(statusMappingConfig.getStatusExpression(), Boolean.TRUE).execute(expParam)) {
//                    QuickCheckPushInfoBO pushInfoBO = buildQuickCheckPushInfoBO(medicalPromise, angelWork, statusMappingConfig);
//                    // 快检推送信息
//
//                    try {
//                        quickCheckThirdExportServiceRpc.quickCheckAppoint(pushInfoBO);
//
//                    }catch (BusinessException b){
//                      /*  dongDongRobotRpc.sendDongDongRobotMessage(String.format("当前订单仍未有骑手接单，请尽快排查解决（实验室名称：%s，订单号：%s，配送方式：%s，运单id：%s，三方运单id：%s，预约上门时间：%s，检测项目名称：%s，上门地址：%s，订单备注：%s）",
//                                        stationName, jdhDispatch.getServiceInfo().getSourceVoucherId(),
//                                        deliveryTypeDesc, shipId, outShipId,
//                                        jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime(), serviceItemName,
//                                        jdhDispatch.getServiceLocation().getServiceLocationDetail(), Optional.ofNullable(jdhDispatch.getServiceInfo().getRemark()).orElse("")),
//                                jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));*/
//
//
//                    }
//
//
//                    quickCheckThirdExportServiceRpc.quickCheckAppoint(pushInfoBO);
//                    medicalPromise.setStatus(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
//                    medicalPromiseRepository.save(medicalPromise);
//
//                    eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SUBMIT,
//                            MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).beforeStatus(beforeStatus).status(medicalPromise.getStatus()).specimenCode(medicalPromise.getSpecimenCode()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).build()));
//
//                    wsPushMsg(medicalPromise, statusMappingConfig);
//                    break;
//                }
//            }
//        }
    }

    /**
     * 实验室迁移
     * @param event 事件对象，包含迁移所需的信息
     */
    private void migrationStation(Event event){
        log.info("ProviderPromiseEventSubscriber migrationStation event={}", JSON.toJSONString(event));
        MedicalPromiseEventBody body = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);

        //通知原实验室作废
        if (StringUtils.isNotBlank(body.getBeforeStationId())){
            Boolean invalid = quickCheckThirdExportServiceRpc.invalidQuickCheckAppoint(
                    QuickCheckInvalidBO.builder()
                            .storeId(body.getBeforeStationId())
                            .sampleId(String.valueOf(body.getMedicalPromiseId()))
                            .build()
            );
            log.info("ProviderPromiseEventSubscriber migrationStation invalid,result={}", invalid);
        }

        //同步最新实验室
        List<MedicalPromise> medicalPromiseList = getMedicalPromiseList(body.getMedicalPromiseId(), null, null);
        AngelWork angelWork = getAngelWork(null, medicalPromiseList.get(0).getPromiseId());
        log.info("ProviderPromiseEventSubscriber migrationStation medicalPromiseList={}, angelWork={}", JSON.toJSONString(medicalPromiseList), JSON.toJSONString(angelWork));
        Map<Long, List<MedPromiseHistoryDTO>> medPromiseToHistory = new HashMap<>();
        if (CollectionUtils.isNotEmpty(medicalPromiseList)){
            Set<Long> medicalPromiseIds = medicalPromiseList.stream().map(MedicalPromise::getMedicalPromiseId).collect(Collectors.toSet());
            List<MedPromiseHistoryDTO> medPromiseHistoryDTOS = medPromiseHistoryApplication.queryMedPromiseHistoryList(MedPromiseHistoryRequest.builder().medicalPromiseIds(medicalPromiseIds).build());
            medPromiseToHistory = medPromiseHistoryDTOS.stream().collect(Collectors.groupingBy(MedPromiseHistoryDTO::getMedicalPromiseId));
        }
        for (MedicalPromise medicalPromise : medicalPromiseList) {
            if (!verifyQuickCheck(medicalPromise)){
                continue;
            }
            for (QuickCheckStatusMappingConfig statusMappingConfig : duccConfig.getQuickCheckStatusMappingConfig()) {
                Map<String, Object> expParam = buildExpParam(angelWork, medicalPromise, null);
                if ((Boolean) AviatorEvaluator.compile(statusMappingConfig.getStatusExpression(), Boolean.TRUE).execute(expParam)){
                    QuickCheckPushInfoBO pushInfoBO = buildQuickCheckPushInfoBO(medicalPromise, angelWork, statusMappingConfig,medPromiseToHistory);
                    // 快检推送信息
                    if(isAPIVersion3(medicalPromise)) {
                        // 快检推送信息
                        quickCheckThirdExportServiceRpc.quickCheckPushInfo(buildPushInfoV3(pushInfoBO));
                    }else {
                        quickCheckThirdExportServiceRpc.quickCheckAppoint(pushInfoBO);
                    }
                    // webSocket消息推送
                    wsPushMsg(medicalPromise, statusMappingConfig);
                    break;
                }
            }
        }
    }

    /**
     * 查询履约单
     * @param promiseId
     * @return
     */
    private PromiseDto getJdhPromise(Long promiseId) {
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(promiseId);
        PromiseDto jdhPromise = promiseApplication.findByPromiseId(promiseIdRequest);
        if (Objects.isNull(jdhPromise) || CollectionUtils.isEmpty(jdhPromise.getPatients())){
            //没有履约信息或者受检人信息
            throw new BusinessException(MedPromiseErrorCode.PROMISE_MEDICAL_PROMISE_NULL);
        }
        return jdhPromise;
    }

    private Boolean verifyQuickCheck(MedicalPromise medicalPromise){
        if (duccConfig.getQuickCheckStationIdBlackList().contains(medicalPromise.getStationId())){
            log.info("ProviderPromiseEventSubscriber verifyQuickCheck stationId no match");
            return false;
        }
        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(medicalPromise.getVerticalCode());
        if (!Arrays.asList(BusinessModeEnum.SELF_TEST.getCode(), BusinessModeEnum.ANGEL_TEST.getCode(),BusinessModeEnum.SELF_TEST_TRANSPORT.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode()).contains(jdhVerticalBusiness.getBusinessModeCode())){
            log.info("ProviderPromiseEventSubscriber verifyQuickCheck businessModeCode no match");
            return false;
        }
        return true;
    }

    /**
     * 报告重置
     */
    private void reportReset(Event event) {
        MedicalPromiseEventBody body = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        if (body == null || body.getMedicalPromiseId() == null) {
            return;
        }
        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(body.getMedicalPromiseId()).build());
        if (medicalPromise == null || StringUtils.isBlank(medicalPromise.getStationId())) {
            return;
        }
        QuickCheckReportRetractBO quickCheckReportRetractBO = new QuickCheckReportRetractBO();
        quickCheckReportRetractBO.setStoreId(medicalPromise.getStationId());
        quickCheckReportRetractBO.setSampleId(medicalPromise.getMedicalPromiseId().toString());
        quickCheckReportRetractBO.setSampleBarcode(medicalPromise.getSpecimenCode());
        quickCheckThirdExportServiceRpc.reportRetract(quickCheckReportRetractBO);
    }

    private void concessionTest(Event event){
        MedicalPromiseEventBody body = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        if (body == null || body.getMedicalPromiseId() == null) {
            return;
        }
        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(body.getMedicalPromiseId()).build());
        if (medicalPromise == null || StringUtils.isBlank(medicalPromise.getStationId())) {
            return;
        }
        log.info("ProviderPromiseEventSubscriber concessionTest start");
        SendLaboratorySocketMsgRequest wsMsg = new SendLaboratorySocketMsgRequest();
        wsMsg.setWsEventType(WsEventTypeEnum.LAB_STATUS_SYNC.getType());
        wsMsg.setStationId(medicalPromise.getStationId());
        Map<String, Object> data = new HashMap<>();
        data.put("compositeStatus", MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_AGREE.getSubStatus());
        data.put("compositeStatusDesc", MedicalPromiseSubStatusEnum.SAMPLE_CHECK_REFUSE_CHECK_AGREE.getDesc());
        data.put("medicalPromiseId", medicalPromise.getMedicalPromiseId());
        data.put("medicalPromiseStatus", medicalPromise.getStatus());
        wsMsg.setData(data);
        reachApplication.sendLaboratorySocketMsg(wsMsg);
        log.info("ProviderPromiseEventSubscriber concessionTest end");
    }

    /**
     * 新版同步检测单和同步运力接口分开调用
     */
    private boolean isAPIVersion3(MedicalPromise medicalPromise) {
        // shop端VenderResourceAttributeEnum，配置了推送运力接口的
        Integer resourceAttribute = 32;
        try{
            GetProviderParam getProviderParam = new GetProviderParam();
            if (medicalPromise.getProviderId() != null) {
                getProviderParam.setChannelNo(medicalPromise.getProviderId());
            } else {
                if (StringUtils.isNotBlank(medicalPromise.getStationId())) {
                    StoreInfoBo storeInfoBo = providerStoreExportServiceRpc.queryByStoreId(medicalPromise.getStationId());
                    if (storeInfoBo != null && storeInfoBo.getChannelNo() != null) {
                        getProviderParam.setChannelNo(storeInfoBo.getChannelNo());
                    }
                }
            }
            if (getProviderParam.getChannelNo() == null) {
                log.info("isAPIVersion3 {}", false);
                return false;
            }
            List<XfylProviderResourceDTO> providerResourceDTOS = providerRepository.getSourceProviderResourceList(getProviderParam);
            if (CollUtil.isEmpty(providerResourceDTOS)) {
                log.info("isAPIVersion3 {}", false);
                return false;
            }
            boolean ret = providerResourceDTOS.stream().map(XfylProviderResourceDTO::getAttributeType).filter(Objects::nonNull).collect(Collectors.toList()).contains(resourceAttribute);
            log.info("isAPIVersion3 {}", ret);
            return ret;
        } catch (Exception e) {
            log.error("判断API接口是否新逻辑失败,isNewVersion exception", e);
        }
        log.info("isAPIVersion3 {}", false);
        return false;
    }

    /**
     * 换绑编码
     */
    private void codeReset(Event event) {
        log.info("codeReset event={}", JSON.toJSONString(event));
        MedicalPromiseEventBody body = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        if (body != null) {
            QuickCheckModifyBO modifyBO = new QuickCheckModifyBO();
            modifyBO.setSampleId(body.getMedicalPromiseId().toString());
            modifyBO.setSampleBarcode(body.getSpecimenCode());
            if (StringUtils.isNotBlank(body.getStationId())) {
                modifyBO.setStoreId(body.getStationId());
            } else {
                MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(body.getMedicalPromiseId()).build());
                if (medicalPromise != null) {
                    modifyBO.setStoreId(medicalPromise.getStationId());
                }
            }
            if (StringUtils.isNotBlank(modifyBO.getStoreId())) {
                quickCheckThirdExportServiceRpc.quickCheckModify(buildQuickCheckModifyV3(modifyBO));
            }
        }
    }


    /**
     * 构建检验单基础数据
     * @param pushInfoBO
     * @return
     */
    private QuickCheckPushInfoBO buildPushInfoV3(QuickCheckPushInfoBO pushInfoBO) {
        QuickCheckPushInfoBO v3 = new QuickCheckPushInfoBO();
        v3.setSampleId(pushInfoBO.getSampleId());
        v3.setSampleBarcode(pushInfoBO.getSampleBarcode());
        v3.setUserName(pushInfoBO.getUserName());
        v3.setUserPhone(pushInfoBO.getUserPhone());
        v3.setUserGender(pushInfoBO.getUserGender());
        v3.setUserAge(pushInfoBO.getUserAge());
        v3.setSamplingTechnician(pushInfoBO.getSamplingTechnician());
        v3.setSamplingTime(pushInfoBO.getSamplingTime());
        v3.setServiceItemId(pushInfoBO.getServiceItemId());
        v3.setServiceItemName(pushInfoBO.getServiceItemName());
        v3.setStoreId(pushInfoBO.getStoreId());
        v3.setStoreName(pushInfoBO.getStoreName());
        // V3版本到秒
        v3.setAppointmentStartTime(StringUtils.isBlank(pushInfoBO.getAppointmentStartTime()) ? null : pushInfoBO.getAppointmentStartTime() + ":00");
        v3.setUserMarriage(pushInfoBO.getUserMarriage());
        v3.setUserBirth(pushInfoBO.getUserBirth());
        v3.setOrderingPhysician(pushInfoBO.getOrderingPhysician());
        v3.setOrderingTime(pushInfoBO.getOrderingTime());
        v3.setPartnerSourceOrderId(pushInfoBO.getPartnerSourceOrderId());
        v3.setOuterPatientId(pushInfoBO.getOuterPatientId());
        v3.setSubStatus(pushInfoBO.getSubStatus());
        v3.setCoreStatus(pushInfoBO.getCoreStatus());
        v3.setStatus(pushInfoBO.getStatus());
        return v3;
    }

    /**
     * 构建检验单修改基础数据
     * @param pushInfoBO
     * @return
     */

    private QuickCheckModifyBO buildQuickCheckModifyV3(QuickCheckModifyBO pushInfoBO) {
        QuickCheckModifyBO v3 = new QuickCheckModifyBO();
        v3.setSampleId(pushInfoBO.getSampleId());
        v3.setSampleBarcode(pushInfoBO.getSampleBarcode());
        v3.setUserName(pushInfoBO.getUserName());
        v3.setUserPhone(pushInfoBO.getUserPhone());
        v3.setUserGender(pushInfoBO.getUserGender());
        v3.setUserAge(pushInfoBO.getUserAge());
        v3.setStoreId(pushInfoBO.getStoreId());
        v3.setStoreName(pushInfoBO.getStoreName());
        // V3版本到秒
        v3.setAppointmentStartTime(StringUtils.isBlank(pushInfoBO.getAppointmentStartTime()) ? null : pushInfoBO.getAppointmentStartTime() + ":00");
        v3.setUserMarriage(pushInfoBO.getUserMarriage());
        v3.setUserBirth(pushInfoBO.getUserBirth());
        return v3;
    }

    /**
     * 构建检验单修改基础数据
     * @param pushInfoBO
     * @return
     */

    private QuickCheckPushSampleDeliverBO buildQuickCheckPushSampleDeliverV3(QuickCheckPushInfoBO pushInfoBO) {
        QuickCheckPushSampleDeliverBO v3 = new QuickCheckPushSampleDeliverBO();
        v3.setSampleId(pushInfoBO.getSampleId());
        v3.setDeliverEtaTime(pushInfoBO.getDeliverEtaTime());
        v3.setDeliverType(pushInfoBO.getDeliverType());
        v3.setStatus(pushInfoBO.getStatus());
        v3.setPickUpCode(pushInfoBO.getPickUpCode());
        v3.setSenderName(pushInfoBO.getSenderName());
        v3.setSenderPhone(pushInfoBO.getSenderPhone());
        v3.setRequireCollectSampleTime(pushInfoBO.getRequireCollectSampleTime());
        v3.setRequirePushReportTime(pushInfoBO.getRequirePushReportTime());
        v3.setStoreId(pushInfoBO.getStoreId());
        v3.setSubStatus(pushInfoBO.getSubStatus());
        v3.setCoreStatus(pushInfoBO.getCoreStatus());
        return v3;
    }

}
