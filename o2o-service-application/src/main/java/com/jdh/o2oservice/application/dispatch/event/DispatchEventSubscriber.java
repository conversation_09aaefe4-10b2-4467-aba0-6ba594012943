package com.jdh.o2oservice.application.dispatch.event;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jd.newnethp.trade.center.export.core.enums.CancelCodeEnum;
import com.jd.newnethp.trade.center.export.core.enums.CancelOperatorEnum;
import com.jd.newnethp.trade.center.export.core.enums.CancelReasonCodeEnum;
import com.jd.newnethp.trade.center.export.core.enums.NethpDiagEventSourceEnum;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.event.*;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.common.enums.DispatchTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationInventoryBusinessTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.InventoryChannelEnum;
import com.jdh.o2oservice.core.domain.angel.enums.InventoryModifyTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.JobNatureEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelWorkEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.event.*;
import com.jdh.o2oservice.core.domain.dispatch.model.*;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchRepQuery;
import com.jdh.o2oservice.core.domain.dispatch.rpc.NewNethpDispatchRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.NewNethpOrderTransferByDoctorParam;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.NewNethpTradeCancelDiagParam;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.enums.JdOrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.export.angel.cmd.ModifyInventoryCmd;
import com.jdh.o2oservice.export.angel.cmd.ReduceInventoryCmd;
import com.jdh.o2oservice.export.angel.cmd.ReleaseInventoryCmd;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelPageRequest;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.dispatch.cmd.*;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchDto;
import com.jdh.o2oservice.export.dispatch.query.DispatchQueryRequest;
import com.jdh.o2oservice.export.product.cmd.ItemMaterialPackageRelCmd;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchEventSubscriber
 * @Description 派单域，事件订阅处理
 * <AUTHOR>
 * @Date 2024/4/22 18:22
 **/
@Slf4j
@Component
public class DispatchEventSubscriber {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * dispatchApplication
     */
    @Resource
    private DispatchApplication dispatchApplication;

    /**
     * angelApplication
     */
    @Resource
    private AngelApplication angelApplication;

    /**
     * jdOrderApplication
     */
    @Resource
    private JdOrderApplication jdOrderApplication;

    /**
     * 标准项目
     */
    @Resource
    private ProductServiceItemApplication productServiceItemApplication;

    /**
     * dispatchRepository
     */
    @Resource
    private DispatchRepository dispatchRepository;

    /**
     * medicalPromiseRepository
     */
    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     *
     */
    @Resource
    private AngelWorkRepository angelWorkRepository;

    /**
     *
     */
    @Resource
    private AngelShipRepository angelShipRepository;

    /**
     *
     */
    @Autowired
    private VerticalBusinessRepository businessRepository;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * commonDuccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * dongDongRobotRpc
     */
    @Resource
    private DongDongRobotRpc dongDongRobotRpc;
    /**
     *
     */
    @Autowired
    private PromiseRepository promiseRepository;

    /**
     * stationApplication
     */
    @Autowired
    private StationApplication stationApplication;

    /**
     * verticalBusinessRepository
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * 注册事件使用者
     */
    @PostConstruct
    public void registerEventConsumer() {

        //=======>>>>>> 派单任务 监听创建派单任务消息 -> 触发调用服务者派单
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_CREATE, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchCall", this::dispatchCall, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(3,60000)));

        //=======>>>>>> 派单任务 监听派单任务作废消息 -> 作废互医派单任务
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_INVALID, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchNethpInvalid", this::dispatchNethpInvalid, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3,2000,2.0,30000)));

        //=======>>>>>> 派单任务 监听派单任务重新派单消息 -> 调用互医重新派单
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_REDISPATCH, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchNethpReDispatch", this::dispatchNethpReDispatch, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(2,2000,2.0,30000)));

        //=======>>>>>> 履约单 监听 护士服务完成，骑手取货完成，-> 状态变更为 服务完成
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_FINISH_SERVED, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchAngelServiceComplete", this::dispatchAngelServiceComplete, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3,2000,2.0,30000)));

        //=======>>>>>>派单任务 超时未接单提醒
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_FAIL_ALARM_DELAY, WrapperEventConsumer.newDelayInstance(DomainEnum.DISPATCH,
                "dispatchFailAlarm", this::dispatchFailAlarm));

        //=======>>>>>>派单任务 派单无人抢，指定派给护士
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_FAIL_TARGET_DISPATCH, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchFailTargetDispatchRobot", this::dispatchFailTargetRobot, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(2,2000,2.0,30000)));

        //=======>>>>>>派单任务 无人抢单且指定派全职失败的情况
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_FAIL, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchFailRobot", this::dispatchFailRobot, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(2,2000,2.0,30000)));

        //=======>>>>>>派单任务 护士接单库存扣减
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_RECEIVED, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchReceivedReduceInventory",this::dispatchReduceInventory,Boolean.TRUE,Boolean.TRUE));

        //=======>>>>>>派单任务 护士接单库存扣减
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_ASSIGN_SUCCESS_RECEIVED, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchAssignSuccessReceivedReduceInventory",this::dispatchReduceInventory,Boolean.TRUE,Boolean.TRUE));

        //=======>>>>>>派单任务 护士转单库存扣减
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_TARGET_DISPATCH, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchTargetDispatchReduceInventory",this::targetDispatchReduceInventory,Boolean.TRUE,Boolean.TRUE));


        //=======>>>>>>派单任务 改约,库存交互
        eventConsumerRegister.register(DispatchEventTypeEnum.DISPATCH_SUCCESS_AFTER_MODIFY_DATE_SUCCESS, WrapperEventConsumer.newInstance(DomainEnum.DISPATCH,
                "dispatchSuccessModifyDateSuccessReduceInventory",this::modifyDispatchReduceInventory,Boolean.TRUE,Boolean.TRUE));

    }

    /**
     * 修改派单库存操作
     *
     * @param event 事件
     */
    private void modifyDispatchReduceInventory(Event event){
        log.info("DispatchEventSubscriber -> modifyDispatchReduceInventory event:{}", JSON.toJSONString(event));
        DispatchCallbackEventBody eventBody = JSON.parseObject(event.getBody(), DispatchCallbackEventBody.class);

        JdhDispatch dispatch = dispatchRepository.findDispatch(DispatchRepQuery.builder().dispatchId(eventBody.getDispatchId()).build());
        log.info("DispatchEventSubscriber -> modifyDispatchReduceInventory dispatch:{}", JSON.toJSONString(dispatch));

        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(dispatch.getVerticalCode());
        //护士检测的处理
        ArrayList<String> businessModeCodeList = Lists.newArrayList(BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode());
        if(businessModeCodeList.contains(jdhVerticalBusiness.getBusinessModeCode())){

            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(AngelRequest.builder().angelId(eventBody.getAngelId()).build());
            //全职护士库存扣减
            if(Objects.nonNull(jdhAngelDto) && Objects.equals(JobNatureEnum.FULL_TIME.getValue(),jdhAngelDto.getJobNature())){

                DomainAppointmentTime beforeTime = dispatch.getServiceInfo().getBeforeTime();
                DomainAppointmentTime afterTime = dispatch.getServiceInfo().getAppointmentTime();

                ModifyInventoryCmd cmd = new ModifyInventoryCmd();
                cmd.setPin(dispatch.getUserPin());
                cmd.setModifyType(InventoryModifyTypeEnum.APPOINTMENT_TIME.getType());
                cmd.setInventoryChannelNo(InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel());
                cmd.setAfterInventoryChannelNo(InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel());
                cmd.setAngelStationId(jdhAngelDto.getStationId());
                cmd.setAfterAngelStationId(jdhAngelDto.getStationId());
                cmd.setBusinessId(dispatch.getServiceInfo().getSourceVoucherId());
                cmd.setScheduleBeginTime(TimeUtils.localDateTimeToDate(beforeTime.getAppointmentStartTime()));
                cmd.setScheduleEndTime(TimeUtils.localDateTimeToDate(beforeTime.getAppointmentEndTime()));
                cmd.setAfterScheduleBeginTime(TimeUtils.localDateTimeToDate(afterTime.getAppointmentStartTime()));
                cmd.setAfterScheduleEndTime(TimeUtils.localDateTimeToDate(afterTime.getAppointmentEndTime()));
                cmd.setModifyReemptionNum(NumConstant.NUM_1);

                stationApplication.modifyInventory(cmd);
            }
        }
    }

    /**
     * targetDispatchReduceInventory
     *
     * @param event 事件
     */
    private void targetDispatchReduceInventory(Event event){
        log.info("DispatchEventSubscriber -> targetDispatchReduceInventory event:{}", JSON.toJSONString(event));
        TargetDispatchEventBody eventBody = JSON.parseObject(event.getBody(), TargetDispatchEventBody.class);
        Long angelId = eventBody.getTargetAngelId();
        JdhDispatch dispatch = dispatchRepository.findDispatch(DispatchRepQuery.builder().dispatchId(eventBody.getDispatchId()).build());
        log.info("DispatchEventSubscriber -> targetDispatchReduceInventory dispatch:{}", JSON.toJSONString(dispatch));

        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(dispatch.getVerticalCode());
        //护士检测的处理,且第一次触发
        ArrayList<String> businessModeCodeList = Lists.newArrayList(BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode());
        if(businessModeCodeList.contains(jdhVerticalBusiness.getBusinessModeCode()) && Boolean.TRUE.equals(eventBody.getIsFirstReceived())){
            Integer beforeStatus = eventBody.getBeforeStatus();
            Integer afterStatus = eventBody.getAfterStatus();
            //状态符合
            if(JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus().equals(afterStatus) &&
                    (JdhDispatchStatusEnum.DISPATCH_WAIT.getStatus().equals(beforeStatus)
                            || JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus().equals(beforeStatus)
                            || JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus().equals(beforeStatus))
            ){
                JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(AngelRequest.builder().angelId(angelId).build());
                //全职护士库存扣减、兼职释放
                if(Objects.equals(JobNatureEnum.FULL_TIME.getValue(),jdhAngelDto.getJobNature())){
                    callReduceInventory(dispatch,jdhAngelDto);
                }else{
                    //释放
                    callReleaseInventory(dispatch);
                }
            }
        }
    }

    /**
     * 释放库存
     *
     * @param dispatch dispatch
     */
    private void callReleaseInventory(JdhDispatch dispatch) {
        ReleaseInventoryCmd cmd = new ReleaseInventoryCmd();
        cmd.setBusinessId(dispatch.getServiceInfo().getSourceVoucherId());
        cmd.setPin(dispatch.getUserPin());
        cmd.setBusinessType(AngelStationInventoryBusinessTypeEnum.ORDER.getType());
        stationApplication.releaseInventory(cmd);
    }

    /**
     * dispatchReduceInventory
     *
     * @param event 事件
     */
    private void dispatchReduceInventory(Event event){
        log.info("DispatchEventSubscriber -> dispatchCallReduceInventory event:{}", JSON.toJSONString(event));
        DispatchCallbackEventBody eventBody = JSON.parseObject(event.getBody(), DispatchCallbackEventBody.class);
        Long angelId = eventBody.getAngelId();
        JdhDispatch dispatch = dispatchRepository.findDispatch(DispatchRepQuery.builder().dispatchId(eventBody.getDispatchId()).build());
        log.info("DispatchEventSubscriber -> dispatchCallReduceInventory dispatch:{}", JSON.toJSONString(dispatch));

        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(dispatch.getVerticalCode());
        //护士检测的处理，第一次接单
        ArrayList<String> businessModeCodeList = Lists.newArrayList(BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode());
        if(businessModeCodeList.contains(jdhVerticalBusiness.getBusinessModeCode())
                && Boolean.TRUE.equals(eventBody.getIsFirstReceived())
        ){
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(AngelRequest.builder().angelId(angelId).build());
            //全职护士库存扣减
            if(Objects.equals(JobNatureEnum.FULL_TIME.getValue(),jdhAngelDto.getJobNature())){
                callReduceInventory(dispatch,jdhAngelDto);
            }else{
                callReleaseInventory(dispatch);
            }
        }
    }

    /**
     * callReduceInventory
     *
     * @param dispatch    dispatch
     * @param jdhAngelDto jdhAngelDto
     */
    private void callReduceInventory(JdhDispatch dispatch,JdhAngelDto jdhAngelDto){
        ReduceInventoryCmd cmd = new ReduceInventoryCmd();
        cmd.setAngelStationId(jdhAngelDto.getStationId());
        cmd.setPin(dispatch.getUserPin());
        cmd.setBusinessId(dispatch.getServiceInfo().getSourceVoucherId());
        cmd.setBusinessType(AngelStationInventoryBusinessTypeEnum.ORDER.getType());
        cmd.setScheduleDay(dispatch.getServiceInfo().getAppointmentTime().formatAppointDate());
        cmd.setScheduleTime(dispatch.getServiceInfo().getAppointmentTime().formatAppointTimeDesc());
        cmd.setInventoryChannelNo(InventoryChannelEnum.NURSE_CHANNEL.getInventoryChannel());
        stationApplication.reduceInventory(cmd);
    }

    /**
     * 派单任务创建成功
     *
     * @param event 事件
     */
    private void dispatchCall(Event event) {
        log.info("DispatchEventSubscriber -> dispatchCall event:{}", JSON.toJSONString(event));
        DispatchWaitEventBody eventBody = JSON.parseObject(event.getBody(), DispatchWaitEventBody.class);
        AngelDispatchCmd cmd = AngelDispatchCmd.builder()
                .dispatchId(eventBody.getDispatchId())
                //.userPin()
                .voucherId(eventBody.getVoucherId())
                .promiseId(eventBody.getPromiseId())
                .eventCode(event.getEventCode())
                .build();
        dispatchApplication.angelDispatch(cmd);

        JdhDispatch jdhDispatch = dispatchRepository.find(JdhDispatchIdentifier.builder().dispatchId(eventBody.getDispatchId()).build());

        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(jdhDispatch.getVerticalCode());
        //未派单成功校验、报警
        String logId= Objects.toString(MDC.get("PFTID"), null);
        CompletableFuture.runAsync(() -> {
            try {
                MDC.put("PFTID", logId);
                Long delayTime = 30 * 60L;
                //骑手上门的派单任务
                if (Objects.nonNull(jdhVerticalBusiness) && Objects.equals(BusinessModeEnum.SELF_TEST.getCode(), jdhVerticalBusiness.getBusinessModeCode())) {
                    //PRD:https://joyspace.jd.com/pages/Gj8ZIdppDsPRA0q9KbVS
                    //①骑手上门检测即时单：【已接单(服务者)】-【运单创建时间】=5分钟，则触发预警，每5分钟发一次，直到有骑手接单为止
                    //②骑手上门检测预约单：【用户预约上门开始时间】-【当前时间】=25分钟，则触发预警，每5分钟发一次，直到有骑手接单为止
                    Date startTime = TimeUtils.localDateTimeToDate(jdhDispatch.getServiceInfo().getAppointmentTime().getAppointmentStartTime());
                    DateTime dateTime = DateUtil.offsetMinute(startTime, - duccConfig.getHomeSelfTestAlarm().getInteger("appointStartTimeInterval"));
                    delayTime = Objects.equals(jdhDispatch.getDispatchType(), DispatchTypeEnum.IMMEDIATELY.getType()) ? duccConfig.getHomeSelfTestAlarm().getLong("createDispatchInterval") * 60 : DateUtil.between(dateTime, new Date(), DateUnit.SECOND);
                    log.info("DispatchEventSubscriber -> dispatchCall 骑手上门 发送延迟消息，到期未派单成功报警 logId={}, dispatchId={}, delayTime={}", logId, jdhDispatch.getDispatchId(), delayTime);
                }
                log.info("DispatchEventSubscriber -> dispatchCall 发送延迟消息，到期未派单成功报警 logId={}, dispatchId={}, delayTime={}", logId, jdhDispatch.getDispatchId(), delayTime);
                eventCoordinator.publishDelay(EventFactory.newDelayEvent(jdhDispatch, DispatchEventTypeEnum.DISPATCH_FAIL_ALARM_DELAY, null, delayTime));
            }finally {
                MDC.remove("PFTID");
            }
        });
    }

    /**
     * 超时未接单提醒
     * @param event
     */
    private void dispatchFailAlarm(Event event){
        log.info("DispatchEventSubscriber -> dispatchFailAlarm event:{}", JSON.toJSONString(event));
        JdhDispatch jdhDispatch = dispatchRepository.find(JdhDispatchIdentifier.builder().dispatchId(Long.valueOf(event.getAggregateId())).build());
        //待派单、已派单、派单失败 报警
        ArrayList<Integer> alarmStatus = Lists.newArrayList(JdhDispatchStatusEnum.DISPATCH_WAIT.getStatus(), JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus(), JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus());
        if (Objects.nonNull(jdhDispatch) && alarmStatus.contains(jdhDispatch.getDispatchStatus())) {
            JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(jdhDispatch.getVerticalCode());
            if (Objects.nonNull(jdhVerticalBusiness) && Objects.equals(BusinessModeEnum.SELF_TEST.getCode(), jdhVerticalBusiness.getBusinessModeCode())) {
                log.info("DispatchEventSubscriber -> alarm disable 骑手上门也发送报警 jdhDispatch={}", JsonUtil.toJSONString(jdhDispatch));
                if (Lists.newArrayList(JdhDispatchStatusEnum.DISPATCH_WAIT.getStatus(), JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus()
                        , JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus()).contains(jdhDispatch.getDispatchStatus())) {
                    Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
                    JSONObject jsonObject = robotAlarmMap.get("骑手超时未接单");
                    if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
                        log.info("DispatchEventSubscriber -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
                        return;
                    }
                    //实验室名称
                    String stationName = "";
                    //检测项目名称
                    String serviceItemName = "";
                    List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(jdhDispatch.getPromiseId()).build());
                    if (CollectionUtils.isNotEmpty(medicalPromiseList)) {
                        List<String> stationNameList = medicalPromiseList.stream().map(MedicalPromise::getStationName).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        List<String> serviceItemNameList = medicalPromiseList.stream().map(MedicalPromise::getServiceItemName).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        stationName = Joiner.on("、").join(stationNameList);
                        serviceItemName = Joiner.on("、").join(serviceItemNameList);
                    }

                    //配送方式
                    String deliveryTypeDesc = "";
                    String outShipId = "";
                    String shipId = "";

                    AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
                    angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
                    angelWorkDBQuery.setPromiseId(jdhDispatch.getPromiseId());
                    AngelWork angelWork = angelWorkRepository.findAngelWork(angelWorkDBQuery);
                    if (Objects.nonNull(angelWork)) {
                        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
                        angelShipDBQuery.setWorkId(angelWork.getWorkId());
                        List<AngelShip> list = angelShipRepository.findList(angelShipDBQuery);
                        if (CollectionUtils.isNotEmpty(list)) {
                            AngelShip angelShip = list.get(0);
                            deliveryTypeDesc = DeliveryTypeEnum.getEnumDescByType(angelShip.getType());
                            outShipId = angelShip.getOutShipId();
                            shipId = Objects.isNull(angelShip.getShipId()) ? "" : String.valueOf(angelShip.getShipId());
                        }
                    }

                    dongDongRobotRpc.sendDongDongRobotMessage(String.format("当前订单仍未有骑手接单，请尽快排查解决（实验室名称：%s，订单号：%s，配送方式：%s，运单id：%s，三方运单id：%s，预约上门时间：%s，检测项目名称：%s，上门地址：%s，订单备注：%s）",
                            stationName, jdhDispatch.getServiceInfo().getSourceVoucherId(),
                            deliveryTypeDesc, shipId, outShipId,
                            jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime(), serviceItemName,
                            jdhDispatch.getServiceLocation().getServiceLocationDetail(), Optional.ofNullable(jdhDispatch.getServiceInfo().getRemark()).orElse("")),
                            jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
                }

                return;
            }
            ArrayList<String> businessModeCodeList = Lists.newArrayList(BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_CARE.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode());
            if (Objects.isNull(jdhVerticalBusiness) || businessModeCodeList.contains(jdhVerticalBusiness.getBusinessModeCode())) {
                log.info("DispatchEventSubscriber -> alarm disable 护士上门检测和护理不发送报警={}", JSON.toJSONString(jdhDispatch));
                return;
            }
            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("超时未接单提醒");
            if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
                log.info("DispatchEventSubscriber -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
                return;
            }
            List<AppointmentPatient> patients = jdhDispatch.getServiceInfo().getPatients();
            int patientSize = patients.size();
            //查询履约单拿skuId，然后查商品域查询商品名称和所需耗材
            //查询订单信息
            String orderUserPhone = "";
            String orderUserName = "";

            if (StringUtils.isNotBlank(jdhDispatch.getServiceInfo().getSourceVoucherId())) {
                JdOrder jdOrder = jdOrderApplication.queryJdOrderAndItemExt(Long.valueOf(jdhDispatch.getServiceInfo().getSourceVoucherId()));
                log.info("DispatchEventSubscriber -> dispatchFailAlarm, jdOrder={}",JSON.toJSONString(jdOrder));
                JdOrderExt jdOrderExtDetail = null;
                if (Objects.nonNull(jdOrder) && CollectionUtils.isNotEmpty(jdOrder.getJdOrderExtList())) {
                    jdOrderExtDetail = jdOrder.getJdOrderExtList().stream().filter(ext -> Objects.equals(JdOrderExtTypeEnum.APPOINTMENT_INFO.getExtType(), ext.getExtType())).findFirst().get();
                }
                if(Objects.nonNull(jdOrderExtDetail) && StringUtils.isNotBlank(jdOrderExtDetail.getExtContext())) {
                    OrderAppointmentInfoValueObject orderAppointmentInfo = JsonUtil.parseObject(jdOrderExtDetail.getExtContext(),OrderAppointmentInfoValueObject.class);
                    orderUserPhone = Objects.nonNull(orderAppointmentInfo) && Objects.nonNull(orderAppointmentInfo.getAddressInfo()) ? orderAppointmentInfo.getAddressInfo().getMobile() : "暂无电话";
                    orderUserName = Objects.nonNull(orderAppointmentInfo) && Objects.nonNull(orderAppointmentInfo.getAddressInfo()) ? nameMaskSuffix(orderAppointmentInfo.getAddressInfo().getName()) : "暂无姓名";
                }
            }

            //查询项目及耗材信息
            //获取服务项目ID
            Set<Long> itemIds = jdhDispatch.getServiceInfo().getPatients().stream().flatMap(dispatchAppointmentPatientDto -> dispatchAppointmentPatientDto.getServiceItems().stream()).map(ServiceItem::getItemId).collect(Collectors.toSet());
            //查询服务项目列表
            List<ServiceItemDto> serviceItemDtos = productServiceItemApplication.queryServiceItemList(ServiceItemQuery.builder().itemIds(itemIds).build());
            //遍历获取项目、耗材名称
            Set<String> itemNameList = new HashSet<>();
            List<String> materialPackageNameList = new ArrayList<>();
            serviceItemDtos.forEach(serviceItemDto -> {
                //项目名称列表add
                itemNameList.add(serviceItemDto.getItemName());
                List<String> materialNames = serviceItemDto.getMaterialList().stream().map(ItemMaterialPackageRelCmd::getMaterialPackageName).distinct().collect(Collectors.toList());
                materialPackageNameList.addAll(materialNames);
            });
            //根据耗材名称 ： 数量 分组
            Map<String, Long> materialNameMap = materialPackageNameList.stream()
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
            //组装每个耗材对应数量字符串列表，例如： 耗材A×2、耗材B×3
            List<String> materialNameList = materialNameMap.entrySet().stream()
                    .map(entry -> entry.getKey() + "×" + entry.getValue())
                    .collect(Collectors.toList());
            String materialPackageAggregatedDesc = Joiner.on("、").join(materialNameList);
            String serviceItemAggregatedDesc = Joiner.on("、").join(itemNameList);

            dongDongRobotRpc.sendDongDongRobotMessage(String.format("当前订单仍未有服务者接单，请尽快排查解决（履约单号：%s，订单号：%s，预约人姓名：%s，预约人电话：%s，预约上门时间：%s，上门地址：%s，项目名称：%s，所需耗材：%s， 服务人数：%s）",
                    jdhDispatch.getPromiseId(), jdhDispatch.getServiceInfo().getSourceVoucherId(), orderUserName, orderUserPhone,
                    jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime(), jdhDispatch.getServiceLocation().getServiceLocationDetail(),
                    serviceItemAggregatedDesc, materialPackageAggregatedDesc, patientSize), jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
        }
    }

    /**
     * 作废互医派单任务
     * @param event
     */
    private void dispatchNethpInvalid(Event event) {
        log.info("DispatchEventSubscriber -> dispatchNethpInvalid event:{}", JSON.toJSONString(event));
        InvalidDispatchEventBody eventBody = JSON.parseObject(event.getBody(), InvalidDispatchEventBody.class);

        if (Objects.isNull(eventBody.getDispatchId())) {
            log.warn("DispatchEventSubscriber -> dispatchNethpInvalid 派单任务ID为空，暂不处理");
            return;
        }
        JdhDispatch jdhDispatch = dispatchRepository.find(JdhDispatchIdentifier.builder().dispatchId(eventBody.getDispatchId()).build());
        log.info("DispatchEventSubscriber -> dispatchNethpInvalid jdhDispatch:{}", JSON.toJSONString(jdhDispatch));
        //需要同步互医取消的服务模式list
        ArrayList<String> nethpCancelCodeList = Lists.newArrayList(BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode(), BusinessModeEnum.ANGEL_CARE.getCode());
        if (Objects.nonNull(jdhDispatch) && nethpCancelCodeList.contains(jdhDispatch.getServiceInfo().getBusinessModeCode()) && StringUtils.isNotBlank(jdhDispatch.getOutDispatchId())) {
            //调用互医接口取消派单
            NewNethpTradeCancelDiagParam cancelDiagParam = NewNethpTradeCancelDiagParam.builder()
                    .diagId(StringUtils.isNotBlank(jdhDispatch.getOutDispatchId()) ? Long.valueOf(jdhDispatch.getOutDispatchId()):null)
                    .diagEventSourceCode(NethpDiagEventSourceEnum.USER.getCode())
                    .cancelCode(CancelCodeEnum.USER_DIAG.getCancelCode())
                    .cancelReasonCode(CancelReasonCodeEnum.OTHER.getCode())
                    .cancelOperatorCode(CancelOperatorEnum.USER.getCode())
                    .dispatchId(jdhDispatch.getDispatchId())
                    .dispatchExecuteRoute(jdhDispatch.getServiceInfo().getDispatchExecuteRoute())
                    .flowTaskId(jdhDispatch.getServiceInfo().getFlowId())
                    .build();
            dispatchApplication.handleNewNethpDispatch(DispatchNewNethpHandleCmd.builder().command(JSON.toJSONString(cancelDiagParam)).type(7).build());
        }
    }

    /**
     * 重派
     * @param event
     */
    private void dispatchNethpReDispatch(Event event) {
        log.info("DispatchEventSubscriber -> dispatchNethpReDispatch event:{}", JSON.toJSONString(event));
        ReDispatchEventBody eventBody = JSON.parseObject(event.getBody(), ReDispatchEventBody.class);

        if (Objects.isNull(eventBody.getDispatchId())) {
            log.warn("DispatchEventSubscriber -> dispatchNethpReDispatch 派单任务ID为空，暂不处理");
            return;
        }
        JdhDispatch jdhDispatch = dispatchRepository.find(JdhDispatchIdentifier.builder().dispatchId(eventBody.getDispatchId()).build());
        log.info("DispatchEventSubscriber -> dispatchNethpReDispatch jdhDispatch:{}", JSON.toJSONString(jdhDispatch));

        ArrayList<String> nethpCancelCodeList = Lists.newArrayList(BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode(), BusinessModeEnum.ANGEL_CARE.getCode());
        if (Objects.nonNull(jdhDispatch) && nethpCancelCodeList.contains(jdhDispatch.getServiceInfo().getBusinessModeCode())) {
            //护士不接此单、护士终止服务，roleType = 5，reasonType = 8，operator=外部护士ID（互医护士主数据ID）
            //系统重派此单 可能因为履约要求，客服或运营将此订单从医生手上回收的 roleType = 4，reasonType = 6，operator=erp
            boolean isAngel = Objects.equals(eventBody.getRoleType(), 5);

            NewNethpOrderTransferByDoctorParam transferByDoctorParam = NewNethpOrderTransferByDoctorParam.builder()
                    .diagId(StringUtils.isNotBlank(jdhDispatch.getOutDispatchId()) ? Long.valueOf(jdhDispatch.getOutDispatchId()):null)
                    .operator(isAngel ? eventBody.getOutAngelId() : eventBody.getOperator())
                    .roleType(isAngel ? 5 : 4)
                    .targetType(3)
                    .reasonType(isAngel ? 8 : 6)
                    .reason("医生当前不可服务当前订单需转诊")
                    .dispatchExecuteRoute(jdhDispatch.getServiceInfo().getDispatchExecuteRoute())
                    .dispatchId(jdhDispatch.getDispatchId())
                    .flowTaskId(jdhDispatch.getServiceInfo().getFlowId())
                    .tenantType(NewNethpDispatchRpc.TENANT_TYPE)
                    .eventCode(event.getEventCode())
                    .build();

            dispatchApplication.handleNewNethpDispatch(DispatchNewNethpHandleCmd.builder().command(JSON.toJSONString(transferByDoctorParam)).type(3).build());
        }
    }

    /**
     * 派单任务拉完成
     *
     * @param event 事件
     */
    private void dispatchAngelServiceComplete(Event event) {
        log.info("DispatchEventSubscriber -> dispatchAngelServiceComplete event:{}", JSON.toJSONString(event));
        AngelWorkEventBody angelWorkEventBody = JSON.parseObject(event.getBody(), AngelWorkEventBody.class);
        DispatchCompleteCmd cmd = DispatchCompleteCmd.builder()
                .promiseId(angelWorkEventBody.getPromiseId())
                .build();
        dispatchApplication.dispatchComplete(cmd);
    }

    /**
     *
     * @param event
     */
    private void dispatchFailTargetRobot(Event event){

        log.info("DispatchEventSubscriber -> dispatchFailTargetRobot event:{}", JSON.toJSONString(event));
        JdhDispatch jdhDispatch = dispatchRepository.find(JdhDispatchIdentifier.builder().dispatchId(Long.valueOf(event.getAggregateId())).build());
        JdhDispatchDetail jdhDispatchDetail = jdhDispatch.getAngelDetailList().stream().filter(p -> Objects.equals(JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus(), p.getDispatchDetailStatus())).findFirst().orElse(null);

        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(jdhDispatch.getVerticalCode());
        if (Objects.nonNull(jdhVerticalBusiness) && Objects.equals(BusinessModeEnum.SELF_TEST.getCode(), jdhVerticalBusiness.getBusinessModeCode())) {
            log.info("DispatchEventSubscriber -> alarm disable 骑手上门也发送报警 jdhDispatch={}", JsonUtil.toJSONString(jdhDispatch));
            if (Lists.newArrayList(JdhDispatchStatusEnum.DISPATCH_WAIT.getStatus(), JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus()
                    , JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus()).contains(jdhDispatch.getDispatchStatus())) {
                Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
                JSONObject jsonObject = robotAlarmMap.get("骑手超时未接单");
                if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
                    log.info("DispatchEventSubscriber -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
                    return;
                }
                dongDongRobotRpc.sendDongDongRobotMessage(String.format("当前订单仍未有骑手接单，请尽快排查解决（订单ID：%s，预约上门时间：%s，上门地址：%s）",
                        jdhDispatch.getServiceInfo().getSourceVoucherId(), jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime(),
                        jdhDispatch.getServiceLocation().getServiceLocationDetail()), jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            }

            return;
        }

        if (!duccConfig.getDispatchFailTargetRobotSwitch()){
            return;
        }

        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get("超时未接单提醒");
        if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
            log.info("DispatchEventSubscriber -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
            return;
        }
        List<AppointmentPatient> patients = jdhDispatch.getServiceInfo().getPatients();
        int patientSize = patients.size();
        //查询履约单拿skuId，然后查商品域查询商品名称和所需耗材
        //查询订单信息
        String orderUserPhone = "";
        String orderUserName = "";

        if (StringUtils.isNotBlank(jdhDispatch.getServiceInfo().getSourceVoucherId())) {
            JdOrder jdOrder = jdOrderApplication.queryJdOrderAndItemExt(Long.valueOf(jdhDispatch.getServiceInfo().getSourceVoucherId()));
            log.info("DispatchEventSubscriber -> dispatchFailAlarm, jdOrder={}",JSON.toJSONString(jdOrder));
            JdOrderExt jdOrderExtDetail = null;
            if (Objects.nonNull(jdOrder) && CollectionUtils.isNotEmpty(jdOrder.getJdOrderExtList())) {
                jdOrderExtDetail = jdOrder.getJdOrderExtList().stream().filter(ext -> Objects.equals(JdOrderExtTypeEnum.APPOINTMENT_INFO.getExtType(), ext.getExtType())).findFirst().get();
            }
            if(Objects.nonNull(jdOrderExtDetail) && StringUtils.isNotBlank(jdOrderExtDetail.getExtContext())) {
                OrderAppointmentInfoValueObject orderAppointmentInfo = JsonUtil.parseObject(jdOrderExtDetail.getExtContext(),OrderAppointmentInfoValueObject.class);
                orderUserPhone = Objects.nonNull(orderAppointmentInfo) && Objects.nonNull(orderAppointmentInfo.getAddressInfo()) ? orderAppointmentInfo.getAddressInfo().getMobile() : "暂无电话";
                orderUserName = Objects.nonNull(orderAppointmentInfo) && Objects.nonNull(orderAppointmentInfo.getAddressInfo()) ? nameMaskSuffix(orderAppointmentInfo.getAddressInfo().getName()) : "暂无姓名";
            }
        }

        //查询项目及耗材信息
        //获取服务项目ID
        Set<Long> itemIds = jdhDispatch.getServiceInfo().getPatients().stream().flatMap(dispatchAppointmentPatientDto -> dispatchAppointmentPatientDto.getServiceItems().stream()).map(ServiceItem::getItemId).collect(Collectors.toSet());
        //查询服务项目列表
        List<ServiceItemDto> serviceItemDtos = productServiceItemApplication.queryServiceItemList(ServiceItemQuery.builder().itemIds(itemIds).build());
        //遍历获取项目、耗材名称
        Set<String> itemNameList = new HashSet<>();
        List<String> materialPackageNameList = new ArrayList<>();
        serviceItemDtos.forEach(serviceItemDto -> {
            //项目名称列表add
            itemNameList.add(serviceItemDto.getItemName());
            List<String> materialNames = serviceItemDto.getMaterialList().stream().map(ItemMaterialPackageRelCmd::getMaterialPackageName).distinct().collect(Collectors.toList());
            materialPackageNameList.addAll(materialNames);
        });
        //根据耗材名称 ： 数量 分组
        Map<String, Long> materialNameMap = materialPackageNameList.stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        //组装每个耗材对应数量字符串列表，例如： 耗材A×2、耗材B×3
        List<String> materialNameList = materialNameMap.entrySet().stream()
                .map(entry -> entry.getKey() + "×" + entry.getValue())
                .collect(Collectors.toList());
        String materialPackageAggregatedDesc = Joiner.on("、").join(materialNameList);
        String serviceItemAggregatedDesc = Joiner.on("、").join(itemNameList);


        //TODO
        JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(jdhDispatch.getPromiseId()).build());
        String format = String.format("护士到家订单%s-%s无人抢单，已指派给%s(%s),请知晓并酌情处理。（履约单号：%s，订单号：%s，预约人姓名：%s，预约人电话：%s，预约上门时间：%s，上门地址：%s，项目名称：%s，所需耗材：%s， 服务人数：%s）",
                jdhPromise.getSourceVoucherId(),jdhDispatch.getPromiseId(), jdhDispatchDetail.getAngelName(), jdhDispatchDetail.getAngelId(),jdhDispatch.getPromiseId(), jdhDispatch.getServiceInfo().getSourceVoucherId(), orderUserName, orderUserPhone,
                jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime(), jdhDispatch.getServiceLocation().getServiceLocationDetail(),
                serviceItemAggregatedDesc, materialPackageAggregatedDesc, patientSize);

        dongDongRobotRpc.sendDongDongRobotMessage(format,jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));

    }

    /**
     *
     * @param event
     */
    private void dispatchFailRobot(Event event){
        log.info("DispatchEventSubscriber -> dispatchFailTargetRobot event:{}", JSON.toJSONString(event));
        JdhDispatch jdhDispatch = dispatchRepository.find(JdhDispatchIdentifier.builder().dispatchId(Long.valueOf(event.getAggregateId())).build());

        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(jdhDispatch.getVerticalCode());
        if (Objects.nonNull(jdhVerticalBusiness) && Objects.equals(BusinessModeEnum.SELF_TEST.getCode(), jdhVerticalBusiness.getBusinessModeCode())) {
            log.info("DispatchEventSubscriber -> alarm disable 骑手上门也发送报警 jdhDispatch={}", JsonUtil.toJSONString(jdhDispatch));
            if (Lists.newArrayList(JdhDispatchStatusEnum.DISPATCH_WAIT.getStatus(), JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus()
                    , JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus()).contains(jdhDispatch.getDispatchStatus())) {
                Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
                JSONObject jsonObject = robotAlarmMap.get("骑手超时未接单");
                if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
                    log.info("DispatchEventSubscriber -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
                    return;
                }
                dongDongRobotRpc.sendDongDongRobotMessage(String.format("当前订单仍未有骑手接单，请尽快排查解决（订单ID：%s，预约上门时间：%s，上门地址：%s）",
                        jdhDispatch.getServiceInfo().getSourceVoucherId(), jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime(),
                        jdhDispatch.getServiceLocation().getServiceLocationDetail()), jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            }

            return;
        }

        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get("超时未接单提醒");
        if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
            log.info("DispatchEventSubscriber -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
            return;
        }
        List<AppointmentPatient> patients = jdhDispatch.getServiceInfo().getPatients();
        int patientSize = patients.size();
        //查询履约单拿skuId，然后查商品域查询商品名称和所需耗材
        //查询订单信息
        String orderUserPhone = "";
        String orderUserName = "";

        if (StringUtils.isNotBlank(jdhDispatch.getServiceInfo().getSourceVoucherId())) {
            JdOrder jdOrder = jdOrderApplication.queryJdOrderAndItemExt(Long.valueOf(jdhDispatch.getServiceInfo().getSourceVoucherId()));
            log.info("DispatchEventSubscriber -> dispatchFailAlarm, jdOrder={}",JSON.toJSONString(jdOrder));
            JdOrderExt jdOrderExtDetail = null;
            if (Objects.nonNull(jdOrder) && CollectionUtils.isNotEmpty(jdOrder.getJdOrderExtList())) {
                jdOrderExtDetail = jdOrder.getJdOrderExtList().stream().filter(ext -> Objects.equals(JdOrderExtTypeEnum.APPOINTMENT_INFO.getExtType(), ext.getExtType())).findFirst().get();
            }
            if(Objects.nonNull(jdOrderExtDetail) && StringUtils.isNotBlank(jdOrderExtDetail.getExtContext())) {
                OrderAppointmentInfoValueObject orderAppointmentInfo = JsonUtil.parseObject(jdOrderExtDetail.getExtContext(),OrderAppointmentInfoValueObject.class);
                orderUserPhone = Objects.nonNull(orderAppointmentInfo) && Objects.nonNull(orderAppointmentInfo.getAddressInfo()) ? orderAppointmentInfo.getAddressInfo().getMobile() : "暂无电话";
                orderUserName = Objects.nonNull(orderAppointmentInfo) && Objects.nonNull(orderAppointmentInfo.getAddressInfo()) ? nameMaskSuffix(orderAppointmentInfo.getAddressInfo().getName()) : "暂无姓名";
            }
        }

        //查询项目及耗材信息
        //获取服务项目ID
        Set<Long> itemIds = jdhDispatch.getServiceInfo().getPatients().stream().flatMap(dispatchAppointmentPatientDto -> dispatchAppointmentPatientDto.getServiceItems().stream()).map(ServiceItem::getItemId).collect(Collectors.toSet());
        //查询服务项目列表
        List<ServiceItemDto> serviceItemDtos = productServiceItemApplication.queryServiceItemList(ServiceItemQuery.builder().itemIds(itemIds).build());
        //遍历获取项目、耗材名称
        Set<String> itemNameList = new HashSet<>();
        List<String> materialPackageNameList = new ArrayList<>();
        serviceItemDtos.forEach(serviceItemDto -> {
            //项目名称列表add
            itemNameList.add(serviceItemDto.getItemName());
            List<String> materialNames = serviceItemDto.getMaterialList().stream().map(ItemMaterialPackageRelCmd::getMaterialPackageName).distinct().collect(Collectors.toList());
            materialPackageNameList.addAll(materialNames);
        });
        //根据耗材名称 ： 数量 分组
        Map<String, Long> materialNameMap = materialPackageNameList.stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        //组装每个耗材对应数量字符串列表，例如： 耗材A×2、耗材B×3
        List<String> materialNameList = materialNameMap.entrySet().stream()
                .map(entry -> entry.getKey() + "×" + entry.getValue())
                .collect(Collectors.toList());
        String materialPackageAggregatedDesc = Joiner.on("、").join(materialNameList);
        String serviceItemAggregatedDesc = Joiner.on("、").join(itemNameList);


        JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(jdhDispatch.getPromiseId()).build());


        String format = String.format("护士到家订单%s-%s派单失败，需人工介入，请尽快处理。（履约单号：%s，订单号：%s，预约人姓名：%s，预约人电话：%s，预约上门时间：%s，上门地址：%s，项目名称：%s，所需耗材：%s， 服务人数：%s）",
                jdhPromise.getSourceVoucherId(),jdhDispatch.getPromiseId(),jdhDispatch.getPromiseId(), jdhDispatch.getServiceInfo().getSourceVoucherId(), orderUserName, orderUserPhone,
                jdhDispatch.getServiceInfo().getAppointmentTime().formatAppointmentStartTime(), jdhDispatch.getServiceLocation().getServiceLocationDetail(),
                serviceItemAggregatedDesc, materialPackageAggregatedDesc, patientSize);

        dongDongRobotRpc.sendDongDongRobotMessage(format,jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));

    }

    /**
     * 姓名脱敏
     * 将 name 字符串中除了第一个字符以外的所有字符替换为 "**"
     * @return
     */
    public String nameMaskSuffix(String name){
        if (StringUtils.isBlank(name) || name.length() == 1) {
            return name;
        }
        StringBuilder maskedName = new StringBuilder();
        maskedName.append(name.charAt(0));
        for (int i = 1; i < name.length(); i++) {
            maskedName.append("*");
        }
        return maskedName.toString();
    }


}