package com.jdh.o2oservice.application.angelpromise.service;

import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecord;
import com.jdh.o2oservice.export.angelpromise.cmd.SubmitAngelServiceRecordCmd;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordFlowQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordGroupQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;

import java.util.List;

/**
 * 护理单
 */
public interface AngelServiceRecordApplication {

    /**
     * 提交护理单
     * @param cmd
     * @return
     */
    SubmitAngelServiceRecordDto submitAngelServiceRecord(SubmitAngelServiceRecordCmd cmd);

    /**
     * 查询服务者服务项详情
     * @param query
     * @return
     */
    AngelServiceRecordDto queryAngelServiceRecordFlow(AngelServiceRecordFlowQuery query);

    /**
     * 校验是否开启了护理单配置
     * @param cmd
     * @return
     */
    Boolean checkAngelServiceRecordConfig(AngelServiceRecordQuery cmd);

    /**
     * 校验护理单是否已完成
     * @param cmd
     * @return
     */
    Boolean checkAngelServiceRecordFinish(AngelServiceRecordQuery cmd);

    /**
     * 通过taskId查询护理单相关pin
     * @param cmd
     * @return
     */
    Boolean queryAngelTaskAuthorityByTaskId(AngelServiceRecordFlowQuery cmd);

    /**
     * 运营端：通过pin查询是否有查看护理单权限
     * @param cmd
     * @return
     */
    Boolean queryBindAccountInfoManByPin(AngelServiceRecordFlowQuery cmd);

    /**
     * 查询节点配置数据
     * @param serviceItemIdList
     * @param businessMode
     * @param serviceType
     * @return
     */
    List<AngelServiceRecordQuestionGroupDto> queryQuestionGroupConfigList(List<Long> serviceItemIdList, String businessMode, String serviceType);


    /**
     * 查询被服务人医嘱信息
     *
     * @param angelWorkQuery
     * @param promiseDto
     * @return
     */
    PromisePatientPicDto queryPatientDoctorAdvice(AngelWorkQuery angelWorkQuery, PromiseDto promiseDto);

    /**
     * 查询节点下的题及答案
     *
     * @param groupQuery
     * @return
     */
    AngelServiceRecordGroupQuestionDto queryRecordGroupQuestion(AngelServiceRecordGroupQuery groupQuery);

    /**
     * 查询护理单
     * @param taskId
     * @return
     */
    AngelServiceRecord queryAngelServiceRecordByTaskId(Long taskId);
}
