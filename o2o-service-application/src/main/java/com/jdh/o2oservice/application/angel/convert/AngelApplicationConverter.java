package com.jdh.o2oservice.application.angel.convert;

import cn.hutool.core.date.DateUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.model.CpsSkuConfig;
import com.jdh.o2oservice.core.domain.angel.bo.AngelExtBo;
import com.jdh.o2oservice.core.domain.angel.bo.JdUnionGoodsListBo;
import com.jdh.o2oservice.core.domain.angel.context.*;
import com.jdh.o2oservice.core.domain.angel.enums.AngelAuditProcessStatusEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelExtendKeyEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelTakeOrderStatusEnum;
import com.jdh.o2oservice.core.domain.angel.enums.NethpAngelQualificationStatusEnum;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpBaseDoctorInfoClientBo;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpDoctorPracticeOfficeBo;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpDoctorqualificationsBo;
import com.jdh.o2oservice.base.model.CredentialNumber;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.UserName;
import com.jdh.o2oservice.export.angel.cmd.AngelBindStationCmd;
import com.jdh.o2oservice.export.angel.cmd.AngelBindStationMasterCmd;
import com.jdh.o2oservice.export.angel.cmd.SubmitAngelCmd;
import com.jdh.o2oservice.export.angel.dto.*;
import com.jdh.o2oservice.export.angel.query.GenerateClickUrlQuery;
import com.jdh.o2oservice.export.angel.query.JdUnionGoodsListQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: AngelApplicationConverter
 * @Author: zhangxiaojie17
 * @Date: 2024/4/18
**/
@Mapper
public interface AngelApplicationConverter {

    AngelApplicationConverter INS = Mappers.getMapper(AngelApplicationConverter.class);

    default AngelSubmitContext cmd2SubmitContext(SubmitAngelCmd submitAngelCmd) {
        AngelSubmitContext context = new AngelSubmitContext();
        JdhAngel jdhAngel = cmd2JdhAngel(submitAngelCmd);
        context.setJdhAngel(jdhAngel);

        return context;
    }

    default AngelBindStationMasterContext cmd2AngelBindStationMasterContext(AngelBindStationMasterCmd angelBindStationMasterCmd) {
        AngelBindStationMasterContext angelBindStationMasterContext = new AngelBindStationMasterContext();
        JdhAngelBindStationMasterEntity jdhAngelBindStationMasterEntity = cmd2JdhAngelBindStationMasterEntity(angelBindStationMasterCmd);
        angelBindStationMasterContext.setJdhAngelBindStationMasterEntity(jdhAngelBindStationMasterEntity);
        return angelBindStationMasterContext;
    }

    default AngelBindStationContext cmd2AngelBindStationContext(AngelBindStationCmd angelBindStationCmd) {
        AngelBindStationContext angelBindStationContext = new AngelBindStationContext();
        JdhAngelBindStationEntity jdhAngelBindStationEntity = cmd2JdhAngelBindStationEntity(angelBindStationCmd);
        angelBindStationContext.setJdhAngelBindStationEntity(jdhAngelBindStationEntity);
        return angelBindStationContext;
    }

    JdhAngelBindStationEntity cmd2JdhAngelBindStationEntity(AngelBindStationCmd angelBindStationCmd);

    JdhAngelBindStationMasterEntity cmd2JdhAngelBindStationMasterEntity(AngelBindStationMasterCmd angelBindStationMasterCmd);

    JdhAngel cmd2JdhAngel(SubmitAngelCmd submitAngelCmd);

    @Mapping(target = "professionTitleCodeList", source = "jdhAngelProfessionRelList", qualifiedByName = "convertProfessionTitleCodeList")
    JdhAngelDto convert2JdhAngelDto(JdhAngel jdhAngel);

    List<AngelSkillDictDto> convert2AngelSkillDictDto(List<JdhAngelSkillDict> jdhAngelSkillDicts);

    List<AngelSkillDictDto> convertRel2AngelSkillDictDto(List<JdhAngelSkillRel> jdhAngelSkillRelList);

    List<JdhAngelSkillRelDto> convertAngelSkillRelDto(List<JdhAngelSkillRel> jdhAngelSkillRelList);

    JdhAngelDetailDto convert2JdhAngelDetailDto(JdhAngel jdhAngel);

    @Mapping(target = "qualificationUrlList", expression = "java(org.apache.commons.lang3.StringUtils.isBlank(qualificationRel.getQualificationUrl()) ? null : java.util.Arrays.stream(qualificationRel.getQualificationUrl().split(\",\")).collect(java.util.stream.Collectors.toList()))")
    JdhAngelProfessionQualificationRelDto convert2JdhAngelProfessionQualificationRelDto(JdhAngelProfessionQualificationRel qualificationRel);

    default List<JdhAngelDto> convert2AngelDtoForPage(Boolean nameMask, List<JdhAngel> jdhAngelList, Map<Long, AngelStationDto> jdhStationDtoMap) {
        if (CollectionUtils.isEmpty(jdhAngelList)) {
            return new ArrayList<>();
        }

        List<JdhAngelDto> list = new ArrayList<>();
        for (JdhAngel jdhAngel : jdhAngelList) {
            JdhAngelDto jdhAngelDto = INS.convert2JdhAngelDto(jdhAngel);
            //脱敏
            if (Objects.equals(true, nameMask)) {
                jdhAngelDto.setAngelName(new UserName(jdhAngelDto.getAngelName()).mask());
                jdhAngelDto.setPhone(new PhoneNumber(jdhAngelDto.getPhone()).mask());
                jdhAngelDto.setIdCard(new CredentialNumber(jdhAngelDto.getIdCardType(), jdhAngelDto.getIdCard()).mask());
                jdhAngelDto.setEmergencyContactName(new UserName(jdhAngelDto.getEmergencyContactName()).mask());
                jdhAngelDto.setEmergencyContactPhone(new PhoneNumber(jdhAngelDto.getEmergencyContactPhone()).mask());
            }
            //敏感数据加密
            jdhAngelDto.setAngelNameEncrypt(new UserName(jdhAngelDto.getAngelName()).encrypt());
            jdhAngelDto.setPhoneEncrypt(new PhoneNumber(jdhAngelDto.getPhone()).encrypt());
            jdhAngelDto.setIdCardEncrypt(new CredentialNumber(jdhAngelDto.getIdCardType(), jdhAngelDto.getIdCard()).encrypt());

            List<JdhAngelProfessionRel> jdhAngelProfessionRelList = jdhAngel.getJdhAngelProfessionRelList();

            //职业名称列表
            List<String> professionNamesStrList = new ArrayList<>();
            //职业职级名称列表
            List<String> professionTitleNamesStrList = new ArrayList<>();
            //职业机构名称列表
            List<String> professionInstitutionNamesStrList = new ArrayList<>();
            //职级列表
            List<String> professionTitleCodeList = new ArrayList<>();

            for (JdhAngelProfessionRel jdhAngelProfessionRel : jdhAngelProfessionRelList) {
                //职业名称
                String professionName = jdhAngelProfessionRel.getProfessionName();
                professionNamesStrList.add(professionName);

                /*JdhAngelProfessionTitleDict jdhAngelProfessionTitleDict = jdhAngelProfessionRel.getJdhAngelProfessionTitleDict();
                //职业职级名称
                String professionTitleName = jdhAngelProfessionTitleDict.getProfessionTitleName();*/
                //职业职级名称
                professionTitleNamesStrList.add(jdhAngelProfessionRel.getProfessionTitleName());
                //职业机构名称
                String professionInstitutionName = jdhAngelProfessionRel.getInstitutionName();
                professionInstitutionNamesStrList.add(professionInstitutionName);
                //职级code
                professionTitleCodeList.add(jdhAngelProfessionRel.getProfessionTitleCode());
            }
            jdhAngelDto.setAngelProfessionNames(StringUtils.join(professionNamesStrList, ","));
            jdhAngelDto.setAngelProfessionTitleNames(StringUtils.join(professionTitleNamesStrList, ","));
            jdhAngelDto.setAngelProfessionInstitutionNames(StringUtils.join(professionInstitutionNamesStrList, ","));
            jdhAngelDto.setProfessionTitleCodeList(professionTitleCodeList);

            //服务者绑定的服务站信息
            if (Objects.nonNull(jdhAngel.getStationId())) {
                AngelStationDto jdhStationDto = jdhStationDtoMap.get(jdhAngel.getStationId());
                jdhAngelDto.setJdhStationDto(jdhStationDto);
            }

            //服务者技能信息
            List<JdhAngelSkillRel> jdhAngelSkillRelList = jdhAngel.getJdhAngelSkillRelList();
            if (CollectionUtils.isNotEmpty(jdhAngelSkillRelList)) {
                //技能列表
                List<String> skillNamesStrList = new ArrayList<>();
                for (JdhAngelSkillRel jdhAngelSkillRel : jdhAngelSkillRelList) {
                    skillNamesStrList.add(jdhAngelSkillRel.getAngelSkillName());
                }
                jdhAngelDto.setSkillNames(StringUtils.join(skillNamesStrList, ","));
            }

            list.add(jdhAngelDto);
        }

        return list;
    }

    default JdhAngel cmd2JdhAngel(NethpBaseDoctorInfoClientBo nethpDoctor, NethpDoctorqualificationsBo nethpDoctorqualificationsBo, NethpDoctorPracticeOfficeBo nethpDoctorPracticeOfficeBo) {
        JdhAngel jdhAngel = new JdhAngel();
        //互医医生id
        jdhAngel.setNethpDocId(nethpDoctor.getPlatformId());
        //服务者pin
        jdhAngel.setAngelPin(nethpDoctor.getPin());
        //服务者姓名
        jdhAngel.setAngelName(nethpDoctor.getName());
        //服务者头图
        jdhAngel.setHeadImg(nethpDoctor.getImg());
        //性别 1-男 2-女
        jdhAngel.setGender(Integer.valueOf(nethpDoctor.getSex()));
        //证件号
        jdhAngel.setIdCard(nethpDoctor.getIdCard());
        //证件类型 1-身份证 2-护照 默认1
        jdhAngel.setIdCardType(1);
        //证件号索引
        jdhAngel.setIdCardIndex(nethpDoctor.getIdCard());
        //扩展信息
        AngelExtBo angeExtBo = new AngelExtBo();
        angeExtBo.setGreatDirection(nethpDoctor.getAdept());
        jdhAngel.setAngeExtBo(angeExtBo);
        //身份证图片url
        List<String> idCardImgList = nethpDoctor.getIdCardImgList();
        if (CollectionUtils.isNotEmpty(idCardImgList) && idCardImgList.size() >= 2) {
            //身份证人像图
            String idCardImgOppositeUrl = idCardImgList.get(0);
            //身份证国徽图
            String idCardImgFrontUrl = idCardImgList.get(1);
            jdhAngel.setIdCardImgOppositeUrl(idCardImgOppositeUrl);
            jdhAngel.setIdCardImgFrontUrl(idCardImgFrontUrl);
        }
        //电话
        jdhAngel.setPhone(nethpDoctor.getPhone());
        //电话索引
        jdhAngel.setPhoneIndex(nethpDoctor.getPhone());
        //紧急联系人姓名-默认空
        //紧急联系人电话-默认空
        //人员标签 0兼职 1全职 默认空
        //接单状态 0关闭 1开启 默认0
        jdhAngel.setTakeOrderStatus(AngelTakeOrderStatusEnum.CLOSE.getCode());
        //服务站点id 默认空
        //站长erp 默认空
        Integer qualificationStatus = Integer.valueOf(nethpDoctor.getQualificationStatus());
        //审核状态-待审核
        if (NethpAngelQualificationStatusEnum.JBXXRZ.getCode().equals(qualificationStatus)) {
            jdhAngel.setAuditProcessStatus(AngelAuditProcessStatusEnum.AUDIT_GENERAL_REGISTER.getCode());
        } else if (NethpAngelQualificationStatusEnum.AUDIT_WAIT.getCode().equals(qualificationStatus)) {
            jdhAngel.setAuditProcessStatus(AngelAuditProcessStatusEnum.AUDIT_WAIT.getCode());
        } else if (NethpAngelQualificationStatusEnum.AUDIT_REJECT.getCode().equals(qualificationStatus)) {
            //审核状态-审核驳回
            jdhAngel.setAuditProcessStatus(AngelAuditProcessStatusEnum.AUDIT_REJECT.getCode());
        } else if (NethpAngelQualificationStatusEnum.AUDIT_PASS_2.getCode().equals(qualificationStatus)
                || NethpAngelQualificationStatusEnum.AUDIT_PASS_4.getCode().equals(qualificationStatus)
                || NethpAngelQualificationStatusEnum.AUDIT_PASS_5.getCode().equals(qualificationStatus)) {
            //审核状态-审核通过
            jdhAngel.setAuditProcessStatus(AngelAuditProcessStatusEnum.AUDIT_PASS.getCode());
        }
        //审核时间
        if (Objects.nonNull(nethpDoctor.getQualificationPassTime())) {
            Date qualificationPassTime = nethpDoctor.getQualificationPassTime();
            jdhAngel.setAuditProcessDate(DateUtil.format(qualificationPassTime, CommonConstant.YMDHMS));
        }
        //个人简介
        jdhAngel.setIntroduction(nethpDoctor.getIntroduction());

        //服务者职业信息
        List<JdhAngelProfessionRel> jdhAngelProfessionRelList = new ArrayList<>();
        JdhAngelProfessionRel jdhAngelProfessionRel = new JdhAngelProfessionRel();
        //职业code
        jdhAngelProfessionRel.setProfessionCode(String.valueOf(nethpDoctor.getProfessionType()));
        //职业名称
        jdhAngelProfessionRel.setProfessionName(nethpDoctor.getProfessionName());
        //职级code
        jdhAngelProfessionRel.setProfessionTitleCode(String.valueOf(nethpDoctor.getTitleId()));
        //职级名称
        jdhAngelProfessionRel.setProfessionTitleName(nethpDoctor.getTitleName());
        //所属机构code
        jdhAngelProfessionRel.setInstitutionCode(String.valueOf(nethpDoctor.getHospitalId()));
        //所属机构名称
        jdhAngelProfessionRel.setInstitutionName(nethpDoctor.getHospitalName());

        //职业资质信息
        List<JdhAngelProfessionQualificationRel> jdhAngelProfessionQualificationRelList = new ArrayList<>();
        JdhAngelProfessionQualificationRel jdhAngelProfessionQualificationRel = new JdhAngelProfessionQualificationRel();
        //职业code
        jdhAngelProfessionQualificationRel.setProfessionCode(String.valueOf(nethpDoctor.getProfessionType()));
        //资质code
        jdhAngelProfessionQualificationRel.setQualificationCode("执业证书");
        //资质名称
        jdhAngelProfessionQualificationRel.setQualificationName("执业证书");
        //资质证图片链接
        List<String> practiceImgList = nethpDoctor.getPracticeImgList();
        if (CollectionUtils.isNotEmpty(practiceImgList)) {
            String qualificationUrl = StringUtils.join(practiceImgList, ",");
            jdhAngelProfessionQualificationRel.setQualificationUrl(qualificationUrl);
        }
        jdhAngelProfessionQualificationRelList.add(jdhAngelProfessionQualificationRel);

        jdhAngelProfessionRel.setJdhAngelProfessionQualificationRelList(jdhAngelProfessionQualificationRelList);

        jdhAngelProfessionRelList.add(jdhAngelProfessionRel);
        jdhAngel.setJdhAngelProfessionRelList(jdhAngelProfessionRelList);

        jdhAngel.setOperator("nethp_sync");

        if (Objects.equals(nethpDoctor.getInviteCodeType(),4) && StringUtils.isNotBlank(nethpDoctor.getInviteCode())) {
            jdhAngel.refreshExtend(AngelExtendKeyEnum.INVITE_ANGEL_ID, nethpDoctor.getInviteCode());
        }

        jdhAngel.setTechnicalTitle(nethpDoctor.getTitleName());
        if(Objects.nonNull(nethpDoctorqualificationsBo)) {
            jdhAngel.setCertificateNo(nethpDoctorqualificationsBo.getPracticeCode());
            jdhAngel.setCertificateNoIndex(nethpDoctorqualificationsBo.getPracticeCode());
            jdhAngel.setGrade(nethpDoctorqualificationsBo.getTitleName());
        }

        if(Objects.nonNull(nethpDoctorPracticeOfficeBo)) {
            jdhAngel.setCertificateIssuingAuthority(nethpDoctorPracticeOfficeBo.getPracticeOffice());
            jdhAngel.setSpeciality(nethpDoctorPracticeOfficeBo.getMajor());
        }
        return jdhAngel;
    }


    public static void main(String[] args) {
        //职业名称列表
        String professionNamesStr = "";
        ArrayList<String> objects = new ArrayList<>();
        for (int i = 0; i < 3; i++) {

        }
        String join = StringUtils.join(objects, ",");

        System.out.println(join);
    }

    List<JdhDepartmentDto> toJdhDepartmentDto(List<JdhDepartment> jdhDepartments);

    JdUnionGoodsDto convert2JdUnionGoodsDto(JdUnionGoodsListBo bo);
    List<JdUnionGoodsDto> convert2JdUnionGoodsDtoList(List<JdUnionGoodsListBo> boList);

    JdUnionGoodsListContext convert2JdUnionGoodsListContext(JdUnionGoodsListQuery query);
    GenerateClickUrlContext convert2GenerateClickUrlContext(GenerateClickUrlQuery query);

    CpsSkuConfigDto convert2CpsSkuConfigDto(CpsSkuConfig cpsSkuConfig);
    List<CpsSkuConfigDto> convert2CpsSkuConfigDtoList(List<CpsSkuConfig> cpsSkuConfigList);

    /**
     * 转换
     *
     * @param jdhAngelProfessionRelList 源
     * @return {@link List}
     */
    @Named("convertProfessionTitleCodeList")
    default List<String> convertProfessionTitleCodeList(List<JdhAngelProfessionRel> jdhAngelProfessionRelList){
        if(CollectionUtils.isEmpty(jdhAngelProfessionRelList)){
            return new ArrayList<>();
        }
        return jdhAngelProfessionRelList.stream().map(JdhAngelProfessionRel::getProfessionTitleCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }
}
