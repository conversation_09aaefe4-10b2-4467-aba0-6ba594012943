package com.jdh.o2oservice.application.via.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelEcologyApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.application.support.service.UserFeedbackApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.application.via.handler.floor.Floor;
import com.jdh.o2oservice.application.via.handler.homeTest.AngelTestOrderDetailHandler_V2;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.EnvTypeEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkTypeEnum;
import com.jdh.o2oservice.core.domain.product.enums.ProductAggregateCodeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseHistoryRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.*;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.UserName;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.PromisegoRequestAddress;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.PromisegoRequestAppointmentTime;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.UserPromisegoBo;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.UserPromisegoRequestBo;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.*;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.core.domain.trade.enums.AvailableAppointmentTimeSceneEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ModifyAppointmentTimeSceneEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeAggregateEnum;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angel.query.JdhAngelEcologyQueryRequest;
import com.jdh.o2oservice.export.angelpromise.dto.AngelTrackDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelTrackQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderItemDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderServiceFeeInfoDTO;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import com.jdh.o2oservice.export.user.dto.UserFeedbackAggregationDTO;
import com.jdh.o2oservice.export.user.query.UserFeedbackRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 * XfylHomeTest 履约详情页
 * <AUTHOR>
 * @date 2024/04/16
 */
@Slf4j
@Service
public class XfylHomeTestOrderDetailHandler extends AbstractViaDataFillHandler implements MapAutowiredKey {

    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    /**
     * promiseHistoryRepository
     */
    @Autowired
    private PromiseHistoryRepository promiseHistoryRepository;

    /**
     * angelApplication
     */
    @Autowired
    private AngelApplication angelApplication;

    /**
     * angelPromiseApplication
     */
    @Autowired
    private AngelPromiseApplication angelPromiseApplication;

    /**
     * medicalPromiseApplication
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * tradeApplication
     */
    @Autowired
    private TradeApplication tradeApplication;

    /**
     * productApplication
     */
    @Autowired
    private ProductApplication productApplication;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * angelWorkApplication
     */
    @Autowired
    private AngelWorkApplication angelWorkApplication;

    /**
     * userFeedbackApplication
     */
    @Autowired
    private UserFeedbackApplication userFeedbackApplication;

    /**
     * promiseGoRpcService
     */
    @Autowired
    private PromiseGoRpcService promiseGoRpcService;

    /**
     * verticalBusinessRepository
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * duccConfig
     */
    @Autowired
    private DuccConfig duccConfig;

    @Resource
    private CallRecordApplication callRecordApplication;

    @Resource
    private AngelEcologyApplication angelEcologyApplication;

    private static List<Integer> MEDICAL_WAIT_TOLAB_STATUS_LIST = Arrays.asList(JdhPromiseStatusEnum.COMPLETE.getStatus(),JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus());

    @Resource
    private AngelTestOrderDetailHandler_V2 angelTestOrderDetailHandler_V2;

    /**
     * checkParam
     *
     * @param ctx ctx
     */
    private void checkParam(FillViaConfigDataContext ctx){
        //场景
        AssertUtils.hasText(ctx.getScene(), SupportErrorCode.VIA_CONFIG_NOT_EXIT);
        AssertUtils.hasText(ctx.getOrderId(), SupportErrorCode.VIA_ORDER_ID_NOT_EXIT);
    }

    /**
     * 命中状态映射
     * {
     *         "statusExpression": "!include(seq.list(1,7,8,9),orderStatus) && include(seq.list(1,2),promiseStatus)",
     *         "mainTitle": "待护士接单",
     *         "title":"正在通知护士接单，预计 <span>{angelWorkReceiveTime}</span> 前接单",
     *         "titleDynamicField":["angelWorkReceiveTime"]
     *         "dynamicCursorMinutes": 30,
     *         "mainIcon": "https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     *         "promiseTitle":"上门信息",
     *         "promiseTitleDynamicField":[]
     *         "stepGuideFinishCodeList":[],
     *         "stepGuideFinishIcon":"https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     *         "stepGuideProcessCodeList":["angelReceiveWork"],
     *         "stepGuideProcessIcon":"https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     *         "stepGuideWaitCodeList":["service","submitTest","testing","reported"],
     *         "stepGuideWaitIcon":"https://storage.360buyimg.com/jdhhealth-public/laputa/e4cd9031-86eb-4255-9006-a7a84a07a409.png",
     *         "footerButtonCodeList": ["refundBtn","contactCustomerBtn","rePurchaseBtn"],
     *         "hiddenFloorCode":["promiseAngelInfo","promideCodeInfo"],
     *         "hiddenPatientFieldList":[],
     *         "hiddenAngelInfoList":[],
     *         "hiddenAngelBtnList":[]
     *     }
     *
     * @param statusMapping 订单状态映射
     * @param order              order
     * @param jdhPromise         jdhPromise
     * @param medicalPromiseList medicalPromiseList
     * @return {@link ViaStatusMapping}
     */
    private ViaStatusMapping hitStatusMapping(JdOrderDTO order,JdhPromise jdhPromise,List<MedicalPromiseDTO> medicalPromiseList,List<ViaStatusMapping> statusMapping){
        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus",Objects.isNull(order) ?  null : order.getOrderStatus());
        param.put("promiseStatus",Objects.isNull(jdhPromise) ?  null : jdhPromise.getPromiseStatus());
        param.put("medPromiseStatusList",CollUtil.isEmpty(medicalPromiseList) ? null : medicalPromiseList.stream().map(MedicalPromiseDTO::getStatus).collect(Collectors.toList()));

        for (ViaStatusMapping viaStatusMapping : statusMapping) {
            if ((boolean) AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(),Boolean.TRUE).execute(param)) {
                return viaStatusMapping;
            }
        }
        throw new SystemException(SupportErrorCode.VIA_STATUS_MAPPING_ERROR);
    }

    /**
     * 获取动态字段值
     *
     * @param dynamicFieldList     动态字段列表
     * @param dynamicCursorMinutes 动态分钟数
     * @param jdOrder              JD订单
     * @param jdhPromise           jdhPromise
     * @param medicalPromiseList   medicalPromiseList
     * @return {@link Map}<{@link String},{@link String}>
     */
    private Map<String,String> getDynamicFieldValue(JdOrderDTO jdOrder,
                                                    JdhPromise jdhPromise,
                                                    List<JdhPromiseHistory> promiseHistories,
                                                    List<MedicalPromiseDTO> medicalPromiseList,
                                                    List<String> dynamicFieldList,
                                                    Integer dynamicCursorMinutes,JdhSkuDto jdhSkuDto){
        if(CollUtil.isEmpty(dynamicFieldList)){
            return null;
        }
        Map<String,String> result = new HashMap<>(dynamicFieldList.size());
        Date now = new Date();
        Date tomorrow = DateUtil.tomorrow().toJdkDate();

        for (String field : dynamicFieldList) {
            // 退款金额
            if(ViaDynamicFieldEnum.REFUND_AMOUNT.getField().equals(field)){
                result.put(ViaDynamicFieldEnum.REFUND_AMOUNT.getField(),
                        Objects.isNull(jdOrder.getRefundAmount()) ? "" : jdOrder.getRefundAmount().toPlainString());
            }

            // 预估服务者接单时间  提交派单时间 + 30分钟
            if(ViaDynamicFieldEnum.ANGEL_WORK_RECEIVE_TIME.getField().equals(field)){
                log.info("getDynamicFieldValue promiseHistories={}", JSON.toJSONString(promiseHistories));
                log.info("getDynamicFieldValue jdhPromise={}", JSON.toJSONString(jdhPromise));
                log.info("getDynamicFieldValue dynamicCursorMinutes={}", JSON.toJSONString(dynamicCursorMinutes));
                String waitPickUpTime = getWaitPickUpTime(promiseHistories, jdhPromise, dynamicCursorMinutes);
                log.info("getDynamicFieldValue waitPickUpTime={}", JSON.toJSONString(waitPickUpTime));
                result.put(ViaDynamicFieldEnum.ANGEL_WORK_RECEIVE_TIME.getField(), waitPickUpTime);
            }

            //预估服务者上门日期
            if(ViaDynamicFieldEnum.ANGEL_HOME_DATE.getField().equals(field)){
                //如果是今天，展示"今天"，否则yyyy-MM-dd 日期
                LocalDateTime appointmentStartTime = jdhPromise.getAppointmentTime().getAppointmentStartTime();
                if(DateUtil.isSameDay(now,TimeUtils.localDateTimeToDate(appointmentStartTime))){
                    result.put(ViaDynamicFieldEnum.ANGEL_HOME_DATE.getField(),"今天");
                }else if(DateUtil.isSameDay(tomorrow,TimeUtils.localDateTimeToDate(appointmentStartTime))){
                    result.put(ViaDynamicFieldEnum.ANGEL_HOME_DATE.getField(),"明天");
                }else{
                    result.put(ViaDynamicFieldEnum.ANGEL_HOME_DATE.getField(),TimeUtils.localDateTimeToStr(appointmentStartTime,TimeFormat.SHORT_PATTERN_LINE));
                }
            }

            //预估服务者上门开始时间
            //立即预约 1 - 开始时间  预约开始时间 + 1h
            //非立即预约 1 - 开始时间  预约开始时间
            if(ViaDynamicFieldEnum.ANGEL_HOME_START_TIME.getField().equals(field)){
                PromiseAppointmentTime appointmentTime = jdhPromise.getAppointmentTime();
//                Boolean isImmediately = appointmentTime.getIsImmediately();
//                if(isImmediately){
//                    if(ServiceTypeEnum.TEST.getServiceType().equals(jdhPromise.getServiceType())) {
//                        Date startTime = TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime());
//                        result.put(ViaDynamicFieldEnum.ANGEL_HOME_START_TIME.getField(),TimeUtils.dateTimeToStr(startTime,TimeFormat.DATE_PATTERN_HM_SIMPLE));
//                    }else {
//                        DateTime startTime = DateUtil.offset(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()), DateField.MINUTE, dynamicCursorMinutes);
//                        result.put(ViaDynamicFieldEnum.ANGEL_HOME_START_TIME.getField(),TimeUtils.dateTimeToStr(startTime,TimeFormat.DATE_PATTERN_HM_SIMPLE));
//                    }
//                }else{
//                    result.put(ViaDynamicFieldEnum.ANGEL_HOME_START_TIME.getField(),TimeUtils.localDateTimeToStr(appointmentTime.getAppointmentStartTime(),TimeFormat.DATE_PATTERN_HM_SIMPLE));
//                }
                result.put(ViaDynamicFieldEnum.ANGEL_HOME_START_TIME.getField(),TimeUtils.localDateTimeToStr(appointmentTime.getAppointmentStartTime(),TimeFormat.DATE_PATTERN_HM_SIMPLE));
            }

            //预估服务者上门结束时间
            //立即预约   结束时间  预约结束时间 + 1h
            //非理解预约 结束时间  预约结束时间
            if(ViaDynamicFieldEnum.ANGEL_HOME_END_TIME.getField().equals(field)){
                PromiseAppointmentTime appointmentTime = jdhPromise.getAppointmentTime();
                Boolean isImmediately = appointmentTime.getIsImmediately();
                if(isImmediately){
                    if(ServiceTypeEnum.TEST.getServiceType().equals(jdhPromise.getServiceType())) {
                        DateTime endTime = DateUtil.offset(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()), DateField.MINUTE, dynamicCursorMinutes);
                        result.put(ViaDynamicFieldEnum.ANGEL_HOME_END_TIME.getField(),TimeUtils.dateTimeToStr(endTime,TimeFormat.DATE_PATTERN_HM_SIMPLE));
                    }else {
//                        DateTime endTime = DateUtil.offset(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentEndTime()), DateField.MINUTE, dynamicCursorMinutes);
//                        result.put(ViaDynamicFieldEnum.ANGEL_HOME_END_TIME.getField(),TimeUtils.dateTimeToStr(endTime,TimeFormat.DATE_PATTERN_HM_SIMPLE));
                        result.put(ViaDynamicFieldEnum.ANGEL_HOME_END_TIME.getField(),TimeUtils.localDateTimeToStr(appointmentTime.getAppointmentEndTime(),TimeFormat.DATE_PATTERN_HM_SIMPLE));
                    }
                }else{
                    result.put(ViaDynamicFieldEnum.ANGEL_HOME_END_TIME.getField(),TimeUtils.localDateTimeToStr(appointmentTime.getAppointmentEndTime(),TimeFormat.DATE_PATTERN_HM_SIMPLE));
                }
            }

            //预估服务者服务完成日期 如果是今天，展示"今天"，否则yyyy-MM-dd 日期
            if(ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_DATE.getField().equals(field)){
                Optional<JdhPromiseHistory> jdhPromiseHistory = promiseHistories.stream()
                        .filter(ele -> JdhPromiseStatusEnum.SERVICING.getStatus().equals(ele.getAfterStatus()))
                        .findFirst();
                Date servicingTime = jdhPromise.getUpdateTime();
                if(jdhPromiseHistory.isPresent()){
                    JdhPromiseHistory history = jdhPromiseHistory.get();
                    servicingTime = history.getCreateTime();
                }

                if(DateUtil.isSameDay(now,servicingTime)){
                    result.put(ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_DATE.getField(),"今天");
                }else if(DateUtil.isSameDay(tomorrow,servicingTime)){
                    result.put(ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_DATE.getField(),"明天");
                }else{
                    result.put(ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_DATE.getField(),TimeUtils.dateTimeToStr(servicingTime,TimeFormat.SHORT_PATTERN_LINE));
                }

            }

            //预估服务者服务完成时间  变更服务中时间 + （所有sku服务时长总和）× 服务人数
            if(ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_TIME.getField().equals(field)){
                Optional<JdhPromiseHistory> jdhPromiseHistory = promiseHistories.stream()
                        .filter(ele -> JdhPromiseStatusEnum.SERVICING.getStatus().equals(ele.getAfterStatus()))
                        .findFirst();
                Date servicingTime = jdhPromise.getUpdateTime();
                if(jdhPromiseHistory.isPresent()){
                    JdhPromiseHistory history = jdhPromiseHistory.get();
                    servicingTime = history.getCreateTime();
                }

                int totalServiceTime = jdhSkuDto.getServiceDuration();

                long promisePatientNums = jdhPromise.getPatients().size();

                DateTime completeTime = DateUtil.offset(servicingTime, DateField.MINUTE, (int) (totalServiceTime * promisePatientNums));

                result.put(ViaDynamicFieldEnum.ANGEL_SERVICE_COMPLETE_TIME.getField(),TimeUtils.dateTimeToStr(completeTime,TimeFormat.DATE_PATTERN_HM_SIMPLE));
            }

            //预估送检时间  服务变更完成时间 + 1小时
            if(ViaDynamicFieldEnum.SUBMIT_TEST_TIME.getField().equals(field)){
                Optional<JdhPromiseHistory> jdhPromiseHistory = promiseHistories.stream()
                        .filter(ele -> JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus().equals(ele.getAfterStatus()))
                        .findFirst();
                Date servicingCompleteTime = jdhPromise.getUpdateTime();
                if(jdhPromiseHistory.isPresent()){
                    JdhPromiseHistory history = jdhPromiseHistory.get();
                    servicingCompleteTime = history.getCreateTime();
                }

                DateTime submitTestTime = DateUtil.offset(servicingCompleteTime, DateField.MINUTE, dynamicCursorMinutes);
                result.put(ViaDynamicFieldEnum.SUBMIT_TEST_TIME.getField(),TimeUtils.dateTimeToStr(submitTestTime,TimeFormat.DATE_PATTERN_HM_SIMPLE));
            }

            //预估出报告时间
            // 取实验室检测单变更检测中时间 + 实验室检测项检测时长总和（多个检测单，取最长）
            if(ViaDynamicFieldEnum.REPORT_TIME_TIME.getField().equals(field)){
                Date reportedTime = null;
                for (MedicalPromiseDTO medicalPromiseDTO : medicalPromiseList) {
                    Date checkTime = medicalPromiseDTO.getCheckTime();
                    Integer testDuration = medicalPromiseDTO.getTestDuration();
                    if(Objects.nonNull(checkTime) && Objects.nonNull(testDuration)){
                        DateTime offset = DateUtil.offset(checkTime, DateField.MINUTE, testDuration);
                        if(Objects.isNull(reportedTime)){
                            reportedTime = offset;
                        }else{
                            reportedTime = reportedTime.getTime() > offset.getTime() ? reportedTime : offset;
                        }
                    }
                }
                result.put(ViaDynamicFieldEnum.REPORT_TIME_TIME.getField(),TimeUtils.dateTimeToStr(reportedTime,TimeFormat.DATE_PATTERN_HM_SIMPLE));

            }

        }
        log.info("XfylHomeTestOrderDetailHandler -> getDynamicFieldValue result:{}",JSON.toJSONString(result));
        return result;
    }

    /**
     * 处理摘要信息
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdOrder       JD订单
     * @param jdhPromise    promise
     *
     *              * {
     *              *     "floorCode": "promiseSummaryInfo",
     *              *     "floorName": "履约信息概要",
     *              *     "floorConfigList": [
     *              *         {
     *              *             "mainTitle": "待护士接单",
     *              *             "mainIcon":"https://img11.360buyimg.com/imagetools/jfs/t1/138462/3/30857/44483/64589a51F1d9b5d61/bb3348ce18928273.png",
     *              *             "title":""
     *              *        }
     *              *     ]
     *              * }
     *
     */
    private void handleSummaryInfo(ViaFloorInfo viaFloorInfo,
                                   ViaStatusMapping statusMapping,
                                   JdOrderDTO jdOrder,
                                   JdhPromise jdhPromise,
                                   List<JdhPromiseHistory> promiseHistories,
                                   List<MedicalPromiseDTO> medicalPromiseList,
                                   JdhSkuDto jdhSkuDto,
                                   FillViaConfigDataContext ctx){
        log.info("XfylHomeTestOrderDetailHandler handleSummaryInfo start statusMapping={}", JSON.toJSONString(statusMapping));
        List<ViaFloorConfig> floorConfig = new ArrayList<>();
        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
        viaFloorConfig.setMainTitle(StrUtil.format(statusMapping.getMainTitle(),getDynamicFieldValue(jdOrder,jdhPromise,promiseHistories,medicalPromiseList,statusMapping.getMainTitleDynamicField(),statusMapping.getDynamicCursorMinutes(),jdhSkuDto)));
        viaFloorConfig.setMainIcon(statusMapping.getMainIcon());


        String title = "";

        //promisego开关
        if (duccConfig.getPromisegoSwitch()){
            //如果开关打开，并且白名单为空 或者 白名单做了配置，则只对这些pin预测
            Set<String> promisegoWhitePin = duccConfig.getPromisegoWhitePin();
            if (CollectionUtils.isEmpty(promisegoWhitePin) ||
                    (CollectionUtils.isNotEmpty(promisegoWhitePin) && promisegoWhitePin.contains(jdOrder.getUserPin()))
            ){
                //promise不为空，进入调用promisego时效预测逻辑
                if(Objects.nonNull(jdhPromise)){
                    JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(jdhPromise.getVerticalCode());
                    //如果是骑手检测
                    if (StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(),jdhVerticalBusiness.getBusinessModeCode()) && StringUtils.isNotBlank(statusMapping.getAggregateStatus())){
                        String aggregateStatus = getProcessTrackMessage(statusMapping.getAggregateStatus(),jdhVerticalBusiness.getBusinessModeCode(),jdhPromise);
                        if (StringUtils.isNotBlank(aggregateStatus)) {
                            title = aggregateStatus;
                        }
                    }
                    //派单阶段 并且是 预约单
                    if (StringUtil.equals("dispatch",statusMapping.getAggregateStatus()) && !jdhPromise.getAppointmentTime().getIsImmediately()){
                        viaFloorConfig.setMainTitle("待送检员上门");
                        if (StringUtils.isBlank(title)){
                            Date now = new Date();
                            Date tomorrow = DateUtil.tomorrow().toJdkDate();
                            LocalDateTime appointmentStartTime = jdhPromise.getAppointmentTime().getAppointmentStartTime();
                            LocalDateTime appointmentEndTime = jdhPromise.getAppointmentTime().getAppointmentEndTime();
                            String startTime = DateUtil.format(appointmentStartTime, CommonConstant.HM);
                            String endTime = DateUtil.format(appointmentEndTime, CommonConstant.HM);

                            String prefix;
                            if (DateUtil.isSameDay(now, TimeUtils.localDateTimeToDate(appointmentStartTime))){
                                prefix = "";
                            }else if (DateUtil.isSameDay(tomorrow, TimeUtils.localDateTimeToDate(appointmentStartTime))){
                                prefix = "[明天]";
                            }else {
                                prefix = DateUtil.format(appointmentStartTime, CommonConstant.YMD3);
                            }
                            title = "送检员预计<span id='appointTitleId'>" + prefix + " " + startTime + "-" + endTime + "</span>上门";
                        }
                    }
                }
            }
        }
        log.info("XfylHomeTestOrderDetailHandler handleSummaryInfo final before title={}", title);
        title = StringUtils.isNotBlank(title) ? title : StrUtil.format(statusMapping.getTitle(),getDynamicFieldValue(jdOrder,jdhPromise,promiseHistories,medicalPromiseList,statusMapping.getTitleDynamicField(),statusMapping.getDynamicCursorMinutes(),jdhSkuDto));
        log.info("XfylHomeTestOrderDetailHandler handleSummaryInfo final after title={}", title);
        viaFloorConfig.setTitle(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), title));
        viaFloorConfig.setMainTitle(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), viaFloorConfig.getMainTitle()));
        floorConfig.add(viaFloorConfig);
        viaFloorInfo.setFloorConfigList(floorConfig);
        log.info("XfylHomeTestOrderDetailHandler handleSummaryInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 处理步骤指南信息
     * {
     * "floorCode": "promiseStepGuideInfo",
     * "floorName": "履约信息步骤条信息",
     * "floorConfigList": [
     * {
     * "title": "预约",
     * "description":"04/01 08:29",
     * "status":"process",
     * "icon":"https://img11.360buyimg.com/imagetools/jfs/t1/138462/3/30857/44483/64589a51F1d9b5d61/bb3348ce18928273.png",
     * "code":"appointment"
     * },
     * ]
     * }
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     */
    private void handleStepGuideInfo(ViaFloorInfo viaFloorInfo,ViaStatusMapping statusMapping){
        for (ViaFloorConfig viaFloorConfig : viaFloorInfo.getFloorConfigList()) {
            String stepCode = viaFloorConfig.getStepCode();
            //完成
            if (statusMapping.getStepGuideFinishCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.FINISH.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideFinishIcon());
            }
            //进行中
            if (statusMapping.getStepGuideProcessCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.PROCESS.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideProcessIcon());
            }
            //等待
            if (statusMapping.getStepGuideWaitCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.WAIT.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideWaitIcon());
            }
        }
        log.info("XfylHomeTestOrderDetailHandler handleStepGuideInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * {
     * "floorCode": "promiseAngelInfo",
     * "floorName": "履约服务者信息",
     * "floorConfigList": [
     * {
     * "fieldKey":"angelHeadImg",
     * "fieldValue":"xxxx"
     * },
     * {
     * "fieldKey":"angelName",
     * "fieldValue":"王护士"
     * },
     * {
     * "btnList":[
     * {
     * "code": "viewHomeBtn",
     * "name": "查看主页",
     * "style": "primary",
     * "action": {
     * "url": "https://laputa.jd.com/jdh-healthcare-pass/detection/detectionProductDetail?sku=100102462832",
     * "type": "jump"
     * }
     * },
     * {
     * "code": "viewPositionBtn",
     * "name": "查看位置",
     * "style": "primary",
     * "action": {
     * "url": "https://laputa.jd.com/jdh-healthcare-pass/detection/detectionProductDetail?sku=100102462832",
     * "type": "jump"
     * }
     * }
     * ]
     * }
     * ]
     * }
     *
     * @param viaFloorInfo     通用楼层信息
     * @param statusMapping    状态映射
     * @param jdhPromise       jdhPromise
     * @param promiseHistories promiseHistories
     */
    private void handlePromiseAngelInfo(ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping, JdhPromise jdhPromise, List<JdhPromiseHistory> promiseHistories, JdhSkuDto jdhSku, FillViaConfigDataContext ctx){
        // 查询护士信息
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();

        AngelWorkQuery angelWorkQuery = new AngelWorkQuery();
        angelWorkQuery.setPromiseId(jdhPromise.getPromiseId());
        AngelWorkDetailDto angelWork = angelPromiseApplication.queryAngelWork(angelWorkQuery);
        queryAngelDto(jdhPromise.getPromiseId());
        log.info("XfylHomeTestOrderDetailHandler handlePromiseAngelInfo angelWork:{}", JSON.toJSONString(angelWork));
        while (iterator.hasNext()){
            ViaFloorConfig viaFloorConfig = iterator.next();
            if(statusMapping.getHiddenAngelFieldList().contains(viaFloorConfig.getFieldKey())){
                iterator.remove();
                continue;
            }
            if(ViaAngelInfoFieldEnum.HOME_DATE.getField().equals(viaFloorConfig.getFieldKey())){
                //不合适
                if(JdhPromiseStatusEnum.SERVICING.getStatus().equals(jdhPromise.getPromiseStatus())){
                    Optional<JdhPromiseHistory> jdhPromiseHistory = promiseHistories.stream()
                            .filter(ele -> JdhPromiseStatusEnum.SERVICING.getStatus().equals(ele.getAfterStatus()))
                            .findFirst();
                    Date servicingTime = jdhPromise.getUpdateTime();
                    if(jdhPromiseHistory.isPresent()){
                        JdhPromiseHistory history = jdhPromiseHistory.get();
                        servicingTime = history.getCreateTime();
                    }
                    viaFloorConfig.setFieldValue(TimeUtils.dateTimeToStr(servicingTime,TimeFormat.LONG_PATTERN_LINE) + " 已上门");
                }else{
                    JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(jdhPromise.getVerticalCode());
                    if(Objects.nonNull(jdhVerticalBusiness) && BusinessModeEnum.checkTestAndCare(jdhVerticalBusiness.getBusinessModeCode())) {
                        viaFloorConfig.setFieldValue("预约 " + getFullAppointmentDateDesc(jdhPromise.getAppointmentTime()) + " 上门");
                    }
                }
                continue;
            }
            if(ViaAngelInfoFieldEnum.ANGEL_NAME.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(angelWork)){
                    iterator.remove();
                }else{
                    String angelName = angelWork.getAngelName();
                    String  angelType = AngelWorkTypeEnum.getEnumByCode(angelWork.getWorkType()).getAngelType();
                    if (Objects.nonNull(jdhSku)){
                        angelType = productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), angelType);
                    }
                    viaFloorConfig.setFieldValue(new UserName(angelName).mask() + "-" + angelType);
                }
                continue;
            }
            if(ViaAngelInfoFieldEnum.ANGEL_PHONE.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(angelWork)){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(angelWork.getAngelPhone());
                }
                continue;
            }

            if(ViaAngelInfoFieldEnum.HEAD_IMG.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.nonNull(angelWork)){
                    viaFloorConfig.setFieldValue(angelWork.getAngelHeadImg());
                }else{
                    iterator.remove();
                }
                continue;
            }

            // 按钮
            List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
            if(CollUtil.isNotEmpty(btnList)){
                Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
                while (btnInfoIterator.hasNext()){
                    ViaBtnInfo btnInfo = btnInfoIterator.next();
                    if(statusMapping.getHiddenAngelBtnList().contains(btnInfo.getCode())){
                        btnInfoIterator.remove();
                        continue;
                    }
                    //查看主页
                    if(ViaAngelInfoBtnEnum.VIEW_HOME.getBtn().equals(btnInfo.getCode())){

                    }
                    //查看位置
                    if(ViaAngelInfoBtnEnum.VIEW_POSITION.getBtn().equals(btnInfo.getCode())){
                        AngelTrackQuery angelTrackQuery = new AngelTrackQuery();
                        angelTrackQuery.setPromiseId(jdhPromise.getPromiseId());
                        AngelTrackDto track = angelWorkApplication.getTransferTrack(angelTrackQuery);
                        if (Objects.nonNull(track) && StringUtils.isNotBlank(track.getTrackUrl())){
                            try {
                                ViaActionInfo action = btnInfo.getAction();
                                String trackUrl = track.getTrackUrl();
                                log.info("XfylHomeTestOrderDetailHandler handlePromiseAngelInfo trackUrl={}", trackUrl);
                                String phone = callRecordApplication.getAppointmentPhone(jdhPromise);
                                PhoneNumber phoneNumber = new PhoneNumber();
                                String encodedPhone = URLEncoder.encode(phoneNumber.encrypt(phone), "UTF-8");
                                trackUrl = trackUrl+"&userType=appointmentUser&encryptPhone="+encodedPhone;
                                action.setUrl(trackUrl);
                            } catch (Exception e) {
                                log.error("XfylHomeTestOrderDetailHandler handlePromiseAngelInfo trackUrl error e", e);
                            }
                        }else{
                            btnInfoIterator.remove();
                            continue;
                        }
                    }
                }
            }

            if(ViaAngelInfoFieldEnum.CONTACT_ANGEL.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(angelWork)){
                    iterator.remove();
                }else{
                    if (Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType(),AngelWorkStatusEnum.SERVICING.getType())
                            .contains(angelWork.getStatus())){
                        ViaActionInfo action = viaFloorConfig.getAction();
                        if (action != null){
                            action.getParams().put("promiseId",angelWork.getPromiseId());
                            ViaActionInfo nextAction = action.getNextAction();
                            if (Objects.nonNull(nextAction)) {
                                nextAction.getParams().put("promiseId", angelWork.getPromiseId());
                            }
                        }
                    }else {
                        iterator.remove();
                        continue;
                    }
                }
                continue;
            }
        }
        log.info("XfylHomeTestOrderDetailHandler handlePromiseAngelInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * queryAngelDto
     *
     * @param promiseId promiseId
     * @return {@link JdhAngelDto}
     */
    private JdhAngelDto queryAngelDto(Long promiseId){
        try {
            AngelWorkQuery angelWorkQuery = new AngelWorkQuery();
            angelWorkQuery.setPromiseId(promiseId);
            AngelWorkDetailDto workDetailDto = angelPromiseApplication.queryWorkListOrRecently(angelWorkQuery);
            log.info("XfylHomeTestOrderDetailHandler queryAngelDto,workDetailDto:{}",JSON.toJSONString(workDetailDto));
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelId(Long.parseLong(workDetailDto.getAngelId()));
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            log.info("XfylHomeTestOrderDetailHandler queryAngelDto,jdhAngelDto:{}",JSON.toJSONString(jdhAngelDto));
            return jdhAngelDto;
        }catch (Exception e){
            log.error("XfylHomeTestOrderDetailHandler queryAngelDto error",e);
            return null;
        }
    }


    /**
     * {
     *     "floorCode": "promiseTitleInfo",
     *     "floorName": "履约信息标题",
     *     "floorConfigList": [
     *         {
     *             "mainTitle":"护士接单。我们将为您安排专业护士提供服务"
     *         }
     *     ]
     * }
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdOrder       JD订单
     * @param jdhPromise    jdhPromise
     */
    private void handlePromiseTitleInfo(ViaFloorInfo viaFloorInfo,
                                        ViaStatusMapping statusMapping,
                                        JdOrderDTO jdOrder,
                                        JdhPromise jdhPromise,
                                        List<JdhPromiseHistory> promiseHistories,List<MedicalPromiseDTO> medicalPromiseList,JdhSkuDto jdhSkuDto, FillViaConfigDataContext ctx){
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
        viaFloorConfig.setMainTitle(StrUtil.format(statusMapping.getPromiseTitle(),getDynamicFieldValue(jdOrder,jdhPromise,promiseHistories,medicalPromiseList,statusMapping.getPromiseTitleDynamicField(),statusMapping.getDynamicCursorMinutes(),jdhSkuDto)));
        if (Objects.nonNull(jdhSkuDto)){
            viaFloorConfig.setMainTitle(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), viaFloorConfig.getMainTitle()));
            viaFloorConfig.setTitle(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), ctx.getScene(), viaFloorConfig.getTitle()));
        }
        floorConfigList.add(viaFloorConfig);
        log.info("XfylHomeTestOrderDetailHandler handlePromiseTitleInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 处理楼层列表
     *
     * @param ctx                上下文
     * @param statusMapping      状态映射
     * @param jdOrder            JD订单
     * @param jdhPromise         jdhPromise
     * @param medicalPromiseList medicalPromiseList
     * @param promiseHistories   promiseHistories
     */
    private void dealFloorList(FillViaConfigDataContext ctx,
                               ViaStatusMapping statusMapping,
                               JdOrderDTO jdOrder,
                               JdhPromise jdhPromise,
                               List<JdhPromiseHistory> promiseHistories,
                               List<MedicalPromiseDTO> medicalPromiseList,
                               JdhSkuDto jdhSkuDto){
        ViaConfig viaConfig = ctx.getViaConfig();

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (ViaFloorInfo viaFloorInfo : viaConfig.getFloorList()) {
            //概要 summaryInfo
            if (ViaFloorEnum.PROMISE_SUMMARY_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleSummaryInfo(viaFloorInfo,statusMapping,jdOrder,jdhPromise,promiseHistories,medicalPromiseList,jdhSkuDto,ctx),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handleSummaryInfo exception",exception);
                    return null;
                }));
                continue;
            }

            //步骤条 stepGuideInfo
            if(ViaFloorEnum.PROMISE_STEP_GUIDE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handleStepGuideInfo(viaFloorInfo,statusMapping),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handleStepGuideInfo exception",exception);
                    return null;
                }));
                continue;
            }

            //履约信息 - 标题 promiseTitleInfo
            if(ViaFloorEnum.PROMISE_TITLE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handlePromiseTitleInfo(viaFloorInfo,statusMapping,jdOrder,jdhPromise,promiseHistories,medicalPromiseList,jdhSkuDto,ctx),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handlePromiseTitleInfo exception",exception);
                    return null;
                }));
                continue;
            }

            //履约信息 - 服务者 promiseAngelInfo
            if(ViaFloorEnum.PROMISE_ANGEL_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handlePromiseAngelInfo(viaFloorInfo,statusMapping,jdhPromise,promiseHistories,jdhSkuDto,ctx),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handlePromiseAngelInfo exception",exception);
                    return null;
                }));
                continue;
            }

            //履约信息 - 消费码 promiseCodeInfo
            if(ViaFloorEnum.PROMISE_CODE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handlePromiseCodeInfo(viaFloorInfo,jdhPromise),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handlePromiseCodeInfo exception",exception);
                    return null;
                }));
                continue;
            }

            //履约信息 - 被服务者 promisePatientInfo
            if(ViaFloorEnum.PROMISE_PATIENT_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handelPromisePatientInfo(viaFloorInfo,statusMapping,jdhPromise),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handelPromisePatientInfo exception",exception);
                    return null;
                }));
                continue;
            }

            //样本信息楼层 materialInfo
            if(ViaFloorEnum.PROMISE_MATERIAL_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handelMaterialInfo(viaConfig,viaFloorInfo,jdhPromise,medicalPromiseList),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handelMaterialInfo exception",exception);
                    return null;
                }));
                continue;
            }

            //保险信息 insuranceInfo

            //购买商品信息 orderSkuInfo
            if(ViaFloorEnum.PROMISE_ORDER_SKU_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handelOrderSkuInfo(viaConfig,viaFloorInfo,jdOrder),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handelOrderSkuInfo exception",exception);
                    return null;
                }));
                continue;
            }

            //订单信息 orderInfo
            if(ViaFloorEnum.PROMISE_ORDER_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handelOrderInfo(viaFloorInfo,jdOrder),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handelOrderInfo exception",exception);
                    return null;
                }));
                continue;
            }

            //企微卡片信息 weChatCardInfo
            if (ViaFloorEnum.PROMISE_WECHAT_CARD_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handelWeChatCardInfo(viaFloorInfo, jdOrder, ctx.getUserPin()), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handelWeChatCardInfo exception", exception);
                    return null;
                }));
            }

            //底部按钮 footerButtons
            if(ViaFloorEnum.PROMISE_FOOTER_BUTTONS.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handleFooterButtons(ctx,viaFloorInfo,statusMapping,jdOrder,jdhPromise, medicalPromiseList),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handleFooterButtons exception",exception);
                    return null;
                }));
                continue;
            }

            // 填充模版数据
            Map<String, Object> sourceData = Maps.newHashMap();
            sourceData.put(PromiseAggregateEnum.PROMISE.getCode(), jdhPromise);
            sourceData.put(TradeAggregateEnum.ORDER.getCode(), jdOrder);
            sourceData.put("ctx", ctx);
            /**
             * 检测项目条码信息（自采样需要）
             */
            if(ViaFloorEnum.PROMISE_SPECIMEN_CODE.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handleFooterMedicalPromise(viaFloorInfo, sourceData, medicalPromiseList),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handleFooterButtons exception",exception);
                    return null;
                }));
                continue;
            }

            /**
             * 采样教程楼层（自采样需要）
             */
            if(ViaFloorEnum.PROMISE_SAMPLE_COURSE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())){
                futures.add(CompletableFuture.runAsync(() -> handleFooterSampleCourse(viaFloorInfo, sourceData, jdhPromise),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handleFooterButtons exception",exception);
                    return null;
                }));
                continue;
            }

            //用户反馈
            if (ViaFloorEnum.PROMISE_USER_FEEDBACK_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleUserFeedbackInfo(viaFloorInfo, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylHomeTestOrderDetailHandler dealFloorList handleUserFeedbackInfo exception", exception);
                    return null;
                }));
                continue;
            }


            //处理楼层数据
            ViaFloorEnum viaFloorEnum = ViaFloorEnum.getByFloorCode(viaFloorInfo.getFloorCode());
            if(viaFloorEnum!=null&&StringUtils.isNotEmpty(viaFloorEnum.getBeanName())){
                Floor floor = SpringUtil.getBean(viaFloorEnum.getBeanName());
                futures.add(CompletableFuture.runAsync(() -> floor.handleData(ctx,sourceData,viaFloorInfo,statusMapping,jdhPromise,jdOrder,promiseHistories,medicalPromiseList), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestOrderDetailHandlerV3 dealFloorList {} exception",viaFloorEnum.getFloorCode(), exception);
                    return null;
                }));
            }

        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 订单信息
     * {
     *     "floorCode": "orderInfo",
     *     "floorName": "订单信息",
     *     "floorConfigList": [
     *         {
     *            "fieldKey":"orderTotalAmount",
     *            "fieldValue":"0.01",
     *            "showDetail":true,
     *            "feeItemList":[
     *
     *            ]
     *         },
     *         {
     *            "fieldKey":"orderId",
     *            "fieldValue":"284362257873",
     *            "copyBtn":true
     *         },
     *         {
     *            "fieldKey":"paymentTime",
     *            "fieldValue":"2023-12-04 17:30:53"
     *         },
     *         {
     *            "fieldKey":"paymentWayDesc",
     *            "fieldValue":"在线支付"
     *         },
     *         {
     *            "fieldKey":"orderUserPhone",
     *            "fieldValue":"176****3020"
     *         },
     *         {
     *            "fieldKey":"orderUserName",
     *            "fieldValue":"*先生"
     *         },
     *         {
     *            "fieldKey":"addressDetail",
     *            "fieldValue":"北京大兴区旧宫地区旧宫新苑南区14号楼二单元302"
     *         }
     *     ]
     * }
     *
     * @param viaFloorInfo       通用楼层信息
     * @param jdOrder            JD订单
     */
    private void handelOrderInfo(ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder) {
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();
        while (iterator.hasNext()){
            ViaFloorConfig viaFloorConfig = iterator.next();
            if(ViaOrderInfoFieldEnum.ORDER_ID.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getOrderId())){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(jdOrder.getOrderId().toString());
                }
                continue;
            }
            if(ViaOrderInfoFieldEnum.ORDER_AMOUNT.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getOrderAmount())){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(jdOrder.getOrderAmount().toPlainString());
                    List<JdOrderServiceFeeInfoDTO> jdOrderServiceFeeInfos = jdOrder.getJdOrderServiceFeeInfos();
                    viaFloorConfig.setValue(JSON.toJSONString(jdOrderServiceFeeInfos));
                }
                continue;
            }

            // 时段费
            if (ViaOrderInfoFieldEnum.TIME_PERIOD_FEE.getField().equals(viaFloorConfig.getFieldKey())) {
                if (CollectionUtils.isEmpty(jdOrder.getJdOrderServiceFeeInfos())){
                    iterator.remove();
                    continue;
                } else {
                    List<JdOrderServiceFeeInfoDTO> feeInfoList = jdOrder.getJdOrderServiceFeeInfos().stream().filter(s -> FeeAggregateTypeEnum.TIME_PERIOD_FEE.getSubType().equals(s.getAggregateSubType())
                            && Objects.nonNull(s.getServiceFee()) && s.getServiceFee().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(feeInfoList)){
                        iterator.remove();
                        continue;
                    }
                    viaFloorConfig.setFieldValue(feeInfoList.get(0).getServiceFee().toString());
                }
                continue;
            }

            // 上门费
            if (ViaOrderInfoFieldEnum.HOME_VISIT_FEE.getField().equals(viaFloorConfig.getFieldKey())) {
                if (CollectionUtils.isEmpty(jdOrder.getJdOrderServiceFeeInfos())){
                    iterator.remove();
                    continue;
                } else {
                    List<JdOrderServiceFeeInfoDTO> feeInfoList = jdOrder.getJdOrderServiceFeeInfos().stream().filter(s -> FeeAggregateTypeEnum.HOME_VISIT_FEE.getSubType().equals(s.getAggregateSubType())
                            && Objects.nonNull(s.getServiceFee()) && s.getServiceFee().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(feeInfoList)){
                        iterator.remove();
                        continue;
                    }
                    viaFloorConfig.setFieldValue(feeInfoList.get(0).getServiceFee().toString());
                }
                continue;
            }

            // 动态调整费
            if (ViaOrderInfoFieldEnum.DYNAMIC_FEE.getField().equals(viaFloorConfig.getFieldKey())) {
                if (CollectionUtils.isEmpty(jdOrder.getJdOrderServiceFeeInfos())){
                    iterator.remove();
                    continue;
                } else {
                    List<JdOrderServiceFeeInfoDTO> feeInfoList = jdOrder.getJdOrderServiceFeeInfos().stream().filter(s -> FeeAggregateTypeEnum.DYNAMIC_FEE.getSubType().equals(s.getAggregateSubType())
                            && Objects.nonNull(s.getServiceFee()) && s.getServiceFee().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(feeInfoList)){
                        iterator.remove();
                        continue;
                    }
                    viaFloorConfig.setFieldValue(feeInfoList.get(0).getServiceFee().toString());
                }
                continue;
            }

            if(ViaOrderInfoFieldEnum.ORDER_CREATE_TIME.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getCreateTime())){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(TimeUtils.dateTimeToStr(jdOrder.getCreateTime(), TimeFormat.LONG_PATTERN_LINE));
                }
                continue;
            }
            if(ViaOrderInfoFieldEnum.PAY_TYPE_DESC.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getPayType())){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(PayTypeEnum.getDescOfType(jdOrder.getPayType().toString()));
                }
                continue;
            }
            if(ViaOrderInfoFieldEnum.ORDER_USER_PHONE.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getAddressInfo()) || StrUtil.isBlank(jdOrder.getAddressInfo().getMobile())){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(new PhoneNumber(jdOrder.getAddressInfo().getMobile()).mask());
                }
                continue;
            }
            if(ViaOrderInfoFieldEnum.ORDER_USER_NAME.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getAddressInfo()) || StrUtil.isBlank(jdOrder.getAddressInfo().getName())){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(new UserName(jdOrder.getAddressInfo().getName()).mask());
                }
                continue;
            }
            if(ViaOrderInfoFieldEnum.ADDRESS_DETAIL.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getAddressInfo()) || StrUtil.isBlank(jdOrder.getAddressInfo().getFullAddress())){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(jdOrder.getAddressInfo().getFullAddress());
                }
                continue;
            }
            if(ViaOrderInfoFieldEnum.ORDER_REMARK.getField().equals(viaFloorConfig.getFieldKey())){
                if(StrUtil.isEmpty(jdOrder.getRemark())){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(jdOrder.getRemark());
                }
                continue;
            }
            if(ViaOrderInfoFieldEnum.ORDER_DISCOUNT.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getOrderDiscount()) || jdOrder.getOrderDiscount().compareTo(new BigDecimal("0")) <= 0){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(jdOrder.getOrderDiscount().toString());
                }
                continue;
            }
            if(ViaOrderInfoFieldEnum.ORDER_COUPON.getField().equals(viaFloorConfig.getFieldKey())){
                if(Objects.isNull(jdOrder.getOrderCoupon()) || jdOrder.getOrderCoupon().compareTo(new BigDecimal("0")) <= 0){
                    iterator.remove();
                }else{
                    viaFloorConfig.setFieldValue(jdOrder.getOrderCoupon().toString());
                }
                continue;
            }
        }
    }

    /**
     * 订单SKU信息
     * {
     *     "floorCode": "orderSkuInfo",
     *     "floorName": "订单商品信息",
     *     "floorConfigList": [
     *         {
     *             "value":{
     *                 "skuAmount": 6490,
     *                 "skuExpireDate": "2025-04-15",
     *                 "skuImage": "jfs/t1/235524/3/11500/108038/65a13846F3d18a04d/d079e0fd4563a6cf.jpg",
     *                 "skuName": "京东到家快检 呼吸道病毒细菌上门检测,常见呼吸道感染、甲流、乙流、支原体、多重病毒细菌一次性检测，附赠采样试剂盒",
     *                 "skuNo": "100077601060",
     *                 "skuNum": 2
     *             },
     *             "action": {
     *                 "url": "https://laputa.jd.com/jdh-healthcare-pass/detection/detectionProductDetail?sku=100102462832",
     *                 "type": "jump"
     *             }
     *         },
     *         {
     *             "value":{
     *                 "skuAmount": 6490,
     *                 "skuExpireDate": "2025-04-15",
     *                 "skuImage": "jfs/t1/235524/3/11500/108038/65a13846F3d18a04d/d079e0fd4563a6cf.jpg",
     *                 "skuName": "京东到家快检 呼吸道病毒细菌上门检测,常见呼吸道感染、甲流、乙流、支原体、多重病毒细菌一次性检测，附赠采样试剂盒",
     *                 "skuNo": "100077601060",
     *                 "skuNum": 2
     *             },
     *             "action": {
     *                 "url": "https://laputa.jd.com/jdh-healthcare-pass/detection/detectionProductDetail?sku=100102462832",
     *                 "type": "jump"
     *             }
     *         }
     *     ]
     * }
     *
     * @param viaConfig          VIA配置
     * @param viaFloorInfo       通用楼层信息
     * @param jdOrder            JD订单
     */
    private void handelOrderSkuInfo(ViaConfig viaConfig, ViaFloorInfo viaFloorInfo, JdOrderDTO jdOrder) {
        List<JdOrderItemDTO> jdOrderItemList = jdOrder.getJdOrderItemList();
        List<ViaFloorConfig> floorConfigList = new ArrayList<>();

        for (JdOrderItemDTO jdOrderItemDTO : jdOrderItemList) {
            // action
            ViaActionInfo action = new ViaActionInfo();
            action.setUrl(MessageFormat.format(viaConfig.getSkuDetailUrl(),jdOrderItemDTO.getSkuId().toString()));
            action.setType(ActionType.JUMP.getCode());
            //如果是加项商品，跳转url为空，即在订详无法跳转
            if (IsAddedEnum.IS_ADDED.getValue().equals(jdOrderItemDTO.getIsAdded())) {
                action.setUrl(null);
            }
            floorConfigList.add(ViaFloorConfig.builder().value(JSON.toJSONString(jdOrderItemDTO)).action(action).build());
        }


        viaFloorInfo.setFloorConfigList(floorConfigList);
    }

    /**
     * 处理样本信息楼层
     * {
     *     "floorCode": "materialInfo",
     *     "floorName": "样本信息",
     *     "floorConfigList": [
     *         {
     *             "title": "*杰伦 | JD24281231298",
     *             "viaStatus":1,
     *             "statusDesc":"送检中",
     *             "targetUrl":""
     *         }
     *     ]
     * }
     *
     * @param viaFloorInfo  通用楼层信息
     * @param jdhPromise    jdhPromise
     */
    private void handelMaterialInfo(ViaConfig viaConfig,ViaFloorInfo viaFloorInfo,JdhPromise jdhPromise,List<MedicalPromiseDTO> medicalPromiseList) {
        if(CollUtil.isNotEmpty(medicalPromiseList)){
            Map<Long, JdhPromisePatient> promisePatientMap = jdhPromise.getPatients().stream().collect(Collectors.toMap(JdhPromisePatient::getPromisePatientId, Function.identity()));
            List<ViaStatusMapping> medicalStatusMapping = viaConfig.getMedicalStatusMapping();
            List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();
            //按人归堆，如果一个人下的某一条 已出报告 只展示人名 和查看按钮
            //展示效果：
            //张三            已出报告
            //李四 | JD1111    检测中
            //李四 | JD1111    检测中
            MedicalPromiseDTO completeMed = medicalPromiseList.stream().filter(p -> Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(), p.getStatus())).findFirst().orElse(null);
            Boolean completeExist = Objects.nonNull(completeMed) ? Boolean.TRUE : Boolean.FALSE;
            Map<Long, List<MedicalPromiseDTO>> patientMedPromiseList = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromisePatientId));
            for (Map.Entry<Long, List<MedicalPromiseDTO>> entry : patientMedPromiseList.entrySet()) {
                List<MedicalPromiseDTO> medPromiseList = entry.getValue();

                Map<Integer, List<MedicalPromiseDTO>> showTypeToList = medPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getReportShowType));
                ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
                List<ViaBtnInfo> newBtnList = new ArrayList<>();
                viaFloorConfig.setBtnList(newBtnList);
                AtomicReference<Boolean> report = new AtomicReference<>(Boolean.FALSE);
                viaFloorConfigList.add(viaFloorConfig);
                MedicalPromiseDTO first = medPromiseList.get(0);
                JdhPromisePatient patient = promisePatientMap.get(first.getPromisePatientId());
                //如果已出报告，则有title
                if (completeExist){
                    viaFloorConfig.setTitle(Objects.isNull(patient.getUserName()) ? "" : patient.getUserName().mask() + "的检测报告");
                }
                showTypeToList.forEach((showType,list)->{


                    //如果是结构化页面
                    if (Objects.equals(ReportShowTypeEnum.STRUCT.getType(),showType)){
                        //判断是否有出报告的检测单
                        //是否有已出报告,且没有退款
                        List<MedicalPromiseDTO> reportedList = list.stream().filter(ele ->
                                (MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(ele.getStatus())) &&
                                        !(JdhFreezeEnum.FREEZE.getStatus().equals(ele.getFreeze())
                                                || MedicalPromiseStatusEnum.INVALID.getStatus().equals(ele.getStatus()))
                        ).collect(Collectors.toList());
                        String tip = list.stream().map(MedicalPromiseDTO::getServiceItemName).collect(Collectors.joining(","));
                        log.info("handelMaterialInfo->name={},reportedList={},tip={}",patient.getUserName(),reportedList,tip);
                        //如果出报告
                        if(CollUtil.isNotEmpty(reportedList)){
                            report.set(Boolean.TRUE);

                            MedicalPromiseDTO reportedMedPromise = reportedList.get(0);
                            JdhPromisePatient jdhPromisePatient = promisePatientMap.get(reportedMedPromise.getPromisePatientId());

                            for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
                                log.info("handelMaterialInfo->,statusList={},stats={}",JSON.toJSONString(viaStatusMapping.getStatusList()),reportedMedPromise.getStatus());
                                if (viaStatusMapping.getStatusList().contains(reportedMedPromise.getStatus())) {
                                    viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                                    viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                                    List<ViaBtnInfo> btnList = viaStatusMapping.getBtnList();
                                    if(CollUtil.isNotEmpty(btnList)){
                                        for (ViaBtnInfo viaBtnInfo : btnList) {
                                            if(ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode().equals(viaBtnInfo.getCode())){
                                                if(Objects.nonNull(jdhPromisePatient) && Objects.nonNull(jdhPromisePatient.getPatientId())){
                                                    ViaBtnInfo newBtnInfo = JSON.parseObject(JSON.toJSONString(viaBtnInfo),ViaBtnInfo.class);
                                                    newBtnInfo.getAction().setUrl(MessageFormat.format(newBtnInfo.getAction().getUrl(),jdhPromise.getSourceVoucherId(),jdhPromisePatient.getPatientId().toString()));
                                                    newBtnInfo.setBtnTip(tip);
                                                    newBtnInfo.setLevel(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                                                    newBtnList.add(newBtnInfo);
                                                }
                                            }
                                        }
                                    }
                                    break;
                                }
                            }

//                            fillMedPromiseFloorConfig(viaFloorConfig,reportedMedPromise,medicalStatusMapping,jdhPromisePatient,jdhPromise);
//                            viaFloorConfigList.add(viaFloorConfig);
                        }else {
                            //如果未出报告
                            for (MedicalPromiseDTO medicalPromiseDTO : list) {
                                ViaBtnInfo newBtnInfo = new ViaBtnInfo();
                                newBtnList.add(newBtnInfo);
                                newBtnInfo.setStyle("text");
                                String btnTip = completeExist ? medicalPromiseDTO.getServiceItemName() : patient.getUserName().mask() + " | " + medicalPromiseDTO.getSpecimenCode();
                                newBtnInfo.setBtnTip(btnTip);
                                for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
                                    log.info("handelMaterialInfo2->,statusList={},stats={}",JSON.toJSONString(viaStatusMapping.getStatusList()),medicalPromiseDTO.getStatus());
                                    if (viaStatusMapping.getStatusList().contains(medicalPromiseDTO.getStatus())) {
                                        newBtnInfo.setName(viaStatusMapping.getStatusDesc());
                                        newBtnInfo.setLevel(viaStatusMapping.getViaStatus());
                                        if (!Boolean.TRUE.equals(report.get())){
                                            viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                                            viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                                        }
                                        break;
                                    }
                                }
                            }


                        }

                    }else {
                        //如果是PDF页面
                        //遍历
                        //判断是否出报告
                        //如果出报告
                        //如果未出报告
                        for (MedicalPromiseDTO medPromise : list) {
                            ViaBtnInfo newBtnInfo = new ViaBtnInfo();
                            newBtnList.add(newBtnInfo);
                            String btnTip = completeExist ? medPromise.getServiceItemName() : patient.getUserName().mask() + " | " + medPromise.getSpecimenCode();
                            newBtnInfo.setBtnTip(btnTip);
                            //如果出报告了
                            if (Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(),medPromise.getStatus())){
                                report.set(Boolean.TRUE);
                                newBtnInfo.setName("去查看");
                                newBtnInfo.setStyle("primaryBordered");
                                ViaActionInfo ai = new ViaActionInfo();
                                newBtnInfo.setAction(ai);
                                Map<String,Object> params = new HashMap<>();
                                params.put("medicalPromiseId",medPromise.getMedicalPromiseId());
                                ai.setParams(params);
                                ai.setType("request");
                                ai.setFunctionId("jdh_o2oservice_queryUrlForCenter");
                                newBtnInfo.setLevel(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                                viaFloorConfig.setViaStatus(MedicalPromiseStatusEnum.COMPLETED.getStatus());
                                viaFloorConfig.setStatusDesc("已出报告");
                            }else {
                                //如果未出报告
                                newBtnInfo.setStyle("text");
                                for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
                                    if (viaStatusMapping.getStatusList().contains(medPromise.getStatus())) {
                                        newBtnInfo.setName(viaStatusMapping.getStatusDesc());
                                        newBtnInfo.setLevel(viaStatusMapping.getViaStatus());
                                        if (!Boolean.TRUE.equals(report.get())){
                                            viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                                            viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                    }




                });

                if (CollectionUtils.isNotEmpty(newBtnList)){
                    newBtnList.sort(Comparator.comparing(ViaBtnInfo::getLevel).reversed());
                }





//                if(CollUtil.isNotEmpty(reportedList)){
//                    ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
//                    MedicalPromiseDTO reportedMedPromise = reportedList.get(0);
//                    JdhPromisePatient jdhPromisePatient = promisePatientMap.get(reportedMedPromise.getPromisePatientId());
//                    viaFloorConfig.setTitle(Objects.isNull(jdhPromisePatient.getUserName()) ? "" : jdhPromisePatient.getUserName().mask() + "的检测报告");
//                    fillMedPromiseFloorConfig(viaFloorConfig,reportedMedPromise,medicalStatusMapping,jdhPromisePatient,jdhPromise);
//                    viaFloorConfigList.add(viaFloorConfig);
//                }else{
//                    for (MedicalPromiseDTO medicalPromiseDTO : medPromiseList) {
//                        // 冻结或者作废不展示
//                        if(JdhFreezeEnum.FREEZE.getStatus().equals(medicalPromiseDTO.getFreeze()) || MedicalPromiseStatusEnum.INVALID.getStatus().equals(medicalPromiseDTO.getStatus())) {
//                            continue;
//                        }
//                        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
//                        JdhPromisePatient jdhPromisePatient = promisePatientMap.get(medicalPromiseDTO.getPromisePatientId());
//                        viaFloorConfig.setTitle(jdhPromisePatient.getUserName().mask() + " | " + medicalPromiseDTO.getSpecimenCode());
//                        fillMedPromiseFloorConfig(viaFloorConfig,medicalPromiseDTO,medicalStatusMapping,jdhPromisePatient,jdhPromise);
//                        viaFloorConfigList.add(viaFloorConfig);
//                    }
//                }
            }
            viaFloorInfo.setFloorConfigList(viaFloorConfigList);
        }
        log.info("XfylHomeTestOrderDetailHandler handelMaterialInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * fillMedPromiseFloorConfig
     *
     * @param viaFloorConfig       viaFloorConfig
     * @param medPromise           medPromise
     * @param medicalStatusMapping medicalStatusMapping
     * @param jdhPromisePatient    jdhPromisePatient
     * @param jdhPromise           jdhPromise
     */
    private void fillMedPromiseFloorConfig(ViaFloorConfig viaFloorConfig,
                                           MedicalPromiseDTO medPromise,
                                           List<ViaStatusMapping> medicalStatusMapping,
                                           JdhPromisePatient jdhPromisePatient,
                                           JdhPromise jdhPromise){
        for (ViaStatusMapping viaStatusMapping : medicalStatusMapping) {
            if (viaStatusMapping.getStatusList().contains(medPromise.getStatus())) {
                viaFloorConfig.setViaStatus(viaStatusMapping.getViaStatus());
                viaFloorConfig.setStatusDesc(viaStatusMapping.getStatusDesc());
                List<ViaBtnInfo> btnList = viaStatusMapping.getBtnList();
                if(CollUtil.isNotEmpty(btnList)){
                    List<ViaBtnInfo> newBtnList = new ArrayList<>();
                    for (ViaBtnInfo viaBtnInfo : btnList) {
                        if(ViaBtnCodeEnum.VIEW_REPORT_BTN.getCode().equals(viaBtnInfo.getCode())){
                            if(Objects.nonNull(jdhPromisePatient) && Objects.nonNull(jdhPromisePatient.getPatientId())){
                                ViaBtnInfo newBtnInfo = JSON.parseObject(JSON.toJSONString(viaBtnInfo),ViaBtnInfo.class);
                                newBtnInfo.getAction().setUrl(MessageFormat.format(newBtnInfo.getAction().getUrl(),jdhPromise.getSourceVoucherId(),jdhPromisePatient.getPatientId().toString()));
                                newBtnList.add(newBtnInfo);
                            }
                        }
                    }
                    viaFloorConfig.setBtnList(newBtnList);
                }
                break;
            }
        }
    }

    /**
     * 处理底部按钮
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdOrder       JD订单
     */
    private void handleFooterButtons(FillViaConfigDataContext ctx,ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping, JdOrderDTO jdOrder,JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList) {
        //申请退款 refundBtn
        //再次购买 rePurchaseBtn
        //联系客服 contactCustomerBtn
        //取消订单 cancelOrderBtn
        //立即支付 payNowBtn
        //修改时间
        ViaConfig viaConfig = ctx.getViaConfig();
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
        // 退款标识
        boolean hasInvalid = false;
        if (CollectionUtils.isNotEmpty(medicalPromiseList)){
            hasInvalid = medicalPromiseList.stream().anyMatch(m -> MedicalPromiseStatusEnum.INVALID.getStatus().equals(m.getStatus()));
        }

        while (btnInfoIterator.hasNext()){
            ViaBtnInfo btnInfo = btnInfoIterator.next();

            Map<String, Object> actionCommonParams = new HashMap<>();
            actionCommonParams.put("verticalCode",viaConfig.getVerticalCode());
            actionCommonParams.put("serviceType",viaConfig.getServiceType());
            actionCommonParams.put("envType",ctx.getEnvType());
            actionCommonParams.put("orderId", EntityUtil.getFiledDefaultNull(jdOrder, JdOrderDTO::getOrderId));
            actionCommonParams.put("promiseId",Objects.nonNull(jdhPromise) ? jdhPromise.getPromiseId() : null);

            if (!statusMapping.getFooterButtonCodeList().contains(btnInfo.getCode())) {
                btnInfoIterator.remove();
                continue;
            }

            //申请退款
            ViaActionInfo action = btnInfo.getAction();
            if(ViaBtnCodeEnum.REFUND_BTN.getCode().equals(btnInfo.getCode())){
                if (hasInvalid){
                    btnInfoIterator.remove();
                    continue;
                }
                action.setParams(new HashMap<>(actionCommonParams));
                ViaActionInfo nextAction = action.getNextAction();
                actionCommonParams.put("orderId",jdOrder.getOrderId());
                actionCommonParams.put("refundType",1);
                actionCommonParams.put("refundSource","1");
                actionCommonParams.put("voucherId",jdhPromise.getVoucherId());
                actionCommonParams.put("promiseId",jdhPromise.getPromiseId());
                nextAction.setParams(actionCommonParams);
            }


            //再次购买
            if (ViaBtnCodeEnum.RE_PURCHASE_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    String jumpUrl = MessageFormat.format(btnInfo.getJumpUrlRule(), jdOrder.getJdOrderItemList().get(0).getSkuId().toString());
                    action.setUrl(jumpUrl);
                }catch (Exception e){
                    log.error("XfylHomeTestOrderDetailHandler handleFooterButtons RE_PURCHASE_BTN fail", e);
                    btnInfoIterator.remove();
                    continue;
                }

            }


            //联系客服
            if(ViaBtnCodeEnum.CONTACT_CUSTOMER_BTN.getCode().equals(btnInfo.getCode())){
                try {
                    String jumpUrl = JSON.parseObject(btnInfo.getJumpUrlRule(), new TypeReference<Map<String, String>>() {
                    }).get(EnvTypeEnum.get(ctx.getEnvType()).getCode());
                    if (EnvTypeEnum.JD_APP.getCode().equals(ctx.getEnvType())) {
                        //openapp.jdmobile://virtual?params={"category":"jump","des":"jd_dongdong_chat","entry":"jd_sdk_kjzydxy","orderId":"{0}"}
                        action.setUrl(jumpUrl.replace("{0}", jdOrder.getVenderId()).replace("{1}", jdOrder.getOrderId().toString()));
                    } else {
                        //https://jdcs.m.jd.com/chat/index.action?entry=jd_sdk_kjzydxy&orderId={0}
                        action.setUrl(MessageFormat.format(jumpUrl, jdOrder.getVenderId(), jdOrder.getOrderId().toString()));
                    }
                }catch (Exception e){
                    log.error("XfylHomeTestOrderDetailHandler handleFooterButtons CONTACT_CUSTOMER_BTN fail", e);
                    btnInfoIterator.remove();
                    continue;
                }
            }

            //取消订单
            if(ViaBtnCodeEnum.CANCEL_ORDER_BTN.getCode().equals(btnInfo.getCode())){
                actionCommonParams.put("orderId",jdOrder.getOrderId().toString());
                action.setParams(actionCommonParams);
            }

            //立即支付
            if(ViaBtnCodeEnum.PAY_NOW_BTN.getCode().equals(btnInfo.getCode())){
                actionCommonParams.put("orderId",jdOrder.getOrderId().toString());
                if(StrUtil.isNotBlank(ctx.getOpenId())){
                    actionCommonParams.put("openId",ctx.getOpenId());
                }
                if(StrUtil.isNotBlank(ctx.getCallWxType())){
                    actionCommonParams.put("callWxType",ctx.getCallWxType());
                }
                action.setParams(actionCommonParams);
            }

            //护士评价
            if (ViaBtnCodeEnum.EVALUATE_ANGEL_BTN.getCode().equals(btnInfo.getCode())) {
                try {
                    Map<Long, String> urlMap = angelEcologyApplication.queryAngelEcologyPageUrl(
                            JdhAngelEcologyQueryRequest.builder().envType(ctx.getEnvType()).orderId(jdOrder.getOrderId()).build());
                    String jumpUrl = urlMap.get(jdhPromise.getPromiseId());
                    //有链接设置url
                    if (StringUtils.isNotBlank(jumpUrl)) {
                        action.setUrl(jumpUrl);
                    } else {//无连接不返回按钮
                        btnInfoIterator.remove();
                    }
                } catch (Exception e) {
                    log.error("XfylHomeTestOrderDetailHandler handleFooterButtons EVALUATE_ANGEL_BTN fail", e);
                    btnInfoIterator.remove();
                    continue;
                }
            }

            if (ViaBtnCodeEnum.MODIFY_DATETIME_BTN.getCode().equals(btnInfo.getCode())) {
                if (Objects.nonNull(jdhPromise) && Objects.equals(JdhPromiseStatusEnum.MODIFY_ING.getStatus(), jdhPromise.getPromiseStatus())) {
                    log.error("XfylHomeTestOrderDetailHandler handleFooterButtons 修改预约中状态的履约单不展示修改时间按钮");
                    btnInfoIterator.remove();
                    continue;
                }
                Map<String, Object> actionParams = new HashMap<>();
                actionParams.put("verticalCode",viaConfig.getVerticalCode());
                actionParams.put("serviceType",viaConfig.getServiceType());
                actionParams.put("envType",ctx.getEnvType());
                actionParams.put("scene", AvailableAppointmentTimeSceneEnum.USER_MODIFY_DATE.getName());
                actionParams.put("promiseId",jdhPromise.getPromiseId());
                action.setParams(actionParams);
                ViaActionInfo nextAction = action.getNextAction();
                Map<String, Object> nextActionParams = new HashMap<>();
                nextActionParams.put("verticalCode",viaConfig.getVerticalCode());
                nextActionParams.put("serviceType",viaConfig.getServiceType());
                nextActionParams.put("envType",ctx.getEnvType());
                nextActionParams.put("scene", ModifyAppointmentTimeSceneEnum.USER_MODIFY_DATE.getName());
                nextActionParams.put("reasonType", 1);
                nextActionParams.put("reasonContent", "");
                nextActionParams.put("promiseId", String.valueOf(jdhPromise.getPromiseId()));
                nextAction.setParams(nextActionParams);
            }
        }

        log.info("XfylHomeTestOrderDetailHandler handleFooterButtons viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 履约信息 - 被服务者
     * {
     *     "floorCode": "promisePatientInfo",
     *     "floorName": "履约被服务者信息",
     *     "floorConfigList": [
     *         {
     *            "fieldKey":"appointmentTime",
     *            "fieldValue": "2024-01-01 00:00 - 01:00"
     *         },
     *         {
     *            "fieldKey":"patientList",
     *            "fieldValue":[
     *                 {
     *                    "userName":"*先生"
     *                 },
     *                 {
     *                    "userName":"*女士"
     *                 }
     *            ]
     *         },
     *         {
     *            "fieldKey":"patientAddress",
     *            "fieldValue": "北京大兴区旧宫地区旧宫新苑南区14号楼二单元302"
     *         },
     *         {
     *            "fieldKey":"remark",
     *            "fieldValue": "上门前请提前联系"
     *         }
     *     ]
     * }
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdhPromise    jdhPromise
     */
    private void handelPromisePatientInfo(ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping,JdhPromise jdhPromise) {
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Iterator<ViaFloorConfig> iterator = floorConfigList.iterator();
        while (iterator.hasNext()){
            ViaFloorConfig viaFloorConfig = iterator.next();
            if (statusMapping.getHiddenPatientFieldList().contains(viaFloorConfig.getFieldKey())) {
                iterator.remove();
                continue;
            }

            if(ViaPatientInfoFieldEnum.APPOINTMENT_TIME.getField().equals(viaFloorConfig.getFieldKey())){
                viaFloorConfig.setFieldValue(getFullAppointmentDateDesc(jdhPromise.getAppointmentTime()));
            }
            if(ViaPatientInfoFieldEnum.PATIENT_ADDRESS.getField().equals(viaFloorConfig.getFieldKey())){
                viaFloorConfig.setFieldValue(jdhPromise.getStore().getStoreAddr());
            }
            if(ViaPatientInfoFieldEnum.PATIENT_LIST.getField().equals(viaFloorConfig.getFieldKey())){
                List<JdhPromisePatient> patients = jdhPromise.getPatients();
                List<Map<String,String>> value = new ArrayList<>();
                for (JdhPromisePatient patient : patients) {
                    value.add(MapUtil.builder("userName",patient.getUserName().mask()).build());
                }
                viaFloorConfig.setFieldValue(JSON.toJSONString(value));
            }
            if(ViaPatientInfoFieldEnum.REMARK.getField().equals(viaFloorConfig.getFieldKey())){
                List<JdhPromiseExtend> promiseExtends = jdhPromise.getPromiseExtends();
                for (JdhPromiseExtend promiseExtend : promiseExtends) {
                    if(promiseExtend.getAttribute().equals(PromiseExtendKeyEnum.ORDER_REMARK.getFiledKey())){
                        viaFloorConfig.setFieldValue(promiseExtend.getOrderRemark());
                    }
                }
            }
        }
        log.info("XfylHomeTestOrderDetailHandler handelPromisePatientInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 获取完整预约日期描述
     *
     * @param appointmentTime 预约时间
     * @return {@link String}
     */
    private String getFullAppointmentDateDesc(DomainAppointmentTime appointmentTime){
        Date tomorrow = DateUtil.tomorrow().toJdkDate();
        String dateDesc = " ";
        if(DateUtil.isSameDay(new Date(), TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()))){
            dateDesc = "[今天]";
        }
        if(DateUtil.isSameDay(tomorrow, TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()))){
            dateDesc = "[明天]";
        }
        return appointmentTime.formatAppointDate() +
                dateDesc +
                appointmentTime.formatAppointTimeDesc();
    }

    /**
     * handlePromiseCodeInfo
     * {
     * "floorCode": "promiseCodeInfo",
     * "floorName": "履约消费码信息",
     * "floorConfigList": [
     * {
     * "promiseCode":"1231",
     * "noticeTip":"请在护士上门后出示"
     * }
     * ]
     * }
     *
     * @param viaFloorInfo viaFloorInfo
     * @param jdhPromise   jdhPromise
     */
    private void handlePromiseCodeInfo(ViaFloorInfo viaFloorInfo,JdhPromise jdhPromise) {
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        viaFloorConfig.setPromiseCode(jdhPromise.getCode());
        log.info("XfylHomeTestOrderDetailHandler handlePromiseCodeInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 隐藏楼层
     *
     * @param statusMapping 状态映射
     * @param floorList     楼层列表
     */
    private void clearHiddenFloor(ViaStatusMapping statusMapping,List<ViaFloorInfo> floorList,JdhSkuDto jdhSkuDto){
        //1、移除要展示的楼层
        boolean needRemoveInsuranceInfo = false;
        if(Objects.nonNull(jdhSkuDto)){
            //不需要投保
            if(!NumConstant.NUM_1.equals(jdhSkuDto.getRequiredInsure())){
                needRemoveInsuranceInfo = true;
            }
        }

        Iterator<ViaFloorInfo> iterator = floorList.iterator();
        while (iterator.hasNext()){
            ViaFloorInfo viaFloorInfo = iterator.next();
            //目前C端订单详情,去掉了保险楼层的展示
            if(statusMapping.getHiddenFloorCode().contains(viaFloorInfo.getFloorCode())
                    || (ViaFloorEnum.PROMISE_INSURANCE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode()) && needRemoveInsuranceInfo)){
                iterator.remove();
            }
        }
    }

    @Override
    public void handle(FillViaConfigDataContext ctx) {
        log.info("XfylHomeTestOrderDetailHandler handle ctx:{}", JSON.toJSONString(ctx));
        Integer version = ctx.getViaConfig().getVersion();
        if (Objects.equals(2, version)) {
            log.info("xfylHomeTestOrderDetailHandlerV2 handle");
            angelTestOrderDetailHandler_V2.handle(ctx);
        } else {
            log.info("xfylHomeTestOrderDetailHandler handle");
            this.handle2(ctx);
        }
    }

    /**
     * 填充数据
     *
     * @param ctx ctx
     */
    @SuppressWarnings("all")
    public void handle2(FillViaConfigDataContext ctx) {
        log.info("XfylHomeTestOrderDetailHandler handle ctx:{}", JSON.toJSONString(ctx));
        // ==>>>> 入参校验
        checkParam(ctx);
        ViaConfig viaConfig = ctx.getViaConfig();

        // ==>>>> 数据获取
        //查订单
        JdOrderDTO jdOrder = tradeApplication.getOrderDetail(OrderDetailParam.builder().orderId(ctx.getOrderId()).pin(ctx.getUserPin()).querySource("C").build());
        log.info("XfylHomeTestOrderDetailHandler handle jdOrder:{}", JSON.toJSONString(jdOrder));
        if(Objects.isNull(jdOrder)){
            throw new SystemException(SupportErrorCode.VIA_ORDER_INFO_NOT_EXIT);
        }

        //查履约单
        String sourceVoucherId = Objects.nonNull(jdOrder.getParentId()) && jdOrder.getParentId() > 0 ? jdOrder.getParentId().toString() : jdOrder.getOrderId().toString();
        JdhPromise jdhPromise = promiseRepository.findPromise(PromiseRepQuery.builder().sourceVoucherId(sourceVoucherId).userPin(jdOrder.getUserPin()).build());
        log.info("XfylHomeTestOrderDetailHandler handle jdhPromise:{}", JSON.toJSONString(jdhPromise));

        List<CompletableFuture> futures = new ArrayList<>();
        //查商品配置
        CompletableFuture<JdhSkuDto> skuDtoCf = null;
        if(CollUtil.isNotEmpty(jdOrder.getJdOrderItemList())){
            JdOrderItemDTO jdOrderItemDTO = jdOrder.getJdOrderItemList().get(0);
            Long skuId = jdOrderItemDTO.getSkuId();
            if(Objects.nonNull(skuId)){
                skuDtoCf = CompletableFuture.supplyAsync(() -> querySkuInfo(skuId), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
                futures.add(skuDtoCf);
            }
        }

        //查履约单历史记录
        CompletableFuture<List<JdhPromiseHistory>> promiseHistoryCf = null;
        if(Objects.nonNull(jdhPromise)){
            promiseHistoryCf = CompletableFuture.supplyAsync(() -> queryPromiseHistory(jdhPromise),executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(promiseHistoryCf);
        }

        //查检测单
        CompletableFuture<List<MedicalPromiseDTO>> medPromiseListCf = null;
        if(Objects.nonNull(jdhPromise)){
            medPromiseListCf = CompletableFuture.supplyAsync(() -> queryMedicalPromiseList(jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(medPromiseListCf);
        }

        //异步编译 Aviator
        futures.add(CompletableFuture.runAsync(() -> compileAviator(viaConfig), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)));

        if(CollUtil.isNotEmpty(futures)){
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }


        try {
            // ==>>>> 过滤statusMapping
            ViaStatusMapping statusMapping = hitStatusMapping(jdOrder,
                    jdhPromise,
                    Objects.isNull(medPromiseListCf) ? null : medPromiseListCf.get(),
                    viaConfig.getStatusMapping());
            log.info("XfylHomeTestOrderDetailHandler handle statusMapping:{}", JSON.toJSONString(statusMapping));

            // ==>>>> 移除当前状态下隐藏的楼层
            List<ViaFloorInfo> floorList = viaConfig.getFloorList();
            clearHiddenFloor(statusMapping,floorList,Objects.isNull(skuDtoCf) ? null : skuDtoCf.get());
            log.info("XfylHomeTestOrderDetailHandler handle floorList:{}", JSON.toJSONString(floorList));

            // ==>>>> 楼层处理
            dealFloorList(ctx,
                    statusMapping,
                    jdOrder,
                    jdhPromise,
                    Objects.isNull(promiseHistoryCf) ? null : promiseHistoryCf.get(),
                    Objects.isNull(medPromiseListCf) ? null : medPromiseListCf.get(),
                    Objects.isNull(skuDtoCf) ? null : skuDtoCf.get());
            log.info("XfylHomeTestOrderDetailHandler handle viaConfig:{}", JSON.toJSONString(viaConfig));
        }catch (Exception e){
            log.error("XfylHomeTestOrderDetailHandler handle error", e);
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_HAND_ERROR);
        }
    }


    public static void main(String[] args) {

//        ViaConfig viaConfig = JSON.parseObject(json, ViaConfig.class);
        String foolorJson = "{\"floorCode\":\"promiseSpecimenCode\",\"floorName\":\"检测样本信息\",\"floorConfigList\":[{\"groupInfoList\":[],\"btnList\":[{\"code\":\"bindSpecimenCode\",\"name\":\"提交样本信息\",\"style\":\"request\",\"action\":{\"type\":\"request\",\"functionId\":\"\",\"params\":{\"verticalCode\":\"verticalCode\",\"serviceType\":\"serviceType\",\"envType\":\"envType\"},\"parseMap\":{\"verticalCode\":{\"filedKey\":\"verticalCode\",\"parseType\":1,\"parseExpression\":\"promise.verticalCode\"},\"serviceType\":{\"filedKey\":\"verticalCode\",\"parseType\":1,\"parseExpression\":\"promise.serviceType\"},\"envType\":{\"filedKey\":\"envType\",\"parseType\":1,\"parseExpression\":\"ctx.envType\"}},\"extendParams\":[{\"paramField\":\"fuzzyStoreName\"}]}}]}]}";
        ViaFloorInfo viaFloorInfo = JSON.parseObject(foolorJson, ViaFloorInfo.class);
        XfylHomeTestOrderDetailHandler handler = new XfylHomeTestOrderDetailHandler();
        Map<String, Object> sourceData = Maps.newHashMap();

        JdhPromise promise = new JdhPromise();
        List<PromiseService> services = Lists.newArrayList();
        PromiseService service = new PromiseService();
        service.setServiceId(1243565L);
        services.add(service);
        promise.setServices(services);
        promise.setVerticalCode("xfylHomeTest");
        promise.setServiceType("test");
        sourceData.put("promise", promise);

        FillViaConfigDataContext ctx = new FillViaConfigDataContext();
        ctx.setEnvType("envType");
        sourceData.put("ctx", ctx);

        List<MedicalPromiseDTO> medicalPromiseList = Lists.newArrayList();
        MedicalPromiseDTO dto1 = new MedicalPromiseDTO();
        dto1.setServiceItemId("1111");
        dto1.setServiceItemName("检测项目1");
        dto1.setMedicalPromiseId(2345667L);
        dto1.setName("用户一");
        medicalPromiseList.add(dto1);

        MedicalPromiseDTO dto2 = new MedicalPromiseDTO();
        dto2.setServiceItemId("33333");
        dto2.setServiceItemName("检测项目3");
        dto2.setMedicalPromiseId(22345647L);
        dto2.setName("用户一");
        medicalPromiseList.add(dto2);

        MedicalPromiseDTO dto3 = new MedicalPromiseDTO();
        dto3.setServiceItemId("1111");
        dto3.setServiceItemName("检测项目1");
        dto3.setMedicalPromiseId(2143546757L);
        dto3.setName("测试用户二");
        medicalPromiseList.add(dto3);


        MedicalPromiseDTO dto4 = new MedicalPromiseDTO();
        dto4.setServiceItemId("33333");
        dto4.setServiceItemName("检测项目3");
        dto4.setMedicalPromiseId(23469837439L);
        dto4.setName("测试用户二");
        medicalPromiseList.add(dto3);

        handler.handleFooterMedicalPromise(viaFloorInfo, sourceData, medicalPromiseList);

        System.out.println(JSON.toJSONString(viaFloorInfo));

        ViaFloorInfo tutorialUrlFloor = JSON.parseObject("{\"floorCode\":\"sampleCourseInfo\",\"floorName\":\"采样教程\",\"floorConfigList\":[{\"action\":{\"type\":\"jump\",\"initSwitch\":true,\"url\":\"{JdhService.tutorialUrl}\"}}]}", ViaFloorInfo.class);
        handler.handleFooterSampleCourse(tutorialUrlFloor, sourceData,  promise);
        System.out.println("==================");
        System.out.println(JSON.toJSONString(tutorialUrlFloor));

    }
    /**
     * compileAviator
     *
     * @param viaConfig VIA配置
     */
    private void compileAviator(ViaConfig viaConfig){
        try {
            for (ViaStatusMapping viaStatusMapping : viaConfig.getStatusMapping()) {
                if(StrUtil.isNotBlank(viaStatusMapping.getStatusExpression())){
                    AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(),Boolean.TRUE);
                }
            }
        }catch (Exception e){
            log.info("XfylHomeTestOrderDetailHandler handle compileAviator exception",e);
        }
    }

    /**
     * 查询SKU信息
     *
     * @param skuId SKU ID
     * @return {@link JdhSkuDto}
     */
    private JdhSkuDto querySkuInfo(Long skuId){
        try {
            Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder()
                    .querySkuCoreData(Boolean.TRUE)
                    .queryServiceItem(Boolean.TRUE)
                    .skuIdList(Sets.newHashSet(skuId))
                    .build());
            return jdhSkuDtoMap.get(skuId);
        }catch (Exception e){
            log.info("XfylHomeTestOrderDetailHandler handle querySkuInfo exception",e);
            return null;
        }
    }

    /**
     * queryPromiseHistory
     *
     * @param jdhPromise jdh承诺
     * @return {@link List}<{@link JdhPromiseHistory}>
     */
    private List<JdhPromiseHistory> queryPromiseHistory(JdhPromise jdhPromise){
        try {
            List<JdhPromiseHistory> promiseHistories = promiseHistoryRepository.findList(PromiseHistoryRepQuery.builder().promiseId(jdhPromise.getPromiseId()).build());
            log.info("XfylHomeTestOrderDetailHandler handle queryPromiseHistory promiseHistories:{}", JSON.toJSONString(promiseHistories));
            return promiseHistories;
        }catch (Exception e){
            log.info("XfylHomeTestOrderDetailHandler handle queryPromiseHistory exception",e);
            return null;
        }
    }

    /**
     * queryMedicalPromiseList
     *
     * @param jdhPromise jdhPromise
     * @return {@link List}<{@link MedicalPromiseDTO}>
     */
    private List<MedicalPromiseDTO> queryMedicalPromiseList(JdhPromise jdhPromise){
        try{
            MedicalPromiseListRequest medPromiseRequest = new MedicalPromiseListRequest();
            medPromiseRequest.setPromiseId(jdhPromise.getPromiseId());
            medPromiseRequest.setItemDetail(Boolean.TRUE);
            medPromiseRequest.setPatientDetail(Boolean.TRUE);
            List<MedicalPromiseDTO> medicalPromiseList = medicalPromiseApplication.queryMedicalPromiseList(medPromiseRequest);
            log.info("XfylHomeTestOrderDetailHandler handle queryMedicalPromiseList medicalPromiseList:{}", JSON.toJSONString(medicalPromiseList));
            return medicalPromiseList;
        }catch (Exception e){
            log.info("XfylHomeTestOrderDetailHandler handle queryMedicalPromiseList exception",e);
            return null;
        }
    }

    /**
     * 处理检测单项目信息，订单详情的检测单信息是根据用户归堆展示的，每个用户是一个groupInfoList。
     *
     * @param viaFloorInfo  通用楼层信息
     */
    private void handleFooterMedicalPromise(ViaFloorInfo viaFloorInfo, Map<String, Object> sourceData, List<MedicalPromiseDTO> medicalPromiseList) {

        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        // 过滤已经冻结和作废的检测单
        medicalPromiseList = medicalPromiseList.stream().filter(e -> !Objects.equals(e.getFreeze(), YnStatusEnum.YES.getCode()))
                .filter(e -> !Objects.equals(e.getStatus(), MedicalPromiseStatusEnum.INVALID.getStatus())).collect(Collectors.toList());

        Map<String, List<MedicalPromiseDTO>> medicalPromiseMap =  medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getName));

        List<ViaGroupInfo> groups = Lists.newArrayList();
        medicalPromiseMap.forEach((name, list)->{
            ViaGroupInfo groupInfo = new ViaGroupInfo();
            groupInfo.setTitle(name);
            List<ViaFormItem> items = Lists.newArrayListWithExpectedSize(list.size());
            for (MedicalPromiseDTO medicalPromiseDTO : list) {
                ViaFormItem item = new ViaFormItem();
                item.setFormName(medicalPromiseDTO.getServiceItemName());
                item.setFormType(ViaFormTypeEnum.SPECIMEN_CODE.getType());
                item.setParamField(ViaFormTypeEnum.SPECIMEN_CODE.getType());
                item.setValue(ViaFormTypeEnum.SPECIMEN_CODE.getType());
                // 这个表单项除了value之外需要额外的属性，这个属性可能是前端需要，也可能是action需要
                Map<String ,Object> extMap = Maps.newHashMap();
                extMap.put("medicalPromiseId", medicalPromiseDTO.getMedicalPromiseId());
                extMap.put("promiseId", medicalPromiseDTO.getPromiseId());
                extMap.put("specimenCode", medicalPromiseDTO.getSpecimenCode());
                item.setExtMap(extMap);
                item.setPlaceholder("请扫码/输入采集管上的条码");
                item.setRequired(Boolean.TRUE);
                items.add(item);
            }
            groupInfo.setItems(items);
            groups.add(groupInfo);
        });

        viaFloorConfig.setGroupInfoList(groups);
        // 按钮初始化（提交样本信息按钮）
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        if (CollectionUtils.isNotEmpty(btnList)){
            Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
            while (btnInfoIterator.hasNext()){
                ViaBtnInfo btnInfo = btnInfoIterator.next();
                btnInfo.init(sourceData);
            }
        }
        log.info("XfylHomeTestOrderDetailHandler handleFooterMedicalPromise viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 处理采样教程楼层，当前只有自采样的业务需要采样教程。
     * 互医的场景可能有多个SKU，但是只取了一个SKU的采样教程，业务上保证互医渠道的SKU配置一个通用的采样教程。
     * 理想情况下采样教程应该取检测项目维度的。
     *
     * @param viaFloorInfo  通用楼层信息
     */
    private void handleFooterSampleCourse(ViaFloorInfo viaFloorInfo, Map<String, Object> sourceData, JdhPromise jdhPromise) {
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);

        Long serviceId = jdhPromise.getServices().get(0).getServiceId();
        JdhSkuRequest request = new JdhSkuRequest();
        request.setSkuId(serviceId);
        JdhSkuDto skuDto =  productApplication.queryAggregationJdhSkuInfo(request);
        skuDto.setTutorialUrl(skuDto.getTutorialUrl());
        sourceData.put(ProductAggregateCodeEnum.JDH_PRODUCT_SERVICE.getCode(), skuDto);
        ViaActionInfo action = viaFloorConfig.getAction();
        action.init(sourceData);
        log.info("XfylHomeTestOrderDetailHandler handleFooterSampleCourse viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * @param viaFloorInfo 通用楼层信息
     * @param jdhPromise      JD履约单
     */
    private void handleUserFeedbackInfo(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise) {
        if(JdhPromiseStatusEnum.COMPLETE.getStatus().equals(jdhPromise.getPromiseStatus())){
            log.info("UserServiceSurveyFloor.handleData 履约单状态已完成,匿名问卷楼层不展示!!!");
            viaFloorInfo.setRemove(true);
            return;
        }
        List<ViaFloorConfig> floorConfigList = new ArrayList<>();
        UserFeedbackAggregationDTO userFeedbackAggregationDTO = userFeedbackApplication.queryUserFeedback(UserFeedbackRequest.builder().userPin(jdhPromise.getUserPin()).promiseId(jdhPromise.getPromiseId()).businessScene("serviceStandardFeedback").build());
        if (Objects.nonNull(userFeedbackAggregationDTO)) {
            floorConfigList.add(ViaFloorConfig.builder().value(JSON.toJSONString(userFeedbackAggregationDTO)).build());
        }
        viaFloorInfo.setFloorConfigList(floorConfigList);
        log.info("XfylHomeTestOrderDetailHandler handleUserFeedbackInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return ViaPageEnum.HOME_ORDER_DETAIL.getScene() + "_" + BusinessModeEnum.ANGEL_TEST.getCode() + "_" + ServiceTypeEnum.TEST.getServiceType() + "_default";
    }

//    /**
//     * getProcessTrackMessage
//     * @param aggregateStatus
//     * @param promiseId
//     * @param businessMode
//     * @return
//     */
//    private String getProcessTrackMessage(String aggregateStatus,Long promiseId,String businessMode,Boolean isImmediately){
//        UserPromisegoBo userPromisegoBo = promiseGoRpcService.queryUserPromisego(
//                UserPromisegoRequestBo.builder()
//                        .aggregateStatus(aggregateStatus)
//                        .promiseId(promiseId)
//                        .businessMode(businessMode)
//                        .immediately(isImmediately)
//                        .build()
//        );
//        if (Objects.nonNull(userPromisegoBo)){
//            return userPromisegoBo.getCurrScript().getScriptContent();
//        }
//        return null;
//    }


    /**
     * getProcessTrackMessage
     * @param aggregateStatus
     * @param businessMode
     * @return
     */
    private String getProcessTrackMessage(String aggregateStatus,String businessMode,JdhPromise jdhPromise){
        UserPromisegoRequestBo userPromisegoRequestBo = UserPromisegoRequestBo.builder()
                .aggregateStatus(aggregateStatus)
                .promiseId(jdhPromise.getPromiseId())
                .businessMode(businessMode)
                .queryTermScript(Boolean.TRUE)
                .build();
        //时间
        PromiseAppointmentTime promiseAppointmentTime = jdhPromise.getAppointmentTime();
        if(Objects.nonNull(promiseAppointmentTime)){
            PromisegoRequestAppointmentTime appointmentTime = new PromisegoRequestAppointmentTime();
            appointmentTime.setDateType(promiseAppointmentTime.getDateType());
            appointmentTime.setImmediately(promiseAppointmentTime.getIsImmediately());
            appointmentTime.setAppointmentStartTime(promiseAppointmentTime.getAppointmentStartTime());
            appointmentTime.setAppointmentEndTime(promiseAppointmentTime.getAppointmentEndTime());
            userPromisegoRequestBo.setAppointmentTime(appointmentTime);
        }


        //地址
        PromiseStation store = jdhPromise.getStore();
        if(Objects.nonNull(store)){
            PromisegoRequestAddress address = PromisegoRequestAddress.builder()
                    .provinceId(store.getProvinceCode())
                    .cityId(store.getCityCode())
                    .countyId(store.getDistrictCode())
                    .townId(store.getTownCode())
                    .provinceName(store.getProvinceName())
                    .cityName(store.getCityName())
                    .countyName(store.getDistrictName())
                    .townName(store.getTownName())
                    .fullAddress(store.getStoreAddr())
                    .build();
            userPromisegoRequestBo.setAppointmentAddress(address);
        }

        UserPromisegoBo userPromisegoBo = promiseGoRpcService.queryUserPromisego(userPromisegoRequestBo);

        if (Objects.nonNull(userPromisegoBo) && Objects.nonNull(userPromisegoBo.getCurrScript())){
            return userPromisegoBo.getCurrScript().getScriptContent();
        }
        return null;
    }
}
