package com.jdh.o2oservice.application.report.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderEquipmentApplication;
import com.jdh.o2oservice.application.report.convert.ReportDataConvert;
import com.jdh.o2oservice.application.report.service.MedicalReportDataApplication;
import com.jdh.o2oservice.application.support.service.PdfApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.common.enums.AnswerTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.model.MedPromiseHistory;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.query.MedPromiseHistoryQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedPromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.QuickCheckThirdExportServiceRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.QuickCheckResultModifyBO;
import com.jdh.o2oservice.core.domain.report.bo.MedicalReportDataQueryBO;
import com.jdh.o2oservice.core.domain.report.model.AiAnswer;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportData;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportDataRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.core.domain.support.pdf.bo.IndicatorPdfBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.MedicalPromisePdfBo;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.provider.dto.ProviderEquipmentDto;
import com.jdh.o2oservice.export.report.cmd.ModifyCtValueRequest;
import com.jdh.o2oservice.export.report.cmd.ReportDataSaveCmd;
import com.jdh.o2oservice.export.report.dto.*;
import com.jdh.o2oservice.export.report.query.HoleSummaryRequest;
import com.jdh.o2oservice.export.report.query.MedicalReportDataRequest;
import com.jdh.o2oservice.export.report.query.ReportAnswerRequest;
import com.jdh.o2oservice.export.support.command.CreatePdfCmd;
import com.jdh.o2oservice.export.support.dto.PdfCreateDto;
import com.jdh.o2oservice.infrastructure.repository.db.dao.ReportDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/18
 */
@Service
@Slf4j
public class MedicalReportDataApplicationImpl implements MedicalReportDataApplication {


    /**
     * reportDataMapper
     */
    @Autowired
    private ReportDataMapper reportDataMapper;


    /**
     * 报告数据仓储层
     */
    @Autowired
    private MedicalReportDataRepository medicalReportDataRepository;

    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    @Autowired
    private ProviderEquipmentApplication providerEquipmentApplication;

    @Autowired
    private MedicalReportRepository medicalReportRepository;

    @Autowired
    private FileManageService fileManageService;

    @Autowired
    private QuickCheckThirdExportServiceRpc quickCheckThirdExportServiceRpc;

    @Autowired
    private ExecutorPoolFactory executorPoolFactory;

    @Autowired
    private PromiseApplication promiseApplication;

    @Autowired
    private DuccConfig duccConfig;

    @Autowired
    private MedPromiseHistoryRepository medPromiseHistoryRepository;

    @Autowired
    private PdfApplication pdfApplication;

    @Autowired
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    /**
     * 保存医疗报告数据。
     *
     * @param reportDataSaveCmd 报告数据保存命令对象，包含需要保存的医疗报告数据。
     * @return 保存操作是否成功。
     */
    @Override
    public Boolean saveMedicalReportData(ReportDataSaveCmd reportDataSaveCmd) {
        MedicalReportData medicalReportData = new MedicalReportData();
        medicalReportDataRepository.saveMedicalReportData(medicalReportData);
        return Boolean.TRUE;
    }


    /**
     * 查询报告原始数据
     *
     * @param request
     * @return
     */
    @Override
    public MedicalReportDataDTO queryMedicalReportData(MedicalReportDataRequest request) {
        MedicalReportDataQueryBO queryBO = ReportDataConvert.INSTANCE.convert(request);
        MedicalReportData medicalReportData = medicalReportDataRepository.queryMedicalReportData(queryBO);
        return ReportDataConvert.INSTANCE.convert(medicalReportData);
    }

    /**
     * 根据请求查询医疗报告数据列表
     *
     * @param medicalReportDataRequest 医疗报告数据请求对象
     * @return 医疗报告数据列表
     */
    @Override
    public List<MedicalReportDataDTO> queryMedicalReportDataList(MedicalReportDataRequest medicalReportDataRequest) {
        MedicalReportDataQueryBO queryBO = ReportDataConvert.INSTANCE.convert(medicalReportDataRequest);
        List<MedicalReportData> medicalReportData = medicalReportDataRepository.queryMedicalReportDataList(queryBO);
        List<MedicalReportDataDTO> medicalReportDataDTOS = ReportDataConvert.INSTANCE.convert(medicalReportData);
        if (CollectionUtils.isNotEmpty(medicalReportDataDTOS)){
            List<Long> medicalPromiseIds = medicalReportDataDTOS.stream().map(MedicalReportDataDTO::getMedicalPromiseId).collect(Collectors.toList());

            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().medicalPromiseIds(medicalPromiseIds).build());


            if (CollectionUtils.isNotEmpty(medicalPromises)){

                Map<Long, String> mpToName = medicalPromises.stream().collect(Collectors.toMap(MedicalPromise::getMedicalPromiseId, MedicalPromise::getServiceItemName));

                for (MedicalReportDataDTO medicalReportDataDTO : medicalReportDataDTOS) {
                    medicalReportDataDTO.setServiceItemName(mpToName.get(medicalReportDataDTO.getMedicalPromiseId()));
                }

            }



        }
        return medicalReportDataDTOS;
    }

    /**
     * 查询孔位概览
     *
     * @param request 洞口汇总请求对象
     * @return 洞口汇总DTO列表
     */
    @Override
    public List<HoleDTO> queryHoleSummary(HoleSummaryRequest request) {

        //1.查询检测单
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(request.getMedicalPromiseId()).build());
        log.info("MedicalReportDataApplicationImpl->medicalPromise,={}", JSONUtil.toJsonStr(medicalPromise));
        //2.查询设备孔位信息
        ProviderEquipmentDto providerEquipmentDto = providerEquipmentApplication.queryEquipmentDtoByStationIdAndItemId(medicalPromise.getStationId(), Long.valueOf(medicalPromise.getServiceItemId()));
        log.info("MedicalReportDataApplicationImpl->providerEquipmentDto,={}", JSONUtil.toJsonStr(providerEquipmentDto));

        //3，
        List<MedicalReportData> medicalReportData = medicalReportDataRepository.queryMedicalReportDataList(MedicalReportDataQueryBO.builder().experimentalId(request.getExperimentalId()).holeType(request.getHoleType()).build());
        log.info("MedicalReportDataApplicationImpl->medicalReportData,={}", JSONUtil.toJsonStr(medicalReportData));

        Map<String, List<MedicalReportData>> holeToRes = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(medicalReportData)){
            holeToRes = medicalReportData.stream().collect(Collectors.groupingBy(MedicalReportData::getHole));
        }

        List<HoleDTO> holeDTOList = Lists.newArrayList();

        for (int i = 0; i < providerEquipmentDto.getEquipmentHoleRows(); i++) {
            HoleDTO holeDTO = new HoleDTO();
            holeDTO.setExperimentalId(request.getExperimentalId());
            String letter = String.valueOf((char) ('A' + i));
            List<HoleSummaryDTO> holeSummaryDTOS = Lists.newArrayList();
            holeDTO.setHoleSummaryDTOS(holeSummaryDTOS);
            holeDTO.setRow(letter);
            for (int j = 0; j <providerEquipmentDto.getEquipmentHoleColumns(); j++) {
                String hole = letter+ (j+1);

                HoleSummaryDTO holeSummaryDTO = new HoleSummaryDTO();
                holeSummaryDTO.setHole(hole);

                List<MedicalReportData> medicalReportDataList = holeToRes.get(hole);
                if (CollectionUtils.isNotEmpty(medicalReportDataList)){
                    List<MedicalReportData> sort = medicalReportDataList.stream().filter(s->s.getStatus() != null).sorted(Comparator.comparing(MedicalReportData::getStatus).reversed()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(sort)) {
                        holeSummaryDTO.setSummaryStatus(sort.get(0).getStatus());
                    } else {
                        holeSummaryDTO.setSummaryStatus(-1);
                    }
                }else {
                    holeSummaryDTO.setSummaryStatus(CommonConstant.NEGATIVE_ONE);
                }
                holeSummaryDTOS.add(holeSummaryDTO);
            }
            holeDTOList.add(holeDTO);

        }

        log.info("MedicalReportDataApplicationImpl->holeDTOList,={}", JSONUtil.toJsonStr(holeDTOList));



        return holeDTOList;
    }

    /**
     * 查询报告判读
     *
     * @param request 报告答案请求对象
     * @return 报告答案摘要DTO
     */
    @Override
    public ReportAnswerSummaryDTO queryReportAnswer(ReportAnswerRequest request) {
        List<MedicalReportData> medicalReportDatas = medicalReportDataRepository.queryMedicalReportDataList(MedicalReportDataQueryBO.builder().experimentalId(request.getExperimentalId()).build());
        if (CollectionUtils.isEmpty(medicalReportDatas)){
            return null;
        }
        List<AiAnswer> aiAnswerList = Lists.newArrayList();

        for (MedicalReportData medicalReportData : medicalReportDatas) {
            if (StringUtil.isNotBlank(medicalReportData.getAiAnswer())){
                AiAnswer aiAnswer = JsonUtil.parseObject(medicalReportData.getAiAnswer(), AiAnswer.class);
                aiAnswerList.add(aiAnswer);
            }
        }

        if (CollectionUtils.isEmpty(aiAnswerList)){
            return null;
        }

        ReportAnswerSummaryDTO answerSummaryDTO = new ReportAnswerSummaryDTO();
        answerSummaryDTO.setAnswerType(CommonConstant.ONE);
        answerSummaryDTO.setAnswerTypeDesc(AnswerTypeEnum.getDescByType(answerSummaryDTO.getAnswerType()));
        List<AiAnswer> exceptionList = aiAnswerList.stream().filter(p -> StringUtil.equals(p.getAiResult(), "0")).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(exceptionList)){
            return null;
        }

        //判读状态概览，0.无异常，1.有异常
        answerSummaryDTO.setReportAnswerStatus(CommonConstant.ONE);
        List<AiReportAnswerDTO> aiReportAnswerDTOS = Lists.newArrayList();
        answerSummaryDTO.setAiReportAnswerDTOS(aiReportAnswerDTOS);
        Map<String, List<AiAnswer>> holeToAnswer = exceptionList.stream().collect(Collectors.groupingBy(AiAnswer::getHole));

        holeToAnswer.forEach((hole, aiAnswers) -> {
            AiReportAnswerDTO aiReportAnswerDTO = new AiReportAnswerDTO();
            aiReportAnswerDTO.setHole(hole);
            List<AiReportAnswerChannelDTO> answerChannelDTOS = Lists.newArrayList();
            aiReportAnswerDTO.setAiReportAnswerChannelDTOS(answerChannelDTOS);
            aiReportAnswerDTOS.add(aiReportAnswerDTO);
            for (AiAnswer aiAnswer : aiAnswers) {
                AiReportAnswerChannelDTO answerChannelDTO = new AiReportAnswerChannelDTO();
                BeanUtils.copyProperties(aiAnswer, answerChannelDTO);
                answerChannelDTOS.add(answerChannelDTO);
            }

        });
        return answerSummaryDTO;
    }

    /**
     * 修改CT值。
     *
     * @param modifyCtValueRequest 包含要修改的CT值的请求对象。
     * @return 修改操作是否成功。
     */
    @Override
    public Boolean modifyCtValue(ModifyCtValueRequest modifyCtValueRequest) {

        //查询
        List<MedicalReportData> medicalReportData = medicalReportDataRepository.queryMedicalReportDataList(
                MedicalReportDataQueryBO.builder()
                        .experimentalId(modifyCtValueRequest.getExperimentalId())
                        .hole(modifyCtValueRequest.getHole())
                        .channel(modifyCtValueRequest.getChannel())
                        .build()
        );
        log.info("MedicalReportDataApplicationImpl->medicalReportData,={}", JSONUtil.toJsonStr(medicalReportData));
        if (CollectionUtils.isEmpty(medicalReportData)){
            return false;
        }

        //更新
        for (MedicalReportData reportData : medicalReportData) {
            reportData.setValue(modifyCtValueRequest.getCtValue());

            //判端传过来的状态和原状态是否一致
            if (Objects.nonNull(modifyCtValueRequest.getStatus())){
                //原来正常，现在不正常，改为异常
                if (Objects.equals(CommonConstant.ZERO,reportData.getStatus())
                        && !Objects.equals(CommonConstant.ZERO,modifyCtValueRequest.getStatus())
                ){
                    reportData.setStatus(CommonConstant.TWO);
                }

                //原来不正常，现在正常，改为正常
                if (!Objects.equals(CommonConstant.ZERO,reportData.getStatus())
                        && Objects.equals(CommonConstant.ZERO,modifyCtValueRequest.getStatus())
                ){
                    reportData.setStatus(modifyCtValueRequest.getStatus());
                }
            }

        }
        medicalReportDataRepository.updateMedicalReportDataAudit(medicalReportData);

        //更新结构化中的数据
        MedicalReport medicalReport = medicalReportRepository.getByMedicalPromiseId(medicalReportData.get(0).getMedicalPromiseId());
        log.info("MedicalReportDataApplicationImpl->medicalReport,={}", JSONUtil.toJsonStr(medicalReport));
        if (Objects.isNull(medicalReport)){
            return false;
        }

        if (StringUtil.isBlank(medicalReport.getStructReportOss())){
            return false;
        }

        //获取结构化报告

        StructQuickReportContentDTO structReportOss = getStructReportOss(medicalReport.getStructReportOss());
        log.info("MedicalReportDataApplicationImpl->structReportOss,={}", JSONUtil.toJsonStr(structReportOss));
        if (Objects.isNull(structReportOss)){
            return false;
        }
        List<StructQuickReportResultDTO> reportResult = structReportOss.getReportResult();
        if (CollectionUtils.isEmpty(reportResult)){
            return false;
        }

        StructQuickReportResultDTO structQuickReportResultDTO = reportResult.get(0);
        List<StructQuickReportResultIndicatorDTO> indicators = structQuickReportResultDTO.getIndicators();
        if (CollectionUtils.isEmpty(indicators)){
            return false;
        }




        List<QuickCheckResultModifyBO> quickCheckResultModifyBOList = Lists.newArrayList();
        MedicalReportData medicalReportDataFirst = medicalReportData.get(0);

        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(medicalReportDataFirst.getMedicalPromiseId()).build());

        for (StructQuickReportResultIndicatorDTO indicator : indicators) {
            if (StringUtil.equals(medicalReportDataFirst.getIndicatorName(),indicator.getIndicatorName())){
                if (Objects.nonNull(indicator.getResultId())){
                    QuickCheckResultModifyBO quickCheckResultModifyBO = new QuickCheckResultModifyBO();
                    quickCheckResultModifyBO.setCtValue(modifyCtValueRequest.getCtValue());
                    quickCheckResultModifyBO.setResultId(indicator.getResultId() == null ? null : indicator.getResultId().toString());
                    quickCheckResultModifyBO.setMedicalPromiseId(medicalReportDataFirst.getMedicalPromiseId());
                    String[] split = medicalReportDataFirst.getExperimentalId().split("_");
                    quickCheckResultModifyBO.setTaskId(split[1]);
                    quickCheckResultModifyBO.setStationId(medicalPromise.getStationId());
                    quickCheckResultModifyBOList.add(quickCheckResultModifyBO);
                }
                indicator.setCtValue(modifyCtValueRequest.getCtValue());

                //如果为阴性

                if (Objects.nonNull(modifyCtValueRequest.getStatus())){
                    if (Objects.equals(CommonConstant.ZERO,modifyCtValueRequest.getStatus())){
                        indicator.setAbnormalType(CommonConstant.ZERO_STR);
                        indicator.setValue("阴性");
                        indicator.setValueDescription("阴性");
                    }else {
                        indicator.setAbnormalType(CommonConstant.THREE_STR);
                        indicator.setValue("阳性");
                        indicator.setValueDescription("阳性");
                    }
                }

            }
        }

        //上传结构化报告
        //如果是京东实验室端上传的报告，则需要京东自己改
//        if (jdStation){
        dealJdStationReport(medicalReport, structReportOss, indicators);
//        }

        if (CollectionUtils.isNotEmpty(quickCheckResultModifyBOList)){
            for (QuickCheckResultModifyBO quickCheckResultModifyBO : quickCheckResultModifyBOList) {
                Boolean res = quickCheckThirdExportServiceRpc.modifyCtValue(quickCheckResultModifyBO);
            }
        }


        return Boolean.TRUE;
    }

    /**
     * @param request
     * @return
     */
    @Override
    public Boolean judgeHoleSummaryNormal(HoleSummaryRequest request) {
        //1.查询检测单
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(request.getMedicalPromiseId()).build());
        log.info("judgeHoleSummaryNormal->medicalPromise,={}", JSONUtil.toJsonStr(medicalPromise));

        //2
        List<MedicalReportData> medicalReportData = medicalReportDataRepository.queryMedicalReportDataList(MedicalReportDataQueryBO.builder().experimentalId(request.getExperimentalId()).holeType(request.getHoleType()).build());
        log.info("judgeHoleSummaryNormal->medicalReportData,={}", JSONUtil.toJsonStr(medicalReportData));

        MedicalReportData errorData = medicalReportData.stream().filter(p -> StringUtil.isBlank(p.getValue()) || Objects.isNull(p.getStatus()) || StringUtil.isBlank(p.getXValue()) || StringUtil.isBlank(p.getYValue())).findFirst().orElse(null);
        if (Objects.nonNull(errorData)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;

    }

    /**
     * 处理京东实验室端报告，包括上传结构化报告和重新生成PDF。
     * @param medicalReport 医疗报告对象，包含结构化报告的信息。
     * @param structReportOss 结构化报告的OSS存储路径。
     * @param indicators 结构化报告的指标结果列表。
     */
    private void dealJdStationReport(MedicalReport medicalReport, StructQuickReportContentDTO structReportOss, List<StructQuickReportResultIndicatorDTO> indicators) {
        //1.上传结构化报告
        String[] split = medicalReport.getStructReportOss().split("\\/");
        String fileName = split[split.length-1];
        ByteArrayInputStream inputStream = new ByteArrayInputStream(JsonUtil.toJSONString(structReportOss).getBytes(StandardCharsets.UTF_8));
        fileManageService.put(fileName, inputStream, FileManageServiceImpl.FolderPathEnum.REPORT, null,Boolean.FALSE);
        try {
            inputStream.close();
        }catch (Exception e){
            log.info("dealJdStationReport->inputStream,close,error",e);
        }
        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(medicalReport.getMedicalPromiseId()).build());
        //2.重新生成PDF
        try {
            CreatePdfCmd createPdfCmd = new CreatePdfCmd();
            createPdfCmd.setTemplate("pdfCreateSelfTest");
            String pdfFileName = medicalPromise.getMedicalPromiseId() +"_"+DateUtil.format(new Date(),CommonConstant.YMDHMSSS2)+".pdf";
            createPdfCmd.setPdfName(pdfFileName);
            List<IndicatorPdfBo> indicatorPdfBos = org.assertj.core.util.Lists.newArrayList();
            MedicalPromisePdfBo medicalPromisePdfBo = new MedicalPromisePdfBo();
            medicalPromisePdfBo.setCheckTime(DateUtil.format(medicalPromise.getCheckTime(),CommonConstant.YMDHMS));
            medicalPromisePdfBo.setServiceItemName(medicalPromise.getServiceItemName());
            medicalPromisePdfBo.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
            PromiseDto byPromiseId = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(medicalPromise.getPromiseId()).build());
            if (Objects.nonNull(byPromiseId) ){
                if (CollectionUtils.isNotEmpty(byPromiseId.getPatients())){
                    List<PromisePatientDto> patients = byPromiseId.getPatients();
                    PromisePatientDto promisePatientDto = patients.stream().filter(p -> Objects.equals(p.getPromisePatientId(), medicalPromise.getPromisePatientId())).findFirst().orElse(null);
                    if (Objects.nonNull(promisePatientDto)){
                        medicalPromisePdfBo.setUserName(Objects.nonNull(promisePatientDto.getUserName()) ? promisePatientDto.getUserName().getName() : null);
                        medicalPromisePdfBo.setUserGenderStr(GenderEnum.getReportTextOfType(promisePatientDto.getGender()));
                        medicalPromisePdfBo.setAge(Objects.nonNull(promisePatientDto.getBirthday()) ? promisePatientDto.getBirthday().getAge() : null);
                    }
                }
            }
            medicalPromisePdfBo.setSpecimenCode(medicalPromise.getSpecimenCode());
            medicalPromisePdfBo.setStationName(medicalPromise.getStationName());
            medicalPromisePdfBo.setReportTime(DateUtil.format(new Date(),CommonConstant.YMDHMS));
            Map<String, String> stationTestCheck = duccConfig.getStationTestCheckUserConfig().get(medicalPromise.getStationId());
            StoreInfoBo storeInfoBo = providerStoreExportServiceRpc.queryByStoreId(medicalPromise.getStationId());

            if (Objects.nonNull(storeInfoBo)){
                medicalPromisePdfBo.setTestUser(storeInfoBo.getInspectorName());
                medicalPromisePdfBo.setCheckUser(storeInfoBo.getReviewerName());
            }
            if (StringUtil.isBlank(medicalPromisePdfBo.getCheckUser()) && StringUtil.isBlank(medicalPromisePdfBo.getTestUser())){
                medicalPromisePdfBo.setTestUser(MapUtils.isNotEmpty(stationTestCheck) ? stationTestCheck.get("testUser") : null);
                medicalPromisePdfBo.setCheckUser(MapUtils.isNotEmpty(stationTestCheck) ? stationTestCheck.get("checkUser") : null);
            }

            MedPromiseHistoryQuery medPromiseHistoryQuery = new MedPromiseHistoryQuery();
            medPromiseHistoryQuery.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
            medPromiseHistoryQuery.setAfterStatus(2);
            List<MedPromiseHistory> medPromiseHistories = medPromiseHistoryRepository.queryMedPromiseHistoryList(medPromiseHistoryQuery);
            if (CollectionUtils.isNotEmpty(medPromiseHistories)){
                medPromiseHistories.sort((order1,order2)->order2.getCreateTime().compareTo(order1.getCreateTime()));
                medicalPromisePdfBo.setSampleTime(DateUtil.format(medPromiseHistories.get(0).getCreateTime(),CommonConstant.YMDHMS));
            }
            for (StructQuickReportResultIndicatorDTO indicatorDTO : indicators){
                IndicatorPdfBo indicatorPdfBo = new IndicatorPdfBo();
                indicatorPdfBo.setIndicatorName(indicatorDTO.getIndicatorName());
                indicatorPdfBo.setUnit(indicatorDTO.getUnit());
                indicatorPdfBo.setAbnormalType(indicatorDTO.getAbnormalType());
                indicatorPdfBo.setAdvice(indicatorDTO.getAdvice());
                indicatorPdfBo.setValue(indicatorDTO.getValue());
                indicatorPdfBo.setNormalRangeValue(indicatorDTO.getNormalRangeValue());
                indicatorPdfBo.setValueDescription(indicatorDTO.getValueDescription());
                indicatorPdfBo.setCtValue(indicatorDTO.getCtValue());
                indicatorPdfBo.setReferenceRangeValue(indicatorDTO.getReferenceRangeValue());
                indicatorPdfBos.add(indicatorPdfBo);
                medicalPromisePdfBo.setIndicatorPdfBos(indicatorPdfBos);
            }
            medicalPromisePdfBo.setSpecimenCode(medicalPromise.getSpecimenCode());
            createPdfCmd.setPdfInfo(JsonUtil.toJSONString(medicalPromisePdfBo));
            createPdfCmd.setUploadToOss(Boolean.TRUE);
            JSONObject configObj = JSON.parseObject(duccConfig.getReportPdfConfig());
            if (configObj.getBoolean("pdfCreateSelfTestV2Switch")){
                createPdfCmd.setTemplate("pdfCreateSelfTestV2");
            }
            PdfCreateDto pdf = pdfApplication.createPdf(createPdfCmd);
            log.info("modifyCtValue,createPdf->pdf={}",JsonUtil.toJSONString(pdf));
            if (pdf.getResult()){
                medicalReport.setReportOss(pdf.getOssPath());
                medicalReportRepository.updateByCondition(medicalReport);
            }

        }catch (Exception e){
            log.info("modifyCtValue,createPdfError,medicalPromiseId = {}",String.valueOf(medicalPromise.getMedicalPromiseId()),e);
        }
    }


    /**
     * 从指定的 OSS URL 获取结构化报告内容并解析为 StructQuickReportContentDTO 对象。
     * @param url 指定的 OSS URL
     * @return 解析后的 StructQuickReportContentDTO 对象，若 URL 无效或解析失败则返回 null
     */
    private StructQuickReportContentDTO getStructReportOss(String url){
        if (StringUtils.isBlank(url)){
            return null;
        }
        InputStream inputStream = fileManageService.get(url);

        try(BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream))) {
            String structReportJson = bufferedReader.lines().collect(Collectors.joining("\n"));

            if (StringUtils.isNotBlank(structReportJson)) {
                StructQuickReportContentDTO structQuickReportContentDTO = JSON.parseObject(structReportJson, StructQuickReportContentDTO.class);
                return structQuickReportContentDTO;
            }

        }catch (Exception e){
            log.info("getStructReportOss error,url={}",url,e);
        }
//        String structReportJson = new BufferedReader(new InputStreamReader(inputStream)).lines().collect(Collectors.joining("\n"));
//        if (StringUtils.isNotBlank(structReportJson)) {
//            StructQuickReportContentDTO structQuickReportContentDTO = JSON.parseObject(structReportJson, StructQuickReportContentDTO.class);
//            try {
//                inputStream.close();
//            }catch (Exception e){
//                log.info("MedicalReportEventSubscriber->getStructReportOss,url={},close error",url,e);
//            }
//            return structQuickReportContentDTO;
//        }
        return null;
    }

    public  boolean isZero(String s) {
        try {
            return Double.parseDouble(s) == 0.0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

}
