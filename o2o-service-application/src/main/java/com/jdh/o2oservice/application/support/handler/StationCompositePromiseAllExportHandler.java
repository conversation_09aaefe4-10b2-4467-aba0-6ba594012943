package com.jdh.o2oservice.application.support.handler;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.provider.service.ProviderQueryApplication;
import com.jdh.o2oservice.base.util.PageInfoUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.base.model.PhoneNumber;
import com.jdh.o2oservice.base.model.UserName;
import com.jdh.o2oservice.core.domain.support.file.context.FileExportContext;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.service.AbstractFileExportHandler;
import com.jdh.o2oservice.export.provider.dto.StationCompositeMedicalPromiseDTO;
import com.jdh.o2oservice.export.provider.query.PageCompositeMedicalPromiseRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.BlockingQueue;

/**
 * 实验室端全部检测单导出
 * @author: yangxiyu
 * @date: 2024/3/20 5:31 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class StationCompositePromiseAllExportHandler extends AbstractFileExportHandler {
    @Resource
    private ProviderQueryApplication providerQueryApplication;

    @Override
    public String getMapKey() {
        return FileExportTypeEnum.STATION_COMPOSITE_PROMISE_ALL_EXPORT.getType();
    }


    /**
     * 分页查询预约单数据
     * @param ctx
     */
    @Override
    protected void preHandle(FileExportContext ctx) {

    }


    @Override
    protected void getData(FileExportContext ctx) {

        Map<String, Object> queryParam = ctx.getQueryParam();
        String json = JSON.toJSONString(queryParam);
        PageCompositeMedicalPromiseRequest request = JSON.parseObject(json, PageCompositeMedicalPromiseRequest.class);
        request.setPageNum(1);
        request.setPageSize(500);
        int count = 0;
        BlockingQueue<Map<String, Object>> queue = ctx.getQueue();
        PageDto<StationCompositeMedicalPromiseDTO> page;
        do {
            page =  providerQueryApplication.pageCompositeMedicalPromise(request);

            if (!PageInfoUtil.isEmpty(page)){
                for (StationCompositeMedicalPromiseDTO record : page.getList()) {
                    Map<String, Object> column = new HashMap<>();
                    if (StringUtils.isNotBlank(record.getTransferName())){
                        record.setTransferName(UserName.decrypt(record.getTransferName()));
                    }
                    if (StringUtils.isNotBlank(record.getTransferPhoneEncrypt())){
                        record.setTransferPhone(PhoneNumber.decrypt(record.getTransferPhoneEncrypt()));
                    }
                    column.put("compositeMedicalPromise", record);
                    queue.add(column);
                }
                count += page.getList().size();
            }

            request.setPageNum(request.getPageNum() + 1);
        }while (!PageInfoUtil.isEmpty(page) && page.getPageNum() < page.getTotalPage());
        log.info("StationCompositePromiseAllExportHandler->getData totalCount={}", count);
        ctx.addTail();
    }
}
