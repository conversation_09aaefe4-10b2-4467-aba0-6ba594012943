package com.jdh.o2oservice.application.angelpromise.event;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.angelpromise.AngelWorkStatusApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.*;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.AngelWorkStatusEventEnum;
import com.jdh.o2oservice.common.enums.DispatchDetailTypeEnum;
import com.jdh.o2oservice.core.domain.angel.context.InsureNurseContext;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NurseVisitPolicyBo;
import com.jdh.o2oservice.core.domain.angel.service.AngelDomainService;
import com.jdh.o2oservice.core.domain.angelpromise.assembler.JdhAngelWorkAssembler;
import com.jdh.o2oservice.core.domain.support.rpc.AngelRealTrackRpc;
import com.jdh.o2oservice.core.domain.support.rpc.bo.AngelLocationBo;
import com.jdh.o2oservice.core.domain.angelpromise.bo.AngelTaskExtStateBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskExtStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelTaskStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelWorkStatusContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelWorkEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.factory.JdhAngelTaskFactory;
import com.jdh.o2oservice.core.domain.angelpromise.factory.JdhAngelWorkFactory;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelWorkPolicyIdCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelTaskDomainService;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.core.domain.support.rpc.param.AngelLocationRealParam;
import com.jdh.o2oservice.export.angelpromise.cmd.AngelCheckBarCodeCmd;
import com.jdh.o2oservice.export.angelpromise.cmd.AngelWorkExecuteCmd;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkStatusDto;
import com.jdh.o2oservice.export.dispatch.cmd.DispatchAngelDetail;
import com.jdh.o2oservice.export.dispatch.cmd.DispatchCallbackCmd;
import com.jdh.o2oservice.export.dispatch.cmd.ReDispatchCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName:AngelWorkEventConsumer
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/19 20:40
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AngelWorkEventConsumer {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * 修改派单状态application
     */
    @Resource
    private DispatchApplication dispatchApplication;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelTaskRepository angelTaskRepository;

    @Resource
    private AngelTaskDomainService angelTaskDomainService;

    @Resource
    private AngelTaskHistoryRepository angelTaskHistoryRepository;

    @Resource
    private AngelWorkHistoryRepository angelWorkHistoryRepository;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private AngelWorkStatusApplication angelWorkStatusApplication;

    @Resource
    private AngelDomainService angelDomainService;

    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Resource
    private AngelShipRepository angelShipRepository;

    @Resource
    private AngelWorkApplication angelWorkApplication;

    @Resource
    private AngelRealTrackRpc angelRealTrackRpc;

    @PostConstruct
    public void registerEventConsumer() {
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_INIT,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "workInit", this::handleWorkInit, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_RECEIVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "callBackDispatch", this::handleWorkReceive, Boolean.TRUE, Boolean.FALSE,
                        EventConsumerRetryTemplate.exponentialRetryInstance(3, 1000, 2.0, 1000)));

        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_WAITING_SERVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "waitServedStatus", this::handleWorkPrepareStatus, Boolean.FALSE, Boolean.FALSE));

        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_DONE_SERVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "workDoneServe", this::handleWorkDoneServe, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_CANCEL_SERVED,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "workCancel", this::handleWorkCancel, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_B_CANCEL,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "workCancelMan", this::handleWorkManCancel, Boolean.TRUE, Boolean.FALSE));

        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_IN_DELIVERY,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "workShipJudge", this::judgeWorkShip, Boolean.TRUE, Boolean.FALSE,
                        EventConsumerRetryTemplate.exponentialRetryInstance(3, 1000, 2.0, 1000))
        );

        //=======>>>>>> 服务者工单完成 触发录音文件解析
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_FINISH_SERVED, WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE,
                "parseSound", this::parseSound, Boolean.FALSE, Boolean.FALSE));
    }

    private void parseSound(Event event){
    }

    private void handleWorkDoneServe(Event event) {
        log.info("AngelWorkEventConsumer -> handleWorkDoneServe,处理工单上门结束!event={}", JSON.toJSONString(event));
        AngelWork angelWork = getAngelWork(event);
        //执行工单配送中
        if(angelWork.isToHomeTest()){
            try{
                AngelCheckBarCodeCmd cmd = new AngelCheckBarCodeCmd();
                cmd.setWorkId(angelWork.getWorkId());
                cmd.setUserPin(angelWork.getAngelPin());
                angelWorkApplication.confirmTransferOrderDeliver(cmd);
            }catch (Exception ex) {
                log.error("AngelWorkEventConsumer -> handleWorkDoneServe, 模拟确认工单配送中失败!", ex);
            }
        }
        log.info("AngelWorkEventConsumer -> handleWorkDoneServe,处理工单上门结束完成!");
    }

    /**
     * 服务者履约出门状态变更
     * @param event
     */
    private void handleWorkPrepareStatus(Event event) {
        log.info("[AngelWorkEventConsumer.handleWorkPrepareStatus],护士出门了!");
        //body内容: {"angelId":"156491772854887","jdOrderId":299196507906,"promiseId":158744512037466,"sourceId":158744513085847,"workId":158744967643225}
        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setWorkId(Long.valueOf(event.getAggregateId()));
        List<AngelTask> taskList = angelTaskRepository.findList(angelTaskDBQuery);
        if(CollectionUtils.isEmpty(taskList)){
            log.error("[AngelWorkEventConsumer.handleWorkPrepare],任务单信息不存在!");
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_TASK_NOT_EXIST);
        }

        //更新任务状态
        List<AngelTaskExtStateBo> angelTaskExtStateBoList = Lists.newArrayList();
        taskList.stream().forEach(task -> {
            AngelTaskExtStateBo extStateBo = AngelTaskExtStateBo.builder()
                    .taskId(task.getTaskId())
                    .taskExtStatus(AngelBizExtStatusEnum.ANGEL_GOUT_OUT.getType())
                    .build();
            angelTaskExtStateBoList.add(extStateBo);
        });
        AngelTaskExtStatusContext angelTaskExtStatusContext = AngelTaskExtStatusContext.builder()
                .angelTaskExtStateBoList(angelTaskExtStateBoList)
                .angelTaskEventTypeEnum(AngelTaskBizExtEventTypeEnum.ANGEL_TASK_EXT_EVENT_OUT)
                .workId(Long.valueOf(event.getAggregateId()))
                .build();

        angelTaskDomainService.executeTaskExt(angelTaskExtStatusContext);
        executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
            //给护士投保
            InsureNurseContext insureNurseContext = JSON.parseObject(event.getBody(),InsureNurseContext.class);
            AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(insureNurseContext.getWorkId()).build());
            if(Objects.isNull(angelWork) || Objects.equals(angelWork.getWorkType(), AngelWorkTypeEnum.RIDER.getType())) {
                log.info("[AngelWorkEventConsumer.handleWorkPrepareStatus], 工单不存在或者非护士类型工单，不需要投保!");
                return;
            }
            //维护护士出门时间
            try {
                insureNurseContext.setServiceDepartureTime(DateUtils.parseDate(TimeUtils.localDateTimeToStr(event.getPublishTime(), TimeFormat.LONG_PATTERN_LINE),CommonConstant.YMDHMS));
            } catch (ParseException e) {
                log.info("[AngelWorkEventConsumer.handleWorkPrepareStatus] 时间转换异常");
            }
            //维护服务项目
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(insureNurseContext.getPromiseId()).build());
            if(CollectionUtils.isNotEmpty(medicalPromises)){
                insureNurseContext.setServiceProjects(medicalPromises.stream().map(MedicalPromise::getServiceItemName).collect(Collectors.toList()));
            }
            insureNurseContext.setCreateTime(angelWork.getCreateTime());
            insureNurseContext.setServicePlace(angelWork.getJdhAngelWorkExtVo().getAngelOrder().getFullAddress());
            NurseVisitPolicyBo nurseVisitPolicyBo = angelDomainService.insureNurses(insureNurseContext);
            if(nurseVisitPolicyBo!=null&&StringUtils.isNotBlank(nurseVisitPolicyBo.getPolicyId())){
                AngelWorkPolicyIdCmd angelWorkPolicyIdCmd = new AngelWorkPolicyIdCmd();
                angelWorkPolicyIdCmd.setInsureId(nurseVisitPolicyBo.getPolicyId());
                angelWorkPolicyIdCmd.setWorkId(insureNurseContext.getWorkId());
                angelWorkRepository.updatePolicyId(angelWorkPolicyIdCmd);
            }
        });
    }

    /**
     * 工单初始化
     *
     * @param event
     */
    private void handleWorkInit(Event event) {
        log.info("[AngelWorkEventConsumer.handleWorkInit],工单初始化完成!event={}", JSON.toJSONString(event));
        AngelWork angelWork = getAngelWork(event);

        //护士上门推送接单事件
        if(angelWork.isGoHomeMode()){
            AngelWorkEventBody angelWorkEventBody = JdhAngelWorkAssembler.ins.convertToAngelWorkEventBody(angelWork);
            if(Objects.nonNull(angelWork.getJdhAngelWorkExtVo()) && Objects.nonNull(angelWork.getJdhAngelWorkExtVo().getAngelCharge())){
                angelWorkEventBody.setDispatchMarkupPrice(angelWork.getJdhAngelWorkExtVo().getAngelCharge().getDispatchMarkupPrice());
                angelWorkEventBody.setOrderSettleAmount(angelWork.getJdhAngelWorkExtVo().getAngelCharge().getOrderSettleAmount());
            }
            Event publishEvent = EventFactory.newDefaultEvent(angelWork, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_RECEIVED, angelWorkEventBody);
            eventCoordinator.publish(publishEvent);
        }

        //查询服务者位置信息
        AngelLocationRealParam angelLocationRealParam = new AngelLocationRealParam();
        angelLocationRealParam.setAngelId(angelWork.getAngelId());
        angelLocationRealParam.setWorkId(angelWork.getWorkId());
        angelLocationRealParam.setPromiseId(angelWork.getPromiseId());
        AngelLocationBo angelLocationBo = angelRealTrackRpc.queryAngelLocationRealTrack(angelLocationRealParam);

        //历史数据入库
        saveWorkHistory(angelWork, angelLocationBo, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_INIT);
        saveTaskHistory(angelWork, AngelTaskStatusEnum.INIT);

        log.info("[AngelWorkEventConsumer.handleWorkInit],工单初始化完成!");
    }

    /**
     * 任务单历史数据
     *
     */
    private void saveTaskHistory(AngelWork angelWork, AngelTaskStatusEnum angelTaskStatusEnum) {
        AngelTaskDBQuery angelTaskDBQuery = new AngelTaskDBQuery();
        angelTaskDBQuery.setWorkId(angelWork.getWorkId());
        List<AngelTask> angelTasks = angelTaskRepository.findList(angelTaskDBQuery);
        AngelTaskStatusContext angelTaskStatusContext = AngelTaskStatusContext.builder()
                .angelTaskEventTypeEnum(AngelTaskEventTypeEnum.ANGEL_TASK_EVENT_WAITING_SERVED)
                .workId(angelWork.getWorkId())
                .build();
        angelTaskStatusContext.setAngelTaskList(angelTasks);
        List<AngelTaskHistory> history = JdhAngelTaskFactory.createEventHistory(angelTaskStatusContext, angelTaskStatusEnum.getType());
        angelTaskHistoryRepository.batchSave(history);
    }

    /**
     * 工单历史数据
     *
     * @return
     */
    private void saveWorkHistory(AngelWork angelWork, AngelLocationBo angelLocationBo, AngelWorkEventTypeEnum angelWorkEventTypeEnum) {
        AngelWorkStatusContext workHisContext = AngelWorkStatusContext.builder()
                .angelLocationBo(angelLocationBo)
                .angelWorkEventTypeEnum(angelWorkEventTypeEnum)
                .eventCode(angelWorkEventTypeEnum.getCode())
                .workStatus(angelWork.getWorkStatus())
                .angelId(angelWork.getAngelId())
                .cPin(angelWork.getAngelPin())
                .build();
        angelWork.setWorkStatus(AngelWorkStatusEnum.INIT.getType());
        workHisContext.setAngelWork(angelWork);
        AngelWorkHistory angelWorkHistory = JdhAngelWorkFactory.createHistory(workHisContext);
        angelWorkHistoryRepository.save(angelWorkHistory);
    }

    /**
     * 工单取消
     * 1、非骑手模式下的工单工单取消后重新派单
     *
     * @param event
     */
    public void handleWorkCancel(Event event) {
        log.info("[AngelWorkEventConsumer.handleWorkCancel],工单取消了!");
        AngelWork angelWork = getAngelWork(event);
        if (Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())) {
            log.info("[AngelWorkEventConsumer.handleWorkReceive],护士单不需要重新派单!workId={}", angelWork.getWorkId());
            return;
        }
        ReDispatchCmd cmd = new ReDispatchCmd();
        cmd.setDispatchId(angelWork.getSourceId());
        cmd.setPromiseId(angelWork.getPromiseId());
        cmd.setOperator(angelWork.getAngelId());
        cmd.setRoleType(5);
        cmd.setVerticalCode(angelWork.getVerticalCode());
        cmd.setServiceType(angelWork.getServiceType());
        dispatchApplication.reDispatch(cmd);
    }

    /**
     * 运营端端取消工单
     *
     * @param event
     */
    private void handleWorkManCancel(Event event) {
        log.info("[AngelWorkEventConsumer.handleWorkManCancel],工单被运营端取消了!");
        AngelWork angelWork = getAngelWork(event);
        if (Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())) {
            log.info("[AngelWorkEventConsumer.handleWorkReceive],护士单不需要重新派单!workId={}", angelWork.getWorkId());
            return;
        }
        log.info("[AngelWorkEventConsumer.handleWorkManCancel],工单被运营端取消了,end!");
    }

    /**
     * 处理接单
     * 1、如果是骑手的工单需要回调派单需改状态为已接单
     *
     * @param event
     */
    private void handleWorkReceive(Event event) {
        log.info("[AngelWorkEventConsumer.handleWorkReceive],start");
        AngelWork angelWork = getAngelWork(event);
        if (!Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())) {
            log.info("[AngelWorkEventConsumer.handleWorkReceive],非骑手工单不需要同步变更工单状态!workId={}", angelWork.getWorkId());
            return;
        }

        //推送派单状态未接单
        DispatchCallbackCmd cmd = new DispatchCallbackCmd();
        cmd.setDispatchId(angelWork.getSourceId());
        cmd.setDispatchStatus(JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus());

        List<DispatchAngelDetail> angelDetailList = Lists.newArrayList();
        DispatchAngelDetail angelDetail = new DispatchAngelDetail();
        angelDetail.setAngelId(StringUtils.isNotBlank(angelWork.getAngelId()) ? Long.valueOf(angelWork.getAngelId()) : null);
        angelDetail.setAngelName(angelWork.getAngelName());
        angelDetail.setDispatchDetailType(DispatchDetailTypeEnum.ASSIGN.getType());
        angelDetailList.add(angelDetail);

        cmd.setAngelDetailList(angelDetailList);
        cmd.setPromiseId(angelWork.getPromiseId());
        dispatchApplication.callBack(cmd);

        //工单状态流转到已出门
        log.info("[AngelWorkEventConsumer.handleWorkReceive],骑手接单执行工单状态到已出门！");
        AngelWorkExecuteCmd workCmd = new AngelWorkExecuteCmd();
        workCmd.setWorkId(String.valueOf(angelWork.getWorkId()));
        workCmd.setEventCode(AngelWorkStatusEventEnum.ANGEL_WORK_WAITING_SERVED.getEventCode());
        workCmd.setOperator("system");
        AngelWorkStatusDto angelWorkStatusDto = angelWorkStatusApplication.executeAngelWork(workCmd);
        log.info("[AngelWorkEventConsumer.handleWorkReceive],骑手接单执行工单状态到已出门结果！angelWorkStatusDto={}", JSON.toJSONString(angelWorkStatusDto));
        log.info("[AngelWorkEventConsumer.handleWorkReceive],end");
    }

    /**
     * 获取工单
     *
     * @param event
     * @return
     */
    private AngelWork getAngelWork(Event event) {
        String aggregateId = event.getAggregateId();
        if (StringUtils.isBlank(aggregateId)) {
            log.error("[AngelWorkEventConsumer.handleWorkReceive],事件参数异常!");
            throw new BusinessException(AngelPromiseBizErrorCode.EVENT_INFO_ERROR);
        }

        AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(Long.valueOf(aggregateId)));
        if (Objects.isNull(angelWork)) {
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }

        return angelWork;
    }

    /**
     * 判断工单是否全部配送中
     * @param event
     */
    private void judgeWorkShip(Event event){
        log.info("judgeWorkShip->start");
        String shipIdStr = event.getAggregateId();
        //查运单
        AngelShip angelShip = angelShipRepository.find(AngelShipIdentifier.builder().shipId(Long.valueOf(shipIdStr)).build());
        //查工单
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());
        if(AngelWorkStatusEnum.SERVICED.getType().equals(angelWork.getWorkStatus())) {
            try{
                AngelCheckBarCodeCmd cmd = new AngelCheckBarCodeCmd();
                cmd.setWorkId(angelShip.getWorkId());
                cmd.setUserPin(angelWork.getAngelPin());
                angelWorkApplication.confirmTransferOrderDeliver(cmd);
            }catch (Exception ex) {
                log.error("AngelWorkEventConsumer -> judgeWorkShip, 模拟确认工单配送中失败!", ex);
            }
        }else {
            log.info("judgeWorkShip工单的状态不允许执行配送中!angelWork={}", JSON.toJSONString(angelWork));
        }
    }
}
