package com.jdh.o2oservice.export.settlement.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName AngelAccountMoneySummaryRequest
 * @Description
 * <AUTHOR>
 * @Date 2025/9/3 20:41
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelAccountMoneySummaryRequest {

    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * pin
     */
    private String userPin;
}