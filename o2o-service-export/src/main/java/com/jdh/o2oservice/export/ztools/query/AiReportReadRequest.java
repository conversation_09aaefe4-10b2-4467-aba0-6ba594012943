package com.jdh.o2oservice.export.ztools.query;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description Ai解读
 */
@Data
public class AiReportReadRequest {

    private Integer version;//版本号

    private String keyWordFomart="以下是一位%s%s用户的检测报告结果，请给予报告结果解读和针对性建议，限制在 100 个字内，给出最重要最精华的内容，相关报告数据内容和患者的信息，我都会单独呈现给用户，无需重复说明；避免一些非常常规的建议，考虑用户性别和年龄，尤其是老人和幼童，给出有用、易于执行，有利于改善症状的偏物理方法，生活技巧的建议，可以推荐用药。该解读面向用户，请让用户觉得眼前一亮，觉得你的建议有用，不是敷衍。并给予总领全局的总结性建议来引申出这些具体建议：%s";//prompt
}
