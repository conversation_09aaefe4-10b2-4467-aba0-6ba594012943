package com.jdh.o2oservice.export.settlement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName AngelSettlementDateSummaryDto
 * @Description
 * <AUTHOR>
 * @Date 2025/9/12 14:22
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelSettlementDateSummaryDto {

    /**
     * 入账月份，例如“2025-08-01”
     */
    private String date;

    /**
     * 结算明细分页数据
     */
    private List<AngelSettlementDto> settleDetailList;
}