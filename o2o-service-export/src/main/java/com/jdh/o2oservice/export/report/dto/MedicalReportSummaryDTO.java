package com.jdh.o2oservice.export.report.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-26 17:09
 * @Desc : 报告汇总
 */
@Data
public class MedicalReportSummaryDTO implements Serializable {

    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 检测项报告
     */
    private List<PromiseMedicalReportDTO> reportDtoList;

    /**
     * 异常指标
     */
    private List<ReportIndicatorDTO> allAbnormalIndicatorDTOList;

    private Boolean openSickCert=false;//是否可开具病假单

    private Date sickCertEndDay;//开具病假单截止时间

    /**
     * 正常指标
     */
    private List<ReportIndicatorDTO> normalIndicatorDTOList;

}
