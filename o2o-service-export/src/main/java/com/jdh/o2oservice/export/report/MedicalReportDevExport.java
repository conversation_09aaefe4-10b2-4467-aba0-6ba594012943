package com.jdh.o2oservice.export.report;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.report.cmd.*;
import com.jdh.o2oservice.export.report.query.MedicalReportIndicatorRateFromEsRequest;

/**
 * MedicalReportDevExport
 * <AUTHOR>
 * @date 2024-11-14 10:14
 */
public interface MedicalReportDevExport {



    /**
     * 刷新医疗报告指标的状态。
     * @param medicalReportIndicatorFlushCmd 包含要刷新的医疗报告指标信息的命令对象。
     * @return 如果成功刷新指标状态，则返回 true；否则返回 false。
     */
    Boolean flushReportIndicatorId(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd);



    /**
     * 刷新医疗报告指标。
     * @param medicalReportIndicatorFlushCmd 包含刷新操作的命令对象。
     * @return 是否成功刷新指标。
     */
    Boolean flushReportIndicator(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd);


    /**
     * 刷新医疗报告指标的检查时间。
     * @param medicalReportIndicatorFlushCmd 包含需要刷新的医疗报告指标信息的命令对象。
     * @return 如果成功刷新了检查时间，则返回 true；否则返回 false。
     */
    Boolean flushCheckTime(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd);


    /**
     * 修复结构化报告。
     * @param fixStructReportCmd 修复结构化报告的命令对象。
     * @return 修复操作是否成功。
     */
    Response<Boolean> fixStructReport(FixStructReportCmd fixStructReportCmd);


    /**
     * 模拟预约单完成事件(人+sku)。
     * @param promisePatientSkuFinishCmd 包含完成承诺所需信息的命令对象。
     * @return 操作结果，true表示成功，false表示失败。
     */
    Response<Boolean> mockPromisePatientSkuFinish(PromisePatientSkuFinishCmd promisePatientSkuFinishCmd);


    /**
     * 刷新报告中心ID
     * @param fixStructReportCmd 固定结构的报告命令对象
     * @return 操作结果，true表示成功，false表示失败
     */
    Response<Boolean> flushReportCenterId(FixStructReportCmd fixStructReportCmd);


    /**
     * 通过检测单同步报告中心的数据。
     * @param fixStructReportCmd 报告中心同步命令对象，包含了同步所需的信息。
     * @return 同步操作的结果，true表示成功，false表示失败。
     */
    Response<Boolean> syncReportCenterByMp(FixStructReportCmd fixStructReportCmd);


    Response<Boolean> flushReportDrugConfig(FlushReportDrugConfigCmd flushReportDrugConfigCmd);


    Response<String> getObjectMetadata(String allPathFileName,String bucket);


    /**
     * 修复空白 JPG 图片。
     * @param fixStructReportCmd 包含修复信息的命令对象。
     * @return 修复操作是否成功。
     */
    Response<Boolean> fixEmptyJpg(FixStructReportCmd fixStructReportCmd);

    /**
     * 校验
     * @param angelStationId
     * @return
     */
    Response<Boolean> checkStationSkuItemConfig(Long angelStationId);


    /**
     * 刷新报告ct值
     * @param fixStructReportCmd 报表命令结构体，包含报表的相关信息。
     * @return 是否成功刷新报表计数器值。
     */
    Response<Boolean> flushReportCtValue(FixStructReportCmd fixStructReportCmd);


    /**
     * 同步项目和指标
     * @param syncServiceItemIndicatorCmd
     * @return
     */
    Response<Boolean> syncServiceItemIndicator(SyncServiceItemIndicatorCmd syncServiceItemIndicatorCmd);

    /**
     * 刷新医疗报告指标。
     * @param medicalReportIndicatorFlushCmd 包含刷新操作的命令对象。
     * @return 是否成功刷新指标。
     */
    Response<Boolean> flushReportIndicatorIdOnly(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd);

    /**
     * 刷新医疗报告指标。
     * @param medicalReportIndicatorFlushToEsCmd 包含刷新操作的命令对象。
     * @return 是否成功刷新指标。
     */
    Response<Boolean> flushReportIndicatorsToEs(MedicalReportIndicatorFlushToEsCmd medicalReportIndicatorFlushToEsCmd);

    /**
     * 刷新医疗报告指标。
     * @param request 包含刷新操作的命令对象。
     * @return 是否成功刷新指标。
     */
    Response<Object> getRateIndicator(MedicalReportIndicatorRateFromEsRequest request);

}
