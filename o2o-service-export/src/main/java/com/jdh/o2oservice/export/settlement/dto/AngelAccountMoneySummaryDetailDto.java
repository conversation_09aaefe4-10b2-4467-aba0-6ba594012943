package com.jdh.o2oservice.export.settlement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName AngelAccountMoneyDetailDto
 * @Description
 * <AUTHOR>
 * @Date 2025/9/3 20:19
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelAccountMoneySummaryDetailDto {

    /**
     * 金额分组ID
     */
    private Integer itemTypeGroup;

    /**
     * 金额分组名称
     */
    private String groupName;

    /**
     * 金额
     */
    private BigDecimal money;

    /**
     * 金额描述
     */
    private String moneyDesc;
}