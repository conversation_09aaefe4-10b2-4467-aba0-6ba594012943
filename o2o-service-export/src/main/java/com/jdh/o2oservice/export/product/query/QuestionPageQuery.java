package com.jdh.o2oservice.export.product.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Data
public class QuestionPageQuery {

    private Long quesId;//题目id

    private String name;//题目名称

    private Integer type;//题目类型

    private Long serviceItemId;//服务项目id

    private String userName;//操作者

    private Integer source=1;//1题库 2ducc


    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageNum = 1;

    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageSize = 10;


    /**
     * 存储要查询的题目ID列表。
     */
    private List<Long> quesIds;
}
