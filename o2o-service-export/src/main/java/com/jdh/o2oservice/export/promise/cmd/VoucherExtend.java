package com.jdh.o2oservice.export.promise.cmd;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * VoucherExtend
 *
 * <AUTHOR>
 * @date 2024/01/17
 */
@Data
public class VoucherExtend {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * autoPromise
     */
    private Boolean autoPromise;

    /**
     * skuId
     */
    private String skuId;

    /**
     * skuName
     */
    private String skuName;

    /**
     * 商家ID
     */
    private String venderId;

    /**
     * 草稿id
     */
    private String draftId;

    /**
     * 收获地址手机号
     */
    private String orderPhone;

    /**
     * 下单备注
     */
    private String orderRemark;

    /**
     * 履约人数
     */
    private Integer promisePatientNum;
    /**
     * 是否包含加项
     */
    private Integer hasAdded;

    /**
     * 赠品对应主商品sku
     */
    private Long mainSkuId;

    /**
     * 赠品对应主商品名称
     */
    private String mainSkuName;

    /**
     * 意向护士
     */
    private IntendedNurse intendedNurse;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag;

    /**
     * 实验室开放平台扩展字段
     */
    private String openTestInfo;

    /**
     * 就医记录文件ID
     */
    private List<Long> medicalCertificateFileIds;

    /**
     * 合作方来源子渠道
     */
    private String saleChannelId;

    /**
     * 合作方来源
     */
    private Integer partnerSource;

    /**
     * 外部渠道的订单号
     */
    private String partnerSourceOrderId;

    /**
     * 危险等级
     */
    private Set<Integer> dangerLevelSet;
    /**
     * 外部医院患者ID
     */
    private String outerPatientId;

}
