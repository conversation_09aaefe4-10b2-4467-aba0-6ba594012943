package com.jdh.o2oservice.export.provider;


import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.provider.cmd.JdhStoreTransferStationAddCmd;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.StationCompositeMedicalPromiseDTO;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import com.jdh.o2oservice.export.provider.query.PageCompositeMedicalPromiseRequest;

import java.util.List;


public interface ProviderStoreJsfExport {

    /**
     * 分页查询站点服务项目关联信息。
     * @param request 分页查询请求对象，包含查询条件和分页信息。
     * @return 分页查询结果，包含当前页数据和分页信息。
     */
    Response<PageDto<JdhStationServiceItemRelDto>> pageStationServiceItemRel(JdhStationServiceItemRelRequest request);


    /**
     * 检测单列表查询
     *
     * @param request 请求
     * @return {@link Response }<{@link PageDto }<{@link StationCompositeMedicalPromiseDTO }>>
     */
    Response<PageDto<StationCompositeMedicalPromiseDTO>> pageCompositeMedicalPromise(PageCompositeMedicalPromiseRequest request);

    /**
     * 添加默认接驳点
     * @param jdhStoreTransferStationAddCmd
     * @return
     */
    Response<Boolean> addDefaultTransformStation(JdhStoreTransferStationAddCmd jdhStoreTransferStationAddCmd);



    /**
     * 查询质控项目list
     * @return 包含所有质量服务项目的列表。
     */
    Response<List<ServiceItemDto>> queryQualityServiceItemList();

}
