package com.jdh.o2oservice.export.product.dto;
import lombok.Data;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
@Data
public class ServiceAreaDto implements Serializable {

    /**
     * 1-城市围栏  2-全国地图
     */
    private Integer mapType;

    /**
     * 围栏地理数据
     */
    private Map<String, Object> geometryMap;

    /**
     * 城市坐标点地里数据
     */
    private List<Map<String, Object>> features;

    /**
     * 经度
     */
    private String userPositionLng;

    /**
     * 纬度
     */
    private String userPositionLat;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 城市名称
     */
    private List<String> cityNameList;

    /**
     * 服务站
     */
    private Map<Long, String> angelStationMap;

    /**
     * 省
     */
    private List<String> provinceCodeList;


}
