package com.jdh.o2oservice.export.support.dto;

import lombok.Data;

import java.util.List;

/**
 * @Author: maoxianglin1
 * @Date: 2025/9/2 15:18
 * @Description: 虚拟号呼叫弹窗内容
 **/
@Data
public class VirtualPhonePopupDTO {

    /**
     * 主图
     */
    private String mainImage;

    /**
     * 顶部提示文案:
     */
    private String topTipText;

    /**
     * 呼出号码
     */
    private String callNumber;

    /**
     * 呼出号码输入提示文案
     */
    private String callNumberInputTipText;

    /**
     * 中间提示文案
     */
    private String middleTipText;

    /**
     * 提示文案列表
     */
    private String attentionText;

    /**
     * 呼叫按钮文案
     */
    private String callButtonText;

    /**
     * 修改号码文案
     */
    private String repairNumberText;

    /**
     * 修改号码弹窗
     */
    private VirtualPhonePopupDTO repairNumberPop;
}
