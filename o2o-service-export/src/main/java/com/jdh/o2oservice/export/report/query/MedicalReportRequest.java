package com.jdh.o2oservice.export.report.query;

import com.jdh.o2oservice.common.result.request.AbstractRequest;
import lombok.Data;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-26 16:58
 * @Desc :
 */
@Data
public class MedicalReportRequest  extends AbstractRequest {
    /**
     * 报告单id
     */
    private Long reportId;
    /**
     * 患者pin
     */
    private String userPin;
    /**
     * 跳转页面-来源
     */
    private String source;

    /**
     * 履约检测单ID
     */
    private Long medicalPromiseId;
    /**
     * encStr是map对象经过RSA加密后并且base64编码后的数据，解密后得到的json对象，取其中的sid字段
     * 根据sid查会话关系的成员，确认医助与患者权限
     * 没有传sid的情况下，同接口2
     */
    private String encStr;
    /**
     * 履约单ID
     */
    private Long promiseId;

    private Long patientId;//档案id
}
