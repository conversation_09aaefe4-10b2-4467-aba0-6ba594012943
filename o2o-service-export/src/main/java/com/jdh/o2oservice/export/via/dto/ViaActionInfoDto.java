package com.jdh.o2oservice.export.via.dto;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * @Description ViaActionInfoDto
 * @Date 2024/9/7 下午6:54
 * <AUTHOR>
 **/
@Data
@Slf4j
public class ViaActionInfoDto {

    /**
     * 类型
     */
    private String type;

    /**
     * 函数ID
     */
    private String functionId;

    /**
     * URL
     */
    private String url;

    /**
     * 固定参数
     */
    private Map<String,Object> params;

    /**
     * false时不会初始化参数
     */
    private Boolean initSwitch;

    /**
     * 扩展参数
     */
    private List<Map<String,Object>> extendParams;

    /**
     * 下一个动作
     */
    private ViaActionInfoDto nextAction;

    /**
     * 当actionType是alert、toast是的文案信息
     */
    private String text;

}
