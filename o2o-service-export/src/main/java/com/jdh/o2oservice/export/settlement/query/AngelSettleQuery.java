package com.jdh.o2oservice.export.settlement.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 9:30 上午
 * @Description:
 */
@Data
public class AngelSettleQuery extends AbstractPageQuery implements Serializable {
    private static final long serialVersionUID = -2406981332933100852L;


    /**
     * 服务者id
     */
    private Long angelId;
    /**
     * 护士姓名
     */
    private String angelName;
    /**
     * orderId
     */
    private Long orderId;
    /**
     * pin
     */
    private String userPin;
    /**
     * 结算id
     */
    private Long settleId;

    /**
     * 结算类型 1 收入2 支出
     */
    private Integer settlementType;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结尾
     */
    private Date createTimeEnd;
    /**
     * 结算业务id
     */
    private String settlementBusinessId;
    /**
     * 查询金额类型
     * 1、结算
     * 2、支出
     * 3、提现
     */
    private Integer amountSearchType;
    /**
     * 是否查询结算明细
     */
    private Boolean querySettleDetail = Boolean.TRUE;

    /**
     * 提现流水号
     */
    private Long settlementNo;

    /**
     * 预计结算时间
     */
    private Date expectSettleTime;
    /**
     * 结算状态 1-冻结中 2-已结算
     */
    private Integer settleStatus;
    /**
     * 人员标签 0-兼职 1-全职
     */
    private Integer jobNature;
    /**
     * 结算时间开始
     */
    private Date settleTimeStart;
    /**
     * 结算时间结束
     */
    private Date settleTimeEnd;
    /**
     * 收入类型 1-上门检测服务 2-上门护理服务 3-调整项(保底) 4-激励 5-其他费项 6-手工调账
     */
    private Integer itemType;
    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * 可提现时间开始
     */
    private Date cashTimeStart;

    /**
     * 可提现时间结尾
     */
    private Date cashTimeEnd;
    /**
     * applyErp
     */
    private String applyErp;
    /**
     * angelIdList
     */
    private List<Long> angelIdList;

    /**
     * 预计结算时间-查询开始时间
     */
    private Date expectSettleStartTime;

    /**
     * 请求版本
     */
    private String version;

    /**
     * 查询当天
     */
    private Boolean selectToday;

    /**
     * 创建时间开始
     */
    private String createDateStart;

    /**
     * 创建时间结尾
     */
    private String createDateEnd;

    /**
     * 查询的月份
     */
    private String queryMonth;

    /**
     * 偏移量-月份
     */
    private String offsetMonth;

    /**
     * 金额子类型：1-订单收入 2-调账收入 3-考核激励 4-申诉补款 5-任务激励 6-调账扣除 7-考核扣除
     */
    private Integer settleItemGroupType;
}
