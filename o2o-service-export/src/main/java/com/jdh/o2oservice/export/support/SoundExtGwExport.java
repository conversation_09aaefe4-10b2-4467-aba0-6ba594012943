package com.jdh.o2oservice.export.support;

import com.jdh.o2oservice.common.result.response.Response;

import java.util.Map;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28
 */
public interface SoundExtGwExport {

    /**
     * 查询声音扩展参数。
     * @param request 声音扩展参数请求对象。
     * @return 包含声音扩展参数的响应对象。
     */
    Response<Object> querySoundExtParam(Map<String, String> params);

}
