package com.jdh.o2oservice.export.trade.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * AppointmentTimeParam 预约时间入参
 *
 * <AUTHOR>
 * @version 2024/3/7 14:44
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppointmentTimeParam implements Serializable {
    private String appointmentStartTime;
    private String appointmentEndTime;
    private Integer dateType;
    /**
     * 是否立即预约
     */
    private Boolean isImmediately;

}
