package com.jdh.o2oservice.export.ztools.dto;

import com.jdh.o2oservice.export.product.dto.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ProductProgramAutoBotsDto
 * <AUTHOR>
 * @date 2024-07-27 13:06 2024-07-27 13:10
 */
@Data
public class ProductProgramAutoBotsDto {

    /**
     * 主站商品id
     */
    private Long skuId;

    /**
     * 售卖状态 0-不可售卖 1-可售卖,仅控制是否可下单,非上下架
     */
    private Integer saleStatus;

    /**
     * 服务类型
     */
    private Integer serviceType;

    /**
     * 服务类型
     */
    private String serviceTypeName;

    /**
     * 服务时长,单位分钟
     */
    private Integer serviceDuration;

    /**
     * 服务资源类型集合 1-骑手 2-护士 3-护工 4-康复师,多个值
     */
    private List<Integer> serviceResourceType;

    /**
     * 需提前预约时间,单位小时
     */
    private Integer advanceAppointTime;

    /**
     * 未来可约天数,单位天
     */
    private Integer maxScheduleDays;

    /**
     * 每天可预约时间段,["08:00-12:00", "14:00-18:00"]
     */
    private List<String> dayTimeFrame;

    /**
     * 实验室分发规则 1-整单 2-时效 3-成本,多个值
     */
    private List<Integer> stationAssignType;

    /**
     * 是否需要投保 0-不需要 1-需要
     */
    private Integer requiredInsure;

    /**
     * 商品标签,多个
     */
    private List<String> tags;

    /**
     * 服务须知,JSON对象集合
     */
    private List<JdhSkuServiceNoticeDto> serviceNotice;

    /**
     * 服务流程图
     */
    private String serviceProcessImgId;

    /**
     * 服务流程图
     */
    private String serviceProcessImg;

    /**
     * 是否实名 0-否 1-是
     */
    private Integer requiredRealName;

    /**
     * 弃用
     * 新的采样教程有三个字段组成，采样教程视频封面tutorialVideoThumbnailUrl；
     * 采样教程视频链接tutorialVideoUrl；采样教程图文轮播图tutorialCarouselUrl
     */
    @Deprecated
    private String tutorialUrl;

    /**
     * 知情同意书地址
     */
    private String informedConsentUrl;

    /**
     * 预约模板id
     */
    private Integer appointTemplateId;

    /**
     * 最大年龄
     */
    private Integer minAge;

    /**
     * 最大年龄
     */
    private Integer maxAge;

    /**
     * 年龄范围,最小最大值逗号隔开 0,150
     */
    private List<Integer> ageRange;

    /**
     * 适用性别，用户性别 1-男 2-女
     */
    private List<Integer> genderLimit;

    /**
     * 客户确认信息类型,1-医嘱证明 2-换着签字
     */
    private List<Integer> customerConfirmType;

    /**
     * 服务记录类型,1-废料处理 2-服务记录 3-上传着装照片
     */
    private List<Integer> serviceRecordType;

    /**
     * 服务资源结算价
     */
    private BigDecimal resourceSettlementPrice;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 门店组id
     */
    private String locGroupId;

    /**
     * 是否loc商品
     */
    private String isLoc;

    /**
     * sku状态
     */
    private Integer skuStatus;

    /**
     * 商品主图
     */
    private String imgDfsUrl;

    /** 店铺id */
    private String shopId;
    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * yymb
     */
    private String yymb;

    /**
     * 商家名称
     */
    private String venderName;

    /**
     * productId
     */
    private String productId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 到店商品类型标
     */
    private String ddsplx;

    /**
     * 同城到店业务商品
     * 1是，0或null 否
     */
    private String isddlm;

    /**
     * 是否测试商品
     */
    private String isTest;

    /**
     * 是否能用京券
     * 1或null：可以  0：否
     */
    private String isCanUseJQ;
    /**
     * 是否能用东券
     * 1或null：可以  0：否
     */
    private String isCanUseDQ;
    /**
     * 是否支持全球购
     * 1或null：可以  0：否
     */
    private String isGlobalPurchase;
    /**
     * 是否百亿补贴
     * 1和2都是有百亿补贴  0/无返回 表示没有百亿补贴
     */
    private String msbybt;

    /**
     * xfyl
     */
    private String xfyl;

    /**
     * locfwlx
     */
    private String locfwlx;

    /**
     * 商家id
     */
    private Long venderId;

    /**
     * spuId
     */
    private String spuId;

    /**
     * outerId
     */
    private String outerId;

    /**
     * 一级类目
     */
    private Integer firstCategoryId;

    /**
     * 二级类目
     */
    private Integer secondCategoryId;

    /**
     * 三级类目
     */
    private Integer thirdCategoryId;

    /**
     * 服务项目列表
     */
    List<ServiceItemDto> serviceItemList;

    /**
     * 服务项目id列表,前端使用
     */
    List<Long> serviceItemIdList;

    /**
     * 管理服务项名称，多个使用、隔开，由服务端处理成字符串下发。
     */
    String serviceItemNames;

    /**
     * 升级加项skuId，多个使用、隔开，由服务端处理成字符串下发。
     */
    String skuRelSkuIds;

    /**
     * 京东价格
     */
    private String jdPrice;

    /**
     * 商品类型；0为主品，1为加项品
     */
    private Integer skuType;

    /**
     * 技术难度 0-1000
     */
    private Integer technicalLevel;

    /**
     * 购买后服务有效时间,单位天
     */
    private Integer buyValidPeriod;

    /**
     * 采样教程轮播图url集合，格式：[url,url]
     */
    private List<String> tutorialCarouselUrl;

    /**
     * 采样教程视频
     */
    private String tutorialVideoUrl;

    /**
     * 采样教程视频封面图
     */
    private String tutorialVideoThumbnailUrl;

    /**
     * 采样方法说明 fileId
     */
    private String tutorialMethodUrl;

    /**
     * 采样方法说明跳转url
     */
    private String tutorialMethodJumpUrl;

    /**
     * 采样教程轮播图url集合，格式：[url,url]
     */
    private List<Long> tutorialCarousel;

    /**
     * 采样教程视频
     */
    private String tutorialVideo;

    /**
     * 采样教程视频封面图
     */
    private String tutorialVideoThumbnail;

    /**
     * 采样方法说明 fileId
     */
    private String tutorialMethod;
}
