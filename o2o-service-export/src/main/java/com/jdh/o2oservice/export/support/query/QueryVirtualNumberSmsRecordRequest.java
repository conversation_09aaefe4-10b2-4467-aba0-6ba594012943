package com.jdh.o2oservice.export.support.query;

import com.jdh.o2oservice.common.result.request.AbstractRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: maoxianglin1
 * @Date: 2025/9/3 11:06
 * @Description: 查询虚拟号发送短信入参
 **/
@Data
public class QueryVirtualNumberSmsRecordRequest extends AbstractRequest implements Serializable {

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 短信ID，唯一确定一次短信
     */
    private String smsId;

    /**
     * 服务者pin
     */
    private String angelPin;
}