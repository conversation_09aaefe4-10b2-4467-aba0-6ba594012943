package com.jdh.o2oservice.export.ztools;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.ztools.dto.ProductProgramAutoBotsDto;
import com.jdh.o2oservice.export.ztools.query.ProductProgramAutoBotParam;

/**
 * @ClassName O2oConsoleCheckToolService
 * @Description
 * <AUTHOR>
 * @Date 2025/4/2 16:28
 */
public interface O2oAutobotsService {

    /**
     * 商品查询工具
     * @param autoBotParam
     * @return
     */
    Response<ProductProgramAutoBotsDto> autoBotsQueryProductInfo(ProductProgramAutoBotParam autoBotParam);

}
