package com.jdh.o2oservice.export.report.cmd;

import lombok.Data;

import java.util.Set;

/**
 * MedicalReportIndicatorFlushCmd
 * <AUTHOR>
 * @date 2024-11-14 10:17
 */
@Data
public class MedicalReportIndicatorFlushCmd {

    /**
     * 指定开始页码。
     */
    private Integer startPage;

    /**
     * 结束页码。
     */
    private Integer endPage;

    /**
     * 每页显示的记录数。
     */
    private Integer pageSize;

    /**
     * 医疗承诺ID，用于关联医疗报告指标和相应的医疗承诺。
     */
    private Long medicalPromiseId;

    /**
     * 一级分类编码
     */
    private Set<Long> firstIndicatorCategory;

    /**
     * 二级分类编码
     */
    private Set<Long> secondIndicatorCategory;

    /**
     * 指定开始的ID。
     */
    private Long startId;


    /**
     * 指定结束的ID。
     */
    private Long endId;

}
