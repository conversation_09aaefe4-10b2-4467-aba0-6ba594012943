package com.jdh.o2oservice.export.product.query;

import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkSpecimenDto;
import com.jdh.o2oservice.export.promise.dto.PromiseAppointmentTimeDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author: maoxianglin1
 * @Date: 2025/7/28 18:29
 * @Description:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromiseServiceStartTimeDescRequest {

    /**
     * 类型：参考 PromiseServiceStartTimeModuleTypeEnum
     */
    private String functionId;

    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * 服务者工单信息
     */
    private List<AngelWorkSpecimenDto> angelWorkSpecimens;

    /**
     * 2、护士上门检测
     */
    private Integer workType;

    /**
     * 预约时间
     */
    private PromiseAppointmentTimeDto appointmentTime;

    /**
     * 等同于下单时间
     */
    private Date promiseCreateTime;

    /**
     * 服务者工单状态
     */
    private Integer workStatus;
    /**
     *
     */
    private String sourceVoucherId;
}
