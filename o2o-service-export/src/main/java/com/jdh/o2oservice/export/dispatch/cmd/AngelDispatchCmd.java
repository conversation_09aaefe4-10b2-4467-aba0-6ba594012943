package com.jdh.o2oservice.export.dispatch.cmd;

import com.jdh.o2oservice.common.result.request.AbstractBusinessIdentity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName AngelDispatchCmd
 * @Description
 * <AUTHOR>
 * @Date 2024/4/22 18:32
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelDispatchCmd extends AbstractBusinessIdentity implements Serializable {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 履约单ID
     */
    private Long dispatchId;

    /**
     * 服务单ID
     */
    private Long voucherId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 派单流程id
     */
    private Long flowId;
    /**
     *
     */
    private String flowExecuteType;

    /**
     * 是否继续执行派单流程
     */
    private Boolean executeDispatchPipelineContinue;

    /**
     * 事件编码
     */
    private String eventCode;
}