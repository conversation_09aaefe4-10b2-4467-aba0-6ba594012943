package com.jdh.o2oservice.export.angelpromise.cmd;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @ClassName:ShipInfoForCallBackRequest
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/21 22:13
 * @Vserion: 1.0
 **/
public class ShipInfoForCallBackRequest {

    /**
     * 达达物流订单号，默认为空
     */
    private String clientId;

    /**
     * 第三方订单ID，对应下单接口中的origin_id
     */
    private String orderId;

    /**
     * 订单状态(待接单＝1,待取货＝2,配送中＝3,已完成＝4,已取消＝5, 已追加待接单=8,妥投异常之物品返回中=9, 妥投异常之物品返回完成=10, 骑士到店=100,创建达达运单失败=1000）
     */
    private Integer orderStatus;

    /**
     * 重复回传状态原因(1-重新分配骑士，2-骑士转单)。重复的状态消息默认不回传，如系统支持可在开发助手-应用信息中开启【运单重抛回调通知】开关
     */
    private Integer repeatReasonType;

    /**
     * 订单取消原因,其他状态下默认值为空字符串
     */
    private String cancelReason;

    /**
     * 订单取消原因Id,20025,代表长时间未完成订单
     */
    private Integer cancelReasonId;

    /**
     * 订单取消原因来源(1:达达配送员取消；2:商家主动取消；3:系统或客服取消；0:默认值)
     */
    private Integer cancelFrom;

    /**
     * 更新时间，时间戳除了创建达达运单失败=1000的精确毫秒，其他时间戳精确到秒
     */
    private Long updateTime;

    /**
     * 对client_id, order_id, update_time的值进行字符串升序排列，再连接字符串，取md5值
     */
    private String signature;

    /**
     * 达达配送员id，接单以后会传
     */
    private Integer dmId;

    /**
     * 配送员姓名，接单以后会传
     */
    private String dmName;

    /**
     * 配送员手机号，接单以后会传
     */
    private String dmMobile;

    /**
     * 收货码
     */
    private String finishCode;

    /**
     * 配送员纬度
     */
    private double latitude;

    /**
     * 配送员经度
     */
    private double longitude;

    private String logisticsMessage;//物流动态

    public String getClientId() {
        return clientId;
    }
    @JsonProperty("client_id")
    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getOrderId() {
        return orderId;
    }
    @JsonProperty("order_id")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }
    @JsonProperty("order_status")
    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getRepeatReasonType() {
        return repeatReasonType;
    }
    @JsonProperty("repeat_reason_type")
    public void setRepeatReasonType(Integer repeatReasonType) {
        this.repeatReasonType = repeatReasonType;
    }

    public String getCancelReason() {
        return cancelReason;
    }
    @JsonProperty("cancel_reason")
    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public Integer getCancelReasonId() {
        return cancelReasonId;
    }
    @JsonProperty("cancel_reason_id")
    public void setCancelReasonId(Integer cancelReasonId) {
        this.cancelReasonId = cancelReasonId;
    }

    public Integer getCancelFrom() {
        return cancelFrom;
    }
    @JsonProperty("cancel_from")
    public void setCancelFrom(Integer cancelFrom) {
        this.cancelFrom = cancelFrom;
    }

    public Long getUpdateTime() {
        return updateTime;
    }
    @JsonProperty("update_time")
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getSignature() {
        return signature;
    }
    @JsonProperty("signature")
    public void setSignature(String signature) {
        this.signature = signature;
    }

    public Integer getDmId() {
        return dmId;
    }
    @JsonProperty("dm_id")
    public void setDmId(Integer dmId) {
        this.dmId = dmId;
    }

    public String getDmName() {
        return dmName;
    }
    @JsonProperty("dm_name")
    public void setDmName(String dmName) {
        this.dmName = dmName;
    }

    public String getDmMobile() {
        return dmMobile;
    }
    @JsonProperty("dm_mobile")
    public void setDmMobile(String dmMobile) {
        this.dmMobile = dmMobile;
    }

    public String getFinishCode() {
        return finishCode;
    }
    @JsonProperty("finish_code")
    public void setFinishCode(String finishCode) {
        this.finishCode = finishCode;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public String getLogisticsMessage() {
        return logisticsMessage;
    }

    public void setLogisticsMessage(String logisticsMessage) {
        this.logisticsMessage = logisticsMessage;
    }
}
