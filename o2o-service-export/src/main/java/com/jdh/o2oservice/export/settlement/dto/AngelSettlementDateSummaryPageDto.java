package com.jdh.o2oservice.export.settlement.dto;

import com.jdh.o2oservice.common.result.response.PageDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName AngelSettlementDateSummaryPageDto
 * @Description
 * <AUTHOR>
 * @Date 2025/9/10 16:07
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelSettlementDateSummaryPageDto {

    /**
     * 筛选入账开始日期，例如“2025-08-01”
     */
    private String createDateStart;

    /**
     * 筛选入账结束日期，例如“2025-08-31”
     */
    private String createDateEnd;

    /**
     * 收入
     */
    @Builder.Default
    private BigDecimal income = BigDecimal.ZERO;

    /**
     * 支出
     */
    @Builder.Default
    private BigDecimal expend = BigDecimal.ZERO;

    /**
     * 提现
     */
    @Builder.Default
    private BigDecimal withdraw = BigDecimal.ZERO;

    /**
     * 结算明细分页数据
     */
    private PageDto<AngelSettlementDto> pageData;
}