package com.jdh.o2oservice.export.trade.dto;

import com.jdh.o2oservice.export.product.dto.ProductLimitBuyDTO;
import com.jdh.o2oservice.export.product.dto.ScriptDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * OrderUserActionDTO 结算页用户行为出参
 *
 * <AUTHOR>
 * @version 2024/3/7 11:56
 **/
@Data
public class OrderUserActionDTO implements Serializable {

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * sku信息
     */
    private List<SkuItemDTO> skuItemDTOList;

    /**
     * 优惠卷域
     */
    private CouponInfoRecommendDTO couponInfoRecommendDTO;

    /**
     * 京豆域
     */
    private JBeanInfoDTO jBeanInfoDTO;

    /**
     * 发票域
     */
    private List<InvoiceTradeDTO> invoiceTradeDTOList;

    /**
     * 地址域
     */
    private List<AddressInfoDTO> addressInfoDTOList;

    /**
     * 支付方式
     */
    private List<PaymentInfoDTO> paymentInfoDTOList;

    /**
     * 支付密码
     */
    private List<PaymentPasswordDTO> paymentPasswordDTOList;

    /**
     * 配送方式
     */
    private List<ShipmentInfoDTO> shipmentInfoDTOList;

    /**
     * 金额
     */
    private AmountInfoDTO amountInfoDTO;

    /**
     * 商家信息
     */
    private List<VenderInfoDTO> venderInfoDTOList;

    /**
     * 包裹信息
     */
    private List<BundleInfoDTO> bundleInfoDTOList;

    /**Å
     * 门店信息
     */
    private List<StoreInfoDTO> storeInfoDTOList;

    /**
     * 备注信息
     */
    private RemarkDTO remarkDTO;

    /**
     * 红包
     */
    private HongBaoInfoDTO hongBaoInfoDTO;
    /**
     * 服务费
     */
    private ServiceFeeInfoDTO serviceFeeInfoDTO;

    /**
     * 促销实体
     */
    private List<PromotionInfoDTO> promotionInfoDTOList;

    /**
     * 结算页实体
     */
    private SettlementOrderDTO settlementOrderDTO;

    /**
     * 不支持的服务项列表
     */
    private List<String> notSupportServiceItemList;
    /**
     * 服务类型
     */
    private String serviceType;
    /**
     * 商品加项实体
     */
    private List<AddSkuItemDTO> addSkuItemDTOS;

    /**
     * 预估信息
     */
    private ScriptDto scriptDto;

    /**
     * 意向护士是否显示
     * true-显示、false=隐藏
     */
    private Boolean intendedNurseDisplay = false;

    /**
     * 服务人员升级
     */
    private ServiceUpgradeDTO serviceUpgradeDTO;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag = false;

    /**
     * 是否展示已有采样盒楼层
     */
    private Boolean showPreSampleFlag = false;

    /**
     * 患者档案信息
     */
    private List<PatientDTO> patientDTOList;

    /**
     * 就医证明
     */
    private MedicalCertificateDTO medicalCertificateDTO;

    /**
     * 限购
     */
    private List<ProductLimitBuyDTO> productLimitBuyDTOList;

}
