package com.jdh.o2oservice.export.support.dto;

import lombok.Data;

/**
 * @Author: maoxianglin1
 * @Date: 2025/9/2 18:43
 * @Description: 云鼎虚拟号发送短信实体
 **/
@Data
public class SmsEventNotificationDTO {

    /**
     * 短信唯一标识。
     */
    private String smsId;
    /**
     * 绑定关系ID。
     */
    private String bindId;
    /**
     * 真实发送方号码。
     * （请注意：各模式绑定、解绑、更新接口传参进平台时均传11位数手机号，但平台向客户侧推送事件、通话话单、短信话单时主被叫号码/短信发送和接收方号码均以86国家码开头，例如8613803111233）
     */
    private String callNo;
    /**
     * 真实接收方号码。
     */
    private String peerNo;
    /**
     * 分机号。
     */
    private String extensionNo;
    /**
     * 发送方显示号码
     */
    private String senderShow;
    /**
     * 接收方显示号码
     * （AXB模式下，如运营商批准开启透传功能，短信接收方接收短信时显示的发件人号码也为隐私号，即短信场景不能透传。在AXYB场景下，A向Y发短信，收件人B显示的发件人号码为X，B向X发短信，收件人A显示的发件人号码为Y。）
     */
    private String receiverShow;
    /**
     * 短信发送时间。该参数取值为时间戳(毫秒)
     */
    private Long smsTime;
    /**
     * 实际转发短信条数。
     * （与普通点对点短信长度一致，140字节或70汉字为1条短信，长短信会拆分为多条下发，以拆分短信条数为基准计算收费条数。）
     */
    private String smsNumber;
    /**
     * 发送结果：
     * 0：成功
     * 1：失败
     * （其他返回结果请参考本章节备注。）
     */
    private String smsResult;
    /**
     * 用户发送的短信内容，仅在短信托收模式下提供
     */
    private String smsContent;
    /**
     * 用户附属信息。
     * 当客户在绑定接口和绑定信息修改接口中携带了"userData"时，对应的话单通知消息中会携带此参数。
     */
    private String userData;

    /**
     * userData 转换的对象
     */
    private CallBillingUserDataDto userDataDto;
}
