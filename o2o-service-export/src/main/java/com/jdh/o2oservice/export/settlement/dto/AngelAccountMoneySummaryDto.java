package com.jdh.o2oservice.export.settlement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName AngelAccountMoneyDto
 * @Description
 * <AUTHOR>
 * @Date 2025/9/3 20:17
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelAccountMoneySummaryDto {

    /**
     * 护士ID
     */
    private Long angelId;

    /**
     * 今日收入
     */
    private BigDecimal todayIncome;

    /**
     * 今日支出
     */
    private BigDecimal todayExpend;

    /**
     * 今日收入明细
     */
    private List<AngelAccountMoneySummaryDetailDto> todayIncomeDetailList;

    /**
     * 今日支出明细
     */
    private List<AngelAccountMoneySummaryDetailDto> todayExpendDetailList;
}