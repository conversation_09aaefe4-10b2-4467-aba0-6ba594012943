package com.jdh.o2oservice.export.angel.cmd;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName:AngelStationUnbindSkuCmd
 * @Description:
 * @Author: yaoqing<PERSON>
 * @Date: 2024/4/28 01:19
 * @Vserion: 1.0
 **/
@Data
public class AngelStationUnbindSkuCmd {

    /**
     * 服务站id
     */
    @NotNull(message = "服务站id不能为空")
    private Long angelStationId;

    /**
     * 商品id
     */
    @NotNull(message = "服务站资源")
    private Long skuId;

    /**
     * 操作人
     */
    private String operator;

}
