package com.jdh.o2oservice.export.settlement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName AngelSettlementMonthSummaryDto
 * @Description
 * <AUTHOR>
 * @Date 2025/9/12 14:18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelSettlementMonthSummaryDto {

    /**
     * 入账月份，例如“2025-08”
     */
    private String month;

    /**
     * 收入
     */
    @Builder.Default
    private BigDecimal income = BigDecimal.ZERO;

    /**
     * 支出
     */
    @Builder.Default
    private BigDecimal expend = BigDecimal.ZERO;

    /**
     * 提现
     */
    @Builder.Default
    private BigDecimal withdraw = BigDecimal.ZERO;

    /**
     * 账户余额
     */
    @Builder.Default
    private BigDecimal accountBalance = BigDecimal.ZERO;

    /**
     * 日期结算明细分页数据
     */
    private List<AngelSettlementDateSummaryDto> dateDetailList;
}