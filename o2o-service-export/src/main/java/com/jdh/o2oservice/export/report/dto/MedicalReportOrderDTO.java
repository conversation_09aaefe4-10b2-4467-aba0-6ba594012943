package com.jdh.o2oservice.export.report.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-29 17:24
 * @Desc : 报告按履约单汇总
 */
@Data
public class MedicalReportOrderDTO implements Serializable {

    /**
     * 履约单id
     */
    private Long promiseId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 互医检测单id
     */
    private String outerOrderId;
    /**
     * 用户pin
     */
    private String userPin;
    /**
     * 患者id
     */
    @Deprecated
    private Long patientId;
    /**
     * 患者名称
     */
    @Deprecated
    private String patientName;
    /**
     * 报告是否全部都已出
     */
    @Deprecated
    private boolean allFinish;
    /**
     * 检测时间
     */
    private Date checkTime;
    /**
     * 检测项报告
     */
    private List<PromiseMedicalReportDTO> promiseMedicalReportDtoList;

    /**
     * 异常结论集合
     */
    private List<ReportConclusionDTO> conclusionList;

    /**
     * 医生信息
     */
    private ReportDoctorDTO reportDoctorDTO;
    /**
     * 检测结论
     */
    private TestConclusionDTO testConclusionDTO;
    /**
     * 温馨提示
     */
    private String tips;
    /**
     * 调研问卷链接
     */
    private String nps;

    /**
     * 百科
     */
    @Deprecated
    private Set<VirusEncyclopediaDTO> virusEncyclopediaDTOSet;

    /**
     * 用户年龄
     */
    @Deprecated
    private Integer userAge;
    /**
     * 包含用户的基本信息和报告状态的DTO对象。
     */
    private MedicalReportUserDTO medicalReportUserDTO;

    /**
     * 用药建议
     */
    private List<MedicineRecommendGroupDTO> recommendGroupDTOS;

    private ReportFloorConfig reportFloorConfig;

    /**
     * 存储医疗报告处方信息的列表。
     */
    private List<MedicalReportRxDTO> medicalReportRxDTOS;
    /**
     * 买药秒送楼层
     */
    private List<QuickDrugDTO> quickDrugDTOS;

    /**
     * 报告类型
     */
    private Integer reportFormatType;

    /**
     * 报告文件链接
     */
    private  String reportFileUrl;


    /**
     * 报告解读医生
     */
    private ReportInterpretationDoctorDTO reportInterpretationDoctorDTO;

}
