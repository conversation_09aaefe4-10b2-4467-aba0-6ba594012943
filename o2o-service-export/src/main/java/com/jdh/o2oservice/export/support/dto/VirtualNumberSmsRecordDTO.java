package com.jdh.o2oservice.export.support.dto;

import lombok.Data;

import java.util.Date;

/**
 * @Author: maoxianglin1
 * @Date: 2025/9/2 21:59
 * @Description: 虚拟号短信记录列表
 **/
@Data
public class VirtualNumberSmsRecordDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务兑换的来源id，兑换id/orderId/outerOrderId
     */
    private String sourceVoucherId;

    /**
     * 真实发送方号码。
     * （请注意：各模式绑定、解绑、更新接口传参进平台时均传11位数手机号，但平台向客户侧推送事件、通话话单、短信话单时主被叫号码/短信发送和接收方号码均以86国家码开头，例如8613803111233）
     */
    private String callNo;
    /**
     * 真实接收方号码。
     */
    private String peerNo;
    /**
     * 分机号。
     */
    private String extensionNo;
    /**
     * 发送方显示号码
     */
    private String senderShow;
    /**
     * 接收方显示号码
     * （AXB模式下，如运营商批准开启透传功能，短信接收方接收短信时显示的发件人号码也为隐私号，即短信场景不能透传。在AXYB场景下，A向Y发短信，收件人B显示的发件人号码为X，B向X发短信，收件人A显示的发件人号码为Y。）
     */
    private String receiverShow;
    /**
     * 短信发送时间。该参数取值为时间戳(毫秒)
     */
    private Date smsTime;
    /**
     * 发送结果：
     * 0：成功
     * 1：失败
     * （其他返回结果请参考本章节备注。）
     */
    private String smsResult;
    /**
     * 用户发送的短信内容，仅在短信托收模式下提供
     */
    private String smsContent;
    /**
     * 用户附属信息。
     * 当客户在绑定接口和绑定信息修改接口中携带了"userData"时，对应的话单通知消息中会携带此参数。
     */
    private String userData;

}
