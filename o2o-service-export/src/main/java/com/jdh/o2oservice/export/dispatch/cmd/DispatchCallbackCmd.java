package com.jdh.o2oservice.export.dispatch.cmd;

import com.jdh.o2oservice.common.enums.DispatchTypeEnum;
import com.jdh.o2oservice.common.result.request.AbstractBusinessIdentity;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName DispatchCallbackCmd
 * @Description
 * <AUTHOR>
 * @Date 2024/4/21 20:12
 **/
@Data
public class DispatchCallbackCmd extends AbstractBusinessIdentity implements Serializable {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 派单ID
     */
    private Long dispatchId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 外部派单ID
     */
    private String outDispatchId;

    /**
     * 派单任务类型 1-即时单 2-预约单
     */
    @NotNull(message = "派单任务类型不能为空")
    private DispatchTypeEnum dispatchType;

    /**
     * 派单任务状态：待派单/已派单/已接单/已取消/派单失败
     * JdhDispatchStatusEnum
     */
    @NotNull(message = "派单任务状态不能为空")
    private Integer dispatchStatus;

    /**
     * 服务者信息
     */
    private List<DispatchAngelDetail> angelDetailList;

    /**
     * 事件时间
     */
    private Date eventTime;

    /**
     * 过期时间
     */
    private Date expireDate;

    /**
     * 派单执行路由：flow nethp
     */
    private String dispatchExecuteRoute;

    /**
     * @see com.jdh.o2oservice.common.enums.DispatchDetailTypeEnum
     */
    private Integer assignType;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 计划完成时间
     */
    private Date planFinishTime;

    /**
     * 当前执行的派单轮次
     */
    private String currentDispatchRoundConfig;
}