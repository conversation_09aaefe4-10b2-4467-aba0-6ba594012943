package com.jdh.o2oservice.export.settlement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName AngelSettleItemGroupTypeDto
 * @Description
 * <AUTHOR>
 * @Date 2025/9/10 11:23
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelSettleItemGroupTypeDto {

    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String desc;

    /**
     * 结算类型 1 收入2 支出
     */
    private Integer settlementType;
}