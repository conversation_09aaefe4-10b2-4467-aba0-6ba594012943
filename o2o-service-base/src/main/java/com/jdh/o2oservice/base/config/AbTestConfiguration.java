package com.jdh.o2oservice.base.config;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jd.abtest.sdk.TouchstoneSDK;
import com.jd.abtest.sdk.entity.TouchstoneEnv;
import com.jd.abtest.sdk.entity.UserInfo;
import com.jd.abtest.sdk.entity.config.TouchstoneSdkConfig;
import com.jd.abtest.v2.api.domain.ExpLabel;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.AbTestBusinessSceneConfig;
import com.jdh.o2oservice.base.ducc.model.AbTestConfig;
import com.jdh.o2oservice.base.model.AbTestExp;
import com.jdh.o2oservice.base.model.AbTestLabel;
import com.nimbusds.jose.util.JSONStringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/21 17:42
 */
@Component
@Slf4j
public class AbTestConfiguration implements CommandLineRunner {

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private Cluster jimClient;

    public static Map<String, TouchstoneSDK> INST_MAP = new HashMap<>();


    /**
     * 实例化试金石sdk
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(String... args) throws Exception {
        try {
            TouchstoneSdkConfig config = new TouchstoneSdkConfig();
            config.setTouchstoneEnv(TouchstoneEnv.PROD);
            // abTest配置
            AbTestConfig abTestConfig = duccConfig.getAbTestConfig();
            List<AbTestBusinessSceneConfig> businessSceneList = abTestConfig.getBusinessSceneList();
            if (CollectionUtils.isEmpty(businessSceneList)){
                return;
            }
            // 产品线
            List<String> productionList = businessSceneList.stream().filter(b -> Boolean.TRUE.equals(b.getOpen()))
                    .map(AbTestBusinessSceneConfig::getProduction).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productionList)){
                return;
            }
            productionList.forEach(p -> {
                TouchstoneSDK touchstoneSDK = TouchstoneSDK.getPrototypeInstance(p, config);
                INST_MAP.put(p, touchstoneSDK);
            });
            log.info("AbTestConfiguration run INST_MAP={}", JSON.toJSONString(INST_MAP));
        } catch (Exception e) {
            log.error("AbTestConfiguration run error e", e);
        }
    }

    /**
     * 分流
     * @param abTestLabel
     * @return
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.base.config.AbTestConfiguration.getExpLabel")
    public AbTestExp getExpLabel(AbTestLabel abTestLabel) {
        try {
            String production = abTestLabel.getProduction();
            String expId = abTestLabel.getExpId();
            String userPin = abTestLabel.getUserPin();
            String uuid = abTestLabel.getUuid();
            if (StringUtils.isAnyBlank(production, expId)) {
                return null;
            }
            if (StrUtil.isAllBlank(userPin, uuid)) {
                return null;
            }

            // 命中白名单，走默认配置
            if (CollectionUtils.isNotEmpty(abTestLabel.getWhiteListPin()) && abTestLabel.getWhiteListPin().contains(userPin)){
                log.info("AbTestConfiguration getExpLabel whiteListPin abTestLabel={}", JSON.toJSONString(abTestLabel));
                AbTestExp abTestExp = new AbTestExp();
                abTestExp.setScene(abTestLabel.getScene());
                abTestExp.setProduction(abTestLabel.getProduction());
                abTestExp.setExpId(abTestLabel.getExpId());
                abTestExp.setLabel(abTestLabel.getExpLabel());
                abTestExp.setExpStatus(abTestLabel.getExpStatus());
                String key = "AB_TEST_" + MDC.get("PFTID");
                jimClient.sAdd(key, JSON.toJSONString(abTestExp));
                return abTestExp;
            }

            // 实验状态：1-进行中 2-结束
            if (NumConstant.NUM_2.equals(abTestLabel.getExpStatus())){
                AbTestExp abTestExp = new AbTestExp();
                abTestExp.setScene(abTestLabel.getScene());
                abTestExp.setProduction(abTestLabel.getProduction());
                abTestExp.setExpId(abTestLabel.getExpId());
                abTestExp.setLabel(abTestLabel.getExpLabel());
                abTestExp.setExpStatus(abTestLabel.getExpStatus());
                String key = "AB_TEST_" + MDC.get("PFTID");
                jimClient.sAdd(key, JSON.toJSONString(abTestExp));
                return abTestExp;
            }
            TouchstoneSDK touchstoneSDK = INST_MAP.get(production);
            if (Objects.isNull(touchstoneSDK)){
                return null;
            }
            // 构建分流参数
            UserInfo userInfo = new UserInfo();
            userInfo.setUserPin(userPin);
            userInfo.setUuid(uuid);
            ExpLabel defaultExpLabel = new ExpLabel();
            defaultExpLabel.setLabel("base");

            // 获取分流
            ExpLabel expLabel = touchstoneSDK.getExpLabel(userInfo, expId, defaultExpLabel);
            log.info("AbTestConfiguration getExpLabel abTestLabel={}, expLabel={}", JSON.toJSONString(abTestLabel), JSON.toJSONString(expLabel));

            // 获取分流参数
            Map<String,Object> shuntParamMap = new HashMap<>();
            if (StringUtils.isNotBlank(abTestLabel.getBooleanKey())){
                shuntParamMap.put(abTestLabel.getBooleanKey(), touchstoneSDK.getBooleanValue(expId, expLabel, abTestLabel.getBooleanKey(), false));
            }
            if (StringUtils.isNotBlank(abTestLabel.getStringKey())){
                shuntParamMap.put(abTestLabel.getStringKey(), touchstoneSDK.getStringValue(expId, expLabel, abTestLabel.getStringKey(), ""));
            }
            if (StringUtils.isNotBlank(abTestLabel.getIntKey())){
                shuntParamMap.put(abTestLabel.getIntKey(), touchstoneSDK.getIntValue(expId, expLabel, abTestLabel.getIntKey(), 0));
            }

            // 实验结果
            AbTestExp abTestExp = new AbTestExp();
            abTestExp.setScene(abTestLabel.getScene());
            abTestExp.setProduction(production);
            abTestExp.setExpId(expLabel.getExpId());
            abTestExp.setBuriedStr(expLabel.getBuriedStr());
            abTestExp.setLabel(expLabel.getLabel());
            abTestExp.setShuntParamMap(shuntParamMap);
            abTestExp.setExpStatus(abTestLabel.getExpStatus());

            String key = "AB_TEST_" + MDC.get("PFTID");
            jimClient.sAdd(key, JSON.toJSONString(abTestExp));
            return abTestExp;
        } catch (Exception e) {
            log.error("AbTestConfiguration getExpLabel error e", e);
            return null;
        }
    }

    /**
     * 查询AbTest业务场景配置
     * @param scene
     * @return
     */
    private AbTestBusinessSceneConfig queryAbTestBusinessSceneConfigByScene(String scene) {
        AbTestConfig abTestConfig = duccConfig.getAbTestConfig();
        List<AbTestBusinessSceneConfig> businessSceneList = abTestConfig.getBusinessSceneList();
        if (CollectionUtils.isEmpty(businessSceneList)){
            return null;
        }
        List<AbTestBusinessSceneConfig> businessSceneFilterList = businessSceneList.stream().filter(b -> Boolean.TRUE.equals(b.getOpen())
                && scene.equals(b.getScene())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(businessSceneFilterList)){
            return null;
        }
        return businessSceneFilterList.get(0);
    }

    /**
     * 商详分享领券链接 AB实验
     * @param userPin
     * @param runnable
     * @return
     */
    public void shareGetCouponExecute(String userPin, Runnable runnable) {
        AbTestBusinessSceneConfig businessSceneConfig = queryAbTestBusinessSceneConfigByScene("shareGetCoupon");
        if (Objects.isNull(businessSceneConfig)){
            log.info("AbTestConfiguration shareGetCouponExecute open false");
            return;
        }
        AbTestLabel abTestLabel = new AbTestLabel();
        abTestLabel.setScene(businessSceneConfig.getScene());
        abTestLabel.setProduction(businessSceneConfig.getProduction());
        abTestLabel.setExpId(businessSceneConfig.getExpId());
        abTestLabel.setUserPin(userPin);
        abTestLabel.setExpStatus(businessSceneConfig.getExpStatus());
        abTestLabel.setExpLabel(businessSceneConfig.getExpLabel());
        abTestLabel.setWhiteListPin(businessSceneConfig.getWhiteListPin());
        AbTestExp expLabel = this.getExpLabel(abTestLabel);
        log.info("AbTestConfiguration shareGetCouponExecute userPin={}, expLabel={}",userPin, JSON.toJSONString(expLabel));
        // 命中实验执行的逻辑
        if (expLabel != null && businessSceneConfig.getExpLabel().equals(expLabel.getLabel())){
            runnable.run();
        }
    }

    /**
     * 京东提问楼层 AB实验
     * @param userPin
     * @param runnable
     * @return
     */
    public void shareSkuQuestionBoxExecute(String userPin, Runnable runnable) {
        AbTestBusinessSceneConfig businessSceneConfig = queryAbTestBusinessSceneConfigByScene("showSkuQuestBox");
        if (Objects.isNull(businessSceneConfig)){
            log.info("AbTestConfiguration shareSkuQuestionBoxExecute open false");
            return;
        }
        AbTestLabel abTestLabel = new AbTestLabel();
        abTestLabel.setScene(businessSceneConfig.getScene());
        abTestLabel.setProduction(businessSceneConfig.getProduction());
        abTestLabel.setExpId(businessSceneConfig.getExpId());
        abTestLabel.setUserPin(userPin);
        abTestLabel.setExpStatus(businessSceneConfig.getExpStatus());
        abTestLabel.setExpLabel(businessSceneConfig.getExpLabel());
        abTestLabel.setWhiteListPin(businessSceneConfig.getWhiteListPin());
        AbTestExp expLabel = this.getExpLabel(abTestLabel);
        log.info("shareSkuQuestionBoxExecute end,userPin,={},expLabel:{}",userPin, JsonUtil.toJSONString(expLabel));
        // 命中实验执行的逻辑
        if (expLabel != null && businessSceneConfig.getExpLabel().equals(expLabel.getLabel())){
            runnable.run();
        }
    }

    /**
     * callAbTestResult
     *
     * @param userPin 用户pin
     * @param scene   场景
     */
    public AbTestExp callAbTestResult(String userPin,String scene){
        AbTestBusinessSceneConfig businessSceneConfig = queryAbTestBusinessSceneConfigByScene(scene);
        if (Objects.isNull(businessSceneConfig)){
            log.info("AbTestConfiguration callAbTestResult open false");
            return null;
        }
        AbTestLabel abTestLabel = new AbTestLabel();
        abTestLabel.setScene(scene);
        abTestLabel.setProduction(businessSceneConfig.getProduction());
        abTestLabel.setExpId(businessSceneConfig.getExpId());
        abTestLabel.setUserPin(userPin);
        abTestLabel.setExpStatus(businessSceneConfig.getExpStatus());
        abTestLabel.setExpLabel(businessSceneConfig.getExpLabel());
        abTestLabel.setWhiteListPin(businessSceneConfig.getWhiteListPin());
        AbTestExp expLabel = this.getExpLabel(abTestLabel);
        log.info("AbTestConfiguration callAbTestResult userPin={},scene={},expLabel={}",userPin, scene, JSON.toJSONString(expLabel));
        return expLabel;
    }

}
