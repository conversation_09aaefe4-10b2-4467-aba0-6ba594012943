package com.jdh.o2oservice.base.util;

import org.apache.commons.lang3.StringUtils;

import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/7
 */
public class NumberUtils {

    private static final String[] CHINESE_NUMBERS = {
            "零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"
    };


    /**
     * 将给定的数字字符串转换为科学计数法表示形式。
     * @param numStr 需要转换的数字字符串。
     * @return 转换后的科学计数法表示形式的字符串。
     */
    public static String toScientificNotation(String numStr) {
        try {
            if (StringUtils.isBlank(numStr)){
                return numStr;
            }
            // 将字符串转换为浮点数
            double num = Double.parseDouble(numStr);
            // 创建一个DecimalFormat实例，并设置科学计数法格式
            DecimalFormat df = new DecimalFormat("0.00E0");
            // 格式化数字
            String formatted = df.format(num);

            // 将E替换为×10^，并将上标数字替换为相应的Unicode字符
            formatted = formatted.replace("E", "×10^");
            formatted = formatted.replace("+", ""); // 去掉正号
            formatted = convertToSuperscript(formatted);

            return formatted;
        } catch (NumberFormatException e) {
            return numStr;
        }
    }

    /**
     * 将字符串中的数字转换为上标形式，并去除'^'符号。
     * @param str 需要转换的字符串。
     * @return 转换后的字符串。
     */
    private static String convertToSuperscript(String str) {
        // 上标数字的Unicode字符
        char[] superscriptDigits = {'⁰', '¹', '²', '³', '⁴', '⁵', '⁶', '⁷', '⁸', '⁹'};
        StringBuilder result = new StringBuilder();
        boolean isExponentPart = false;
        for (char c : str.toCharArray()) {
            if (c == '^') {
                isExponentPart = true;
                result.append(c);
            } else if (isExponentPart && Character.isDigit(c)) {
                // 将数字字符转换为上标字符
                result.append(superscriptDigits[c - '0']);
            } else {
                result.append(c);
            }
        }
        return result.toString().replace("^", "");
    }


    public static String toChinese(int number) {
        if (number < 0 || number > 100) {
            throw new IllegalArgumentException("仅支持 0-100 的数字转换");
        }

        if (number <= 10) {
            return CHINESE_NUMBERS[number];
        }

        if (number <= 19) {
            return "十" + (number % 10 == 0 ? "" : CHINESE_NUMBERS[number % 10]);
        }

        if (number < 100) {
            int ten = number / 10;
            int unit = number % 10;
            return CHINESE_NUMBERS[ten] + "十" + (unit != 0 ? CHINESE_NUMBERS[unit] : "");
        }
        return "一百";
    }


    /**
     * 判断字符串是否表示为0
     * @param value 要判断的字符串
     * @return 如果字符串可以被解析为0，则返回true；否则返回false
     */
    public static  boolean isZero(String value) {
        try {
            return Double.parseDouble(value) == 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }


    /**
     * 批量中文转数字
     * @param chineseNumbers 中文数字字符串数组
     * @return 转换后的数字数组，转换失败的元素为null
     */
    public static Integer[] chineseToNumbers(String... chineseNumbers) {
        if (chineseNumbers == null || chineseNumbers.length == 0) {
            return new Integer[0];
        }

        Integer[] results = new Integer[chineseNumbers.length];
        for (int i = 0; i < chineseNumbers.length; i++) {
            results[i] = chineseToNumber(chineseNumbers[i]);
        }
        return results;
    }

    /**
     * 判断字符串是否为有效的中文数字
     * @param chineseNumber 待检查的字符串
     * @return 如果是有效的中文数字返回true，否则返回false
     */
    public static boolean isValidChineseNumber(String chineseNumber) {
        return chineseToNumber(chineseNumber) != null;
    }

    /**
     * 中文数字转换，带默认值
     * @param chineseNumber 中文数字字符串
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的数字或默认值
     */
    public static Integer chineseToNumber(String chineseNumber, Integer defaultValue) {
        Integer result = chineseToNumber(chineseNumber);
        return result != null ? result : defaultValue;
    }

    public static void main(String[] args) {
        // 测试数字转中文
        System.out.println("=== 数字转中文测试 ===");
        System.out.println("12 -> " + toChinese(12));
        System.out.println("25 -> " + toChinese(25));
        System.out.println("100 -> " + toChinese(100));

        System.out.println("\n=== 中文转数字测试 ===");
        // 测试中文转数字
        String[] testCases = {
            "零", "一", "二", "三", "十", "十五", "二十", "二十三",
            "一百", "一百零五", "一千", "一千二百三十四",
            "一万", "三万五千", "十万", "九十九万九千九百九十九",
            "壹", "拾", "佰", "仟", "萬", // 繁体测试
            "无效输入", "", null
        };

        for (String testCase : testCases) {
            Integer result = chineseToNumber(testCase);
            System.out.println(testCase + " -> " + result);
        }

        System.out.println("\n=== 批量转换测试 ===");
        Integer[] results = chineseToNumbers("一", "十", "一百", "一千", "一万");
        for (int i = 0; i < results.length; i++) {
            System.out.println("结果[" + i + "]: " + results[i]);
        }

        System.out.println("\n=== 有效性检查测试 ===");
        System.out.println("'三十五' 是否有效: " + isValidChineseNumber("三十五"));
        System.out.println("'abc' 是否有效: " + isValidChineseNumber("abc"));
    }

}
