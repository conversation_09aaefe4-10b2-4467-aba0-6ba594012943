package com.jdh.o2oservice.base.util;

import org.apache.commons.lang3.StringUtils;

import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/7
 */
public class NumberUtils {

    private static final String[] CHINESE_NUMBERS = {
            "零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"
    };


    /**
     * 将给定的数字字符串转换为科学计数法表示形式。
     * @param numStr 需要转换的数字字符串。
     * @return 转换后的科学计数法表示形式的字符串。
     */
    public static String toScientificNotation(String numStr) {
        try {
            if (StringUtils.isBlank(numStr)){
                return numStr;
            }
            // 将字符串转换为浮点数
            double num = Double.parseDouble(numStr);
            // 创建一个DecimalFormat实例，并设置科学计数法格式
            DecimalFormat df = new DecimalFormat("0.00E0");
            // 格式化数字
            String formatted = df.format(num);

            // 将E替换为×10^，并将上标数字替换为相应的Unicode字符
            formatted = formatted.replace("E", "×10^");
            formatted = formatted.replace("+", ""); // 去掉正号
            formatted = convertToSuperscript(formatted);

            return formatted;
        } catch (NumberFormatException e) {
            return numStr;
        }
    }

    /**
     * 将字符串中的数字转换为上标形式，并去除'^'符号。
     * @param str 需要转换的字符串。
     * @return 转换后的字符串。
     */
    private static String convertToSuperscript(String str) {
        // 上标数字的Unicode字符
        char[] superscriptDigits = {'⁰', '¹', '²', '³', '⁴', '⁵', '⁶', '⁷', '⁸', '⁹'};
        StringBuilder result = new StringBuilder();
        boolean isExponentPart = false;
        for (char c : str.toCharArray()) {
            if (c == '^') {
                isExponentPart = true;
                result.append(c);
            } else if (isExponentPart && Character.isDigit(c)) {
                // 将数字字符转换为上标字符
                result.append(superscriptDigits[c - '0']);
            } else {
                result.append(c);
            }
        }
        return result.toString().replace("^", "");
    }


    public static String toChinese(int number) {
        if (number < 0 || number > 100) {
            throw new IllegalArgumentException("仅支持 0-100 的数字转换");
        }

        if (number <= 10) {
            return CHINESE_NUMBERS[number];
        }

        if (number <= 19) {
            return "十" + (number % 10 == 0 ? "" : CHINESE_NUMBERS[number % 10]);
        }

        if (number < 100) {
            int ten = number / 10;
            int unit = number % 10;
            return CHINESE_NUMBERS[ten] + "十" + (unit != 0 ? CHINESE_NUMBERS[unit] : "");
        }
        return "一百";
    }


    /**
     * 判断字符串是否表示为0
     * @param value 要判断的字符串
     * @return 如果字符串可以被解析为0，则返回true；否则返回false
     */
    public static  boolean isZero(String value) {
        try {
            return Double.parseDouble(value) == 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 根据完整地址生成唯一的数字ID
     * 相同的地址会生成相同的ID，确保一致性
     * @param fullAddress 完整地址
     * @return 地址ID字符串（9位数字）
     */
    public static String generateAddressId(String fullAddress) {
        if (StringUtils.isBlank(fullAddress)) {
            return String.valueOf(System.currentTimeMillis());
        }

        try {
            // 对地址进行标准化处理
            String normalizedAddress = normalizeAddress(fullAddress);

            // 使用更稳定的哈希算法生成一致的数字ID
            long addressId = generateStableHash(normalizedAddress);

            // 确保ID在合理范围内（9位数字：100000000-999999999）
            addressId = Math.abs(addressId) % 900000000L + 100000000L;

            return String.valueOf(addressId);

        } catch (Exception e) {
            // 降级方案：使用时间戳
            return String.valueOf(System.currentTimeMillis());
        }
    }

    /**
     * 生成稳定的哈希值
     * 使用多种哈希算法组合，确保分布均匀且稳定
     * @param input 输入字符串
     * @return 稳定的哈希值
     */
    private static long generateStableHash(String input) {
        if (StringUtils.isBlank(input)) {
            return 0L;
        }

        // 方法1：使用Java内置hashCode
        long hash1 = input.hashCode();

        // 方法2：使用简单的多项式哈希
        long hash2 = 0;
        long prime = 31;
        for (int i = 0; i < input.length(); i++) {
            hash2 = hash2 * prime + input.charAt(i);
        }

        // 方法3：结合字符串长度和字符分布
        long hash3 = input.length() * 1000L;
        if (input.length() > 0) {
            hash3 += input.charAt(0) * 100L;
        }
        if (input.length() > 1) {
            hash3 += input.charAt(input.length() - 1) * 10L;
        }

        // 组合三种哈希值
        return hash1 ^ (hash2 << 16) ^ (hash3 << 8);
    }

    /**
     * 地址标准化处理
     * 去除空格、统一字符等，确保相同地址的一致性
     * @param address 原始地址
     * @return 标准化后的地址
     */
    private static String normalizeAddress(String address) {
        if (StringUtils.isBlank(address)) {
            return "";
        }

        String normalized = address.trim()
                // 去除各种空格
                .replaceAll("\\s+", "") // 去除所有空格
                .replaceAll("　", "") // 去除全角空格
                .replaceAll("\\u00A0", "") // 去除不间断空格

                // 统一标点符号
                .replace("（", "(") // 统一括号
                .replace("）", ")")
                .replace("【", "[") // 统一方括号
                .replace("】", "]")
                .replace("，", ",") // 统一逗号
                .replace("。", ".") // 统一句号
                .replace("；", ";") // 统一分号
                .replace("：", ":") // 统一冒号
                .replace("－", "-") // 统一连字符
                .replace("—", "-")
                .replace("～", "~") // 统一波浪号

                // 统一数字
                .replace("０", "0").replace("１", "1").replace("２", "2")
                .replace("３", "3").replace("４", "4").replace("５", "5")
                .replace("６", "6").replace("７", "7").replace("８", "8")
                .replace("９", "9")

                // 统一常见地址词汇（保持括号内容）
                .replace("大廈", "大厦") // 繁简转换
                .replace("街道", "街").replace("路口", "路")
                .replace("社区", "小区")
                .replace("apartment", "公寓")
                .replace("building", "栋").replace("Building", "栋")
                .replace("floor", "层").replace("Floor", "层")
                .replace("room", "室").replace("Room", "室")

                // 转小写（处理英文部分）
                .toLowerCase();

        // 去除连续的标点符号
        normalized = normalized.replaceAll("[,，.。;；:：\\-－—~～]{2,}", ",");

        // 去除首尾标点符号
        normalized = normalized.replaceAll("^[,，.。;；:：\\-－—~～]+", "");
        normalized = normalized.replaceAll("[,，.。;；:：\\-－—~～]+$", "");

        return normalized;
    }


    public static void main(String[] args) {
        System.out.println("=== NumberUtils 工具类测试 ===\n");

        // 测试数字转中文
        System.out.println("1. 数字转中文测试：");
        System.out.println("12 -> " + toChinese(12));
        System.out.println("25 -> " + toChinese(25));
        System.out.println("100 -> " + toChinese(100));

        // 测试地址ID生成
        System.out.println("\n2. 地址ID生成测试：");
        String[] testAddresses = {
            "北京市朝阳区建国门外大街1号",
            "北京市 朝阳区 建国门外大街 1号", // 相同地址，有空格
            "上海市浦东新区陆家嘴环路1000号",
            "深圳市南山区科技园南区深南大道9988号（腾讯大厦）"
        };

        for (String address : testAddresses) {
            String addressId = generateAddressId(address);
            System.out.println("地址: " + address);
            System.out.println("ID: " + addressId);
            System.out.println("---");
        }

        // 测试一致性
        System.out.println("\n3. 一致性测试：");
        String testAddress = "北京市朝阳区建国门外大街1号";
        String id1 = generateAddressId(testAddress);
        String id2 = generateAddressId("北京市 朝阳区 建国门外大街 1号");
        System.out.println("地址1: " + testAddress + " -> " + id1);
        System.out.println("地址2: 北京市 朝阳区 建国门外大街 1号 -> " + id2);
        System.out.println("一致性: " + (id1.equals(id2) ? "✓ 通过" : "✗ 失败"));

        System.out.println("\n=== 测试完成 ===");
    }

}
