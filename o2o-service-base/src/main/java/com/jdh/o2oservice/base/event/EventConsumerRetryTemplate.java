package com.jdh.o2oservice.base.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName EventConsumerRetryTemplate
 * @Description
 * <AUTHOR>
 * @Date 2024/5/7 16:56
 **/
@Data
@Builder
@AllArgsConstructor
public class EventConsumerRetryTemplate implements EventRetryTemplate{

    public static final int DEFAULT_MAX_ATTEMPTS = 3;

    private static final long DEFAULT_BACK_OFF_PERIOD = 1000L;

    public static final long DEFAULT_INITIAL_INTERVAL = 100L;

    public static final long DEFAULT_MAX_INTERVAL = 30000L;

    public static final double DEFAULT_MULTIPLIER = 2.0;

    private volatile Integer maxAttempts;

    private volatile Long backOffPeriod;

    private volatile Long initialInterval;

    private volatile Long maxInterval;

    private volatile Double multiplier;

    /**
     * 重试策略类型 1-固定时间 2-指数退避
     */
    private volatile Integer retryType;

    private EventConsumerRetryTemplate(){

    }

    /**
     * 默认重试策略
     * 固定3次，间隔1s
     * @return
     */
    public static EventConsumerRetryTemplate defaultInstance() {
        return EventConsumerRetryTemplate.builder().retryType(1).maxAttempts(DEFAULT_MAX_ATTEMPTS).backOffPeriod(DEFAULT_BACK_OFF_PERIOD).build();
    }

    /**
     * 指数退避策略重试
     * @param maxAttempts
     * @param initialInterval
     * @param multiplier
     * @param maxInterval
     * @return
     */
    public static EventConsumerRetryTemplate exponentialRetryInstance(int maxAttempts, long initialInterval, double multiplier, long maxInterval) {
        initialInterval = initialInterval < 1000 ? DEFAULT_BACK_OFF_PERIOD : initialInterval;
        maxInterval = maxInterval < 1000 ? DEFAULT_MAX_INTERVAL : maxInterval;
        return EventConsumerRetryTemplate.builder().retryType(2).maxAttempts(maxAttempts).initialInterval(initialInterval).multiplier(multiplier).maxInterval(maxInterval).build();
    }

    /**
     * 固定时长策略重试
     * @param maxAttempts
     * @param backOffPeriod
     * @return
     */
    public static EventConsumerRetryTemplate fixRetryInstance(int maxAttempts, long backOffPeriod) {
        backOffPeriod = backOffPeriod < 1000 ? DEFAULT_BACK_OFF_PERIOD : backOffPeriod;
        return EventConsumerRetryTemplate.builder().retryType(1).maxAttempts(maxAttempts).backOffPeriod(backOffPeriod).build();
    }

    /**
     * isValid
     * @return
     */
    @Override
    public boolean isValid() {
        if (this.retryType != 1 && this.retryType != 2) {
            return false;
        }
        if (this.retryType == 1) {
            return this.maxAttempts != null && this.backOffPeriod != null;
        }
        else if (this.retryType == 2) {
            return this.maxAttempts != null && this.initialInterval != null && this.multiplier != null && this.maxInterval != null;
        }
        return false;
    }

    /**
     * 获取重试时间
     * @param runNum
     * @return
     */
    @Override
    public long getRetryPeriod(Integer runNum) {
        if (this.retryType == 1) {
            return this.backOffPeriod.intValue() / 1000;
        }
        if (this.retryType == 2) {
            long millisInterval = (long) (this.initialInterval * this.multiplier * runNum);
            return Math.min(millisInterval, this.maxInterval) / 1000;
        }
        return 1;
    }

    /**
     * 是否执行重试
     * @return
     */
    @Override
    public boolean isRetry(Integer runNum) {
        return this.maxAttempts > runNum;
    }
}