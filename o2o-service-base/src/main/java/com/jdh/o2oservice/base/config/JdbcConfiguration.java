package com.jdh.o2oservice.base.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25 16:37
 */
@Component
@Configuration
public class JdbcConfiguration {
    @Bean
    public LoggingJdbcTransactionManager jdbcTransactionManager(DataSource dataSource) {
        return new LoggingJdbcTransactionManager(dataSource);
    }
}
