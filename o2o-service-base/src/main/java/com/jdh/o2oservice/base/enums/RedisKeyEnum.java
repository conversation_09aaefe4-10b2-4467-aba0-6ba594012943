package com.jdh.o2oservice.base.enums;

import lombok.Getter;

import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName:OrderNoSplitMsgApplicationImpl
 * @Description: 缓存key管理枚举
 * @Author: ya<PERSON><PERSON>hai
 * @Date: 2023/12/25 15:08
 * @Vserion: 1.0
 **/
@Getter
public enum RedisKeyEnum {

    /**
     *
     */
    BEAUTY_ORDER_NO_SPLIT_MSG_KEY("beauty_order_no_split_msg_key_", 1L, TimeUnit.SECONDS),

    ORDER_PROCESS_LOCK("orderProcessLock:{0}", 5L, TimeUnit.SECONDS),

    REPEAT_CHECK_PRE("REPEAT_CHECK_POP_ORDER_NO_SPLIT_{0}_{1}", 24L, TimeUnit.HOURS),

    POP_ORDER_SPLIT_KEY("POP_ORDER_SPLIT_KEY_{0}", 10L, TimeUnit.SECONDS),

    POP_ORDER_COMPLETE_KEY("POP_ORDER_COMPLETE_KEY_{0}", 1L, TimeUnit.HOURS),

    LOC_CODE_HANDLE_LOCK_KEY("LOC_CODE_HANDLE_LOCK_KEY_{0}_{1}_{2}", 10L, TimeUnit.SECONDS),

    USER_LAST_ADDRESS_ID_KEY("user_last_address_id_{0}", 100L, TimeUnit.DAYS),

    SAVE_SERVICE_INDICATOR_LOCK_KEY("SAVE_SERVICE_INDICATOR_LOCK_KEY_{0}", 10L, TimeUnit.SECONDS),

    SAVE_SERVICE_GROUP_LOCK_KEY("SAVE_SERVICE_GROUP_LOCK_KEY_{0}", 10L, TimeUnit.SECONDS),

    DELETE_SERVICE_GROUP_LOCK_KEY("DELETE_SERVICE_GROUP_LOCK_KEY_{0}", 10L, TimeUnit.SECONDS),

    SAVE_SERVICE_ITEM_REL_LOCK_KEY("SAVE_SERVICE_ITEM_REL_LOCK_KEY_{0}", 10L, TimeUnit.SECONDS),

    DELETE_SERVICE_ITEM_REL_LOCK_KEY("DELETE_SERVICE_ITEM_REL_LOCK_KEY_{0}", 10L, TimeUnit.SECONDS),

    SAVE_JDH_SERVICE_LOCK_KEY("SAVE_JDH_SERVICE_LOCK_KEY_{0}", 10L, TimeUnit.SECONDS),

    SERVICE_ITEM_COUNT_LOCK_KEY("SERVICE_ITEM_COUNT_LOCK_KEY_{0}", 10L, TimeUnit.MINUTES),

    PRODUCT_ITEM_INFO_LOCK_KEY("PRODUCT_ITEM_INFO_LOCK_KEY_{0}", 1L, TimeUnit.HOURS),

    SERVICE_SOURCE_UPDATE_LOCK_KEY("SERVICE_SOURCE_UPDATE_LOCK_KEY_{0}", 3L, TimeUnit.SECONDS),

    SERVICE_ITEM_DIFF_LOCK_KEY("SERVICE_ITEM_DIFF_LOCK_KEY_{0}", 60L, TimeUnit.MINUTES),

    SELF_HOME_ORDER_COMPLETE_KEY("SELF_HOME_ORDER_COMPLETE_KEY_{0}", 24L, TimeUnit.HOURS),
    /**
     * 创建运单接口防重复提交缓存前缀
     */
    CREATE_SHIP_ORDER_LOCK_KEY_PREFIX("CREATE_SHIP_ORDER_LOCK_KEY_PREFIX{0}:{1}", 10L, TimeUnit.SECONDS),

    /**
     * 工单验证码核销防重
     */
    WORK_CODE_VERIFICATION_LOCK_KEY("WORK_CODE_VERIFICATION_LOCK_KEY_{0}", 3L, TimeUnit.SECONDS),

    /**
     * 检验项目绑定样本条码防重
     */
    BIND_SPECIMEN_CODE_LOCK_KEY("BIND_SPECIMEN_CODE_LOCK_KEY_{0}", 3L, TimeUnit.SECONDS),

    /**
     * 创建运单
     */
    DADA_CREATE_SHIP("O2O:ANGELPROMISE:CREATESHIP:{0}:{1}", 30L, TimeUnit.SECONDS),

    /**
     * 提交样本码信息
     */
    SUBMIT_SAMPLING_CODE("O2O:ANGELPROMISE:SUBMITCODE:{0}", 5L, TimeUnit.SECONDS),

    /**
     * 服务完成
     */
    SERVICE_FINISH("O2O:ANGELPROMISE:SERVICEFINISH:{0}", 5L, TimeUnit.SECONDS),

    /**
     * 取消运单
     */
    DADA_CANCEL_SHIP("O2O:ANGELPROMISE:CANCELSHIP:{0}:{1}", 5L, TimeUnit.SECONDS),

    /**
     * 执行任务单确认送检
     */
    EXECUTE_TASK_CONFIRM_LOCK_KEY("O2O:EXECUTE:TASK:CONFIRM:{0}", 5L, TimeUnit.SECONDS),

    /**
     * 缓存任务的执行时间
     */
    PLAN_OUT_TIME("O2O:ANGELPROMISE:REMINDOUT", 30L, TimeUnit.MINUTES),

    /**
     * 缓存任务的执行时间
     */
    FINISH_SERVICE_TIME("O2O:ANGELPROMISE:FINISH", 30L, TimeUnit.MINUTES),

    /**
     * 缓存任务的执行时间
     */
    START_SERVICE_TIME("O2O:ANGELPROMISE:START", 30L, TimeUnit.MINUTES),

    /**
     * 服务站绑定资源分布式锁
     */
    ANGEL_STATION_BIND_ANGEL_KEY("O2O:STATION_ANGEL:SAVE:LOCK:{0}", 5L, TimeUnit.SECONDS),

    /**
     * 服务站绑定资源分布式锁
     */
    ANGEL_STATION_BIND_SKU_KEY("O2O:STATION_SKU:SAVE:LOCK:{0}", 5L, TimeUnit.SECONDS),

    /**
     * 创建服务站分布式锁
     */
    CREATE_ANGEL_STATION_KEY("O2O:STATION:SAVE:LOCK:{0}", 120L, TimeUnit.SECONDS),

    /**
     * 推送报告锁
     */
    PUSH_REPORT_LOCK_KEY("O2O:PUSH:REPORT:LOCK:{0}",5L,TimeUnit.SECONDS),

    /**
     * 创建样码分布式锁
     */
    CREATE_SPECIMEN_CODE_LOCK_KEY("O2O:PRODUCT:SPECIMEN:CODE:LOCK:{0}", 60L, TimeUnit.MINUTES),

    /**
     * 创建样码需要，每天递增
     */
    CREATE_SPECIMEN_CODE_DAY_INCR_KEY("O2O:PRODUCT:SPECIMEN:CODE:DAY:INCR:{0}", 1L, TimeUnit.DAYS),

    /**
     * 天算流水号缓存
     */
    TIAN_SUAN_KEY("O2O:SUPPORT:TIAN:SUAN:KEY:{0}", 15L, TimeUnit.DAYS),

    /**
     * 服务者排班 保存/编辑 锁
     */
    ANGEL_SCHEDULE_SAVE_LOCK_KEY("O2O:ANGEL:SCHEDULE:SAVE:LOCK:{0}",10L,TimeUnit.SECONDS),

    /**
     * 排班 模板 状态更新 锁
     */
    ANGEL_SCHEDULE_TEMPLATE_UPDATE_STATUS_LOCK_KEY("O2O:ANGEL:SCHEDULE:TEMPLATE:UPDATE:STATUS:LOCK:{0}",200L,TimeUnit.MILLISECONDS),

    /**
     * 创建样码分布式锁
     */
    SEND_EBS_LOCK_KEY("O2O:PRODUCT:EBS:SEND:LOCK:{0}", 10L, TimeUnit.SECONDS),

    /**
     * 运单送达分布式锁
     */
    DELIVER_LOCK_KEY("DELIVER_LOCK_KEY_{0}", 3L, TimeUnit.SECONDS),

    /**
     * 履约检测单作废加锁
     */
    MEDICAL_PROMISE_LOCK_KEY("O2O:MEDICAL:PROMISE:LOCK:{0}",5L,TimeUnit.SECONDS),

    /**
     * 履约检测单全部报告已出锁
     */
    MEDICAL_PROMISE_ALL_GENERATE_REPORT_KEY("O2O:ALL:GENERATE:REPORT:LOCK:{0}",5L,TimeUnit.SECONDS),

    /**
     * 与图地址缓存key前缀
     */
    GEO_ADDRESS_CACHE_PREFIX("O2O_SERVICE_ANGEL_ADDRESS_{0}_{1}_{2}_{3}", 3L, TimeUnit.MINUTES),

    /**
     * 实验室履约单派发锁
     */
    STORE_DISPATCH_LOCK_KEY("O2O:MEDICAL:PROMISE:LOCK:{0}",10L,TimeUnit.SECONDS),


    /**
     * 实验室履约单冻结锁
     */
    MEDICAL_PROMISE_FREEZE_LOCK_KEY("O2O:MEDICAL:PROMISE:FREEZE:LOCK:{0}",5L,TimeUnit.SECONDS),

    /**
     *
     */
    ORDER_SERVICE_SETTLE_LOCK_KEY("ORDER_SERVICE_SETTLE_LOCK_KEY_{0}", 10L, TimeUnit.SECONDS),

    /**
     * 派单任务接单处理锁
     */
    DISPATCH_RECEIVING_ORDER_LOCK_KEY("O2O:DISPATCH:RECEIVING:ORDER:LOCK:{0}", 5L, TimeUnit.SECONDS),

    /**
     * 派单小队更新处理锁
     */
    DISPATCH_TEAM_UPDATE_LOCK_KEY("O2O:DISPATCH_TEAM:UPDATE:DATA:LOCK:{0}", 20L, TimeUnit.SECONDS),

    /**
     * 护士同步主数据
     */
    ANGEL_UPDATE_MAIN_DATA_LOCK_KEY("O2O:ANGEL:UPDATE:MAIN:DATA:LOCK:{0}", 20L, TimeUnit.SECONDS),

    /**
     * 护士同步技能
     */
    ANGEL_UPDATE_SKILL_LOCK_KEY("O2O:ANGEL:UPDATE:SKILL:LOCK:{0}", 10L, TimeUnit.SECONDS),

    /**
     * 护士同步开关诊状态
     */
    ANGEL_WORK_STATUS_LOCK_KEY("O2O:ANGEL:WORK:STATUS:LOCK:{0}", 10L, TimeUnit.SECONDS),

    /**
     * 更新护士全兼职标签
     */
    ANGEL_UPDATE_JOB_NATURE_LOCK_KEY("ANGEL_UPDATE_JOB_NATURE_LOCK_KEY_{0}", 5L, TimeUnit.SECONDS),

    /**
     * 导入机构人员导入锁
     */
    IMPORT_PROVIDER_STAFF_IMPORT_LOCK_KEY("O2O:PROVIDER:STAFF:IMPORT:LOCK:{0}", 3L, TimeUnit.HOURS),

    /**
     * 导入机构人员导入时间记录
     */
    IMPORT_PROVIDER_STAFF_TIME_RECORD_KEY("O2O:PROVIDER:STAFF:IMPORT:TIME:{0}", 48L, TimeUnit.HOURS),

    /**
     * 预约单完成服务之结算
     */
    JDH_APPOINTMENT_SETTLE_LOCK_KEY("JDH_APPOINTMENT_SETTLE_LOCK_KEY_{0}", 5L, TimeUnit.SECONDS),

    /**
     * 预约单完成服务之结算
     */
    JDH_APPOINTMENT_PATIENT_SETTLE_LOCK_KEY("JDH_APPOINTMENT_PATIENT_SETTLE_LOCK_KEY_{0}", 5L, TimeUnit.SECONDS),

    /**
     * 预约单退款之结算
     */
    JDH_ORDER_SETTLE_LOCK_KEY("JDH_ORDER_SETTLE_LOCK_KEY_{0}", 5L, TimeUnit.SECONDS),
    /**
     * 导入机构人员导入百分比
     */
    IMPORT_PROVIDER_STAFF_PERCENTAGE_KEY("O2O:PROVIDER:STAFF:IMPORT:PERCENTAGE:{0}", 24L, TimeUnit.HOURS),

    /**
     * 工单状态变更key
     */
    ANGEL_WORK_STATUS_CHANGE_LOCK_KEY("O2O:ANGEL:PROMISE:WORK:MODIFY:STATUS:{0}:{1}", 5L, TimeUnit.SECONDS),

    /**
     * 任务单状态变更key
     */
    ANGEL_TASK_STATUS_CHANGE_LOCK_KEY("O2O:ANGEL:PROMISE:TASK:MODIFY:STATUS:{0}:{1}", 5L, TimeUnit.SECONDS),

    /**
     * 实验室结算锁
     */
    STATION_SETTLEMENT_LOCK_KEY("O2O:STATION_SETTLEMENT_LOCK_KEY:{0}", 10L, TimeUnit.SECONDS),
    /**
     * 预约单推送天算记录
     */
    STATION_SETTLEMENT_TS_REDIS_KEY("O2O:STATION_SETTLEMENT_TS_REDIS_KEY:{0}", 15L, TimeUnit.DAYS),

    /**
     * 预约单推送天算记录
     */
    STATION_PATIENT_SETTLEMENT_TS_REDIS_KEY("O2O:STATION_PATIENT_SETTLEMENT_TS_REDIS_KEY:{0}", 15L, TimeUnit.DAYS),

    /**
     * 实验室检测单推送EBS记录
     */
    STATION_SETTLEMENT_EBS_REDIS_KEY("O2O:STATION_SETTLEMENT_EBS_REDIS_KEY:{0}", 15L, TimeUnit.DAYS),
    /**
     * 实验室检测单推送EBS锁
     */
    STATION_SETTLEMENT_EBS_LOCK_KEY("O2O:STATION_SETTLEMENT_EBS_LOCK_KEY:{0}", 10L, TimeUnit.SECONDS),
    /**
     * 订单取消支付lock
     */
    ORDER_CANCEL_LOCK_KEY("ORDER_CANCEL_LOCK_KEY_LOCK_KEY:{0}", 3L, TimeUnit.SECONDS),

    /**
     * 检验单的提单信息缓存
     */
    SUBMIT_ORDER_PARTNER_SOURCE_KEY("ORDER_PARTNER_SOURCE_{0}_{1}", 7L, TimeUnit.DAYS),
    /**
     * 退款锁
     */
    JDH_ORDER_REFUND_LOCK_KEY("JDH_ORDER_REFUND_LOCK_KEY_{0}", 5L, TimeUnit.SECONDS),
    /**
     * 服务者上传位置锁
     */
    ANGLE_SUBMIT_LOCATION("ANGLE_SUBMIT_LOCATION_NEW_{0}", 5L, TimeUnit.SECONDS),

    /**
     * 指标导入锁
     */
    IMPORT_INDICATOR_IMPORT_LOCK_KEY("O2O:INDICATOR:IMPORT:LOCK:{0}", 3L, TimeUnit.HOURS),

    /**
     * 指标标准分类导入锁
     */
    IMPORT_INDICATOR_CATEGORY_IMPORT_LOCK_KEY("O2O:INDICATOR:CATEGORY:IMPORT:LOCK:{0}", 3L, TimeUnit.HOURS),

    /**
     * 指标标准分类导入锁
     */
    IMPORT_INDICATOR_BIZ_CATEGORY_IMPORT_LOCK_KEY("O2O:INDICATOR:BIZ:CATEGORY:IMPORT:LOCK:{0}", 3L, TimeUnit.HOURS),

    /**
     * 商品项目导入锁
     */
    IMPORT_PRODUCT_ITEM_IMPORT_LOCK_KEY("O2O:PRODUCT:SERVICE:ITEM:IMPORT:LOCK:{0}", 3L, TimeUnit.HOURS),

    /**
     * 实验室项目导入锁
     */
    IMPORT_STATION_SERVICE_ITEM_IMPORT_LOCK_KEY("O2O:STATION:SERVICE:ITEM:IMPORT:LOCK:{0}", 3L, TimeUnit.HOURS),

    /**
     * 商品导入锁
     */
    IMPORT_PRODUCT_SKU_IMPORT_LOCK_KEY("O2O:PRODUCT:SKU:IMPORT:LOCK:{0}", 3L, TimeUnit.HOURS),

    /**
     * sku和服务站绑定关系数据
     */
    ANGLE_STATION_SKU_REL_DATA("ANGLE_STATION_SKU_REL_DATA_{0}_{1}", 10L, TimeUnit.SECONDS),

    /**
     * 退款成功
     */
    REFUND_SUCC_TRANSACTION_KEY("O2O:REFUND:SUCC:LOCK:{0}", 3L, TimeUnit.SECONDS),

    /**
     * 提交订单前 用户动作为立即购买时 key pin value skuList
     */
    EXECUTE_ACTION_BUY_NOW_PIN_SKU_LIST_KEY("O2O_TRADE_BUY_NOW_PIN_SKU_LIST:{0}", 24L, TimeUnit.HOURS),

    /**
     * 到店小程序门店id缓存
     */
    STORE_PROGRAM_STORE_NO_CACHE_PREFIX("O2O:VIA:STORE_PROGRAM:STORE_CACHE:{0}:{1}", 30L, TimeUnit.MINUTES),
    /**
     * 签收key
     */
    MEDICAL_PROMISE_CHECK_KEY("O2O:MEDICAL:PROMISE:CHECK:LOCK:{0}",3L,TimeUnit.SECONDS),

    MEDICAL_PROMISE_SERIAL_KEY("O2O:MEDICAL:PROMISE:SERIAL:KEY:{0}:{1}",1L,TimeUnit.SECONDS),

    /**
     * 服务站库存扣减锁
     */
    ANGEL_STATION_INVENTORY_LOCK_KEY_PREFIX("angel:station:inventory:{0}:{1}:{2}",3L,TimeUnit.SECONDS),

    /**
     * 商品项目
     */
    JD_PRODUCT_ID_INSERT_LOCK_KEY("O2O:JD:PRODUCT:INSERT:LOCK:{0}", 5L, TimeUnit.SECONDS),

    JD_BIND_SPECIMEN_CODE_LOCK_KEY("O2O:JD:BIND:SPECIMEN:CODE:LOCK:{0}", 10L, TimeUnit.SECONDS),

    JD_SYNC_SPECIMEN_STATION_LOCK_KEY("O2O:JD:BIND:SPECIMEN:CODE:LOCK:{0}", 10L, TimeUnit.SECONDS),

    JD_BATCH_SYNC_SPECIMEN_STATION_LOCK_KEY("O2O:JD:BATCH:SYNC:SPECIMEN:STATION:LOCK:{0}", 10L, TimeUnit.SECONDS),

    JD_MEDICAL_BIND_STATION_LOCK_KEY("O2O:JD:JD_MEDICAL_BIND_STATION_LOCK_KEY:LOCK:{0}", 10L, TimeUnit.SECONDS),

    /**
     * 服务单过期记收锁
     */
    VOUCHER_EXPIRE_SETTLE_LOCK("O2O:JD:PROMISE:SETTLE:VOUCHER_EXPIRE_SETTLE_LOCK:{0}_{1}", 2L, TimeUnit.HOURS),

    /**
     * 服务单过期记收商品数量记录
     */
    VOUCHER_EXPIRE_SETTLE_NUM_PREFIX("O2O:JD:PROMISE:SETTLE:VOUCHER_EXPIRE_SETTLE_NUM_PREFIX:{0}_{1}", 2L, TimeUnit.HOURS),

    REPORT_SHARE_REDIS_KEY("O2O:REPORT:SHARE:REDIS:KEY:{0}:{1}", 1L, TimeUnit.HOURS),

    STRUCT_REPORT_CACHE_KEY("O2O:STRUCT:REPORT:REDIS:KEY:{0}",10L,TimeUnit.MINUTES),
    /**
     * 报告查询-设备免校验缓存，24h 短信,0:promiseId,1:短信验证码
     */
    REPORT_UUID_AUTH_CACHE("O2O:REPORT:UUID:AUTH:CACHE:KEY:{0}:{1}",24L,TimeUnit.HOURS),
    /**
     * 报告查询-设备鉴权短信缓存 0:promiseId,1:短信验证码
     */
    REPORT_UUID_AUTH_MESSAGE_CACHE("O2O:REPORT:UUID:AUTH:C:MESSAGE:ACHE:KEY:{0}:{1}",5L,TimeUnit.MINUTES),

    SAVE_PART_ANGEL_LOCK_KEY("O2O:STRUCT:PART:LOCK:KEY:{0}",10L,TimeUnit.SECONDS),

    PRODUCT_GET_COUPON_KEY("O2O:PRODUCT:GET:COUPON:KEY:{0}", 5L, TimeUnit.SECONDS),


    /**
     * 刷履约单四级地址锁key前缀
     */
    FLUSH_PROMISE_ADDRESS_PREFIX("O2O:FLUSH:ADDRESS:REDIS:KEY:{0}:{1}",10L,TimeUnit.MINUTES),

    /**
     * 缓存闪送店铺前缀
     */
    SHANSONG_SHOP_CACHE_PREFIX("O2O:SHANSONG:SHOP:REDIS:KEY:{0}:{1}",30L,TimeUnit.DAYS),

    /**
     * 派单修改服务日期
     */
    DISPATCH_MODIFY_SERVICE_DATE_LOCK_KEY("O2O:DISPATCH:MODIFY:SERVICE:DATE:LOCK:{0}", 30L, TimeUnit.SECONDS),
    /**
     * 快检一键购药幂等key
     */
    QUICK_CHECK_DRUG_PROMISE_PATIENT("QUICK:CHECK:DRUG:PROMISE:PATIENT:{0}:{1}",3L,TimeUnit.DAYS),

    /**
     * 交易行为服务升级key前缀
     */
    TRADE_ACTION_SERVICE_UPGRADE_KEY("O2O:TRADE:ACTION:SERVICE:UPGRADE:KEY:{0}", 5L, TimeUnit.SECONDS),

    /**
     * 自动化测试白名单缓存key前缀
     */
    AUTO_TEST_WHITE_LIST_CACHE("O2O:AUTO_TEST_WHITE_LIST_CACHE:CACHE:KEY:{0}",7L,TimeUnit.DAYS),
    /**
     * 商品标签-商品类型2的清单
     */
    PRODUCT_LABEL_SKU_TYPE2("{PRODUCT_LABEL}_SKU_TYPE2", 30L, TimeUnit.DAYS),
    /**
     * 商品标签-开城的省
     */
    PRODUCT_LABEL_AVAIABLE_PROVINCE("{PRODUCT_LABEL}_AVAIABLE_PROVINCE", 30L, TimeUnit.DAYS),
    /**
     * 商品标签-开城的市
     */
    PRODUCT_LABEL_AVAIABLE_CITY("{PRODUCT_LABEL}_AVAIABLE_CITY", 30L, TimeUnit.DAYS),
    /**
     * 商品标签-开城的区
     */
    PRODUCT_LABEL_AVAIABLE_COUNTY("{PRODUCT_LABEL}_AVAIABLE_COUNTY", 30L, TimeUnit.DAYS),
    /**
     * 商品标签-开城的镇
     */
    PRODUCT_LABEL_AVAIABLE_TOWN("{PRODUCT_LABEL}_AVAIABLE_TOWN", 30L, TimeUnit.DAYS),

    /**
     * 商详升级费减免弹窗提示
     */
    PRODUCT_DISCOUNT_FEE_CONFIRM("PRODUCT_DISCOUNT_FEE_CONFIRM_{0}", 24L, TimeUnit.HOURS),
    /**
     * 互医检验单结算升级费减免弹窗提示
     */
    NH_SETTLE_DISCOUNT_FEE_CONFIRM("NH_SETTLE_DISCOUNT_FEE_CONFIRM_{0}", 24L, TimeUnit.HOURS),

    /**
     * 修改库存分布式锁前缀
     */
    MODIFY_INVENTORY_PREFIX_KEY("MODIFY_INVENTORY_PREFIX_KEY_{0}_{1}_{2}_{3}_{4}", 10L, TimeUnit.SECONDS),

    /**
     * 实验室-用户家地址距离缓存
     */
    STATION_USER_DISTANCE_KEY("STATION_USER_DISTANCE_KEY_{0}_{1}", 30L, TimeUnit.MINUTES),
    /**
     * 实验室检测单状态推送
     */
    MEDICAL_PROMISE_LAB_PUSH_STATUS_PREFIX_KEY("MEDICAL_PROMISE_LAB_PUSH_STATUS:{0}:{1}:{2}:{3}",1L,TimeUnit.DAYS),

    /**
     * 修改预约
     */
    MODIFY_PROMISE("MODIFY_PROMISE_LOCK_{0}", 5L, TimeUnit.SECONDS),

    /**
     * 数据字段变更
     */
    DB_FIELD_VALUE_CHANGE("DB_FIELD_CHANGE_{0}", 3600L, TimeUnit.SECONDS),

    /**
     * 订单详情缓存
     */
    JD_APP_ORDER_DETAIL_PROCESS_INFO_FLOOR("JD_APP_DETAIL_PROCESS_INFO_FLOOR_{0}", 15L, TimeUnit.DAYS),

    /**
     * 订单列表缓存
     */
    JD_APP_ORDER_LIST_PROCESS_INFO_FLOOR("JD_APP_LIST_PROCESS_INFO_FLOOR_{0}", 15L, TimeUnit.DAYS),
    /**
     * 外部领域实体结算价配置
     */
    SETTLEMENT_EXTERNAL_DOMAIN_FEE_LOCK_KEY("SETTLEMENT_EXTERNAL_DOMAIN_FEE_KEY_{0}", 30L, TimeUnit.SECONDS),
    /**
     * 缓存
     */
    MED_PROMISE_REPORT_SMS_GENERATE("MED_PROMISE_REPORT_SMS_GENERATE_{0}",30L,TimeUnit.SECONDS),

    /**
     * 运力缓存缓存
     */
    STORE_QUERY_MEDICAL_LIST_SHIP_INFO("STORE_QUERY_MEDICAL_LIST_SHIP_INFO_{0}", 5L, TimeUnit.DAYS) ,

    /* 保存风险评估单的缓存键，用于防止重复操作。
     * 键的格式为"SAVE_RISK_LOCK_{0}"，其中"{0}"为占位符，实际使用时会被替换为具体的参数。
     * 该缓存键的过期时间为5秒。
     */
    SAVE_RISK_LOCK("SAVE_RISK_LOCK_{0}", 5L, TimeUnit.SECONDS),


    /**
     * 三方运力经纬度
     */
    DELIVERY_POSITION("DELIVERY_POSITION_{0}", 1L, TimeUnit.DAYS),

    /**
     * 无人机航班信息
     */
    UAV_FLIGHT("UAV_FLIGHT_{0}_{1}_{2}", 1L, TimeUnit.DAYS),
    /**
     * 无人机航班信息
     */
    UAV_FLIGHT_LOCK_KEY("UAV_FLIGHT_LOCK_KEY_{0}_{1}_{2}", 1L, TimeUnit.DAYS),

    /**
     * 护士点击已到达
     */
    ANGEL_WORK_ARRIVED_ACTION("ANGEL_WORK_ARRIVED_ACTION_{0}", 3L, TimeUnit.SECONDS),

    /**
     * 护士点击已到达
     */
    ANGEL_WORK_IN_SERVICE_ACTION("ANGEL_WORK_IN_SERVICE_ACTION_{0}", 3L, TimeUnit.SECONDS),

    /**
     * 护士修改预约
     */
    ANGEL_MODIFY_DATE_LIMIT_TIMES_BY_DAY("ANGEL_MODIFY_DATE_LIMIT_TIMES_BY_DAY_{0}", 1L, TimeUnit.DAYS),


    /**
     * 提交护理单
     */
    SUBMIT_ANGEL_SERVICE_RECORD_KEY("SUBMIT_ANGEL_SERVICE_RECORD_KEY_{0}_{1}", 3L, TimeUnit.SECONDS),

    JD_ORDER_RECEIVE_LOCK_KEY("JD_ORDER_RECEIVE_LOCK_KEY_{0}", 10L, TimeUnit.SECONDS),

    /**
     * 评估单C端退款
     */
    RISK_ASS_REFUND_LOCK_PREFIX("RISK_ASS_REFUND_LOCK_PREFIX_{0}", 10L, TimeUnit.SECONDS),


    SUBMIT_MEDICAL_CERTIFICATE_FILE("SUBMIT_MEDICAL_CERTIFICATE_FILE_{0}", 5L, TimeUnit.SECONDS),



    O2O_TRADE_AVAILABLE_APPOINTMENT_CALENDAR("O2O_TRADE_AVAILABLE_APPOINTMENT_CALENDAR", 1L, TimeUnit.DAYS),



    /**
     * 报告审核
     */
    SAVE_REPORT_AUDIT_LOCK("SAVE_REPORT_AUDIT_LOCK_{0}", 5L, TimeUnit.SECONDS),


    O2O_WAIT_PAYMENT_ORDER_KEY("O2O:WAIT:PAYMENT:ORDER:KEY:{0}", 120L, TimeUnit.MINUTES),


    /**
     * 创建报告检测单,0:stationId,1.medicalPromiseId
     */
    HANDLE_REPORT_AUDIT_LOCK("HANDLE_REPORT_AUDIT_LOCK_{0}_{1}", 5L, TimeUnit.SECONDS),

    /**
     * 提交预约单分布式锁
     */
    SUBMIT_PROMISE_LOCK_PREFIX("SUBMIT_PROMISE_LOCK_PREFIX_{0}", 5L, TimeUnit.SECONDS),

    /**
     * 互医服务者信息变更pin纬度锁
     */
    NETHP_CHANGE_ANGEL_PIN_LOCK("NETHP_CHANGE_ANGEL_PIN_LOCK_{0}", 10L, TimeUnit.SECONDS),

    /**
     * 互医服务者审核消息pin纬度锁
     */
    NETHP_AUDIT_ANGEL_PIN_LOCK("NETHP_AUDIT_ANGEL_PIN_LOCK_{0}", 10L, TimeUnit.SECONDS),


    /**
     * 质控项目redis,永久缓存
     */
    QUALITY_SERVICE_ITEM_KEY("QUALITY_SERVICE_ITEM_KEY", 365L, TimeUnit.DAYS),

    /**
     * 保存报告指标锁
     */
    SAVE_REPORT_INDICATOR_LOCK("SAVE_REPORT_INDICATOR_LOCK_{0}", 5L, TimeUnit.SECONDS),

    /**
     * 服务站库存分布式锁
     */
    ANGEL_STATION_INVENTORY_LOCK("ANGEL_STATION_INVENTORY_LOCK_{0}_{1}", 5L, TimeUnit.SECONDS),

    /**
     * 指定护士 sku 配置
     */
    APPOINT_ANGEL_SKU_IMPORT_LOCK_KEY("APPOINT_ANGEL_SKU_IMPORT_LOCK_KEY_{0}", 3L, TimeUnit.SECONDS),
    ;


    /**
     * 缓存前缀
     */
    private String redisKeyPrefix;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 时间单位
     */
    private TimeUnit expireTimeUnit;

    RedisKeyEnum(String redisKeyPrefix, Long expireTime, TimeUnit expireTimeUnit) {
        this.redisKeyPrefix = redisKeyPrefix;
        this.expireTime = expireTime;
        this.expireTimeUnit = expireTimeUnit;
    }

    public static String getRedisKey(RedisKeyEnum redisKeyEnum, Object... spaceFill){
        return MessageFormat.format(redisKeyEnum.getRedisKeyPrefix(), spaceFill);
    }
}
