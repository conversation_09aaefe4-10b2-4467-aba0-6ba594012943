package com.jdh.o2oservice.base.enums;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @Description 权益提供方
 * @Date 2019/12/27 20:17
 */
public enum ThreadPoolConfigEnum {


    /**
     * 默认线程池大小，线程数时CPU的两倍
     */
    DEFAULT("DEFAULT", 8, 8, null),

    /**
     * 报警线程池大小，线程数时CPU的两倍
     */
    ALARM_POOL("ALARM_POOL", 2, 100, null),

    /**
     * QPM:120 TP99:50ms 集群机器:20台  CompletableFuture数量：15以内
     */
    VIA_FLOOR_HAND_POOL("VIA_FLOOR_HAND_POOL", 16, 500, null),

    /**
     * 运营端订详数据处理
     */
    MAN_VIA_DETAIL_HAND_POOL("MAN_VIA_DATA_HAND_POOL", 4, 100, null),

    /**
     * 地址命中围栏处理
     */
    ADDRESS_GIS_HIT_POOL("ADDRESS_GIS_HIT_POOL", 4, 20, null),

    /**
     * 商品查询-运营端
     * QPM:10 TP99:50ms 集群机器:20台  CompletableFuture数量：3
     */
    SKU_MAN_HAND_POOL("SKU_MAN_HAND_POOL", 2, 10, null),

    /**
     * 商品查询-C端
     * QPM:60 TP99:500ms 集群机器:20台  CompletableFuture数量：25
     */
    SKU_C_HAND_POOL("SKU_C_HAND_POOL", 25, 50, null),

    /**
     * 运营端导入
     */
    MAN_IMPORT_HAND_POOL("MAN_IMPORT_HAND_POOL", 2, 4, null),

    /**
     * 商品价格查询
     * QPM:120 TP99:50ms 集群机器:20台  CompletableFuture数量：1
     */
    SKU_PRICE_C_HAND_POOL("SKU_PRICE_C_HAND_POOL", 20, 200, null),

    /**
     * 自定义列订单查询
     * QPM:1000 TP99:100ms 集群机器:20台  CompletableFuture数量：15
     */
    CUSTOM_ORDER_HAND_POOL("CUSTOM_ORDER_HAND_POOL", 20, 200, null),

    /**
     * OPS Pool
     */
    OPS_POOL("OPS_POOL", 4, 100, null),
    
    DISPATCH_POOL("DISPATCH_POOL", 4, Integer.MAX_VALUE, null),

    /**
     * 报告查询
     */
    REPORT_C_POOL("REPORT_C_POOL", 20, 20, null),
    VOICE_JOB_POOL("VOICE_JOB_POOL", 4, Integer.MAX_VALUE, null),

    /**
     * 操作日志记录线程池，超过任务上限时丢弃
     */
    OPLOG_SAVE_THREAD_POOL("OPLOG_SAVE_THREAD_POOL", 4, 500, new ThreadPoolExecutor.DiscardPolicy()),
    /**
     * 长连接
     */
    WS_POOL("WS_POOL", 4, 100, null),

    /**
     * 操作日志记录线程池
     */
    OPLOG_QUERY_THREAD_POOL("OPLOG_QUERY_THREAD_POOL", 4, 200, null),

    /**
     * 获取图片exif
     */
    GET_IMG_EXIF("GET_IMG_EXIF", 4, 500, null),

    LOW_PRIORITY_POOL("LOW_PRIORITY_POOL", 4, Integer.MAX_VALUE, null),

    /**
     * QPM:1000 TP99:100ms 集群机器:20台  CompletableFuture数量：15
、     */
    CUSTOM_ORDER_DETAIL_HAND_POOL("CUSTOM_ORDER_DETAIL_HAND_POOL", 15, 200, null),


    /**
     * 用户行为接口使用，618峰值一分钟34个请求，20台机器，一分钟2个请求不到，每个请求使用2个CompletableFuture
     */
    EXECUTE_ACTION_HAND_POOL("EXECUTE_ACTION_HAND_POOL", 8, 200, null),

    FILE_MANAGE("FILE_MANAGE", 4, 200, null),

    /**
     * 用户行为
     */
    EXECUTE_SHIP_AUTO_COMPLETE_POOL("EXECUTE_SHIP_AUTO_COMPLETE_POOL", 5, 5, null),
    ;
    
    /**
     * 线程池名字
     */
    private final String poolName;
    
    /**
     * 核心线程数量
     */
    private final int poolSize;
    /**
     * 阻塞任务队列的长度
     */
    private final int capacity;

    /**
     * 线程池的可用线程数和阻塞队列的容量全部打满后，执行此任务策略：
     * 1. CallerRunsPolicy ：这个策略重试添加当前的任务，他会自动重复调用 execute() 方法，直到成功。
     * 2. AbortPolicy ：对拒绝任务抛弃处理，并且抛出异常。
     * 3. DiscardPolicy ：对拒绝任务直接无声抛弃，没有异常信息。
     * 4. DiscardOldestPolicy ：对拒绝任务不抛弃，而是抛弃队列里面等待最久的一个线程，然后把拒绝任务加到队列。
     */
    private final RejectedExecutionHandler rejectedHandler;

    /**
     * @param poolName 线程池名字
     * @param poolSize 核心线程数量
     * @param capacity 阻塞任务队列的长度
     * @param rejectedHandler 拒绝策略
     */
    ThreadPoolConfigEnum(String poolName, int poolSize, int capacity, RejectedExecutionHandler rejectedHandler) {
        this.poolName = poolName;
        this.poolSize = poolSize;
        this.capacity = capacity;
        this.rejectedHandler = rejectedHandler;
    }

    public String getPoolName() {
        return poolName;
    }

    public int getPoolSize() {
        return poolSize;
    }

    public int getCapacity() {
        return capacity;
    }

    public RejectedExecutionHandler getRejectedHandler() {
        return rejectedHandler;
    }
}
