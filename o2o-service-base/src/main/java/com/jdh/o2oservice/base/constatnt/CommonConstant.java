package com.jdh.o2oservice.base.constatnt;

import com.jdh.o2oservice.base.util.TimeUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @author: yang<PERSON><PERSON>
 * @date: 2023/12/22 10:29 上午
 * @version: 1.0
 */
public interface CommonConstant {

    /** 默认过期时间 */
    LocalDateTime EXPIRE_TIME = LocalDateTime.of(2099,12,30,0,0,0);
    Date EXPIRE_DATE = TimeUtils.strToDate("2099-12-30");
    /**
     *
     */
    String APP_NAME = "J-dos-jdh-o2o-service";
    /**
     * MASK_STR
     */
    String MASK_STR = "*";

    /**
     * ORDER_NO_SPLIT_SUFFIX
     */
    String ORDER_NO_SPLIT_SUFFIX = "noSplit";

    /**
     * 缓存默认值
     */
    String REDIS_DEFAULT_VALUE = "1";

    /**
     * 日期常用格式
     */
    String YYYY = "yyyy";

    /**
     * 日期常用格式
     */
    String YMD = "yyyy-MM-dd";
    /**
     * 日期常用格式
     */
    String YMD3 = "yyyy/MM/dd";

    /**
     * 日期常用格式2
     */
    String YMD2 = "yyyyMMdd";

    /**
     * 日期常用格式
     */
    String YMDHMS = "yyyy-MM-dd HH:mm:ss";
    /**
     * 日期常用格式2
     */
    String YMDHMS3 = "yyyy/MM/dd HH:mm";
    /**
     * 日期常用格式
     */
    String YMDCH = "yyyy年MM月dd日";

    /**
     * 日期常用格式
     */
    String YMDHMSS = "yyyy-MM-dd HH:mm:ss.s";

    /**
     * 日期常用格式
     */
    String YMDHMSFF = "yyyy-MM-dd HH:mm:ss.FFF";

    /**
     * 日期常用格式
     */
    String YMDHM = "yyyy-MM-dd HH:mm";

    /**
     * 日期常用格式
     */
    String YMD_END_SPACE = "yyyy-MM-dd ";

    /**
     * 日期常用格式
     */
    String HM = "HH:mm";

    /**
     * 日期常用格式yyyyMMddHHmmss
     */
    String YMDHMS2 = "yyyyMMddHHmmss";
    /**
     * 日期常用格式yyyyMMdd235959
     */
    String YMDHMS2359 = "yyyyMMdd235959";
    /**
     * 日期常用格式yyyyMMdd235959
     */
    String YMDHMS00 = "yyyyMMdd000000";
    /**
     * 体检退款应用-取消来源 systemId=173
     */
    Integer SYSTEM_ID = 173;

    /**
     * 0:
     */
    String ZERO_STR = "0";
    /**
     * 1:
     */
    String ONE_STR = "1";
    /**
     * 3:
     */
    String THREE_STR = "3";


    /**
     * 2:
     */
    String TWO_STR = "2";
    /**
     * 0:
     */
    int NEGATIVE_ONE = -1;
    /**
     * 0:
     */
    int ZERO = 0;
    /**
     * 1:
     */
    int ONE = 1;

    /**
     * 2:
     */
    int TWO = 2;
    /**
     * 3
     */
    int THREE = 3;

    /**
     * 4
     */
    int FOUR = 4;

    /**
     * 5
     */
    int FIVE = 5;
    /**
     * 5
     */
    int SIX = 6;
    /**
     * 7
     */
    int SEVEN = 7;
    /**
     * 8
     */
    int EIGHT = 8;
    /**
     * 9
     */
    int NINE = 9;
    /**
     * 10
     */
    int TEN = 10;
    /**
     * 11 eleven
     */
    int ELEVEN = 11;
    /**
     * 12
     */
    int TWELVE = 12;
    /**
     * 12
     */
    int THIRTEEN = 13;
    /**
     * 15
     */
    int FIFTEEN = 15;
    /**
     * 18
     */
    int EIGHTEEN = 18;

    /**
     * 20 Twenty
     */
    int TWENTY = 20;
    /**
     * 15
     */
    int NUMBER_FIFTEEN= 15;
    /**
     * 20
     */
     int NUMBER_TWENTY = 20;
    /**
     * 50
     */
    Integer NUMBER_FIFTY = 50;
    /**
     * 51
     */
    Integer FIFTY_ONE = 51;

    /**
     * 100
     */
    Integer ONE_HUNDRED = 100;

    /**
     * 120
     */
    Integer ONE_HUNDRED_AND_TWENTY = 120;

    /**
     * 200
     */
    Integer THREE_HUNDRED = 300;

    /**
     * 消费医疗订单类型200
     */
    int ORDER_TYPE = 200;

    String ORDER_TYPE_STR = "200";
    /**
     *
     */
    int NUMBER_FIVE_THOUSAND = 5000;
    /**
     *
     */
    int NUMBER_THOUSAND = 1000;

    /**
     *
     */
    int TWO_DAY_MILLS = 2*24*60*60*1000;

    /**
     * 字符串6
     */
    String NUMBER_SIX = "6";

    /**
     * 图片前缀
     */
    String IMG_URL_PREFIX = "https://jkimg10.360buyimg.com/pop/";

    /**
     * OSS_URL_HTTPS
     */
    String HTTPS = "https";

    /**
     * base配置
     */
    String BASE = "base";

    /**
     * all配置
     */
    String ALL = "all";

    /**
     * 字符：中杠
     */
    String CHARACTER_MIDDLE_BAR = "-";

    int BU_ID = 301;

    /**
     * 健康app标识
     */
    String JDH_APP = "jdhapp";
    /**
     * 健康小程序标识
     */
    String MINIPROGRAM_JDH = "miniprogram_jdh";
    /**
     *  字符：中杠
     */
    String CHECK_ITEM = "检查项";
    /**
     * 新门店默认评分
     */
    String NEW_STORE_TAG_SCORE = "4.1";

    /**
     * 距离单位：千米
     */
    String DISTANCE_UNIT = "km";

    /**
     * 距离单位：米
     */
    String DISTANCE_UNIT_M = "m";
    /**
     *
     */
    String NEWSTORETAGKEY = "new_store";
    /**
     * APP_KEY_FOR_JDDISTRICTFROMLATLNG
     */
    String APP_KEY_FOR_JDDISTRICTFROMLATLNG = "health_examination_getjddistrictfromlatlng";

    /**
     * APP_KEY_FOR_GEO_CODING
     */
    String APP_KEY_FOR_GEO_CODING = "health_examination_geo";


    String DISPATCH_ROUTE_KEY = "DISPATCH_ROUTE_KEY";

    String COMMON_STR = "COMMON";

    String VALID_STR = "VALID";

    String STATUS_VALID_STR = "SIMPLE_VALID";

    String TARGET_DIS_STR = "TARGET_DIS_STR";

    String SELECT_STR = "SELECT";

    String STORE_ITEM_STR = "STORE_ITEM";

    String GIS_STR = "GIS";

    String UPDATE_TIME_SQL_NOW = "`update_time` = now()";

    String FILE_PDF = ".pdf";

    String FILE_JSON = ".json";

    String FILE_MP3 = ".mp3";

    String STRUCT_REPORT_PRE = "struct_";

    String ORI_STRUCT_REPORT_PRE = "oriStruct_";

    /**
     * 日期常用格式yyMMddHHmmssSSS
     */
    String YMDHMSSS2 = "yyMMddHHmmssSSS";

    Double FIFTY = 50.0;

    /**
     * 5
     */
    String SIX_STR = "6";

    /**
     * 字符：下划线
     */
    String CHARACTER_UNDERLINE = "_";

    /**
     * 消费医疗订单类型200 22 75
     */
    public static final List<Integer> ORDER_TYPE_LIST = Arrays.asList(22, 75, 200, 224);

    /**
     * 30
     */
    Integer NUMBER_THIRTY = 30;
    /**
     * 45
     */
    Integer NUMBER_FOURTY_FIVE = 45;
    /**
     * 负一
     */
    int NUMBER_MINUS_ONE = -1;

    /**
     * M站标识
     */
    String M_Station = "m_station";

    /**
     * 负一
     */
    public static final int MINUS_ONE = -1;

    /**
     * 同城属性标识
     */
    String SAME_CITY_ATTRIBUTE = "TCSX";

    /**
     * 系统创建人
     */
    String SYSTEM = "system";

    /**
     * null
     */
    String NULL = "null";

    /**
     * 日期常用格式
     */
    String YMD4 = "yyyy.MM.dd";

    /**
     * 日期常用格式
     */
    String YMDHM2 = "yyyy.MM.dd HH:mm";

    /**
     *
     */
    String ANGEL_ACTIVITY_PREFIX = "ANGEL_ACTIVITY_";

    /**
     * APP_KEY_FOR_ADDRESS_TEXT_PARSE
     */
    String APP_KEY_FOR_ADDRESS_TEXT_PARSE = "9107235CD27D4D5DA577F4F9E8B73E40";

    /**
     * 用户操作限制前缀,全部key为: 前缀 + 用户唯一id + 对外暴露接口类名 + 对外暴露接口方法名
     */
    String USER_OPERATION_LIMIT = "USER_OPERATION_LIMIT";

    /**
     * 业务缩写
     */
    String BUSINESS_CODE_ABBR = "jdkj";

    /**
     * 日期常用格式
     */
    String YMDHM3 = "MM.dd HH:mm";

    String ZERO_STR_2 = "0.00";

    /**
     * 不限制库存的数据
     */
    Integer SUPPER_INVENTORY_NUM = 9999999;

    String VISIT_TIME_SQL_NOW = "`return_visit_time` = now()";

    /**
     * 定义一个整数常量，表示200的值。
     */
    Integer TOW_HUNDRED = 200;

    /**
     * 定义样本检测状态key
     */
    String MEDICAL_PROMISE_STATUS_KEY = "MEDICAL_PROMISE_STATUS_";

    String DOCTOR_ADVICE_BACK= "173649002233873";

    String SIXTY_MINUTES_STR = "60分钟";

    String IMG_PREFIX = "https://img12.360buyimg.com/imagetools/{0}";
    /**
     * 60
     */
    Long SIXTY = 60L;
    /**
     * 内标
     */
    String IC = "内标";
    /**
     * 内标
     */
    String IC2= "IC";

    /**
     * 运营端检测单详情页
     */
    String MAN_PAGE_MEDICAL_PROMISE_DETAIL = "medicalPromiseDetailPage";

    /**
     * 运营端检测单详情页-实验室楼层
     */
    String MAN_PAGE_MEDICAL_PROMISE_DETAIL_STATION_FLOOR = "stationInfoFloor";

    /**
     * 运营端检测单详情页-实验室楼层
     */
    String MAN_PAGE_MEDICAL_PROMISE_DETAIL_STATION_FLOOR_ELE_QUERY_REPORT = "queryReport";

    /**
     *
     */
    int ONE_THOUSAND = 1000;
}
