<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jdh.o2oservice</groupId>
        <artifactId>jdh-o2o-service</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>o2o-service-base</artifactId>

    <dependencies>
        <!-- SpringBoot Web容器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jul-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- log4j-slf4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>

        <!-- SpringBoot 拦截器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- SpringBoot test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!-- Spring框架基本的核心工具 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <!--    Spring Boot 2.x starter 形式 validation    -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- 上帝之手 -->
        <dependency>
            <groupId>com.jd.pioneer</groupId>
            <artifactId>godhand-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq-client-springboot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq-client-ump</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.binlake</groupId>
            <artifactId>binlake-wave.client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jmq-client-core</artifactId>
                    <groupId>com.jd.jmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf-lite</artifactId>
        </dependency>

        <!-- 引用 PFinder SDK 库 -->
        <dependency>
            <groupId>com.jd.pfinder</groupId>
            <artifactId>pfinder-profiler-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.ump</groupId>
            <artifactId>profiler</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.jim.cli</groupId>
            <artifactId>jim-cli-spring</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>traceholder</artifactId>
                    <groupId>com.jd</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>unitrouter</artifactId>
                    <groupId>com.jd</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq-client-spring</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jmq-client-core</artifactId>
                    <groupId>com.jd.jmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.jss</groupId>
            <artifactId>jss-sdk-java</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>joda-time</artifactId>
                    <groupId>joda-time</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-codec</artifactId>
                    <groupId>commons-codec</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-jd-springboot-starter</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions> <!-- 注意：ducc sdk 使用 pom 类型依赖，type=pom 不能省略 -->
        </dependency>

        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-jd-spring</artifactId>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>com.jd.laf.binding</groupId>
            <artifactId>laf-binding-java8</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.security.configsec</groupId>
            <artifactId>spring-configsec-sdk</artifactId>
        </dependency>

        <!--aces start-->
        <dependency>
            <groupId>com.jd.security</groupId>
            <artifactId>aces-mybatisclient</artifactId>
        </dependency>
        <!--  aces - spring bean插件      -->
        <dependency>
            <groupId>com.jd.security</groupId>
            <artifactId>aces-springclient</artifactId>
            <!-- Step 1. 移除以下两个依赖包("tdeclient"和"tdecommon") -->
            <exclusions>
                <exclusion>
                    <groupId>com.jd.security</groupId>
                    <artifactId>tdeclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.security</groupId>
                    <artifactId>tdecommon</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Step 2. 重新导入其它版本的"tdeclient"依赖包 -->
        <dependency>
            <groupId>com.jd.security</groupId>
            <artifactId>tdeclient</artifactId>
        </dependency>
        <!--aces end-->

        <!--   junit     -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <!--    elasticjob    -->
        <dependency>
            <groupId>org.apache.shardingsphere.elasticjob</groupId>
            <artifactId>elasticjob-lite-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>${commons-collections.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${commons-collections4.version}</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>

        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <version>2.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-common</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.purchase.common</groupId>
            <artifactId>purchase-serializer-utils</artifactId>
        </dependency>

        <!-- 阿里数据库连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>4.2.0</version>
        </dependency>

        <!-- jd 弹性库-->
        <dependency>
            <groupId>io.vitess.driver</groupId>
            <artifactId>vtdriver</artifactId>
            <version>1.3.0-RC3</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 藏金阁Matrix -->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>matrix2-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>matrix2-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>profiler</artifactId>
                    <groupId>com.jd.ump</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--  单点登录      -->
        <dependency>
            <groupId>com.jd.ssa</groupId>
            <artifactId>oidc-client</artifactId>
            <version>1.0.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--  京东oss-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.490</version>
        </dependency>

        <!-- easyexcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.7</version>
        </dependency>

        <!--POP商品主数据-->
        <dependency>
            <artifactId>labrador-client</artifactId>
            <groupId>com.jd.gms.component</groupId>
            <version>1.5.83-SNAPSHOT</version>
        </dependency>

        <!-- 京东自营套餐信息 -->
        <dependency>
            <groupId>com.jd.health.medical.examination</groupId>
            <artifactId>jdhealth-medical-examination-man-export</artifactId>
            <version>2.1.17-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.boundaryless.lbs</groupId>
            <artifactId>lbs-center-spi</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--  门店ID、标签查询门店标签信息-->
        <dependency>
            <artifactId>jd-store-soa-client</artifactId>
            <groupId>com.jd.store</groupId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>

        <!--预估到手价-->
        <dependency>
            <groupId>com.jd.trade.guide</groupId>
            <artifactId>guide-sdk</artifactId>
            <version>4.9.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.pap</groupId>
                    <artifactId>priceinfo-api-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.pap</groupId>
            <artifactId>priceinfo-api-sdk</artifactId>
            <version>1.0.25.RELEASE</version>
        </dependency>

        <!--京东级联地址-->
        <dependency>
            <groupId>com.jd.addresstranslation</groupId>
            <artifactId>addressTranslation-api</artifactId>
            <version>1.5.9-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.service.market</groupId>
            <artifactId>market-export</artifactId>
            <version>1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.service.market</groupId>
            <artifactId>market-common</artifactId>
            <version>1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jul-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>jdhealth-medical-examination-export</artifactId>
            <groupId>com.jd.health.medical.examination</groupId>
            <version>1.6.85-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.pop.seller</groupId>
            <artifactId>vender-center-open-api</artifactId>
            <version>1.6.19</version>
        </dependency>

        <!-- 健康app中转收银台-->
        <dependency>
            <groupId>com.jdh</groupId>
            <artifactId>jdh-trade-settle-biz-sdk</artifactId>
            <version>1.0.12-SNAPSHOT</version>
        </dependency>

        <!--数科收银台-->
        <dependency>
            <groupId>com.jd.paytrade.front</groupId>
            <artifactId>paytrade-front-export</artifactId>
            <version>2.8.1.RELEASE</version>
        </dependency>

        <!--数科收银台实体-->
        <dependency>
            <groupId>com.jd.jr.orderinfo.local</groupId>
            <artifactId>jr-orderinfo-local-export</artifactId>
            <version>1.3.1</version>
        </dependency>

        <!--数科收银台实体2-->
        <dependency>
            <groupId>com.jd.payment</groupId>
            <artifactId>pay-common</artifactId>
            <version>0.0.7-SNAPSHOT</version>
        </dependency>

        <!-- 收银台JSF接口 -->
        <dependency>
            <groupId>com.jd.pay.platform</groupId>
            <artifactId>platpay-api</artifactId>
            <version>1.1.9-RELEASE</version>
        </dependency>

        <!--商家端JSF接口-->
        <dependency>
            <groupId>com.jd.health.xfyl</groupId>
            <artifactId>jdhealth-xfyl-merchant-export</artifactId>
        </dependency>

        <!--商家端JSF接口-->
        <dependency>
            <groupId>com.jd.health.xfyl</groupId>
            <artifactId>jdhealth-xfyl-open-export</artifactId>
        </dependency>

        <!--取消原因列表 + 虚拟订单退款-->
        <dependency>
            <groupId>com.jd.fce.orb</groupId>
            <artifactId>orb-contract</artifactId>
            <version>2.0.9-SNAPSHOT</version>
        </dependency>

        <!-- 地图坐标生成 -->
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-main</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 京图路线接口 -->
        <dependency>
            <groupId>com.jd.lbs.jdlbsapi</groupId>
            <artifactId>jdlbsapi-api</artifactId>
        </dependency>

        <!--改写机构号-->
        <dependency>
            <groupId>com.jd.ioms</groupId>
            <artifactId>order-ioms-export</artifactId>
            <version>1.2.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.ioms</groupId>
            <artifactId>ioms-export-cbd-sdk</artifactId>
            <version>3.1.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
        </dependency>

        <dependency>
            <groupId>JNative</groupId>
            <artifactId>JNative</artifactId>
        </dependency>

        <dependency>
            <groupId>jacob</groupId>
            <artifactId>jacob-1.7</artifactId>
        </dependency>

        <!--解析sql语句-->
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jd.abtest</groupId>
            <artifactId>touchtonev2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.dpaukov</groupId>
            <artifactId>combinatoricslib3</artifactId>
        </dependency>

        <!-- 天使之眼 -->
        <dependency>
            <groupId>com.jd.fly.angel.eye</groupId>
            <artifactId>angeleye-core</artifactId>
            <version>1.0.2</version>
        </dependency>
    </dependencies>
</project>
