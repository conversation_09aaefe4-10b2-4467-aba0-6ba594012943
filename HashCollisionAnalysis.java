import java.util.*;

/**
 * generateStableHash 碰撞概率分析
 */
public class HashCollisionAnalysis {
    
    /**
     * 复制 generateStableHash 方法进行分析
     */
    private static long generateStableHash(String input) {
        if (input == null || input.trim().isEmpty()) {
            return 0L;
        }
        
        // 方法1：使用Java内置hashCode
        long hash1 = input.hashCode();
        
        // 方法2：使用简单的多项式哈希
        long hash2 = 0;
        long prime = 31;
        for (int i = 0; i < input.length(); i++) {
            hash2 = hash2 * prime + input.charAt(i);
        }
        
        // 方法3：结合字符串长度和字符分布
        long hash3 = input.length() * 1000L;
        if (input.length() > 0) {
            hash3 += input.charAt(0) * 100L;
        }
        if (input.length() > 1) {
            hash3 += input.charAt(input.length() - 1) * 10L;
        }
        
        // 组合三种哈希值
        return hash1 ^ (hash2 << 16) ^ (hash3 << 8);
    }
    
    /**
     * 生成最终的地址ID（模拟完整流程）
     */
    private static long generateFinalAddressId(String input) {
        long hash = generateStableHash(input);
        // 确保ID在合理范围内（9位数字：100000000-999999999）
        return Math.abs(hash) % 900000000L + 100000000L;
    }

    public static void main(String[] args) {
        System.out.println("=== generateStableHash 碰撞概率分析 ===\n");
        
        // 1. 理论分析
        System.out.println("1. 理论分析：");
        System.out.println("- 哈希函数输出范围：long类型 (-2^63 到 2^63-1)");
        System.out.println("- 总可能值数量：2^64 ≈ 1.84 × 10^19");
        System.out.println("- 最终ID范围：100000000-999999999 (9亿个可能值)");
        System.out.println("- 理论碰撞概率：约 1/900000000 ≈ 1.11 × 10^-9");
        System.out.println();
        
        // 2. 实际测试 - 随机字符串
        System.out.println("2. 随机字符串碰撞测试：");
        testRandomStringCollisions(100000);
        
        // 3. 实际测试 - 地址样式字符串
        System.out.println("\n3. 地址样式字符串碰撞测试：");
        testAddressStyleCollisions(50000);
        
        // 4. 相似字符串测试
        System.out.println("\n4. 相似字符串碰撞测试：");
        testSimilarStringCollisions();
        
        // 5. 哈希分布均匀性测试
        System.out.println("\n5. 哈希分布均匀性测试：");
        testHashDistribution(100000);
        
        // 6. 生日悖论计算
        System.out.println("\n6. 生日悖论分析：");
        calculateBirthdayParadox();
    }
    
    /**
     * 测试随机字符串的碰撞情况
     */
    private static void testRandomStringCollisions(int testCount) {
        Set<Long> hashes = new HashSet<>();
        Set<Long> finalIds = new HashSet<>();
        int hashCollisions = 0;
        int idCollisions = 0;
        
        Random random = new Random(12345); // 固定种子确保可重现
        
        for (int i = 0; i < testCount; i++) {
            // 生成随机字符串
            String randomStr = generateRandomString(random, 10 + random.nextInt(20));
            
            long hash = generateStableHash(randomStr);
            long finalId = generateFinalAddressId(randomStr);
            
            if (hashes.contains(hash)) {
                hashCollisions++;
            }
            if (finalIds.contains(finalId)) {
                idCollisions++;
            }
            
            hashes.add(hash);
            finalIds.add(finalId);
        }
        
        System.out.println("测试样本数量: " + testCount);
        System.out.println("原始哈希碰撞: " + hashCollisions + " 次");
        System.out.println("最终ID碰撞: " + idCollisions + " 次");
        System.out.println("原始哈希碰撞率: " + String.format("%.6f%%", hashCollisions * 100.0 / testCount));
        System.out.println("最终ID碰撞率: " + String.format("%.6f%%", idCollisions * 100.0 / testCount));
        System.out.println("唯一哈希数量: " + hashes.size());
        System.out.println("唯一ID数量: " + finalIds.size());
    }
    
    /**
     * 测试地址样式字符串的碰撞情况
     */
    private static void testAddressStyleCollisions(int testCount) {
        Set<Long> hashes = new HashSet<>();
        Set<Long> finalIds = new HashSet<>();
        int hashCollisions = 0;
        int idCollisions = 0;
        
        String[] cities = {"北京市", "上海市", "深圳市", "广州市", "杭州市", "南京市", "成都市", "武汉市"};
        String[] districts = {"朝阳区", "海淀区", "浦东新区", "南山区", "天河区", "西湖区", "锦江区", "武昌区"};
        String[] streets = {"中山路", "人民路", "建国路", "解放路", "和平路", "胜利路", "文化路", "科技路"};
        
        Random random = new Random(12345);
        
        for (int i = 0; i < testCount; i++) {
            // 生成地址样式字符串
            String address = cities[random.nextInt(cities.length)] +
                           districts[random.nextInt(districts.length)] +
                           streets[random.nextInt(streets.length)] +
                           (random.nextInt(9999) + 1) + "号";
            
            long hash = generateStableHash(address);
            long finalId = generateFinalAddressId(address);
            
            if (hashes.contains(hash)) {
                hashCollisions++;
            }
            if (finalIds.contains(finalId)) {
                idCollisions++;
            }
            
            hashes.add(hash);
            finalIds.add(finalId);
        }
        
        System.out.println("测试样本数量: " + testCount);
        System.out.println("原始哈希碰撞: " + hashCollisions + " 次");
        System.out.println("最终ID碰撞: " + idCollisions + " 次");
        System.out.println("原始哈希碰撞率: " + String.format("%.6f%%", hashCollisions * 100.0 / testCount));
        System.out.println("最终ID碰撞率: " + String.format("%.6f%%", idCollisions * 100.0 / testCount));
        System.out.println("唯一哈希数量: " + hashes.size());
        System.out.println("唯一ID数量: " + finalIds.size());
    }
    
    /**
     * 测试相似字符串的碰撞情况
     */
    private static void testSimilarStringCollisions() {
        String baseAddress = "北京市朝阳区建国门外大街";
        Set<Long> hashes = new HashSet<>();
        Set<Long> finalIds = new HashSet<>();
        int testCount = 0;
        
        // 测试不同的门牌号
        for (int i = 1; i <= 10000; i++) {
            String address = baseAddress + i + "号";
            long hash = generateStableHash(address);
            long finalId = generateFinalAddressId(address);
            
            hashes.add(hash);
            finalIds.add(finalId);
            testCount++;
        }
        
        // 测试不同的楼层和房间号
        for (int floor = 1; floor <= 50; floor++) {
            for (int room = 1; room <= 20; room++) {
                String address = baseAddress + "1号" + floor + "层" + room + "室";
                long hash = generateStableHash(address);
                long finalId = generateFinalAddressId(address);
                
                hashes.add(hash);
                finalIds.add(finalId);
                testCount++;
            }
        }
        
        int hashCollisions = testCount - hashes.size();
        int idCollisions = testCount - finalIds.size();
        
        System.out.println("测试样本数量: " + testCount);
        System.out.println("原始哈希碰撞: " + hashCollisions + " 次");
        System.out.println("最终ID碰撞: " + idCollisions + " 次");
        System.out.println("原始哈希碰撞率: " + String.format("%.6f%%", hashCollisions * 100.0 / testCount));
        System.out.println("最终ID碰撞率: " + String.format("%.6f%%", idCollisions * 100.0 / testCount));
        System.out.println("唯一哈希数量: " + hashes.size());
        System.out.println("唯一ID数量: " + finalIds.size());
    }
    
    /**
     * 测试哈希分布均匀性
     */
    private static void testHashDistribution(int testCount) {
        Map<Integer, Integer> distribution = new HashMap<>();
        Random random = new Random(12345);
        
        for (int i = 0; i < testCount; i++) {
            String randomStr = generateRandomString(random, 15);
            long finalId = generateFinalAddressId(randomStr);
            
            // 取ID的前两位数字作为分布统计
            int prefix = (int) (finalId / 10000000); // 前两位
            distribution.put(prefix, distribution.getOrDefault(prefix, 0) + 1);
        }
        
        System.out.println("分布统计（前两位数字）:");
        distribution.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    double percentage = entry.getValue() * 100.0 / testCount;
                    System.out.println(String.format("  %d: %d 次 (%.2f%%)", 
                            entry.getKey(), entry.getValue(), percentage));
                });
        
        // 计算分布的标准差
        double mean = testCount / (double) distribution.size();
        double variance = distribution.values().stream()
                .mapToDouble(count -> Math.pow(count - mean, 2))
                .average().orElse(0.0);
        double stdDev = Math.sqrt(variance);
        
        System.out.println("分布统计:");
        System.out.println("  平均值: " + String.format("%.2f", mean));
        System.out.println("  标准差: " + String.format("%.2f", stdDev));
        System.out.println("  变异系数: " + String.format("%.4f", stdDev / mean));
    }
    
    /**
     * 生日悖论分析
     */
    private static void calculateBirthdayParadox() {
        long totalSpace = 900000000L; // 9亿个可能的ID
        
        System.out.println("基于生日悖论的碰撞概率分析:");
        System.out.println("ID空间大小: " + totalSpace);
        
        int[] sampleSizes = {1000, 10000, 30000, 50000, 100000, 300000, 500000, 1000000};
        
        for (int n : sampleSizes) {
            double probability = calculateCollisionProbability(n, totalSpace);
            System.out.println(String.format("样本数量 %d: 碰撞概率 %.6f%% (约 1/%d)", 
                    n, probability * 100, (int)(1/probability)));
        }
    }
    
    /**
     * 计算碰撞概率（生日悖论公式的近似）
     */
    private static double calculateCollisionProbability(int n, long totalSpace) {
        if (n >= totalSpace) return 1.0;
        
        // 使用近似公式: P ≈ 1 - e^(-n²/(2*N))
        double exponent = -(double)(n * n) / (2.0 * totalSpace);
        return 1.0 - Math.exp(exponent);
    }
    
    /**
     * 生成随机字符串
     */
    private static String generateRandomString(Random random, int length) {
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789中文测试地址";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }
}
